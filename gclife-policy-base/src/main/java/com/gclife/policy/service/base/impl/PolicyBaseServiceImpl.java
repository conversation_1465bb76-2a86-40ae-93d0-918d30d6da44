package com.gclife.policy.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.endorse.api.EndorseApi;
import com.gclife.endorse.api.EndorseReportApi;
import com.gclife.endorse.model.response.EndorsePayResponse;
import com.gclife.endorse.model.response.ReportEndorseResponse;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.policy.core.jooq.tables.daos.PolicyAccountDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAgentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAgentHistoryDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAllocationDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAllocationRemarkDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyApplicantDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAssignAgentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAssignRemarkDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyAttachmentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyContactInfoDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyCoveragePaymentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyHookDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyInsuredCollectDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyInsuredDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyInsuredExtendDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyOperationDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyPaymentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyPrintInfoDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyReceiptInfoDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyRenewalGrabDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyReturnVisitDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyServiceAgentDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyServiceAgentRemarkDao;
import com.gclife.policy.core.jooq.tables.daos.PolicySpecialContractDao;
import com.gclife.policy.core.jooq.tables.pojos.BaseOperationPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAccountPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAddPremiumPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAgentHistoryPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAllocationPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAllocationRemarkPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyApplicantPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAssignAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAssignRemarkPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAttachmentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyBeneficiaryInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyBeneficiaryPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyContactInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoverageLevelPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePaymentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePremiumPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyHolderPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyHookPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyInsuredCollectPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyInsuredExtendPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyInsuredPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyLoanPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyOccupationNaturePo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyOperationPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPaymentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPrintInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReceiptInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReferralInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyRenewalGrabPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReturnVisitPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyServiceAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyServiceAgentRemarkPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicySpecialContractPo;
import com.gclife.policy.dao.PolicyAgentBaseDao;
import com.gclife.policy.dao.PolicyApplicantBaseDao;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.dao.PolicyBeneficiaryBaseDao;
import com.gclife.policy.dao.PolicyCoverageBaseDao;
import com.gclife.policy.dao.PolicyInsuredBaseDao;
import com.gclife.policy.dao.PolicyPremiumBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyBeneficiaryBaseService;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import com.gclife.policy.service.base.PolicyLoanBaseService;
import com.gclife.policy.service.base.PolicyOtherInfoService;
import com.gclife.policy.service.base.PolicyPremiumBaseService;
import com.gclife.policy.service.base.PolicyReferralInfoBaseService;
import com.gclife.policy.transform.LanguageUtils;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import org.modelmapper.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.policy.model.config.PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE;

/**
 * <AUTHOR>
 * <p>
 * create 18-5-16
 * description:
 */
@Service
public class PolicyBaseServiceImpl extends BaseBusinessServiceImpl implements PolicyBaseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyBaseService.class);
    @Autowired
    private PolicyDao policyDao;
    @Autowired
    private PolicyBaseDao policyBaseDao;
    @Autowired
    private PolicyReceiptInfoDao policyReceiptInfoDao;
    @Autowired
    private PolicyAttachmentDao policyAttachmentDao;
    @Autowired
    private PolicyAccountDao policyAccountDao;
    @Autowired
    private PolicyAgentDao policyAgentDao;
    @Autowired
    private PolicyApplicantDao policyApplicantDao;
    @Autowired
    private PolicyContactInfoDao policyContactInfoDao;
    @Autowired
    private PolicyPaymentDao policyPaymentDao;
    @Autowired
    private PolicyPrintInfoDao policyPrintInfoDao;
    @Autowired
    private PolicyInsuredDao policyInsuredDao;
    @Autowired
    private PolicyInsuredExtendDao policyInsuredExtendDao;
    @Autowired
    private PolicyCoveragePaymentDao policyCoveragePaymentDao;
    @Autowired
    private PolicyInsuredCollectDao policyInsuredCollectDao;
    @Autowired
    private PolicySpecialContractDao policySpecialContractDao;
    @Autowired
    private PolicyRenewalGrabDao policyRenewalGrabDao;
    @Autowired
    private PolicyAgentHistoryDao policyAgentHistoryDao;
    @Autowired
    private PolicyOperationDao policyOperationDao;
    @Autowired
    private PolicyAssignAgentDao policyAssignAgentDao;
    @Autowired
    private PolicyAssignRemarkDao policyAssignRemarkDao;
    @Autowired
    private PolicyHookDao policyHookDao;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyBeneficiaryBaseService policyBeneficiaryBaseService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private PolicyLoanBaseService policyLoanBaseService;
    @Autowired
    private PolicyApplicantBaseDao policyApplicantBaseDao;
    @Autowired
    private PolicyInsuredBaseDao policyInsuredBaseDao;
    @Autowired
    private PolicyCoverageBaseDao policyCoverageBaseDao;
    @Autowired
    private PolicyPremiumBaseDao policyPremiumBaseDao;
    @Autowired
    private PolicyBeneficiaryBaseDao policyBeneficiaryBaseDao;
    @Autowired
    private PolicyAgentBaseDao policyAgentBaseDao;
    @Autowired
    private PolicyReturnVisitDao policyReturnVisitDao;
    @Autowired
    private PolicyAllocationDao policyAllocationDao;
    @Autowired
    private PolicyAllocationRemarkDao policyAllocationRemarkDao;
    @Autowired
    private EndorseApi endorseApi;
    @Autowired
    private PolicyReferralInfoBaseService policyReferralInfoBaseService;
    @Autowired
    private PolicyOtherInfoService policyOtherInfoService;
    @Autowired
    private PolicyServiceAgentDao policyServiceAgentDao;
    @Autowired
    private PolicyServiceAgentRemarkDao policyServiceAgentRemarkDao;
    @Autowired
    private EndorseReportApi endorseReportApi;

    /**
     * 根据保单ID查询保单详情数据
     *
     * @param policyId 保单ID
     * @return PolicyPo
     */
    @Override
    public PolicyPo queryPolicyPo(String policyId) {
        PolicyPo policyPo = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            policyPo = policyBaseDao.queryPolicyPo(policyId);
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return policyPo;
    }

    /**
     * 根据保单ID查询保单详情数据
     *
     * @param policyId 保单ID
     * @return PolicyBo
     */
    @Override
    public PolicyBo queryPolicyBo(String policyId) {
        PolicyBo policyBo = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            // 查询保单基本信息
            policyBo = policyBaseDao.queryPolicyBoByJoin(policyId);
            if (AssertUtils.isNotNull(policyBo)) {

                PolicyApplicantBo policyApplicantBo = policyApplicantBaseDao.queryPolicyApplicant(policyId);
                policyBo.setApplicant(policyApplicantBo);

                // 被保人下的险种信息,单独提取出来
                List<PolicyCoverageBo> policyCoverageBos = policyCoverageBaseService.listPolicyCoverage(policyId);
                if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                    policyBo.setListInsuredCoverage(policyCoverageBos);
                }
                // 查询保单被保人信息
                List<PolicyInsuredBo> policyInsuredBos = this.listPolicyInsuredBo(policyId);
                /*查询保单受益人*/
                List<PolicyBeneficiaryInfoBo> listBeneficiary = policyBeneficiaryBaseService.listBeneficiaryInfoBo(policyId);
                policyBo.setListPolicyInsured(policyInsuredBos);
                //单独设置险种
                if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                    Map<String, List<PolicyCoverageBo>> map = policyCoverageBos.stream()
                            .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                            .collect(Collectors.groupingBy(PolicyCoverageBo::getInsuredId));
                    // 设置被保人险种信息
                    policyInsuredBos.forEach(policyInsuredBo -> {
                        if (AssertUtils.isNotEmpty(map.get(policyInsuredBo.getInsuredId()))) {
                            policyInsuredBo.setListPolicyCoverage(map.get(policyInsuredBo.getInsuredId()));
                            policyInsuredBo.setMult(map.get(policyInsuredBo.getInsuredId()).get(0).getMult());
                        }
                        if (AssertUtils.isNotEmpty(listBeneficiary)) {
                            List<PolicyBeneficiaryInfoBo> list = listBeneficiary.stream().filter(beneficiaryInfoBo -> policyInsuredBo.getInsuredId().equals(beneficiaryInfoBo.getInsuredId())).collect(Collectors.toList());
                            policyInsuredBo.setListPolicyBeneficiary(list);
                        }
                    });
                }
                // 查询保单附件信息
                List<PolicyAttachmentPo> applyAttachmentPos = this.listPolicyAttachment(policyId);
                if (AssertUtils.isNotEmpty(applyAttachmentPos)) {
                    List<PolicyAttachmentBo> policyAttachmentBos = (List<PolicyAttachmentBo>) this.converterList(applyAttachmentPos, new TypeToken<List<PolicyAttachmentBo>>() {
                    }.getType());
                    policyBo.setListPolicyAttachment(policyAttachmentBos);
                }

                // 查询保单缴费信息
                List<PolicyPaymentBo> policyPaymentBos = policyBaseDao.getListPolicyPayment(policyId, null);
                if (AssertUtils.isNotEmpty(policyPaymentBos)) {
                    policyBo.setPolicyPayment(policyPaymentBos.get(0));
                    if (AssertUtils.isNotNull(policyBo.getPolicyPremium())) {
                        policyBo.getPolicyPremium().setPolicyPayment(policyPaymentBos.get(0));
                    }
                    /*查询保单已缴费信息列表*/
                    List<PolicyPaymentBo> listPolicyPaymentBo = policyPaymentBos.stream().filter(policyPaymentBo ->
                                    policyPaymentBo.getPaymentStatusCode().equals(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name()))
                            .collect(Collectors.toList());
                    policyBo.setListPolicyPayment(listPolicyPaymentBo);
                }

                List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.listPolicyAddPremium(policyId);
                if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
                    policyBo.setListPolicyAddPremium(policyAddPremiumPos);
                }

                PolicyLoanPo policyLoanPo = policyLoanBaseService.queryPolicyLoanPo(policyId);
                if (AssertUtils.isNotNull(policyLoanPo)) {
                    policyBo.setLoanContract(policyLoanPo);
                }

                // 查询联系人信息
                PolicyContactInfoPo contactInfoPo = policyBaseDao.getPolicyContactInfo(policyId);
                if (AssertUtils.isNotNull(contactInfoPo)) {
                    PolicyContactInfoBo contactInfoBo = (PolicyContactInfoBo) this.converterObject(contactInfoPo, PolicyContactInfoBo.class);
                    policyBo.setPolicyContactInfo(contactInfoBo);
                }

                // 查询账户信息
                List<PolicyAccountPo> policyAccountPos = policyAccountDao.fetchByPolicyId(policyId);
                if (AssertUtils.isNotNull(policyAccountPos)) {
                    List<PolicyAccountBo> listPolicyAccount = (List<PolicyAccountBo>) this.converterList(policyAccountPos, new TypeToken<List<PolicyAccountBo>>() {
                    }.getType());
                    policyBo.setListPolicyAccount(listPolicyAccount);
                }

                /*查询保单推荐信息*/
                PolicyReferralInfoPo policyReferralInfoPo = policyReferralInfoBaseService.queryPolicyReferralInfoPo(policyId);
                if (AssertUtils.isNotNull(policyReferralInfoPo)) {
                    policyBo.setReferralInfo(policyReferralInfoPo);
                }
                /*查询保单持有人信息*/
                PolicyHolderPo policyHolderPo = policyOtherInfoService.queryPolicyHolderPo(policyId);
                if (AssertUtils.isNotNull(policyHolderPo)) {
                    policyBo.setHolder(policyHolderPo);
                }
                //职业性质
                List<PolicyOccupationNaturePo> policyOccupationNaturePos = policyOtherInfoService.queryAllPolicyOccupationNaturePo(policyId, null);
                if (AssertUtils.isNotEmpty(policyOccupationNaturePos)) {
                    policyBo.setOccupationNature(policyOccupationNaturePos);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return policyBo;
    }

    /**
     * 查询保单代理人数据
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyAgentPo queryPolicyAgent(String policyId) {
        return policyBaseDao.getPolicyAgent(policyId);
    }

    /**
     * 查询投保人信息
     *
     * @param policyIdOrNo 保单ID
     * @return PolicyApplicantBo
     */
    @Override
    public PolicyApplicantBo queryPolicyApplicant(String policyIdOrNo) {
        PolicyApplicantBo policyApplicantBo = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyIdOrNo, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyIdOrNo);

            policyApplicantBo = policyBaseDao.getPolicyApplicant(policyIdOrNo);

        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_APPLICANT_ERROR);
        }
        return policyApplicantBo;
    }

    @Override
    public List<PolicyInsuredBo> listPolicyInsured(String policyId) {
        return this.listPolicyInsured(policyId, null);
    }

    @Override
    public List<PolicyInsuredBo> listPolicyInsuredForPb(String policyId) {
        return this.getPolicyInsuredForPb(policyId, null);
    }


    /**
     * 批量查询被保人
     *
     * @param policyIds 保单ID
     * @return
     */
    @Override
    public List<PolicyInsuredPo> listPolicyInsured(List<String> policyIds) {
        return policyBaseDao.listPolicyInsured(policyIds);
    }

    /**
     * 根据保单ID查询被保人统计信息
     *
     * @param policyId 保单ID
     * @return PolicyInsuredCollectPo
     */
    @Override
    public PolicyInsuredCollectPo queryPolicyInsuredCollect(String policyId) {
        PolicyInsuredCollectPo policyInsuredCollectPo;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            policyInsuredCollectPo = policyBaseDao.getPolicyInsuredCollect(policyId);

            LOGGER.info("policyInsuredCollectPo:" + JSON.toJSONString(policyInsuredCollectPo));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_COLLECT_ERROR);
            }
        }
        return policyInsuredCollectPo;
    }

    @Override
    public List<PolicyInsuredBo> listPolicyInsured(String policyId, String keyword) {
        List<PolicyInsuredBo> policyInsuredBos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            // 查询被保人列表
            policyInsuredBos = policyBaseDao.getPolicyInsuredList(policyId, keyword);

            // 查询被保人险种信息
            List<PolicyCoverageBo> policyCoverageBos = policyCoverageBaseService.listPolicyCoverage(policyId);

            //保全增减员的承保日期取实收日期
            List<String> coverageIds = policyCoverageBos.stream().map(PolicyCoverageBo::getCoverageId).distinct().collect(Collectors.toList());
            ResultObject<List<ReportEndorseResponse>> listResultObject = endorseReportApi.queryEndorseCoverageList(coverageIds);

            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                List<ReportEndorseResponse> endorseResponses = listResultObject.getData();
                policyInsuredBos.forEach(policyInsuredBo -> {
                    endorseResponses.stream().filter(reportEndorseResponse -> policyInsuredBo.getInsuredId().equals(reportEndorseResponse.getInsuredId())).findFirst().
                            ifPresent(reportEndorseResponse -> {
                                if ("gprj20200304004".equals(reportEndorseResponse.getProjectId()) && AssertUtils.isNotNull(reportEndorseResponse.getGainedDate())) {
                                    policyInsuredBo.setEffectiveDate(reportEndorseResponse.getGainedDate());
                                }
                            });
                });
            }

            // 查询险种档次信息
            List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.listPolicyCoverageLevel(policyId, null);
            List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.listPolicyAddPremium(policyId);

            if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                Map<String, List<PolicyCoverageBo>> coverageMap = policyCoverageBos.stream()
                        .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .collect(Collectors.groupingBy(PolicyCoverageBo::getInsuredId));
                // 设置被保人险种信息
                policyInsuredBos.forEach(policyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(coverageMap.get(policyInsuredBo.getInsuredId()))) {
                        policyInsuredBo.setListPolicyCoverage(coverageMap.get(policyInsuredBo.getInsuredId()));
                        policyInsuredBo.setMult(coverageMap.get(policyInsuredBo.getInsuredId()).get(0).getMult());
                    }
                    // 设置险种档次
                    policyInsuredBo.getListCoverage().forEach(coverageBo -> {
                        if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                            if (TerminologyConfigEnum.WHETHER.NO.name().equals(coverageBo.getDutyChooseFlag())) {
                                List<PolicyCoverageLevelPo> levelPos = policyCoverageLevelPos.stream()
                                        .filter(levelPo -> levelPo.getCoverageId().equals(coverageBo.getCoverageId()))
                                        .collect(Collectors.toList());
                                coverageBo.setListCoverageLevel(levelPos);
                            }
                        }
                        //设置保单加费
                        if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
                            List<PolicyAddPremiumPo> addPremiumPos = policyAddPremiumPos.stream()
                                    .filter(policyAddPremiumPo -> AssertUtils.isNotEmpty(policyAddPremiumPo.getCoverageId())
                                            && PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name().equals(policyAddPremiumPo.getAddPremiumStatus())
                                            && policyAddPremiumPo.getCoverageId().equals(coverageBo.getCoverageId()))
                                    .collect(Collectors.toList());
                            coverageBo.setListAddPremium(addPremiumPos);
                        }
                    });
                });

            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
            }
        }
        return policyInsuredBos;
    }


    public List<PolicyInsuredBo> getPolicyInsuredForPb(String policyId, String keyword) {
        List<PolicyInsuredBo> policyInsuredBos;
        List<String> insuredIds = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            //保险证打印新契约取最原始数据
            List<PolicyCoverageLevelBo> policyCoverageLevelPos = policyCoverageBaseService.listGroupPolicyPaymentCoverageLevel(policyId);
            if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                insuredIds = policyCoverageLevelPos.stream().map(PolicyCoverageLevelBo::getInsuredId).distinct().collect(Collectors.toList());
            }

            // 查询被保人列表
            policyInsuredBos = policyBaseDao.getPolicyInsuredListForPb(policyId, insuredIds);

            /*查询保单受益人*/
            List<PolicyBeneficiaryInfoBo> listBeneficiary = policyBeneficiaryBaseService.listBeneficiaryInfoBo(policyId);

            // 查询被保人险种信息
            List<PolicyCoverageBo> policyCoverageBos = policyCoverageBaseService.listPolicyCoverageForPb(policyId);

            //保全增减员的承保日期取实收日期
            List<String> coverageIds = policyCoverageBos.stream().map(PolicyCoverageBo::getCoverageId).distinct().collect(Collectors.toList());
            ResultObject<List<ReportEndorseResponse>> listResultObject = endorseReportApi.queryEndorseCoverageList(coverageIds);

            //设置被保人受益人信息
            policyInsuredBos.forEach(policyInsuredBo -> {
                if (AssertUtils.isNotEmpty(listBeneficiary)) {
                    List<PolicyBeneficiaryInfoBo> list = listBeneficiary.stream().filter(beneficiaryInfoBo -> policyInsuredBo.getInsuredId().equals(beneficiaryInfoBo.getInsuredId())).collect(Collectors.toList());
                    policyInsuredBo.setListPolicyBeneficiary(list);
                }
            });

            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                List<ReportEndorseResponse> endorseResponses = listResultObject.getData();
                policyInsuredBos.forEach(policyInsuredBo -> {
                    endorseResponses.stream().filter(reportEndorseResponse -> policyInsuredBo.getInsuredId().equals(reportEndorseResponse.getInsuredId())).findFirst().
                            ifPresent(reportEndorseResponse -> {
                                if ("gprj20200304004".equals(reportEndorseResponse.getProjectId()) && AssertUtils.isNotNull(reportEndorseResponse.getGainedDate())) {
                                    policyInsuredBo.setEffectiveDate(reportEndorseResponse.getGainedDate());
                                }
                            });
                });
            }

            // 查询险种档次信息
            //List<PolicyCoverageLevelBo> policyCoverageLevelPos = policyCoverageBaseService.listGroupPolicyPaymentCoverageLevel(policyId);
            List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.listPolicyAddPremium(policyId);

            if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                Map<String, List<PolicyCoverageBo>> coverageMap = policyCoverageBos.stream()
                        .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .collect(Collectors.groupingBy(PolicyCoverageBo::getInsuredId));
                // 设置被保人险种信息
                policyInsuredBos.forEach(policyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(coverageMap.get(policyInsuredBo.getInsuredId()))) {
                        policyInsuredBo.setListPolicyCoverage(coverageMap.get(policyInsuredBo.getInsuredId()));
                        policyInsuredBo.setMult(coverageMap.get(policyInsuredBo.getInsuredId()).get(0).getMult());
                    }
                    // 设置险种档次
                    policyInsuredBo.getListCoverage().forEach(coverageBo -> {
                        if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                            if (TerminologyConfigEnum.WHETHER.NO.name().equals(coverageBo.getDutyChooseFlag())) {
                                List<PolicyCoverageLevelPo> levelPos = policyCoverageLevelPos.stream()
                                        .filter(levelPo -> levelPo.getCoverageId().equals(coverageBo.getCoverageId()))
                                        .collect(Collectors.toList());
                                coverageBo.setListCoverageLevel(levelPos);
                            }
                        }
                        //设置保单加费
                        if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
                            List<PolicyAddPremiumPo> addPremiumPos = policyAddPremiumPos.stream()
                                    .filter(policyAddPremiumPo -> AssertUtils.isNotEmpty(policyAddPremiumPo.getCoverageId())
                                            && PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name().equals(policyAddPremiumPo.getAddPremiumStatus())
                                            && policyAddPremiumPo.getCoverageId().equals(coverageBo.getCoverageId()))
                                    .collect(Collectors.toList());
                            coverageBo.setListAddPremium(addPremiumPos);
                        }
                    });
                });

            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
            }
        }
        return policyInsuredBos;
    }

    @Override
    public List<PolicyInsuredBo> listPolicyInsuredBo(String policyId) {

        // 参数校验
        AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
        LOGGER.info("policyId:" + policyId);

        // 查询被保人列表
        return policyBaseDao.getPolicyInsuredList(policyId, null);
    }

    /**
     * 查询投保单被保人列表
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return list
     */
    @Override
    public List<PolicyInsuredBo> getPolicyInsuredByVersionNo(String policyId, String versionNo) {
        return policyBaseDao.getPolicyInsuredByVersionNo(policyId, versionNo);
    }

    /**
     * 查询保单联系信息
     *
     * @param policyId 保单ID
     * @return PolicyContactInfoPo
     */
    @Override
    public PolicyContactInfoPo queryPolicyContactInfo(String policyId) {
        PolicyContactInfoPo policyContactInfoPo = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            policyContactInfoPo = policyBaseDao.getPolicyContactInfo(policyId);

            LOGGER.info("policyContactInfoPo:" + JSON.toJSONString(policyContactInfoPo));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CONTACT_INFO_ERROR);
        }
        return policyContactInfoPo;
    }

    /**
     * 根据保单ID查询保单附件列表
     *
     * @param policyId 保单ID
     * @return list
     */
    @Override
    public List<PolicyAttachmentPo> listPolicyAttachment(String policyId) {
        return this.listPolicyAttachment(policyId, "");
    }

    /**
     * 根据保单ID、附件类型编码查询保单附件列表
     *
     * @param policyId           保单ID
     * @param attachmentTypeCode 保单附件类形编码
     * @return list
     */
    @Override
    public List<PolicyAttachmentPo> listPolicyAttachment(String policyId, String attachmentTypeCode) {
        List<PolicyAttachmentPo> policyAttachmentPos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
//            LOGGER.info("policyId:" + policyId);
//            LOGGER.info("attachmentTypeCode:" + attachmentTypeCode);

            policyAttachmentPos = policyBaseDao.getPolicyAttachmentList(policyId, attachmentTypeCode);

//            LOGGER.info("policyAttachmentPos:" + JSON.toJSONString(policyAttachmentPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPos;
    }

    /**
     * 根据保单ID、附件类型编码集查询保单附件列表
     *
     * @param policyId            保单ID
     * @param attachmentTypeCodes 投保单附件类形编码集
     * @return list
     */
    @Override
    public List<PolicyAttachmentPo> listPolicyAttachment(String policyId, List<String> attachmentTypeCodes) {
        List<PolicyAttachmentPo> policyAttachmentPos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);
            LOGGER.info("attachmentTypeCodes:" + attachmentTypeCodes);

            policyAttachmentPos = policyBaseDao.getApplyAttachmentList(policyId, attachmentTypeCodes);

            LOGGER.info("policyAttachmentPos:" + JSON.toJSONString(policyAttachmentPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPos;
    }

    @Override
    public void deletePolicyAttachment(String policyId, String attachmentTypeCode) {
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            LOGGER.info("policyId:" + policyId);
            LOGGER.info("attachmentTypeCode:" + attachmentTypeCode);

            List<PolicyAttachmentPo> policyAttachmentPos = this.listPolicyAttachment(policyId, attachmentTypeCode);
            //删除数据
            if (AssertUtils.isNotEmpty(policyAttachmentPos)) {
                policyAttachmentDao.delete(policyAttachmentPos);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询保单特别约定
     *
     * @param policyId 保单ID
     * @return PolicySpecialContractPo
     */
    @Override
    public List<PolicySpecialContractPo> listPolicySpecialContract(String policyId) {
        List<PolicySpecialContractPo> policySpecialContractPos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            policySpecialContractPos = policyBaseDao.getPolicySpecialContract(policyId);

            LOGGER.info("policySpecialContractPos:" + JSON.toJSONString(policySpecialContractPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_SPECIAL_CONTRACT_ERROR);
        }
        return policySpecialContractPos;
    }

    @Override
    public PolicyReceiptInfoPo queryPolicyReceiptInfo(String policyId) {
        PolicyReceiptInfoPo policyReceiptInfoPos = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            LOGGER.info("policyId:" + policyId);

            policyReceiptInfoPos = policyBaseDao.getPolicyReceiptInfo(policyId);

            LOGGER.info("policyReceiptInfoPos:" + JSON.toJSONString(policyReceiptInfoPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_RECEIPT_INFO_ERROR);
        }
        return policyReceiptInfoPos;
    }

    /**
     * 查询保单回执
     *
     * @param policyIds 保单ID
     * @return list
     */
    @Override
    public List<PolicyReceiptInfoPo> listPolicyReceiptInfo(List<String> policyIds) {
        return policyBaseDao.listPolicyReceiptInfo(policyIds);
    }

    @Override
    public void savePolicyReceiptInfo(String userId, PolicyReceiptInfoPo policyReceiptInfoPo) {
        try {
            if (!AssertUtils.isNotEmpty(policyReceiptInfoPo.getReceiptInfoId())) {
                //执行新增
                policyReceiptInfoPo.setReceiptInfoId(UUIDUtils.getUUIDShort());
                policyReceiptInfoPo.setCreatedUserId(userId);
                policyReceiptInfoPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyReceiptInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                policyReceiptInfoDao.insert(policyReceiptInfoPo);
            } else {
                //执行修改
                policyReceiptInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyReceiptInfoPo.setUpdatedUserId(userId);
                policyReceiptInfoDao.update(policyReceiptInfoPo);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_RECEIPT_INFO_ERROR);
        }
    }

    /**
     * 保存回执行信息
     *
     * @param policyReceiptInfoPo
     */
    @Override
    public void savePolicyReceiptInfo(PolicyReceiptInfoPo policyReceiptInfoPo) {
        try {
            if (!AssertUtils.isNotEmpty(policyReceiptInfoPo.getReceiptInfoId())) {
                //执行新增
                policyReceiptInfoPo.setReceiptInfoId(UUIDUtils.getUUIDShort());
                policyReceiptInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                policyReceiptInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyReceiptInfoDao.insert(policyReceiptInfoPo);
            } else {
                //执行修改
                policyReceiptInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyReceiptInfoDao.update(policyReceiptInfoPo);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            throwsException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_RECEIPT_INFO_ERROR);
        }
    }

    /**
     * 批量更新回执行信息
     *
     * @param policyReceiptInfoPos 保单回执
     * @param userId               用户ID
     */
    @Override
    public void updatePolicyReceiptInfo(List<PolicyReceiptInfoPo> policyReceiptInfoPos, String userId) {
        if (AssertUtils.isNotEmpty(policyReceiptInfoPos)) {
            policyReceiptInfoPos.forEach(policyReceiptInfoPo -> {
                policyReceiptInfoPo.setUpdatedUserId(userId);
                policyReceiptInfoPo.setUpdatedDate(System.currentTimeMillis());
            });
            policyReceiptInfoDao.update(policyReceiptInfoPos);
        }
    }

    @Override
    public List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyId) {
        return this.listPolicyCoverageOfInsured(policyId, null);
    }

    @Override
    public List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyId, String insuredId) {
        List<PolicyCoveragePo> applyCoveragePos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            LOGGER.info("policyId:" + policyId);
            LOGGER.info("insuredId:" + insuredId);

            applyCoveragePos = policyBaseDao.getPolicyCoverageList(policyId, insuredId);

            LOGGER.info("applyCoveragePos:" + JSON.toJSONString(applyCoveragePos));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
            }
        }
        return applyCoveragePos;
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listGroupPolicyCoverage(String policyId) {
        return policyBaseDao.listGroupPolicyCoverage(policyId);
    }

    /**
     * 查询保单ID对应的险种责任
     *
     * @param policyId 保单ID
     * @return list
     */
    @Override
    public List<PolicyCoverageDutyBo> getPolicyCoverageDutyList(String policyId) {
        List<PolicyCoverageDutyBo> policyCoverageDutyBos = policyBaseDao.getPolicyCoverageDutyList(policyId);
        if (AssertUtils.isNotEmpty(policyCoverageDutyBos)) {
            List<String> coverageDutyIds = policyCoverageDutyBos.stream().filter(policyCoverageDutyBo -> AssertUtils.isNotEmpty(policyCoverageDutyBo.getCoverageDutyId())).map(PolicyCoverageDutyBo::getCoverageDutyId).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(coverageDutyIds)) {
                policyCoverageDutyBos.forEach(policyCoverageDutyBo -> {
                    List<PolicyCoverageLevelPo> applyCoverageLevelPos = this.listPolicyCoverageLevel(policyId, policyCoverageDutyBo.getCoverageDutyId());
                    if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                        policyCoverageDutyBo.setListCoverageLevel(applyCoverageLevelPos);
                    }
                });
            }
        }
        return policyCoverageDutyBos;
    }

    /**
     * 查询险种档次信息
     *
     * @param policyId       保单ID
     * @param coverageDutyId 责任ID
     * @return PolicyCoverageLevelPos
     */
    @Override
    public List<PolicyCoverageLevelPo> listPolicyCoverageLevel(String policyId, String coverageDutyId) {
        return policyCoverageBaseService.listPolicyCoverageLevel(policyId, coverageDutyId);
    }

    /**
     * 根据投保单ID查询保单信息
     *
     * @param applyId 投保单ID
     * @return PolicyPo
     */
    @Override
    public PolicyPo queryPolicyByApplyId(String applyId) {
        PolicyPo policyPo;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, applyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);

            LOGGER.info("applyId:" + applyId);

            policyPo = policyBaseDao.getPolicyByApplyId(applyId);

            LOGGER.info("policyPo:" + JSON.toJSONString(policyPo));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
            }
        }
        return policyPo;
    }

    /**
     * 根据保单号查询保单信息
     *
     * @param policyNo 保单号
     * @return PolicyPo
     */
    @Override
    public PolicyPo queryPolicyByPolicyNo(String policyNo) {
        return policyBaseDao.getPolicyByPolicyNo(policyNo);
    }

    /**
     * 保存保单基础信息
     *
     * @param policyPo 保单基础信息
     */
    @Override
    public void savePolicyPo(PolicyPo policyPo) {
        if (!AssertUtils.isNotEmpty(policyPo.getPolicyId())) {
            //执行新增
            policyPo.setPolicyId(UUIDUtils.getUUIDShort());
            policyPo.setCreatedDate(DateUtils.getCurrentTime());
            policyPo.setVersionNo(DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false));
            policyPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
            policyDao.insert(policyPo);
        } else {
            //执行修改
            policyPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyDao.update(policyPo);
        }
    }

    /**
     * 保存保单基础信息
     *
     * @param policyPo 保单基础信息
     * @param userId   用户ID
     */
    @Override
    public void savePolicy(PolicyPo policyPo, String userId) {
        if (policyPo.isForceSave() || !AssertUtils.isNotEmpty(policyPo.getPolicyId())) {
            // 执行新增
            if (!AssertUtils.isNotEmpty(policyPo.getPolicyId())) {
                policyPo.setPolicyId(UUIDUtils.getUUIDShort());
                policyPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
            }
            policyPo.setVersionNo(DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false));
            policyPo.setCreatedUserId(userId);
            policyPo.setCreatedDate(DateUtils.getCurrentTime());
            policyPo.setUpdatedUserId(userId);
            policyPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyDao.insert(policyPo);
        } else {
            // 执行修改
            policyPo.setUpdatedUserId(userId);
            policyPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyDao.update(policyPo);
        }
    }


    /**
     * 保存保单基础信息
     *
     * @param policyBo 保单基础信息(全量信息保存)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePolicyBo(PolicyBo policyBo) {
        //保存保单信息对象
        this.savePolicyPo(policyBo);
        //保存保单代理人信息
        this.savePolicyAgent(policyBo.getPolicyAgent());
        //保存保单投保人
        this.savePolicyApplicant(policyBo.getPolicyApplicant());
        //保存缴费信息
        policyPremiumBaseService.savePolicyPremium(policyBo.getPolicyPremium());
        //保存保单缴费信息
        this.savePolicyPayment(policyBo.getPolicyPayment());
        //保存保单被保人信息
        policyBo.getListPolicyInsured().forEach(policyInsuredBo -> {
            //保存被保人信息
            this.savePolicyInsured(policyInsuredBo);

            policyInsuredBo.getListPolicyCoverage().stream().filter(e -> AssertUtils.isNotNull(e)).collect(Collectors.toList()).forEach(policyCoverageBo -> {
                //保存保单险种信息
                policyCoverageBaseService.savePolicyCoverage(policyCoverageBo);
                PolicyCoveragePremiumBo policyCoveragePremiumBo = policyCoverageBo.getPolicyCoveragePremium();

                if (AssertUtils.isNotNull(policyCoveragePremiumBo)) {
                    //保存保单险种保费信息
                    policyPremiumBaseService.savePolicyCoveragePremium(policyCoveragePremiumBo);

                    PolicyCoveragePaymentBo policyCoveragePaymentBo = policyCoveragePremiumBo.getPolicyCoveragePayment();
                    if (AssertUtils.isNotNull(policyCoveragePaymentBo)) {
                        //保存保单险种支付信息
                        policyCoverageBaseService.savePolicyCoveragePayment(policyCoveragePaymentBo);
                    }
                }
            });
        });
        //保存保单回执信息
        PolicyReceiptInfoBo policyReceiptInfoBo = policyBo.getPolicyReceiptInfo();
        if (AssertUtils.isNotNull(policyReceiptInfoBo)) {
            this.savePolicyReceiptInfo(policyReceiptInfoBo);
        }
    }

    /**
     * 保存保单账户信息
     *
     * @param policyAccountPo 保单账户信息
     */
    @Override
    @Transactional
    public void savePolicyAccount(PolicyAccountPo policyAccountPo) {
        try {
            LOGGER.info("policyAccountPo:" + JSON.toJSONString(policyAccountPo));
            if (!AssertUtils.isNotEmpty(policyAccountPo.getPolicyAccountId())) {
                //执行新增
                policyAccountPo.setPolicyAccountId(UUIDUtils.getUUIDShort());
                policyAccountPo.setCreatedDate(DateUtils.getCurrentTime());
                policyAccountPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyAccountDao.insert(policyAccountPo);
            } else {
                //执行修改
                policyAccountPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyAccountDao.update(policyAccountPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_ACCOUNT_ERROR);
        }
    }

    /**
     * 保存保单代理人信息
     *
     * @param policyAgentPo 保单代理人信息
     */
    @Override
    @Transactional
    public void savePolicyAgent(PolicyAgentPo policyAgentPo) {
        try {
            LOGGER.info("policyAgentPo:" + JSON.toJSONString(policyAgentPo));
            if (!AssertUtils.isNotEmpty(policyAgentPo.getPolicyAgentId())) {
                //执行新增
                policyAgentPo.setPolicyAgentId(UUIDUtils.getUUIDShort());
                policyAgentPo.setCreatedDate(DateUtils.getCurrentTime());
                policyAgentPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyAgentDao.insert(policyAgentPo);
            } else {
                //执行修改
                policyAgentPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyAgentDao.update(policyAgentPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_AGENT_ERROR);
        }
    }

    /**
     * 保存保单投保人信息
     *
     * @param policyApplicantPo 保单投保人信息
     */
    @Override
    @Transactional
    public void savePolicyApplicant(PolicyApplicantPo policyApplicantPo) {
        try {
            LOGGER.info("policyApplicantPo:" + JSON.toJSONString(policyApplicantPo));
            if (!AssertUtils.isNotEmpty(policyApplicantPo.getApplicantId())) {
                //执行新增
                policyApplicantPo.setApplicantId(UUIDUtils.getUUIDShort());
                policyApplicantPo.setCreatedDate(DateUtils.getCurrentTime());
                policyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyApplicantPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyApplicantDao.insert(policyApplicantPo);
            } else {
                //执行修改
                policyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyApplicantDao.update(policyApplicantPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_APPLICANT_ERROR);
        }
    }

    /**
     * 保存保单联系人信息
     *
     * @param policyContactInfoPo 保单联系人信息
     */
    @Override
    @Transactional
    public void savePolicyContactInfo(PolicyContactInfoPo policyContactInfoPo) {
        try {
            LOGGER.info("policyContactInfoPo:" + JSON.toJSONString(policyContactInfoPo));
            if (!AssertUtils.isNotEmpty(policyContactInfoPo.getPolicyContactId())) {
                //执行新增
                policyContactInfoPo.setPolicyContactId(UUIDUtils.getUUIDShort());
                policyContactInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                policyContactInfoPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyContactInfoDao.insert(policyContactInfoPo);
            } else {
                //执行修改
                policyContactInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyContactInfoDao.update(policyContactInfoPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_CONTACT_INFO_ERROR);
        }
    }

    @Override
    @Transactional
    public void savePolicyAttachment(String userId, PolicyAttachmentPo policyAttachmentPo) {
        try {
            LOGGER.info("policyAttachmentPo:" + JSON.toJSONString(policyAttachmentPo));
            if (!AssertUtils.isNotEmpty(policyAttachmentPo.getPolicyAttachmentId())) {
                //执行新增
                policyAttachmentPo.setPolicyAttachmentId(UUIDUtils.getUUIDShort());
                policyAttachmentPo.setCreatedDate(DateUtils.getCurrentTime());
                policyAttachmentPo.setCreatedUserId(userId);
                policyAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyAttachmentDao.insert(policyAttachmentPo);
            } else {
                //执行修改
                policyAttachmentPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyAttachmentPo.setUpdatedUserId(userId);
                policyAttachmentDao.update(policyAttachmentPo);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_ATTACHMENT_ERROR);
        }
    }

    @Override
    @Transactional
    public void savePolicyAttachment(String userId, List<PolicyAttachmentPo> policyAttachmentPos) {
        try {
            LOGGER.info("policyAttachmentPos:" + JSON.toJSONString(policyAttachmentPos));
            if (AssertUtils.isNotEmpty(policyAttachmentPos)) {
                policyAttachmentPos.forEach(policyAttachmentPo -> {
                    policyAttachmentPo.setPolicyAttachmentId(UUIDUtils.getUUIDShort());
                    policyAttachmentPo.setCreatedUserId(userId);
                    policyAttachmentPo.setCreatedDate(DateUtils.getCurrentTime());
                    policyAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                });
                policyAttachmentDao.insert(policyAttachmentPos);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_ATTACHMENT_ERROR);
        }
    }

    /**
     * 保存特别约定信息
     *
     * @param userId                  用户ID
     * @param policySpecialContractPo 特约信息
     */
    @Override
    public void savePolicySpecialContract(String userId, PolicySpecialContractPo policySpecialContractPo) {
        try {
            LOGGER.info("policySpecialContractPo:" + JSON.toJSONString(policySpecialContractPo));
            if (!AssertUtils.isNotEmpty(policySpecialContractPo.getSpecialContractId())) {
                //执行新增
                policySpecialContractPo.setSpecialContractId(UUIDUtils.getUUIDShort());
                policySpecialContractPo.setCreatedDate(DateUtils.getCurrentTime());
                policySpecialContractPo.setCreatedUserId(userId);
                policySpecialContractPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policySpecialContractDao.insert(policySpecialContractPo);
            } else {
                //执行修改
                policySpecialContractPo.setUpdatedDate(DateUtils.getCurrentTime());
                policySpecialContractPo.setUpdatedUserId(userId);
                policySpecialContractDao.update(policySpecialContractPo);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_SPECIAL_CONTRACT_ERROR);
        }
    }

    /**
     * 批量新增保单特约信息
     *
     * @param userId                   用户ID
     * @param policySpecialContractPos 特约信息集
     */
    @Override
    public void savePolicySpecialContract(String userId, List<PolicySpecialContractPo> policySpecialContractPos) {
        try {
            LOGGER.info("policySpecialContractPos:" + JSON.toJSONString(policySpecialContractPos));
            if (AssertUtils.isNotEmpty(policySpecialContractPos)) {
                policySpecialContractPos.forEach(policySpecialContractPo -> {
                    policySpecialContractPo.setSpecialContractId(UUIDUtils.getUUIDShort());
                    policySpecialContractPo.setCreatedUserId(userId);
                    policySpecialContractPo.setCreatedDate(DateUtils.getCurrentTime());
                    policySpecialContractPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                });
                policySpecialContractDao.insert(policySpecialContractPos);
            }
        } catch (RequestException e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_SPECIAL_CONTRACT_ERROR);
        }
    }

    /**
     * 保存保单缴费信息
     *
     * @param policyPaymentPo 保单缴费信息
     */
    @Override
    @Transactional
    public void savePolicyPayment(PolicyPaymentPo policyPaymentPo) {
        try {
            LOGGER.info("policyPaymentPo:" + JSON.toJSONString(policyPaymentPo));
            if (!AssertUtils.isNotEmpty(policyPaymentPo.getPolicyPaymentId())) {
                //执行新增
                policyPaymentPo.setPolicyPaymentId(UUIDUtils.getUUIDShort());
                policyPaymentPo.setCreatedDate(DateUtils.getCurrentTime());
                policyPaymentPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyPaymentDao.insert(policyPaymentPo);
            } else {
                //执行修改
                policyPaymentPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyPaymentDao.update(policyPaymentPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_PAYMENT_ERROR);
        }
    }

    /**
     * 保存保单缴费信息
     *
     * @param policyId 保单缴费信息
     */
    @Override
    public PolicyPaymentPo queryPolicyPayment(String policyId) {
        PolicyPaymentPo policyPaymentPo = new PolicyPaymentBo();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            LOGGER.info("policyId:" + policyId);

            policyPaymentPo = policyBaseDao.getPolicyPayment(policyId);

            LOGGER.info("policyPaymentPo:" + JSON.toJSONString(policyPaymentPo));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return policyPaymentPo;
    }

    @Override
    public PolicyPaymentPo queryPolicyPaymentById(String policyPaymentId) {
        return policyPaymentDao.findById(policyPaymentId);
    }

    @Override
    public PolicyPaymentBo queryNewPolicyPayment(String policyId) {
        return policyBaseDao.getNewPolicyPayment(policyId);

    }

    @Override
    public PolicyPaymentBo queryFirstPolicyPayment(String policyId) {
        return policyBaseDao.queryFirstPolicyPayment(policyId);
    }

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    @Override
    public List<PolicyPaymentBo> listPolicyPayment(String policyId) {
        return policyBaseDao.getListPolicyPayment(policyId, null);
    }

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    @Override
    public List<PolicyPaymentBo> listPayPolicyPayment(String policyId) {
        List<PolicyPaymentBo> policyPaymentBos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            LOGGER.info("policyId:" + policyId);

            policyPaymentBos = policyBaseDao.getListPayPolicyPayment(policyId);

            LOGGER.info("policyPaymentBos:" + JSON.toJSONString(policyPaymentBos));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return policyPaymentBos;
    }

    @Override
    public List<PolicyPaymentBo> listPayPolicyPayment(String policyId, long endTime, List<String> paymentBusinessType) {
        return policyBaseDao.getListPayPolicyPayment(policyId, endTime, paymentBusinessType);
    }

    /**
     * 查询指定缴费信息列表
     *
     * @param policyPaymentIds 保单缴费ID集
     * @return List
     */
    @Override
    public List<PolicyPaymentBo> listPolicyPayment(List<String> policyPaymentIds) {
        return policyBaseDao.listPolicyPayment(policyPaymentIds);
    }

    /**
     * 保存保单打印信息
     *
     * @param policyPrintInfoPo 保单打印信息
     */
    @Override
    @Transactional
    public void savePolicyPrintInfo(PolicyPrintInfoPo policyPrintInfoPo) {
        try {
            LOGGER.info("policyPrintInfoPo:" + JSON.toJSONString(policyPrintInfoPo));
            if (!AssertUtils.isNotEmpty(policyPrintInfoPo.getPrintInfoId())) {
                //执行新增
                policyPrintInfoPo.setPrintInfoId(UUIDUtils.getUUIDShort());
                policyPrintInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                policyPrintInfoPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyPrintInfoDao.insert(policyPrintInfoPo);
            } else {
                //执行修改
                policyPrintInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyPrintInfoDao.update(policyPrintInfoPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_PRINT_INFO_ERROR);
        }
    }

    /**
     * 保存被保人信息
     *
     * @param policyInsuredPo 被保人信息
     */
    @Override
    @Transactional
    public void savePolicyInsured(PolicyInsuredPo policyInsuredPo) {
        try {
            LOGGER.info("policyInsuredPo:" + JSON.toJSONString(policyInsuredPo));
            if (!AssertUtils.isNotEmpty(policyInsuredPo.getInsuredId())) {
                //执行新增
                policyInsuredPo.setInsuredId(UUIDUtils.getUUIDShort());
                policyInsuredPo.setCreatedDate(DateUtils.getCurrentTime());
                policyInsuredPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyInsuredDao.insert(policyInsuredPo);
            } else {
                //执行修改
                policyInsuredPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyInsuredDao.update(policyInsuredPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_INSURED_ERROR);
        }
    }

    /**
     * 保存被保人拓展新
     *
     * @param policyInsuredExtendPo 被保人拓展信息
     */
    @Override
    @Transactional
    public void savePolicyInsuredExtend(PolicyInsuredExtendPo policyInsuredExtendPo) {
        try {
            LOGGER.info("policyInsuredExtendPo:" + JSON.toJSONString(policyInsuredExtendPo));
            if (!AssertUtils.isNotEmpty(policyInsuredExtendPo.getInsuredExtendId())) {
                //执行新增
                policyInsuredExtendPo.setInsuredExtendId(UUIDUtils.getUUIDShort());
                policyInsuredExtendPo.setCreatedDate(DateUtils.getCurrentTime());
                policyInsuredExtendPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyInsuredExtendDao.insert(policyInsuredExtendPo);
            } else {
                //执行修改
                policyInsuredExtendPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyInsuredExtendDao.update(policyInsuredExtendPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_INSURED_EXTEND_ERROR);
        }
    }

    /**
     * 保存保单被保人统计信息
     *
     * @param policyInsuredCollect 保单被保人统计信息
     */
    @Override
    public void savePolicyInsuredColect(PolicyInsuredCollectPo policyInsuredCollect) {
        try {
            LOGGER.info("policyInsuredCollect:" + JSON.toJSONString(policyInsuredCollect));
            if (!AssertUtils.isNotEmpty(policyInsuredCollect.getInsuredCollectId())) {
                //执行新增
                policyInsuredCollect.setInsuredCollectId(UUIDUtils.getUUIDShort());
                policyInsuredCollect.setCreatedDate(DateUtils.getCurrentTime());
                policyInsuredCollect.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyInsuredCollectDao.insert(policyInsuredCollect);
            } else {
                //执行修改
                policyInsuredCollect.setUpdatedDate(DateUtils.getCurrentTime());
                policyInsuredCollectDao.update(policyInsuredCollect);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_INSURED_COLLECT_ERROR);
        }
    }

    /**
     * 查询当天满期的保单
     *
     * @return
     */
    @Override
    public List<PolicyPo> queryMaturityPolicy(BasePageRequest basePageRequest) {
        return policyBaseDao.queryMaturityPolicy(basePageRequest);
    }


    /**
     * 查询当天满期的保单
     *
     * @return
     */
    @Override
    public List<PolicyCoveragePo> queryMaturityRenewalAdditionCoverage(BasePageRequest basePageRequest) {
        return policyBaseDao.queryMaturityRenewalAdditionCoverage(basePageRequest);
    }

    /**
     * 保单分单指派表
     *
     * @param userId              用户ID
     * @param policyAssignAgentPo 分单数据
     */
    @Override
    public void savePolicyAssignAgentPo(String userId, PolicyAssignAgentPo policyAssignAgentPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(policyAssignAgentPo.getAssignAgentId())) {
            policyAssignAgentPo.setUpdatedDate(currentTime);
            policyAssignAgentPo.setUpdatedUserId(userId);
            policyAssignAgentDao.update(policyAssignAgentPo);
        } else {
            policyAssignAgentPo.setAssignAgentId(UUIDUtils.getUUIDShort());
            policyAssignAgentPo.setCreatedDate(currentTime);
            policyAssignAgentPo.setCreatedUserId(userId);
            policyAssignAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyAssignAgentDao.insert(policyAssignAgentPo);
        }
    }

    /**
     * 批量保存保单分单指派表
     *
     * @param userId               用户ID
     * @param policyAssignAgentPos 分单数据
     */
    @Override
    public void savePolicyAssignAgentPoBatch(String userId, List<PolicyAssignAgentPo> policyAssignAgentPos) {
        if (!AssertUtils.isNotEmpty(policyAssignAgentPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<PolicyAssignAgentPo> insertData = new ArrayList<>();
        List<PolicyAssignAgentPo> updateData = new ArrayList<>();
        policyAssignAgentPos.forEach(policyAssignAgentPo -> {
            if (AssertUtils.isNotEmpty(policyAssignAgentPo.getAssignAgentId())) {
                policyAssignAgentPo.setUpdatedDate(currentTime);
                policyAssignAgentPo.setUpdatedUserId(userId);
                updateData.add(policyAssignAgentPo);
            } else {
                policyAssignAgentPo.setAssignAgentId(UUIDUtils.getUUIDShort());
                policyAssignAgentPo.setCreatedDate(currentTime);
                policyAssignAgentPo.setCreatedUserId(userId);
                policyAssignAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(policyAssignAgentPo);
            }
        });
        policyAssignAgentDao.insert(insertData);
        policyAssignAgentDao.update(updateData);
    }

    /**
     * 批量新增保单缴费信息
     *
     * @param policyPaymentBos 保单缴费信息
     * @param userId           用户ID
     */
    @Override
    public void addPolicyPayment(List<PolicyPaymentBo> policyPaymentBos, String userId) {
        if (AssertUtils.isNotEmpty(policyPaymentBos)) {
            policyPaymentBos.forEach(policyPaymentBo -> {
                if (!AssertUtils.isNotEmpty(policyPaymentBo.getPolicyPaymentId())) {
                    policyPaymentBo.setPolicyPaymentId(UUIDUtils.getUUIDShort());
                    policyPaymentBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                    policyPaymentBo.setCreatedUserId(userId);
                    policyPaymentBo.setCreatedDate(System.currentTimeMillis());
                    policyPaymentBo.setUpdatedUserId(userId);
                    policyPaymentBo.setUpdatedDate(System.currentTimeMillis());
                }

                if (AssertUtils.isNotEmpty(policyPaymentBo.getListPolicyCoveragePayment())) {
                    policyPaymentBo.getListPolicyCoveragePayment().forEach(policyCoveragePaymentBo -> {
                        if (!AssertUtils.isNotEmpty(policyCoveragePaymentBo.getPolicyCoveragePaymentId())) {
                            policyCoveragePaymentBo.setPolicyPaymentId(policyPaymentBo.getPolicyPaymentId());
                            policyCoveragePaymentBo.setPolicyCoveragePaymentId(UUIDUtils.getUUIDShort());
                            policyCoveragePaymentBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                            policyCoveragePaymentBo.setCreatedUserId(userId);
                            policyCoveragePaymentBo.setCreatedDate(System.currentTimeMillis());
                            policyCoveragePaymentBo.setUpdatedUserId(userId);
                            policyCoveragePaymentBo.setUpdatedDate(System.currentTimeMillis());
                        }
                    });
                    List<PolicyCoveragePaymentPo> policyCoveragePaymentPos = (List<PolicyCoveragePaymentPo>) this.converterList(
                            policyPaymentBo.getListPolicyCoveragePayment(), new TypeToken<List<PolicyCoveragePaymentPo>>() {
                            }.getType()
                    );
                    // 保存险种缴费信息
                    policyCoveragePaymentDao.insert(policyCoveragePaymentPos);
                }
            });
            List<PolicyPaymentPo> policyPaymentPos = (List<PolicyPaymentPo>) this.converterList(
                    policyPaymentBos, new TypeToken<List<PolicyPaymentPo>>() {
                    }.getType()
            );

            policyPaymentDao.insert(policyPaymentPos);
        }
    }

    @Override
    public void savePolicyAssignRemarkPo(PolicyAssignRemarkPo policyAssignRemarkPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyAssignRemarkPo.getPolicyAssignRemarkId())) {
            //执行新增
            policyAssignRemarkPo.setPolicyAssignRemarkId(UUIDUtils.getUUIDShort());
            policyAssignRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyAssignRemarkPo.setCreatedUserId(userId);
            policyAssignRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            policyAssignRemarkDao.insert(policyAssignRemarkPo);
        } else {
            //执行修改
            policyAssignRemarkPo.setUpdatedUserId(userId);
            policyAssignRemarkPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyAssignRemarkDao.update(policyAssignRemarkPo);
        }
    }

    /**
     * 批量新增分单备注信息
     *
     * @param policyAssignRemarkPos 分单备注信息
     * @param userId                用户ID
     */
    @Override
    public void addPolicyAssignRemark(List<PolicyAssignRemarkPo> policyAssignRemarkPos, String userId) {
        if (AssertUtils.isNotEmpty(policyAssignRemarkPos)) {
            policyAssignRemarkPos.forEach(policyAssignRemarkPo -> {
                policyAssignRemarkPo.setPolicyAssignRemarkId(UUIDUtils.getUUIDShort());
                policyAssignRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyAssignRemarkPo.setCreatedUserId(userId);
                policyAssignRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            });
            policyAssignRemarkDao.insert(policyAssignRemarkPos);
        }
    }

    @Override
    public List<PolicyPo> listPolicyByAgentId(List<String> agentIds) {
        List<PolicyPo> policyPos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, agentIds, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL);

            policyPos = policyBaseDao.getPolicyByAgentId(agentIds);

            LOGGER.info("policyPos:" + JSON.toJSONString(policyPos));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
            }
        }
        return policyPos;
    }

    /**
     * 保存保单续期订单数据
     *
     * @param policyRenewalGrabPo 保单续期订单数据
     */
    @Override
    @Transactional
    public void savePolicyRenewalGrab(PolicyRenewalGrabPo policyRenewalGrabPo) {
        try {
            if (!AssertUtils.isNotEmpty(policyRenewalGrabPo.getRenewalGrabId())) {
                //执行新增
                policyRenewalGrabPo.setRenewalGrabId(UUIDUtils.getUUIDShort());
                policyRenewalGrabPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyRenewalGrabPo.setCreatedDate(DateUtils.getCurrentTime());
                policyRenewalGrabPo.setReceivableGenerateDate(DateUtils.getCurrentTime());
                policyRenewalGrabDao.insert(policyRenewalGrabPo);
            } else {
                //执行修改
                policyRenewalGrabPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyRenewalGrabDao.update(policyRenewalGrabPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_RENEWAL_GRAB_ERROR);
        }
    }

    @Override
    public PolicyRenewalGrabPo queryPolicyRenewalGrab(String policyId, Long receivableDate) {
        return policyBaseDao.queryPolicyRenewalGrab(policyId, receivableDate);
    }

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyId 保单ID
     * @return list
     */
    @Override
    public List<PolicyRenewalGrabPo> listPolicyRenewalGrab(String policyId) {
        return policyBaseDao.listPolicyRenewalGrab(policyId);
    }

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyIds 保单ID集
     * @return list
     */
    @Override
    public List<PolicyRenewalGrabPo> listPolicyRenewalGrab(List<String> policyIds) {
        return policyBaseDao.listPolicyRenewalGrab(policyIds);
    }

    @Override
    public void deletePolicyRenewalGrab(PolicyRenewalGrabPo policyRenewalGrabPo) {
        policyRenewalGrabDao.delete(policyRenewalGrabPo);
    }

    /**
     * 批量删除抢单记录
     *
     * @param policyRenewalGrabPos 抢单记录
     */
    @Override
    public void deletePolicyRenewalGrab(List<PolicyRenewalGrabPo> policyRenewalGrabPos) {
        if (AssertUtils.isNotEmpty(policyRenewalGrabPos)) {
            policyRenewalGrabDao.delete(policyRenewalGrabPos);
        }
    }

    @Override
    public List<PolicyAgentHistoryPo> listPolicyAgentHistoryByPolicyId(String policyId) {
        List<PolicyAgentHistoryPo> policyAgentHistoryPoList;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            policyAgentHistoryPoList = policyBaseDao.queryListPolicyAgentHistoryPo(policyId);

            LOGGER.info("policyAgentHistoryPoList:" + JSON.toJSONString(policyAgentHistoryPoList));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR);
            }
        }
        return policyAgentHistoryPoList;
    }

    @Override
    public PolicyAgentHistoryPo queryPolicyAgentHistoryByPolicyId(AppRequestHeads appRequestHeads, String policyId) {
        PolicyAgentHistoryPo policyAgentHistoryPo;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            policyAgentHistoryPo = policyBaseDao.queryOnePolicyAgentHistoryPo(policyId);

            LOGGER.info("policyAgentHistoryPo:" + JSON.toJSONString(policyAgentHistoryPo));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR);
            }
        }
        return policyAgentHistoryPo;
    }

    @Override
    @Transactional
    public void savePolicyAgentHistory(PolicyAgentHistoryPo policyAgentHistoryPo) {
        try {
            LOGGER.info("policyAgentHistoryPo:" + JSON.toJSONString(policyAgentHistoryPo));
            if (!AssertUtils.isNotEmpty(policyAgentHistoryPo.getPolicyAgentHistoryId())) {
                //执行新增
                policyAgentHistoryPo.setPolicyAgentHistoryId(UUIDUtils.getUUIDShort());
                policyAgentHistoryPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyAgentHistoryPo.setCreatedDate(DateUtils.getCurrentTime());
                policyAgentHistoryDao.insert(policyAgentHistoryPo);
            } else {
                //执行修改
                policyAgentHistoryPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyAgentHistoryDao.update(policyAgentHistoryPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_AGENT_HISTORY_ERROR);
        }
    }

    /**
     * 批量更新保单状态
     *
     * @param userId       用户ID
     * @param policyIds    　保单ID集
     * @param policyStatus 保单状态
     */
    @Override
    @Transactional
    public void updatePolicy(String userId, List<String> policyIds, String policyStatus) {
        try {
            if (AssertUtils.isNotEmpty(policyIds)) {
                List<PolicyPo> policyPos = policyDao.fetchByPolicyId(policyIds.toArray(new String[1]));
                if (AssertUtils.isNotEmpty(policyPos)) {
                    policyPos.forEach(policyPo -> {
                        policyPo.setPolicyStatus(policyStatus);
                        policyPo.setUpdatedDate(DateUtils.getCurrentTime());
                        policyPo.setUpdatedUserId(userId);
                    });
                }
                policyDao.update(policyPos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_ERROR);
        }
    }

    /**
     * 批量更新保单缴费状态
     *
     * @param userId           用户ID
     * @param policyPaymentPos 　保单缴费数据
     */
    @Override
    public void updatePolicyPayment(String userId, List<PolicyPaymentPo> policyPaymentPos) {
        try {
            if (AssertUtils.isNotEmpty(policyPaymentPos)) {
                policyPaymentPos.forEach(policyPaymentPo -> {
                    policyPaymentPo.setUpdatedDate(DateUtils.getCurrentTime());
                    policyPaymentPo.setUpdatedUserId(userId);
                });
                policyPaymentDao.update(policyPaymentPos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throwsTransactionalException(LOGGER, e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_PAYMENT_ERROR);
        }
    }

    @Override
    public List<PolicyApplicantCoverageBo> queryPolicyApplicantCoverageBo(PolicyListVo policyListVo) {
        List<PolicyApplicantCoverageBo> policyApplicantCoverageBos = new ArrayList<>();
        try {
            policyApplicantCoverageBos = policyBaseDao.queryPolicyApplicantCoverageBo(policyListVo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return policyApplicantCoverageBos;
    }

    @Override
    public List<PolicyEndorseBo> queryPolicyByCustomerId(List<String> customerAgentIds) {
        return policyBaseDao.queryPolicyByCustomerId(customerAgentIds);
    }

    @Override
    public List<PolicyApplicantBo> queryPolicyByCustomerAgentIds(List<String> customerAgentIds) {
        return policyBaseDao.queryPolicyByCustomerAgentIds(customerAgentIds);
    }

    @Override
    public List<PolicyEndorseBo> queryPolicyByApplicantCustomerId(List<String> customerAgentIds) {
        return policyBaseDao.queryPolicyByApplicantCustomerId(customerAgentIds);
    }

    @Override
    public PolicyEndorseInfoBo queryPolicyInfoByPolicyId(String policyId, String versionNo) {
        return policyBaseDao.queryPolicyInfoByPolicyId(policyId, versionNo);
    }

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    @Override
    public List<PolicyEndorsePaymentBo> listPolicyPayment(String policyId, String paymentStatus) {
        return policyBaseDao.listPolicyPayment(policyId, paymentStatus);
    }

    /**
     * 团险被保人下的已缴费信息
     *
     * @param policyId      保单ID
     * @param customerId    客户ID
     * @param paymentStatus
     * @return PolicyEndorsePaymentBos
     */
    @Override
    public List<PolicyCoveragePaymentBo> queryGroupCustomerPayments(String policyId, String customerId, String paymentStatus) {
        return policyBaseDao.queryGroupCustomerPayments(policyId, customerId, paymentStatus);
    }

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    @Override
    public List<PolicyPaymentBo> queryPolicyPayments(String policyId, String paymentStatus) {
        return policyBaseDao.getListPolicyPayment(policyId, paymentStatus);
    }

    @Override
    public List<PolicyPaymentBo> queryPolicyPayment(List<String> policyPaymentIds) {
        return policyBaseDao.queryPolicyPayment(policyPaymentIds);
    }

    /**
     * 查询保单信息跟保人信息（收付费明细报表）
     *
     * @param businessNo payment关联投保单id
     */
    @Override
    public List<PolicyReportBo> queryPolicyReport(List<String> businessNo) {
        return policyBaseDao.queryPolicyReport(businessNo);
    }

    /**
     * 查询业务报表（投保人资料）
     */
    @Override
    public List<PolicyApplicantReportBo> queryPolicyApplicantReport(BasePageRequest basePageRequest, String
            startDate) {
        return policyBaseDao.queryPolicyApplicantReport(basePageRequest, startDate);
    }

    /**
     * 查询业务报表（被保人资料）
     *
     * @param basePageRequest
     * @param startDate
     */
    @Override
    public List<PolicyInsuredReportBo> queryPolicyInsuredsReport(BasePageRequest basePageRequest, String startDate) {
        return policyBaseDao.queryPolicyInsuredsReport(basePageRequest, startDate);
    }

    /**
     * 投保人代表报表数据
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    @Override
    public List<PolicyApplicantReportBo> queryDelegateApplicantReport(BasePageRequest basePageRequest, String startDate) {
        return policyBaseDao.queryDelegateApplicantReport(basePageRequest, startDate);
    }

    /**
     * 查询承保清单
     *
     * @param basePageRequest
     * @param startDate
     * @param reportType
     * @return
     */
    @Override
    public List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate, String reportType) {
        if (PolicyTermEnum.REPORT_TYPE.POLICY.name().equals(reportType)) {
            return policyBaseDao.queryPolicyReportUnderwritingBo(basePageRequest, startDate);
        }
        if (PolicyTermEnum.REPORT_TYPE.REGULATORY_POLICY.name().equals(reportType)) {
            return policyBaseDao.queryPolicyReportUnderwritingRegulatory(basePageRequest, startDate);

        }
        return null;
    }

    /**
     * 根据policyId查询policy_payement中数据
     *
     * @param policyId
     * @return
     */
    @Override
    public List<PolicyPaymentPo> queryAllPolicyPaymentPo(String policyId) {
        return policyBaseDao.queryAllPolicyPaymentPo(policyId);
    }

    @Override
    public List<PolicyPaymentPo> queryPolicyPaymentPo(String policyId) {
        return policyBaseDao.queryPolicyPayment(policyId);
    }

    @Override
    public PolicyAgentPo queryOnePolicyAgentPo(String policyId) {
        return policyBaseDao.queryOnePolicyAgentPo(policyId);
    }

    @Override
    public List<PolicyApplicantPo> queryAllPolicyApplicantPo(List<String> policyIds) {
        return policyBaseDao.queryOnePolicyApplicantPo(policyIds);
    }

    @Override
    public PolicyInsuredPo queryOnePolicyInsuredPo(String policyId) {
        return policyBaseDao.queryOnePolicyInsuredPo(policyId);
    }

    /**
     * 保存保单操作
     *
     * @param policyOperationPo 保单操作数据
     */
    @Override
    public void savePolicyOperation(PolicyOperationPo policyOperationPo) {
        if (AssertUtils.isNotEmpty(policyOperationPo.getPolicyOperationId())) {
            policyOperationPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyOperationDao.update(policyOperationPo);
        } else {
            policyOperationPo.setPolicyOperationId(UUIDUtils.getUUIDShort());
            policyOperationPo.setCreatedDate(DateUtils.getCurrentTime());
            policyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyOperationDao.insert(policyOperationPo);
        }
    }

    /**
     * 保存保单操作
     *
     * @param policyOperationPo 保单操作数据
     * @param userId            用户ID
     */
    @Override
    public void savePolicyOperation(PolicyOperationPo policyOperationPo, String userId) {
        if (AssertUtils.isNotEmpty(policyOperationPo.getPolicyOperationId())) {
            policyOperationPo.setUpdatedUserId(userId);
            policyOperationPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyOperationDao.update(policyOperationPo);
        } else {
            policyOperationPo.setPolicyOperationId(UUIDUtils.getUUIDShort());
            policyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyOperationPo.setCreatedUserId(userId);
            policyOperationPo.setCreatedDate(DateUtils.getCurrentTime());
            policyOperationDao.insert(policyOperationPo);
        }
    }

    /**
     * 查询保单操作
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyOperationPo queryPolicyOperation(String policyId) {
        return policyBaseDao.queryPolicyOperation(policyId, null);
    }

    /**
     * 查询保单特定操作
     *
     * @param policyId      保单ID
     * @param operationCode 操作编码
     * @return
     */
    @Override
    public PolicyOperationPo queryPolicyOperation(String policyId, String operationCode) {
        return policyBaseDao.queryPolicyOperation(policyId, operationCode);
    }

    @Override
    public BaseOperationPo queryBaseOperationPoByOperationCode(String operationCode) {
        return policyBaseDao.queryBaseOperationPoByOperationCode(operationCode);
    }

    /**
     * 删除保单缴费信息
     *
     * @param policyPaymentPo 保单缴费信息
     */
    @Override
    public void deletePolicyPayment(PolicyPaymentPo policyPaymentPo) {
        if (AssertUtils.isNotNull(policyPaymentPo)) {
            policyPaymentDao.delete(policyPaymentPo);
        }
    }

    /**
     * 删除保单缴费信息(含险种缴费)
     *
     * @param policyPaymentBos 保单缴费信息
     */
    @Override
    public void deletePolicyPayment(List<PolicyPaymentBo> policyPaymentBos) {
        if (AssertUtils.isNotEmpty(policyPaymentBos)) {
            policyPaymentBos.forEach(policyPaymentBo -> {
                List<PolicyCoveragePaymentBo> policyCoveragePayments = policyPaymentBo.getListPolicyCoveragePayment();
                if (AssertUtils.isNotEmpty(policyCoveragePayments)) {
                    policyCoveragePayments.forEach(policyCoveragePaymentBo -> {
                        // 删除险种缴费数据
                        policyCoveragePaymentDao.delete(policyCoveragePaymentBo);
                    });
                }
                // 删除保单缴费数据
                policyPaymentDao.delete(policyPaymentBo);
            });
        }
    }

    /**
     * 删除保单操作数据
     *
     * @param policyOperationPo 保单操作数据
     */
    @Override
    public void deletePolicyOperation(PolicyOperationPo policyOperationPo) {
        policyOperationDao.delete(policyOperationPo);
    }

    /**
     * 保存保单挂起表
     *
     * @param policyHookPo 保单挂起
     * @param userId       用户ID
     */
    @Override
    public void savePolicyHookPo(PolicyHookPo policyHookPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyHookPo.getPolicyHookId())) {
            //执行新增
            policyHookPo.setPolicyHookId(UUIDUtils.getUUIDShort());
            policyHookPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyHookPo.setCreatedDate(DateUtils.getCurrentTime());
            policyHookPo.setCreatedUserId(userId);
            policyHookDao.insert(policyHookPo);
        } else {
            //执行修改
            policyHookPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyHookPo.setUpdatedUserId(userId);
            policyHookDao.update(policyHookPo);
        }
    }

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param hookStatus
     * @return PolicyHookPo
     */
    @Override
    public PolicyHookPo getPolicyHook(String policyId, String hookObjectId, String hookStatus) {
        return policyBaseDao.getPolicyHook(policyId, hookObjectId, hookStatus);
    }

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param customerId   customerId
     * @param hookStatus
     * @return PolicyHookPo
     */
    @Override
    public PolicyHookPo getGroupPolicyHook(String policyId, String hookObjectId, String customerId, String hookStatus) {
        return policyBaseDao.getGroupPolicyHook(policyId, hookObjectId, customerId, hookStatus);
    }

    @Override
    public List<PolicyInsuredPo> getGroupPolicyCustomer(String policyId, List<String> policyCustomerIds) {
        return policyBaseDao.getGroupPolicyCustomer(policyId, policyCustomerIds);
    }

    /**
     * 根据保单ID查询回访信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyReturnVisitPo queryReturnVisitByBusinessId(String policyId) {
        return policyBaseDao.queryReturnVisitByBusinessId(policyId);
    }

    /**
     * 保存保单回访信息
     *
     * @param policyReturnVisitPo 保单回访信息
     * @param userId              用户ID
     * @return
     */
    @Override
    public void savePolicyReturnVisit(PolicyReturnVisitPo policyReturnVisitPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyReturnVisitPo.getPolicyReturnVisitId())) {
            //执行新增
            policyReturnVisitPo.setPolicyReturnVisitId(UUIDUtils.getUUIDShort());
            policyReturnVisitPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyReturnVisitPo.setCreateDate(DateUtils.getCurrentTime());
            policyReturnVisitPo.setCreateUserId(userId);
            policyReturnVisitDao.insert(policyReturnVisitPo);
        } else {
            //执行修改
            policyReturnVisitPo.setUpdateDate(DateUtils.getCurrentTime());
            policyReturnVisitPo.setUpdateUserId(userId);
            policyReturnVisitDao.update(policyReturnVisitPo);
        }
    }

    @Override
    public List<ClaimPolicyBo> queryPolicyByCustomerIds(String... customerIds) {
        return policyBaseDao.queryPolicyByCustomerIds(customerIds);
    }

    /**
     * 模糊查询保单信息
     *
     * @param keyword 关键字
     * @return
     */
    @Override
    public List<PolicyApplicantInfoBo> queryListPolicysByKeyword(String keyword) {
        return policyBaseDao.queryListPolicysByKeyword(keyword);
    }

    /**
     * 查询当前用户的团险保单信息
     *
     * @param userId  当前用户
     * @param keyword 关键字
     * @return
     */
    @Override
    public List<PolicyApplicantInfoBo> queryAgentListPolicysByKeyword(String userId, String keyword) {
        return policyBaseDao.queryAgentListPolicysByKeyword(userId, keyword);
    }

    @Override
    public List<PolicyApplicantInfoBo> queryAgentListPolicysByKeywordNew(String userId, String keyword) {
        return policyBaseDao.queryAgentListPolicysByKeywordNew(userId, keyword);
    }

    @Override
    public List<PolicyApplicantPo> queryPolicyApplicantByCustomerId(String customerId) {
        return policyBaseDao.queryPolicyApplicantByCustomerId(customerId);
    }

    @Override
    public Integer countNewApplicantByCustomerIds(List<String> customerIds) {
        return policyBaseDao.countNewApplicantByCustomerIds(customerIds);
    }

    @Override
    public Integer countNewInsuredByCustomerIds(List<String> customerIds) {
        return policyBaseDao.countNewInsuredByCustomerIds(customerIds);
    }

    @Override
    public List<PolicyInsuredPo> queryPolicyInsuredByCustomerId(String customerId) {
        return policyBaseDao.queryPolicyInsuredByCustomerId(customerId);
    }

    @Override
    public List<GroupInsuredInfoBo> queryPolicyInsuredInfoBo(String policyId) {
        return policyBaseDao.queryPolicyInsuredInfoBo(policyId);
    }

    @Override
    public List<PolicyGroupReportUnderwritingBo> queryPolicyGroupReportUnderwritingBo(BasePageRequest basePageRequest, String startDate) {
        return policyBaseDao.queryPolicyGroupReportUnderwritingBo(basePageRequest, startDate);
    }

    @Override
    public List<GroupSdfReportBo> queryListPolicyGroupSdfReportBo(BasePageRequest basePageRequest, String startDate) {
        return policyBaseDao.queryListPolicyGroupSdfReportBo(basePageRequest,startDate);
    }

    @Override
    public List<PolicyApplicantInfoBo> queryReturnVisitPolicyByKeyword(String keyword) {
        return policyBaseDao.queryReturnVisitPolicyByKeyword(keyword);
    }

    /**
     * 根据客户ID查询保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listSimplePolicyByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimpleGroupPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listSimpleGroupPolicyByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listSimplePolicyHistoryByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询历史团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimpleGroupPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listSimpleGroupPolicyHistoryByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据保单ID查询保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public ClaimPolicyBo querySimplePolicyById(String policyId, Long dataEffectiveDate) {
        return policyBaseDao.querySimplePolicyById(policyId, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyHistoryByPolicyId(String policyId, Long dataEffectiveDate) {
        return policyBaseDao.listSimplePolicyHistoryByPolicyId(policyId, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listDetailPolicyByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据客户ID查询历史保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        return policyBaseDao.listDetailPolicyHistoryByCustomerId(customerAgentIds, dataEffectiveDate);
    }

    /**
     * 根据保单ID查询保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public ClaimDetailPolicyBo queryDetailPolicyByPolicyId(String policyId, String customerId, Long dataEffectiveDate) {
        return policyBaseDao.queryDetailPolicyByPolicyId(policyId, customerId, dataEffectiveDate);
    }

    /**
     * 根据保单ID查询历史保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId(String policyId, String customerId, Long dataEffectiveDate) {
        return policyBaseDao.listDetailPolicyHistoryByPolicyId(policyId, customerId, dataEffectiveDate);
    }

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    @Override
    public List<PolicyGroupReportSyncApplicantBo> querySyncApplicantCustomer() {
        return policyBaseDao.querySyncApplicantCustomer();
    }

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    @Override
    public List<PolicyApplicantPo> queryApplicantCustomer() {
        return policyBaseDao.queryApplicantCustomer();
    }

    @Override
    public List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList) {
        return policyBaseDao.queryActualPerformance(policyIdList);
    }

    @Override
    public List<ActualPerformanceReportBo> queryPolicyVersionActualPerformance(List<String> versionNo) {
        return policyBaseDao.queryPolicyVersionActualPerformance(versionNo);
    }

    /**
     * 保单ID查询受益人信息
     *
     * @param policyId
     * @param modifyFlag
     * @return
     */
    @Override
    public List<PolicyBeneficiaryInfoBo> queryPolicyLoanBeneficiary(String policyId, String modifyFlag) {
        return policyBaseDao.queryPolicyLoanBeneficiary(policyId, modifyFlag);
    }

    /**
     * 根据保单ID查询团险保单详细信息
     *
     * @param policyId 团险保单ID
     * @return
     */
    @Override
    public PolicyBo queryGroupPolicyDetail(String policyId) {
        // 查询保单信息
        PolicyBo policyBo = policyBaseDao.queryPolicy(policyId);
        if (AssertUtils.isNotNull(policyBo)) {
            // 查询业务员
            PolicyAgentBo policyAgentBo = policyAgentBaseDao.queryPolicyAgent(policyId);
            policyBo.setPolicyAgent(policyAgentBo);

            // 查询投保人
            PolicyApplicantBo policyApplicantBo = policyApplicantBaseDao.queryPolicyApplicant(policyId);
            policyBo.setApplicant(policyApplicantBo);

            // 查询险种档次
            List<PolicyCoverageLevelPo> coverageLevelPos = policyCoverageBaseDao.listPolicyCoverageLevel(policyId);
            // 查询险种责任
            List<PolicyCoverageDutyBo> coverageDutyBos = policyCoverageBaseDao.listPolicyCoverageDuty(policyId);
            if (AssertUtils.isNotEmpty(coverageDutyBos) && AssertUtils.isNotEmpty(coverageLevelPos)) {
                coverageDutyBos.forEach(coverageDutyBo -> {
                    // 责任设置档次
                    List<PolicyCoverageLevelPo> levelPos = coverageLevelPos.stream()
                            .filter(coverageLevelPo -> coverageDutyBo.getCoverageDutyId().equals(coverageLevelPo.getCoverageDutyId()))
                            .collect(Collectors.toList());
                    coverageDutyBo.setListCoverageLevel(levelPos);
                });
            }

            // 查询险种保费
            List<PolicyCoveragePremiumPo> policyCoveragePremiumPos = policyPremiumBaseDao.listPolicyCoveragePremium(policyId);

            // 查询险种
            List<PolicyCoverageBo> policyCoverageBos = policyCoverageBaseDao.listPolicyCoverage(policyId);
            if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                // 公共险种
                List<PolicyCoverageBo> publicCoverageBos = policyCoverageBos.stream()
                        .filter(coverageBo -> !AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .collect(Collectors.toList());
                policyBo.setListCoverage(publicCoverageBos);

                // 被保人下的险种信息
                List<PolicyCoverageBo> listInsuredCoverage = new ArrayList<>();
                // 非公共险设置责任、档次及险种保费
                policyCoverageBos.stream()
                        .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .forEach(coverageBo -> {
                            if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag()) && AssertUtils.isNotEmpty(coverageDutyBos)) {
                                // 险种设置责任
                                List<PolicyCoverageDutyBo> dutyBos = coverageDutyBos.stream()
                                        .filter(coverageDutyBo -> coverageBo.getCoverageId().equals(coverageDutyBo.getCoverageId()))
                                        .collect(Collectors.toList());
                                coverageBo.setListPolicyCoverageDuty(dutyBos);
                            } else if (AssertUtils.isNotEmpty(coverageLevelPos)) {
                                // 险种设置档次
                                List<PolicyCoverageLevelPo> levelPos = coverageLevelPos.stream()
                                        .filter(coverageLevelPo -> coverageBo.getCoverageId().equals(coverageLevelPo.getCoverageId()))
                                        .collect(Collectors.toList());
                                coverageBo.setListCoverageLevel(levelPos);
                            }

                            // 设置险种保费
                            if (AssertUtils.isNotEmpty(policyCoveragePremiumPos)) {
                                policyCoveragePremiumPos.stream()
                                        .filter(coveragePremiumPo -> coverageBo.getCoverageId().equals(coveragePremiumPo.getCoverageId()))
                                        .findFirst().ifPresent(coveragePremiumPo -> {
                                            PolicyCoveragePremiumBo coveragePremiumBo = (PolicyCoveragePremiumBo) this.converterObject(coveragePremiumPo, PolicyCoveragePremiumBo.class);
                                            coverageBo.setPolicyCoveragePremium(coveragePremiumBo);
                                        });
                            }

                            listInsuredCoverage.add(coverageBo);
                        });
                policyBo.setListInsuredCoverage(listInsuredCoverage);
            }

            List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos = new ArrayList<>();
            // 查询受益人信息
            List<PolicyBeneficiaryPo> beneficiaryPos = policyBeneficiaryBaseDao.listBeneficiary(policyId, null);
            if (AssertUtils.isNotEmpty(beneficiaryPos)) {
                List<String> beneficiaryIds = beneficiaryPos.stream().map(PolicyBeneficiaryPo::getBeneficiaryId).collect(Collectors.toList());
                List<PolicyBeneficiaryBo> beneficiaryBos = (List<PolicyBeneficiaryBo>) this.converterList(
                        beneficiaryPos, new TypeToken<List<PolicyBeneficiaryBo>>() {
                        }.getType()
                );
                // 查询受益信息
                List<PolicyBeneficiaryInfoPo> beneficiaryInfoPos = policyBeneficiaryBaseDao.listBeneficiaryInfo(beneficiaryIds);
                if (AssertUtils.isNotEmpty(beneficiaryInfoPos)) {
                    beneficiaryInfoPos.forEach(beneficiaryInfoPo -> {
                        beneficiaryBos.stream()
                                .filter(beneficiaryBo -> beneficiaryBo.getBeneficiaryId().equals(beneficiaryInfoPo.getBeneficiaryId()))
                                .findFirst().ifPresent(beneficiaryBo -> {
                                    PolicyBeneficiaryInfoBo beneficiaryInfoBo = (PolicyBeneficiaryInfoBo) this.converterObject(beneficiaryInfoPo, PolicyBeneficiaryInfoBo.class);
                                    beneficiaryInfoBo.setPolicyBeneficiary(beneficiaryBo);
                                    policyBeneficiaryInfoBos.add(beneficiaryInfoBo);
                                });
                    });
                }
            }

            // 查询被保人扩展信息
            List<PolicyInsuredExtendPo> insuredExtendPos = policyInsuredBaseDao.listPolicyInsuredExtend(Collections.singletonList(policyId), true);
            policyBo.setListPolicyInsuredExtend(insuredExtendPos);

            // 查询被保人
            List<PolicyInsuredBo> policyInsuredBos = policyInsuredBaseDao.listPolicyInsured(policyId);
            if (AssertUtils.isNotEmpty(policyInsuredBos)) {
                policyInsuredBos.forEach(insuredBo -> {
                    if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                        List<PolicyCoverageBo> coverageBos = policyCoverageBos.stream()
                                .filter(coverageBo -> insuredBo.getInsuredId().equals(coverageBo.getInsuredId()))
                                .collect(Collectors.toList());
                        insuredBo.setListCoverage(coverageBos);
                    }
                    if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBos)) {
                        List<PolicyBeneficiaryInfoBo> beneficiaryInfoBos = policyBeneficiaryInfoBos.stream()
                                .filter(beneficiaryInfoBo -> insuredBo.getInsuredId().equals(beneficiaryInfoBo.getInsuredId()))
                                .collect(Collectors.toList());
                        insuredBo.setListPolicyBeneficiary(beneficiaryInfoBos);
                    }
                    if (AssertUtils.isNotEmpty(insuredExtendPos)) {
                        insuredExtendPos.stream()
                                .filter(insuredExtendPo -> insuredBo.getInsuredId().equals(insuredExtendPo.getInsuredId()))
                                .findFirst().ifPresent(insuredBo::setPolicyInsuredExtendPo);
                    }
                });
            }
            policyBo.setListInsured(policyInsuredBos);

            // 查询被保人统计信息
            PolicyInsuredCollectPo insuredCollectPo = policyInsuredBaseDao.queryPolicyInsuredCollect(policyId);
            policyBo.setPolicyInsuredCollect(insuredCollectPo);

            // 查询保费
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseDao.queryPolicyPremium(policyId);
            policyBo.setPolicyPremium(policyPremiumBo);

            // 查询保单缴费信息
            List<PolicyPaymentBo> policyPaymentBos = policyBaseDao.getListPolicyPayment(policyId, null);
            if (AssertUtils.isNotEmpty(policyPaymentBos)) {
                policyBo.setPolicyPayment(policyPaymentBos.get(0));
                if (AssertUtils.isNotNull(policyBo.getPolicyPremium())) {
                    policyBo.getPolicyPremium().setPolicyPayment(policyPaymentBos.get(0));
                }
                /*查询保单已缴费信息列表*/
                List<PolicyPaymentBo> listPolicyPaymentBo = policyPaymentBos.stream().filter(policyPaymentBo ->
                                policyPaymentBo.getPaymentStatusCode().equals(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name()))
                        .collect(Collectors.toList());
                policyBo.setListPolicyPayment(listPolicyPaymentBo);
            }

            // 查询联系人信息
            PolicyContactInfoPo contactInfoPo = policyBaseDao.getPolicyContactInfo(policyId);
            if (AssertUtils.isNotNull(contactInfoPo)) {
                PolicyContactInfoBo contactInfoBo = (PolicyContactInfoBo) this.converterObject(contactInfoPo, PolicyContactInfoBo.class);
                policyBo.setPolicyContactInfo(contactInfoBo);
            }

            // 查询保单附件信息
            List<PolicyAttachmentPo> applyAttachmentPos = listPolicyAttachment(policyId);
            if (AssertUtils.isNotEmpty(applyAttachmentPos)) {
                List<PolicyAttachmentBo> policyAttachmentBos = (List<PolicyAttachmentBo>) converterList(applyAttachmentPos, new TypeToken<List<PolicyAttachmentBo>>() {
                }.getType());
                policyBo.setListPolicyAttachment(policyAttachmentBos);
            }
        }
        return policyBo;
    }


    @Override
    public List<PolicyInsuredBo> getPolicyAllInsuredList(String policyId) {
        return policyBaseDao.getPolicyAllInsuredList(policyId);
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest) {

        return policyBaseDao.quarterlyStatisticsReserveWithdrawalReport(quarterDate, basePageRequest);
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate) {

        return policyBaseDao.syncPolicyServiceChargeBankChannel(basePageRequest, syncDate);
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannelPayment(BasePageRequest basePageRequest, String syncDate) {
        List<PolicyPaymentBo> policyPaymentBoList = policyBaseDao.queryPolicyPaymentInsuredSum(basePageRequest, syncDate);

        if (!AssertUtils.isNotEmpty(policyPaymentBoList)) {
            return null;
        }
        List<String> policyPaymentIdList = policyPaymentBoList.stream().map(PolicyPaymentBo::getPolicyPaymentId).distinct().collect(Collectors.toList());

        List<ServiceChargeBankChannelBo> serviceChargeBankChannelBoList = policyBaseDao.syncPolicyServiceChargeBankChannelPayment(policyPaymentIdList);

        List<String> policyIdList = serviceChargeBankChannelBoList.stream()
                .filter(serviceChargeBankChannelBo -> POLICY_STATUS_HESITATION_REVOKE.name().equals(serviceChargeBankChannelBo.getPolicyStatus()) && AssertUtils.isNotEmpty(serviceChargeBankChannelBo.getPolicyId()))
                .map(ServiceChargeBankChannelBo::getPolicyId).collect(Collectors.toList());
        List<EndorsePayResponse> endorsePayResponseList = null;
        if (AssertUtils.isNotEmpty(policyIdList)) {
            ResultObject<List<EndorsePayResponse>> resultObject = endorseApi.queryHesitationRevokePayByPolicyIdList(policyIdList);
            AssertUtils.isResultObjectError(this.getLogger(), resultObject);
            endorsePayResponseList = resultObject.getData();
        }
        List<EndorsePayResponse> finalEndorsePayResponseList = endorsePayResponseList;
        serviceChargeBankChannelBoList.forEach(serviceChargeBankChannelBo -> {
            policyPaymentBoList.stream().filter(policyPaymentBo -> policyPaymentBo.getPolicyPaymentId().equals(serviceChargeBankChannelBo.getPolicyPaymentId()))
                    .findFirst().ifPresent(policyPaymentBo -> {
                        serviceChargeBankChannelBo.setInsuredSum(policyPaymentBo.getInsuredSum());
                        serviceChargeBankChannelBo.setTotalLine(policyPaymentBo.getTotalLine());
                    });

            if (BUSINESS_TYPE_NEW_CONTRACT.name().equals(serviceChargeBankChannelBo.getPaymentBusinessType())) {
                serviceChargeBankChannelBo.setQuarterDate(DateUtils.timeStrToString(serviceChargeBankChannelBo.getGainedDate(), DateUtils.FORMATE2));
            } else {
                Long receivableDate = serviceChargeBankChannelBo.getReceivableDate();
                Long gainedDate = serviceChargeBankChannelBo.getGainedDate();
                long quarterDateLong = AssertUtils.isNotNull(receivableDate) ? receivableDate : gainedDate;
                if (AssertUtils.isNotNull(receivableDate) && AssertUtils.isNotNull(gainedDate)) {
                    quarterDateLong = gainedDate.longValue() > receivableDate.longValue() ? gainedDate : receivableDate;
                }
                serviceChargeBankChannelBo.setQuarterDate(DateUtils.timeStrToString(quarterDateLong, DateUtils.FORMATE2));
            }
            if (AssertUtils.isNotEmpty(finalEndorsePayResponseList) && POLICY_STATUS_HESITATION_REVOKE.name().equals(serviceChargeBankChannelBo.getPolicyStatus()) && AssertUtils.isNotEmpty(serviceChargeBankChannelBo.getPolicyId())) {
                finalEndorsePayResponseList.stream().filter(endorsePayResponse -> endorsePayResponse.getApplyId().equals(serviceChargeBankChannelBo.getPolicyId()))
                        .findFirst().ifPresent(endorsePayResponse -> {
                            serviceChargeBankChannelBo.setRefundModeCode(endorsePayResponse.getPaymentMethodCode());
                        });
            }
            if (AssertUtils.isNotNull(policyPaymentBoList)) {
                policyPaymentBoList.stream().filter(policyPaymentBo -> policyPaymentBo.getPolicyPaymentId().equals(serviceChargeBankChannelBo.getPolicyPaymentId()))
                        .findFirst().ifPresent(policyPaymentBo -> {
                            serviceChargeBankChannelBo.setInsuredSum(policyPaymentBo.getInsuredSum());
                        });
            }
        });

        // eMoney的保单设置保额，方便后续计算费率
        serviceChargeBankChannelBoList.stream().filter(serviceChargeBankChannelBo -> PaymentTermEnum.PAYMENT_METHODS.E_MONEY.name().equals(serviceChargeBankChannelBo.getPaymentModeCode()) && "INIT_AGENT_ONLINE003".equals(serviceChargeBankChannelBo.getAgentId()))
                .forEach(serviceChargeBankChannelBo -> {
                    PolicyCoveragePo policyCoveragePo = policyCoverageBaseDao.onePolicyCoveragePo(serviceChargeBankChannelBo.getPolicyId(), serviceChargeBankChannelBo.getProductId());
                    if (AssertUtils.isNotNull(policyCoveragePo)) {
                        serviceChargeBankChannelBo.setAmount(policyCoveragePo.getAmount());
                    }
                });
        return serviceChargeBankChannelBoList;
    }


    @Override
    public List<PolicyAmountBo> queryPolicyAmount(List<String> policyIdList) {
        List<PolicyAmountBo> policyAmountBoList = policyBaseDao.queryPolicyAmount(policyIdList);

        return policyAmountBoList;
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyHistoryStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest) {

        return policyBaseDao.quarterlyHistoryStatisticsReserveWithdrawalReport(quarterDate, basePageRequest);
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalPaymentReport(List<String> insuredIdList) {

        return policyBaseDao.quarterlyStatisticsReserveWithdrawalPaymentReport(insuredIdList);
    }

    @Override
    public PolicyAssignAgentPo queryAssignAgent(String policyId, Long gainedDate) {

        return policyBaseDao.queryAssignAgent(policyId, gainedDate);
    }

    @Override
    public PolicyAssignAgentPo queryAssignDate(String policyId, String agentId, Long gainedDate) {
        return policyBaseDao.queryAssignDate(policyId, agentId, gainedDate);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleReportCoverage(List<String> policyIdList) {
        return policyBaseDao.syncSaleReportCoverage(policyIdList);

    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleReportPolicyDetail(BasePageRequest basePageRequest, String syncDate) {
        return policyBaseDao.syncSaleReportPolicyDetail(basePageRequest, syncDate);
    }

    @Override
    public void savePolicyServiceAgentPo(String userId, PolicyServiceAgentPo policyServiceAgentPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(policyServiceAgentPo.getPolicyServiceAgentId())) {
            policyServiceAgentPo.setUpdatedDate(currentTime);
            policyServiceAgentPo.setUpdatedUserId(userId);
            policyServiceAgentDao.update(policyServiceAgentPo);
        } else {
            policyServiceAgentPo.setPolicyServiceAgentId(UUIDUtils.getUUIDShort());
            policyServiceAgentPo.setCreatedDate(currentTime);
            policyServiceAgentPo.setCreatedUserId(userId);
            policyServiceAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyServiceAgentDao.insert(policyServiceAgentPo);
        }
    }

    @Override
    public void savePolicyServiceAgentRemarkPo(PolicyServiceAgentRemarkPo policyServiceAgentRemarkPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyServiceAgentRemarkPo.getPolicyServiceAgentRemarkId())) {
            //执行新增
            policyServiceAgentRemarkPo.setPolicyServiceAgentRemarkId(UUIDUtils.getUUIDShort());
            policyServiceAgentRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyServiceAgentRemarkPo.setCreatedUserId(userId);
            policyServiceAgentRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            policyServiceAgentRemarkDao.insert(policyServiceAgentRemarkPo);
        } else {
            //执行修改
            policyServiceAgentRemarkPo.setUpdatedUserId(userId);
            policyServiceAgentRemarkPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyServiceAgentRemarkDao.update(policyServiceAgentRemarkPo);
        }
    }

    @Override
    public void batchSavePolicyServiceAgentPo(String userId, List<PolicyServiceAgentPo> policyServiceAgentPos) {
        if (!AssertUtils.isNotEmpty(policyServiceAgentPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<PolicyServiceAgentPo> insertData = new ArrayList<>();
        List<PolicyServiceAgentPo> updateData = new ArrayList<>();
        policyServiceAgentPos.forEach(policyServiceAgentPo -> {
            if (AssertUtils.isNotEmpty(policyServiceAgentPo.getPolicyServiceAgentId())) {
                policyServiceAgentPo.setUpdatedDate(currentTime);
                policyServiceAgentPo.setUpdatedUserId(userId);
                updateData.add(policyServiceAgentPo);
            } else {
                policyServiceAgentPo.setPolicyServiceAgentId(UUIDUtils.getUUIDShort());
                policyServiceAgentPo.setCreatedDate(currentTime);
                policyServiceAgentPo.setCreatedUserId(userId);
                policyServiceAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(policyServiceAgentPo);
            }
        });
        policyServiceAgentDao.insert(insertData);
        policyServiceAgentDao.update(updateData);
    }

    @Override
    public void batchSavePolicyServiceAgentRemarkPo(List<PolicyServiceAgentRemarkPo> policyServiceAgentRemarkPos, String userId) {
        if (AssertUtils.isNotEmpty(policyServiceAgentRemarkPos)) {
            policyServiceAgentRemarkPos.forEach(policyServiceAgentRemarkPo -> {
                policyServiceAgentRemarkPo.setPolicyServiceAgentRemarkId(UUIDUtils.getUUIDShort());
                policyServiceAgentRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyServiceAgentRemarkPo.setCreatedUserId(userId);
                policyServiceAgentRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            });
            policyServiceAgentRemarkDao.insert(policyServiceAgentRemarkPos);
        }
    }

    @Override
    public void updateAuditStatus(Users users, String assignAgentId) {
        policyBaseDao.updateAuditStatus(users, assignAgentId);
    }

    @Override
    public PolicyPo getPolicyPoByPk(String policyId) {
        return policyBaseDao.getPolicyPoByPk(policyId);
    }

    @Override
    public List<PolicyAgentPo> listPolicyAgentPoByPolicyIds(List<String> policyIds) {
        return policyBaseDao.listPolicyAgentPoByPolicyIds(policyIds);
    }

    /**
     * 查询客户投保的保单
     *
     * @param customerIds      客户ID
     * @param policyStatusList 保单状态
     * @return
     */
    @Override
    public List<ClientPolicyBo> listCustomerPolicy(List<String> customerIds, List<String> policyStatusList) {
        return policyBaseDao.listCustomerPolicy(customerIds, policyStatusList);
    }

    /**
     * 查询保障中客户
     *
     * @param agentId          业务员ID
     * @param policyStatusList 保单状态
     * @return
     */
    @Override
    public List<String> listEffectiveCustomer(String agentId, List<String> policyStatusList) {
        return policyBaseDao.listEffectiveCustomer(agentId, policyStatusList);
    }

    @Override
    public void savePolicyAllocationPo(String userId, PolicyAllocationPo policyAllocationPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(policyAllocationPo.getPolicyAllocationId())) {
            policyAllocationPo.setUpdatedDate(currentTime);
            policyAllocationPo.setUpdatedUserId(userId);
            policyAllocationDao.update(policyAllocationPo);
        } else {
            policyAllocationPo.setPolicyAllocationId(UUIDUtils.getUUIDShort());
            policyAllocationPo.setCreatedDate(currentTime);
            policyAllocationPo.setCreatedUserId(userId);
            policyAllocationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyAllocationDao.insert(policyAllocationPo);
        }
    }

    @Override
    public void savePolicyAllocationRemarkPo(PolicyAllocationRemarkPo policyAllocationRemarkPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyAllocationRemarkPo.getPolicyAllocationRemarkId())) {
            //执行新增
            policyAllocationRemarkPo.setPolicyAllocationRemarkId(UUIDUtils.getUUIDShort());
            policyAllocationRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyAllocationRemarkPo.setCreatedUserId(userId);
            policyAllocationRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            policyAllocationRemarkDao.insert(policyAllocationRemarkPo);
        } else {
            //执行修改
            policyAllocationRemarkPo.setUpdatedUserId(userId);
            policyAllocationRemarkPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyAllocationRemarkDao.update(policyAllocationRemarkPo);
        }
    }

    @Override
    public void batchSavePolicyAllocationPo(String userId, List<PolicyAllocationPo> policyAllocationPos) {
        if (!AssertUtils.isNotEmpty(policyAllocationPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<PolicyAllocationPo> insertData = new ArrayList<>();
        List<PolicyAllocationPo> updateData = new ArrayList<>();
        policyAllocationPos.forEach(policyAllocationPo -> {
            if (AssertUtils.isNotEmpty(policyAllocationPo.getPolicyAllocationId())) {
                policyAllocationPo.setUpdatedDate(currentTime);
                policyAllocationPo.setUpdatedUserId(userId);
                updateData.add(policyAllocationPo);
            } else {
                policyAllocationPo.setPolicyAllocationId(UUIDUtils.getUUIDShort());
                policyAllocationPo.setCreatedDate(currentTime);
                policyAllocationPo.setCreatedUserId(userId);
                policyAllocationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(policyAllocationPo);
            }
        });
        policyAllocationDao.insert(insertData);
        policyAllocationDao.update(updateData);
    }

    @Override
    public void batchSavePolicyAllocationRemarkPo(List<PolicyAllocationRemarkPo> policyAllocationRemarkPos, String userId) {
        if (AssertUtils.isNotEmpty(policyAllocationRemarkPos)) {
            policyAllocationRemarkPos.forEach(policyAllocationRemarkPo -> {
                policyAllocationRemarkPo.setPolicyAllocationRemarkId(UUIDUtils.getUUIDShort());
                policyAllocationRemarkPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                policyAllocationRemarkPo.setCreatedUserId(userId);
                policyAllocationRemarkPo.setCreatedDate(DateUtils.getCurrentTime());
            });
            policyAllocationRemarkDao.insert(policyAllocationRemarkPos);
        }
    }

    @Override
    public List<PolicyInsuredBo> pagePolicyInsuredBo(String policyId, PolicyListVo policyListVo) {
        AssertUtils.isNotEmpty(LOGGER, policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
        LOGGER.info("policyId:" + policyId);
        return policyBaseDao.pagePolicyInsuredBo(policyId, policyListVo);
    }

    @Override
    public List<PolicyAndInsuredBo> querySokSanPolicyInsured(String idNo) {
        return policyBaseDao.querySokSanPolicyInsured(idNo);
    }

}
