package com.gclife.report.api;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.model.ResultObject;
import com.gclife.report.api.model.request.ReportApplySuspenseRequest;
import com.gclife.report.api.model.request.ReportUWSpecialTreatmentRequest;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.List;

@FeignClient(name = "gclife-report-service")
public interface ReportBaseApi {


    /**
     * 批量同步财务数据（定时任务调用）
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/payment")
    String syncReportPayment(@RequestParam(name = "pageSize") Integer pageSize,
                             @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步投保人资料
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/customer")
    String syncReportCustomer(@RequestParam(name = "pageSize") Integer pageSize,
                              @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步被保人资料
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/insured")
    String syncReportInsured(@RequestParam(name = "pageSize") Integer pageSize,
                             @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步查询业务报表-承保清单
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/policy")
    String syncReportPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                            @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步查询业务报表-团险承保清单
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/group/policy")
    String syncReportGroupPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                                 @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步查询监管报表-承保清单
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/regulatory/policy")
    String syncReportRegulatoryPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                                      @RequestParam(name = "currentPage") Integer currentPage);


    /**
     * 批量同步续期清单
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/renewal")
    String syncReportRenewal(@RequestParam(name = "pageSize") Integer pageSize,
                             @RequestParam(name = "currentPage") Integer currentPage);

    @ApiOperation(value = "删除续期失效数据(由renewal服务调用)", notes = "删除续期失效数据(由renewal服务调用)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/v1/report/delete/invalid/renewal")
    ResultObject deleteInvalidRenewal(@RequestBody List<String> renewalIds);


    @ApiOperation(value = "批量同步续期清单", notes = "批量同步续期清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/sync/group/renewal")
    public String syncReportGroupRenewal(@RequestParam(name = "pageSize") Integer pageSize,@RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步理赔报表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/claim")
    String syncReportClaim(@RequestParam(name = "pageSize") Integer pageSize,
                           @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步业务员报表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/agent")
    String syncReportAgent(@RequestParam(name = "pageSize") Integer pageSize,
                           @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步客户回访报表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/return/visit")
    String syncReportReturnVisit(@RequestParam(name = "pageSize") Integer pageSize,
                                 @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 批量同步保全报表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping(value = "/v1/report/sync/endorse")
    String syncReportEndorse(@RequestParam(name = "pageSize") Integer pageSize,
                             @RequestParam(name = "currentPage") Integer currentPage);

    @ApiOperation(value = "执行月度统计报表", notes = "执行月度统计报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/sync/monthly/statistics")
    ResultObject<Void> syncMonthlyStatistics();

    @ApiOperation(value = "月度统计特殊处理", notes = "月度统计特殊处理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/v1/report/statistics/special/treatment")
    ResultObject<Void> monthlyStatisticsSpecialTreatment(@RequestBody ReportUWSpecialTreatmentRequest reportUWSpecialTreatmentRequest);

    @ApiOperation(value = "保存 准备金提取报表 数据", notes = "保存 准备金提取报表 数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/quarterly/statistics/reserve/withdrawal/report")
    ResultObject<Void> syncReserveWithdrawalReport(@RequestBody List<ReserveWithdrawalReportBo> reserveWithdrawalReportBoList);

    @ApiOperation(value = "生成 准备金提取报表 附件", notes = "生成 准备金提取报表 附件")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "/v1/report/save/reserve/withdrawal/attachment")
    ResultObject saveReserveWithdrawalAttachment(@RequestParam(name = "quarterDate") String quarterDate) throws IOException;



    @ApiOperation(value = "执行同步银保渠道手续费费用明细表", notes = "执行同步银保渠道手续费费用明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/sync/service/charge/bank/channel")
    ResultObject<Void> syncServiceChargeBankChannel();


    @ApiOperation(value = "执行同步银保渠道手续费费用明细表-支付数据", notes = "执行同步银保渠道手续费费用明细表-支付数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/sync/service/charge/bank/channel/payment")
    ResultObject<Void> syncServiceChargeBankChannelPayment();


    @ApiOperation(value = "销售投保单保单数据同步", notes = "销售投保单保单数据同步")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "/v1/report/sync/sale/apply/policy")
    ResultObject<Void> syncSaleApplyPolicy();

    @ApiOperation(value = "生成月度预缴保费报表", notes = "生成月度预缴保费报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "/v1/report/sync/suspense")
    public ResultObject syncSuspense() throws Exception;

    @ApiOperation(value = "月度预缴保费报表特殊处理", notes = "月度预缴保费报表特殊处理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "/v1/report/suspense/special/treatment")
    ResultObject suspenseSpecialTreatment(@RequestBody ReportApplySuspenseRequest reportApplySuspenseRequest);

    /**
     * 钉钉消息推送月度报表统计 上个月的
     *
     * @param currentMonth 这个月的时间戳
     * @return
     */
    @GetMapping(value = "/v1/report/operation/send/monthly/statistics/dingtalk/msg")
    ResultObject<Void> sendMonthlyStatisticsDingtalkMsg(@RequestParam(name = "currentMonth") Long currentMonth);

    /**
     * 投保单和保单数据校验
     * */
    @GetMapping(value = "v1/system/warning/check/approve/data")
    ResultObject checkApproveData();

    /**
     * 投保单支付数据和财务支付数据校验
     * */
    @GetMapping(value = "v1/system/warning/check/apply/payment/data")
    ResultObject checkApplyPaymentData();

    /**
     * 个团险续期续保支付数据和财务支付数据校验
     * */
    @GetMapping(value = "v1/system/warning/check/renewal/payment/data")
    ResultObject checkRenewalPaymentData();

    /**
     * 系统预警查询保全支付数据和财务支付数据校验
     * */
    @GetMapping(value = "v1/system/warning/check/endorse/payment/data")
    ResultObject checkEndorsePaymentData();

    /**
     * 系统预警查询保全支付数据和财务支付数据校验状态校验
     * */
    @GetMapping(value = "v1/system/warning/check/payment/data/status")
    ResultObject checkPaymentDataStatus();

    /**
     * 批量同步学生发展基金报表
     * */
    @GetMapping(value = "v1/report/sync/group/sdf/policy")
    String syncReportGroupPolicySdf(@RequestParam(name = "pageSize") Integer pageSize,
                                           @RequestParam(name = "currentPage") Integer currentPage);

    /**
     * 月度保费实缴超额预警
     * @param currentMonth
     * @return
     */
    @GetMapping(value = "v1/send/monthly/cash/transaction/msg")
    ResultObject<Void> sendMonthlyCashTransactionMsg(Long currentMonth);
}
