# build
FROM my-oracle-java-8
MAINTAINER "yellow<<EMAIL>>"
#ENV PINPOINT_VER=1.7.3
#ENV PINPOINT_DOWNLOAD_PREFIX_URL=http://release-oss.gc-life.com/gclife/download/pinpoint
#ENV PINPOINT_SAMPLING_RATE=1
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
#    && mkdir -p /assets/pinpoint-agent \
#    && wget ${PINPOINT_DOWNLOAD_PREFIX_URL}/pinpoint-agent-${PINPOINT_VER}.tar.gz -O pinpoint-agent.tar.gz  \
#    && tar zxvf pinpoint-agent.tar.gz -C /assets/pinpoint-agent \
#    && rm -rf pinpoint-agent.tar.gz \
#    && mv /assets/pinpoint-agent/pinpoint-bootstrap-${PINPOINT_VER}.jar /assets/pinpoint-agent/pinpoint-bootstrap.jar \
#    && sed -i "s/profiler.collector.ip=127.0.0.1/profiler.collector.ip=pp-collector/g" /assets/pinpoint-agent/pinpoint.config \
#    && sed -i "s/profiler.sampling.rate=20/profiler.sampling.rate=${PINPOINT_SAMPLING_RATE}/g" /assets/pinpoint-agent/pinpoint.config
ADD @project.build.finalName@.jar /tmp/app.jar
EXPOSE @service.port@
RUN useradd -m jack
USER jack
CMD java $JAVA_OPTIONS -jar /tmp/app.jar
