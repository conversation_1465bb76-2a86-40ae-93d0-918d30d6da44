<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-business-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-platform-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>平台服务层</description>
    <organization>
        <name>大中华人寿保险股份有限公司</name>
        <url>http://www.gc-life.com</url>
    </organization>


    <properties>
        <spring.data.datasource.url>*****************************************</spring.data.datasource.url>
        <spring.data.datasource.driver.classname>org.postgresql.Driver</spring.data.datasource.driver.classname>
        <spring.data.datasource.username>policy</spring.data.datasource.username>
        <spring.data.datasource.password>${env.PGPW_FOR_BUILD}</spring.data.datasource.password>
        <spring.context.path>/platform</spring.context.path>
        <server.port>20000</server.port>
    </properties>

    <developers>
        <developer>
            <name>caoqinghua</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-platform-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-platform-base</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-auth</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Sentinel Datasource Nacos -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!-- SpringCloud Loadbalancer -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jooq</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>


    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.gclife</groupId>
                                    <artifactId>gclife-platform-base</artifactId>
                                    <version>0.0.1-SNAPSHOT</version>
                                    <type>jar</type>
                                    <overWrite>true</overWrite>
                                    <outputDirectory>./target/classes</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <!--<plugin>-->
                <!--<groupId>com.spotify</groupId>-->
                <!--<artifactId>docker-maven-plugin</artifactId>-->
            <!--</plugin>-->
            <!--<plugin>-->
                <!--<artifactId>maven-assembly-plugin</artifactId>-->
                <!--<configuration>-->
                    <!--<archive>-->
                        <!--<manifest>-->
                            <!--<mainClass>${mainClass}</mainClass>-->
                        <!--</manifest>-->
                    <!--</archive>-->
                    <!--<descriptorRefs>-->
                        <!--<descriptorRef>jar-with-dependencies</descriptorRef>-->
                    <!--</descriptorRefs>-->
                <!--</configuration>-->
            <!--</plugin>-->
        </plugins>
    </build>

    <!--<profiles>-->
        <!--<profile>-->
            <!--<id>dev</id>-->
            <!--<properties>-->
                <!--<service.port>13040</service.port>-->
            <!--</properties>-->
            <!--<activation>-->
                <!--<activeByDefault>true</activeByDefault>-->
            <!--</activation>-->
        <!--</profile>-->
    <!--</profiles>-->

    <!--<build>-->
        <!--<finalName>${project.artifactId}</finalName>-->
        <!--<plugins>-->
            <!--<plugin>-->
                <!--<groupId>org.springframework.boot</groupId>-->
                <!--<artifactId>spring-boot-maven-plugin</artifactId>-->
                <!--&lt;!&ndash; <version>1.3.0.RELEASE</version> &ndash;&gt;-->
                <!--<configuration>-->
                    <!--&lt;!&ndash;  指定该Main Class为全局的唯一入口  &ndash;&gt;-->
                    <!--<mainClass>${mainClass}</mainClass>-->
                    <!--&lt;!&ndash;<layout>ZIP</layout>&ndash;&gt;-->
                <!--</configuration>-->
                <!--<executions>-->
                    <!--<execution>-->
                        <!--<goals>-->
                            <!--<goal>repackage</goal>-->
                            <!--&lt;!&ndash; 可以把依赖的包都打包到生成的Jar包中 &ndash;&gt;-->
                        <!--</goals>-->
                    <!--</execution>-->
                <!--</executions>-->
            <!--</plugin>-->
        <!--</plugins>-->
    <!--</build>-->


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <service.port>${server.port}</service.port>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>


</project>