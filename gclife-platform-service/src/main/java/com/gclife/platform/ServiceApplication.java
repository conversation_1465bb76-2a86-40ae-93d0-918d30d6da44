package com.gclife.platform;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Created by cqh on 17-9-13.
 * <AUTHOR>
 */
@EnableTransactionManagement
@SpringBootApplication
@EnableFeignClients(basePackages = "com.gclife")
@ComponentScan(basePackages = {"com.gclife"})
@EnableMethodCache(basePackages = "com.gclife")
@EnableCreateCacheAnnotation
@EnableScheduling
public class ServiceApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(ServiceApplication.class, args);
        String port = context.getBean(Environment.class).getProperty("server.port");
        System.out.println("服务启动成功，访问端口为:"+port);
    }
}
