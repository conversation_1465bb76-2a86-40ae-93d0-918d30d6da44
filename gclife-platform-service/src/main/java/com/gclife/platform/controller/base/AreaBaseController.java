package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.platform.service.business.base.AreaBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-29
 * description:
 */
@Api(tags = "地址基础接口(2018)", description = "地址基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class AreaBaseController extends BaseController {
    @Autowired
    private AreaBaseBusinessService areaBaseBusinessService;

    @ApiOperation(value = "地址级联", notes = "地址级联")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaId", value = "地址ID", example = "0000", paramType = "query", required = false),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/level")
    public ResultObject<List<AreaResponse>> queryAreaChilds(@RequestParam(value = "areaId", required = false) String areaId) {
        return areaBaseBusinessService.queryAreaChilds(areaId);
    }

    @ApiOperation(value = "从底层往上找职级列表", notes = "从底层往上找职级列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/tree/parent/list")
    public ResultObject<List<AreaTreeResponse>> areaParentGet(@RequestParam(value = "areaId", required = false) String areaId) {
        return areaBaseBusinessService.queryAreaParentTreeList(areaId);
    }


    @ApiOperation(value = "根据Id获取地址信息", notes = "根据Id获取地址信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaId", value = "地址id", example = "地址id", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area")
    public ResultObject<AreaResponse> queryOneAreaPo(@RequestParam(value = "areaId", required = true) String areaId) {
        return areaBaseBusinessService.queryOneAreaPo(areaId);
    }

    @ApiOperation(value = "根据地址ids获取地址详情", notes = "根据地址ids获取地址详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaIds", value = "地址ids或者地址codes", example = "地址ids或者地址codes", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "area")
    public ResultObject<List<AreaResponse>> queryAreaPoByIdsOrCodes(@RequestBody List<String> areaIds) {
        return areaBaseBusinessService.queryAreaPoByIdsOrCodes(areaIds);
    }
}
