package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.UserInfoRequest;
import com.gclife.platform.model.request.UserWeixinRequest;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.UserWeixinRelationResponse;
import com.gclife.platform.service.business.UserWeixinBusinessService;
import com.gclife.platform.service.business.UsersBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Api(tags = "Users", description = "用户")
@RefreshScope
@RestController
@RequestMapping("v1/user/weixin/")
public class UsersWeixinController extends BaseController {
    @Autowired
    private UserWeixinBusinessService userWeixinBusinessService;


    @ApiOperation(value = "保存微信用户信息", notes = "保存微信用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "info")
    public ResultObject<BaseResponse> saveUsersWechatInfo(@RequestBody UserWeixinRequest userWeixinRequest) {
        return userWeixinBusinessService.saveUsersWechatInfo(userWeixinRequest);
    }



    @ApiOperation(value = "保存微信用户信息", notes = "保存微信用户信息")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "userId", value = "用户ID", example = "wangchenglong", paramType = "query", required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "relation/info")
    public ResultObject<UserWeixinRelationResponse> queryUserWeixinRelationInfo(String userId) {
        return userWeixinBusinessService.queryUserWeixinRelationInfo(userId);
    }
}