package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.BaseFactorConfigResponse;
import com.gclife.platform.model.response.BranchConfigResponse;
import com.gclife.platform.model.response.NotifyConfigResponse;
import com.gclife.platform.service.business.PlatformConfigService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-1-23
 * description:
 */
@Api(tags = "PlatformConfig", description = "平台配置")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class PlatformConfigController extends BaseController {

    @Autowired
    private PlatformConfigService platformConfigService;

    @ApiOperation(value = "top/branch/config", notes = "查询顶级机构配置")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branchId", value = "机构id", example = "GT", paramType = "query", required = true),
            @ApiImplicitParam(name = "configType", value = "配置类型", example = "AGENT_TYPE", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "top/branch/config")
    public ResultObject<BranchConfigResponse> branchConfigGet(@RequestParam("branchId") String branchId, @RequestParam("configType") String configType) {
        return platformConfigService.getBranchConfig(branchId, configType);
    }

    @ApiOperation(value = "notify/config", notes = "查询支付配置回调url")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessType", value = "缴费业务类型", example = "APPLY", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "notify/config")
    public ResultObject<NotifyConfigResponse> getNotifyConfig(@RequestParam("businessType") String businessType) {
        return platformConfigService.getNotifyConfig(businessType);
    }

    @ApiOperation(value = "查询基础要素配置", notes = "查询基础要素配置")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "configCode", value = "配置编码", example = "内勤登录微信验证", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "base/factor/config")
    public ResultObject<BaseFactorConfigResponse> getBaseFactorConfig(@RequestParam("configCode") String configCode) {
        return platformConfigService.getBaseFactorConfig(configCode);
    }

    @ApiOperation(value = "内勤登录微信验证开关", notes = "内勤登录微信验证开关")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "login/wechat/switch")
    public ResultObject editWeChatSwitch() {
        return platformConfigService.editWeChatSwitch();
    }

}
