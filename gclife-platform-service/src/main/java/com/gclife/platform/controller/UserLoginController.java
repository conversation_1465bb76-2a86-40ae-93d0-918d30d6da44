package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.platform.api.UserLoginBaseApi;
import com.gclife.platform.model.response.UserLoginCheckResponse;
import com.gclife.platform.model.response.UserLoginLogResponse;
import com.gclife.platform.service.business.UserLoginLogBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 17-12-13
 * description:用户登录日志表控制层
 */

@RefreshScope
@RestController
public class UserLoginController extends BaseController implements UserLoginBaseApi {
    @Autowired
    private UserLoginLogBusinessService userLoginLogBusinessService;

    @Override
    public ResultObject userLoginLogPost(@RequestBody AppRequestHeads appRequestHeads, @RequestParam("userId") String userId, @RequestParam("error")String error) {
        return userLoginLogBusinessService.postUserLoginLog(appRequestHeads, userId, error);
    }


}
