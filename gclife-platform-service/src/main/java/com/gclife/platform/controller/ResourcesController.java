package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoSaveURL;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.bo.MenuResourcesBo;
import com.gclife.platform.model.response.GrantedSubMenuResponse;
import com.gclife.platform.service.business.ResourcesBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-12-15
 * description: 资源控制器
 */
@Api(tags = "Resources", description = "资源查询")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class ResourcesController extends BaseController {

    @Autowired
    ResourcesBusinessService resourcesBusinessService;

    @ApiOperation(value = "resources/granted/submenu", notes = "获取有权限子菜单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentMenuValue", value = "父菜单值", paramType = "query", required = true),
            @ApiImplicitParam(name = "httpMethod", value = "HTTP请求类型", paramType = "query", required = true),
            @ApiImplicitParam(name = "resourceCode", value = "服务名，也就是域名", paramType = "query", required = true),
            @ApiImplicitParam(name = "parentType", value = "父菜单类型，如FRONTEND_DOMAIN或FRONTEND_URL", paramType = "query", required = true),
            @ApiImplicitParam(name = "childType", value = "子菜单类型，如FRONTEND_DOMAIN或FRONTEND_URL", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "resources/granted/submenu")
    public ResultObject<GrantedSubMenuResponse> retriveGrantedSubmenu(String parentMenuValue, String httpMethod,
                                                                      String serviceName, String parentType,
                                                                      String childType, HttpServletRequest httpServletRequest)
    {
        return resourcesBusinessService.retriveGrantedSubmenu(getCurrentLoginUsers(), parentMenuValue, httpMethod, serviceName, parentType, childType, httpServletRequest.getHeader("language"));
    }

    @ApiOperation(value = "resources/granted/submenu/1", notes = "获取有权限子菜单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "resources/granted/submenu/{parentResourceId}")
    public ResultObject<GrantedSubMenuResponse> retriveGrantedSubmenu(@PathVariable(value = "parentResourceId")String parentResourceId
                                                                        ,HttpServletRequest httpServletRequest)
    {
        return resourcesBusinessService.retriveByParentResourceId(getCurrentLoginUsers(), parentResourceId, httpServletRequest.getHeader("language"));
    }

    @ApiOperation(value = "resources/granted/submenu/currentUrl", notes = "获取有权限子菜单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUrl", value = "父菜单值", paramType = "query", required = true),
            @ApiImplicitParam(name = "httpMethod", value = "HTTP请求类型", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "resources/granted/submenu/currentUrl")
    public ResultObject<GrantedSubMenuResponse> retriveByCurrentUrl(String currentUrl, String httpMethod, HttpServletRequest httpServletRequest)
    {
        return resourcesBusinessService.retriveByCurrentUrl(getCurrentLoginUsers(), currentUrl, httpMethod, httpServletRequest.getHeader("language"));
    }

    @ApiOperation(value = "access/resources", notes = "权限检查")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "requestUrl", value = "请求的路径，如/accept", paramType = "query"),
                    @ApiImplicitParam(name = "httpMethod", value = "http请求方法，如GET", paramType = "query"),
                    @ApiImplicitParam(name = "serviceName", value = "微服务名，如gclife-apply-front", paramType = "query"),
                    @ApiImplicitParam(name = "type", value = "资源类型，如FRONTEND_URL", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "access/resources")
    public ResultObject<String> accsResCheck(@RequestParam(name = "requestUrl") String requestUrl,
                                      @RequestParam(name = "httpMethod") String httpMethod,
                                      @RequestParam(name = "serviceName") String serviceName,
                                      @RequestParam(name = "type") String type)
    {
        return resourcesBusinessService.accsResCheck(getCurrentLoginUsers(), requestUrl, httpMethod, serviceName, type);
    }

    @ApiOperation(value = "resources/redirect/1", notes = "获取跳转地址")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "resources/redirect/{parentResourceId}")
    public ResultObject<MenuResourcesBo> retriveRedirectUrl(@PathVariable(value = "parentResourceId")String parentResourceId
                                                            , HttpServletRequest httpServletRequest)
    {
        return resourcesBusinessService.retriveRedirectUrl(getCurrentLoginUsers(), parentResourceId, httpServletRequest.getHeader("language"));
    }


    @ApiOperation(value = "resources/granted/button/currentUrl", notes = "获取有权限按钮")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "serviceName", value = "当前前端项目域名", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @GetMapping(value = "resources/granted/button")
    public ResultObject<Map<String, Boolean>> retriveGrantedButton(String serviceName)
    {
        return resourcesBusinessService.retriveGrantedButton(getCurrentLoginUsers(), serviceName);
    }

    @ApiOperation(value = "access/resources/backend", notes = "后端权限检查")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "requestUrl", value = "请求的路径，如/accept", paramType = "query"),
                    @ApiImplicitParam(name = "httpMethod", value = "http请求方法，如GET", paramType = "query")
             }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "access/resources/backend")
    public ResultObject<Boolean> accsResCheckBackend(@RequestParam(name = "requestUrl") String requestUrl,
                                             @RequestParam(name = "httpMethod") String httpMethod)
    {
        return resourcesBusinessService.accsResCheckBackend(getCurrentLoginUsers(), requestUrl, httpMethod);
    }
}
