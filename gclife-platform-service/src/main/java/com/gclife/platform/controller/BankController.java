package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.service.business.BankBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
@Api(tags = "Bank", description = "银行信息API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class BankController extends BaseController {

    @Autowired
    BankBusinessService bankBusinessService;

    @ApiOperation(value = "bank", notes = "根据银行编码获取银行信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "bank/{bankCode}")
    public ResultObject<BankResponse> bankGet(
            @ApiParam(value = "银行编码", name = "bankCode", example = "1")
            @PathVariable(value = "bankCode") String bankCode,String language
    ) {
        return bankBusinessService.getBankById(null, bankCode,this.getLanguage(language));
    }


    @ApiOperation(value = "banks", notes = "获取银行列表信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "banks")
    public ResultObject<List<BankResponse>> banksGet(String language) {
        return bankBusinessService.getBanks(this.getLanguage(language));
    }


    @ApiOperation(value = "获取扣款银行列表信息", notes = "获取扣款银行列表信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "payment/banks")
    public ResultObject<List<BankResponse>> listDeductionBank() {
        return bankBusinessService.getDeductionBanks(this.getCurrentLoginUsers().getLanguage());
    }


    @ApiOperation(value = "获取银行列表", notes = "根据银行类型获取银行列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bankType", value = "银行类型", example = "银行类型(RECEIPT:收款;PAYMENT:付款;DEDUCTION:扣款),为空时查询收款银行"),
            @ApiImplicitParam(name = "validFlag", value = "有效标识", example = "有效标识(effective:有效;invalid:无效;all:全部),为空时默认查询有效")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "banks/{bankType}")
    public ResultObject<List<BankResponse>> listBank(@PathVariable(value = "bankType", required = false) String bankType,
                                                     @RequestParam(value = "validFlag", required = false) String validFlag) {
        return bankBusinessService.listBank(bankType, validFlag);
    }
}