package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.api.InternationalBaseApi;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.vo.SyscodeResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 *
 * <AUTHOR>
 */

@Api(tags = "国际化基础接口(2018)", description = "国际化基础接口(2018)")
@RefreshScope
@RestController
public class InternationBaseController extends BaseController implements InternationalBaseApi {

    @Autowired
    private InternationalBaseBusinessService internationalBaseBusinessService;


    @ApiVersion(1)
    @AutoCheckPermissions
    @Override
    public ResultObject<List<SyscodeResponse>> queryInternational(@RequestParam("codeType") String codeType, @RequestParam(value = "language", required = false) String language) {
        return internationalBaseBusinessService.queryInternational(codeType, this.getLanguage(language));
    }


    @ApiVersion(1)
    @AutoCheckPermissions
    @Override
    public ResultObject<SyscodeResponse> queryOneInternational(@RequestParam("codeType") String codeType, @RequestParam("codeKey") String codeKey, @RequestParam(value = "language", required = false) String language) {
        return internationalBaseBusinessService.queryOneInternational(codeType, codeKey, this.getLanguage(language));
    }


    @ApiVersion(1)
    @AutoCheckPermissions
    @Override
    public ResultObject<List<SyscodeResponse>> queryInternationalByCodeKeys(@RequestBody SyscodeForm syscodeReqFc) {
        syscodeReqFc.setLang(this.getLanguage(syscodeReqFc.getLang()));
        return internationalBaseBusinessService.queryInternationalByCodeKeys(syscodeReqFc);
    }

    @ApiVersion(1)
    @AutoCheckPermissions
    @Override
    public ResultObject<Map<String, List<SyscodeResponse>>> queryBatchInternationalByCodeKeys(@RequestParam(value = "language", required = false) String language, @RequestBody List<String> codeTypes) {
        return internationalBaseBusinessService.queryBatchInternationalByCodeKeys(this.getLanguage(language), codeTypes);
    }

}