package com.gclife.platform.controller.middle;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.request.BranchPagingRequest;
import com.gclife.platform.form.branch.UserBranchRequest;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.BranchBusinessService;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.vo.branch.BranchParentResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchSimpleResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 * <AUTHOR>
 */

@Api(tags = "机构中台接口(2018)", description = "机构中台接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/middle/")
public class BranchMiddleController extends BaseController {

    @Autowired
    private BranchBaseBusinessService branchBaseBusinessService;

    @Autowired
    private BranchBusinessService branchBusinessService;

    @ApiOperation(value = "根据机构ID查询机构信息", notes = "根据机构ID查询机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch")
    public ResultObject<BranchResponse> queryOneBranchById(@RequestParam("branchId") String branchId) {
        return  branchBaseBusinessService.queryOneBranchById(branchId,this.getLanguage(this.getCurrentLoginUsers().getLanguage()));
    }


    @ApiOperation(value = "根据机构IDS查询机构信息", notes = "根据机构IDS查询机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/list")
    public ResultObject<List<BranchResponse>> queryBranchByIds(@RequestBody List<String> branchIds) {
        return  branchBaseBusinessService.queryBranchByIds(branchIds);
    }



    @ApiOperation(value = "查询机构树上的机构集合", notes = "查询机构树上的机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/tree/child/list")
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListById(@RequestParam("branchId") String branchId) {
        return  branchBaseBusinessService.queryBranchTreeBranchListById(branchId);
    }


    @ApiOperation(value = "查询机构树上的机构集合", notes = "查询机构树上的机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/tree/child/list")
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListByIds(@RequestBody List<String> branchIds) {
        return  branchBaseBusinessService.queryBranchTreeBranchListByIds(branchIds);
    }


    @ApiOperation(value = "查询机构树上的(叶子)机构集合", notes = "查询机构树上的(叶子)机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/tree/child/leaf/list")
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(@RequestParam("branchId") String branchId) {

        return  branchBaseBusinessService.queryBranchTreeLeafListById(branchId);
    }


    @ApiOperation(value = "查询机构树上的(叶子)机构集合", notes = "查询机构树上的(叶子)机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/tree/child/leaf/list")
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(@RequestBody List<String> branchIds) {
        return  branchBaseBusinessService.queryBranchTreeLeafListByIds(branchIds);
    }


    @ApiOperation(value = "查询父机构集合(从叶子机构到父机构排序)", notes = "查询父机构集合(从叶子机构到父机构排序)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/tree/parent/list")
    public ResultObject<List<BranchResponse>> queryBranchParentListById(@RequestParam("branchId") String branchId) {
        return  branchBaseBusinessService.queryBranchParentListById(branchId);
    }


    @ApiOperation(value = "查询父机构集合(从叶子机构到父机构排序)", notes = "查询父机构集合(从叶子机构到父机构排序)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/tree/parent/list")
    public ResultObject<List<BranchParentResponse>> queryBranchParentListByIds(@RequestBody List<String> branchIds) {
        return  branchBaseBusinessService.queryBranchParentListByIds(branchIds);
    }




    @ApiOperation(value = "查询机构树", notes = "查询机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/tree")
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeById(@RequestParam("branchId") String branchId) {
        return  branchBaseBusinessService.queryBranchTreeById(branchId);
    }


    @ApiOperation(value = "查询机构树", notes = "查询机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/tree")
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeByIds(@RequestBody List<String> branchIds) {
        return  branchBaseBusinessService.queryBranchTreeByIds(branchIds);
    }




    @ApiOperation(value = "查询用户所属机构", notes = "查询用户所属机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/branch")
    public ResultObject<BranchResponse> queryOneUserBranch(@RequestParam(value = "userId",required = false) String userId) {
        return  branchBaseBusinessService.queryOneUserBranch(AssertUtils.isNotEmpty(userId)?userId:this.getCurrentLoginUsers().getUserId());
    }



    @ApiOperation(value = "查询用户所属机构树", notes = "查询用户所属机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/branch/tree")
    public ResultObject<List<BranchTreeResponse>> queryUserBranchTree() {
        return branchBaseBusinessService.queryUserBranchTree(this.getCurrentLoginUsers().getUserId());
    }


    @ApiOperation(value = "查询用户所属机构树", notes = "查询用户所属机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/branch/tree/list")
    public ResultObject<List<BranchResponse>> queryUserBranchTreeList() {
        return branchBaseBusinessService.queryUserBranchTreeList(this.getCurrentLoginUsers().getUserId());
    }


    @ApiOperation(value = "查询用户管理的机构树(叶子)集合", notes = "查询用户管理的机构(叶子)集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/manager/branch/tree/leaf/list")
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeLeaf(@RequestParam(value = "userId",required = false) String userId) {
        return  branchBaseBusinessService.queryUserOptionBranchTreeLeaf(AssertUtils.isNotEmpty(userId)?userId:this.getCurrentLoginUsers().getUserId());
    }


    @ApiOperation(value = "查询用户管理的机构集合", notes = "查询用户管理的机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/manager/branch/list")
    public ResultObject<List<BranchResponse>> loadUserOptionFirstLevelBranch(@RequestParam(value = "userId",required = false) String userId) {
        return  branchBaseBusinessService.loadUserOptionFirstLevelBranch(AssertUtils.isNotEmpty(userId)?userId:this.getCurrentLoginUsers().getUserId());
    }

    @ApiOperation(value = "查询用户管理的机构树", notes = "查询用户管理的机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/manager/branch/tree")
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTreeFiterNoLeafBranch(@RequestParam(value = "userId",required = false) String userId) {
        return  branchBaseBusinessService.queryUserOptionBranchTreeFiterNoLeafBranch(AssertUtils.isNotEmpty(userId)?userId:this.getCurrentLoginUsers().getUserId());
    }


    @ApiOperation(value = "查询用户管理的机构树", notes = "查询用户管理的机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/manager/branch/all/tree")
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTree(@RequestParam(value = "userId",required = false) String userId) {
        return  branchBaseBusinessService.queryUserOptionBranchTree(AssertUtils.isNotEmpty(userId)?userId:this.getCurrentLoginUsers().getUserId());
    }

    @ApiOperation(value = "保存用户操作机构", notes = "保存用户操作机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "user/manager/branch")
    public ResultObject<BaseResponse> saveUserOptionBranch(@RequestBody UserBranchRequest userRequestRequest) {
        return  branchBaseBusinessService.saveUserOptionBranch(userRequestRequest);
    }


    @ApiOperation(value = "查询某层级机构列表", notes = "查询某层级机构列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "levelNum", value = "机构层级num", example = "PRO8888888888888", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "level/simple/branch/list")
    public ResultObject<List<BranchSimpleResponse>> queryLevelBranch(@RequestParam(value = "levelNum",required = true) Long levelNum) {
        return  branchBaseBusinessService.queryLevelBranch(this.getCurrentLoginUsers().getUserId(),levelNum);
    }

    @ApiOperation(value = "export/branchs", notes = "导出机构清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
    })
    @GetMapping(value = "export/branchs")
    public  void branchBulkExport(HttpServletResponse response) {
        branchBusinessService.exportBranchs(response, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询级机所属层级", notes = "查询级机所属层级")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "GMA", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/level")
    public ResultObject<Long> queryBranchBelongLevel(@RequestParam(value = "branchId", required = true) String branchId) {
        return branchBaseBusinessService.queryBranchBelongLevel(branchId);
    }

    @ApiOperation(value = "模糊查询所有机构", notes = "模糊查询所有机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "GMA", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/fuzzy/list")
    public ResultObject<List<BranchFuzzyResponse>> queryBranchFuzzy(BranchPagingRequest branchPagingRequest) {
        return branchBaseBusinessService.queryBranchFuzzy(branchPagingRequest,null);
    }


    @ApiOperation(value = "模糊查询管理机构", notes = "模糊查询管理机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "GMA", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/manager/fuzzy/list")
    public ResultObject<List<BranchFuzzyResponse>> queryBranchManagerFuzzy(BranchPagingRequest branchPagingRequest) {
        return branchBaseBusinessService.queryBranchFuzzy(branchPagingRequest, PlatformTermEnum.CHANNEL_TYPE.MANAGER.name());
    }


    @ApiOperation(value = "模糊查询个代机构", notes = "模糊查询个代机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "GMA", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/agent/fuzzy/list")
    public ResultObject<List<BranchFuzzyResponse>> queryBranchAgentFuzzy(BranchPagingRequest branchPagingRequest) {
        return branchBaseBusinessService.queryBranchFuzzy(branchPagingRequest,PlatformTermEnum.CHANNEL_TYPE.AGENT.name());
    }
}