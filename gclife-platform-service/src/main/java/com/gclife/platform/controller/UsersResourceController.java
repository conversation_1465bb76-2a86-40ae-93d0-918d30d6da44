package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.UserInfoRequest;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.service.business.UsersBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Api(tags = "Users", description = "用户")
@RefreshScope
@RestController
@RequestMapping("v1/resource/")
public class UsersResourceController extends BaseController {
    @Autowired
    private UsersBusinessService usersBusinessService;

    @ApiOperation(value = "获取有该资源的用户", notes = "获取有该资源的用户(根据管理机构往上找,管理最近机构的用户)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceCode", value = "资源编码code", example = "RECEIVE_TASK", paramType = "query", required = true),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "10020000000000", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "recent/users")
    public ResultObject<List<String>> getResourceUsers(String resourceCode, String branchId) {
        return usersBusinessService.loadResourceUserRecentList(resourceCode, branchId);
    }

    @ApiOperation(value = "获取有该资源的用户", notes = "获取有该资源的所有用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resourceCode", value = "资源编码code", example = "RECEIVE_TASK", paramType = "query", required = true),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "10020000000000", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "users")
    public ResultObject<List<String>> loadResourceUserLastLevelList(String resourceCode, String branchId) {
        return usersBusinessService.loadResourceUserList(resourceCode, branchId);
    }

}