package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.CareerBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:获取职业代码和职业类别
 */
@Api(tags = "Career", description = "职业代码API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class CareerController extends BaseController {
    @Autowired
    private CareerBusinessService careerBusinessService;

    @ApiOperation(value = "career", notes = "获取职业代码")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerId", value = "职业ID", example = "GCL-000240000100002", paramType = "query"),
            @ApiImplicitParam(name = "providerId", value = "保险公司ID", example = "PRO8888888888888", paramType = "query")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career")
    public ResultObject<List<CareerResponse>> careerGet(String careerId, String providerId ,String transformLanguage) {
        return careerBusinessService.getCareerById(careerId, providerId, this.getCurrentLoginUsers(),transformLanguage);
    }


    @ApiOperation(value = "从底层往上找职级列表", notes = "从底层往上找职级列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "parent/career/list")
    public ResultObject<List<CareerTreeResponse>> careerParentGet(String careerId) {
        return careerBusinessService.getCareerParentsById(careerId, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "根据职业id或者职业code获取职业详情", notes = "根据职业id或者职业code获取职业详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerId", value = "职业id", example = "职业id", paramType = "query", required = true),
            @ApiImplicitParam(name = "language", value = "语言", example = "语言", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/info")
    public ResultObject<CareerNewResponse> careerInfoGet(String careerId, String language) {
        return careerBusinessService.getCareerInfoById(careerId, this.getLanguage(language));
    }

    @GetMapping(value = "career/info1")
    public ResultObject careetest1(String careerId, String language){
        return careerBusinessService.getCareerInfoByIdTest(careerId, this.getLanguage(language));
    }
    @ApiOperation(value = "根据职业ids或者职业codes获取职业详情", notes = "根据职业ids或者职业codes获取职业详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerIds", value = "职业ids或者职业codes", example = "职业ids或者职业codes", paramType = "query", required = true),
            @ApiImplicitParam(name = "language", value = "语言", example = "语言", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "career/info")
    public ResultObject<List<CareerResponse>> careerInfoPost(@RequestBody List<String> careerIds, String language) {
        return careerBusinessService.postCareerInfoByIds(careerIds, this.getLanguage(language));
    }

    @ApiOperation(value = "career/name/{careerId}", notes = "根据叶子节点ID获取父类职业名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/name/{careerId}")
    public ResultObject<CareerNameResponse> careerNameGet(@PathVariable String careerId) {
        return careerBusinessService.getCareerNameById(careerId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "根据叶子节点ID集合获取父类职业名称", notes = "根据叶子节点ID集合获取父类职业名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "career/name")
    public ResultObject<List<CareerNameResponse>> postCareerName(@RequestBody List<String> careerIds) {
        return careerBusinessService.postCareerName(careerIds, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询职业树(所有)", notes = "查询职业树(所有)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "providerId", value = "保险公司ID", example = "PRO8888888888888", paramType = "query")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/tree/all")
    public ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(String providerId) {
        return careerBusinessService.queryCareerTreeById(providerId);
    }
}