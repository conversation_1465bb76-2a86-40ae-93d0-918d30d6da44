package com.gclife.platform.controller.event;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.request.EventRequest;
import com.gclife.platform.model.response.EventResponse;
import com.gclife.platform.service.business.event.EventBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
@Api(tags = "Event", description = "事件API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class EventController extends BaseController {

    @Autowired
    EventBusinessService eventBusinessService;

    @ApiOperation(value = "events", notes = "获取首页事件信息列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "events")
    public ResultObject<BasePageResponse<EventResponse>> eventListGet(BasePageRequest basePageRequest) {
        return eventBusinessService.getEventInfo(basePageRequest);
    }

    @ApiOperation(value = "event", notes = "点赞或转发")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dynamicType", value = "动态类型（LIKE:点赞；FORWARD:转发）", example = "LIKE", paramType = "query", required = true),
            @ApiImplicitParam(name = "eventId", value = "事件ID", example = "12121", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "event/action")
    public ResultObject likeOrForward(@RequestParam("dynamicType") String dynamicType, @RequestParam("eventId") String eventId) {
        return eventBusinessService.likeOrForward(getCurrentLoginUsers(),dynamicType,eventId);
    }

    @ApiOperation(value = "event", notes = "获取首页事件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "eventId", value = "事件ID", example = "12121", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "event")
    public ResultObject<EventResponse> eventInfoGet(String eventId) {
        return eventBusinessService.queryOneEventInfo(eventId);
    }

    @ApiOperation(value = "event", notes = "创建事件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "event")
    public ResultObject eventInfoSave(@RequestBody EventRequest eventRequest) {
        return eventBusinessService.saveEventInfo(eventRequest);
    }
}