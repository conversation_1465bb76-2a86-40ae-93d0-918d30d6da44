package com.gclife.platform.controller;

import com.gclife.common.annotation.ErrorTip;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.service.business.AccountBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息
 */
@Api(tags = "Account", description = "账户信息API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class AccountController extends BaseController {

    @Autowired
    private AccountBusinessService accountBusinessService;

    @ApiOperation(value = "account", notes = "根据账户ID获取账户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "account/{accountId}")
    public ResultObject<AccountResponse> getAccount(
            @ApiParam(value = "授权帐号ID", name = "accountId", example = "001")
            @PathVariable(value = "accountId") String accountId
    ) {
        return accountBusinessService.getAccountById(accountId);
    }

    @ApiOperation(value = "account/list", notes = "根据账户ID获取账户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "account/list")
    @ErrorTip(classType = PlatformErrorConfigEnum.class,key = "PLATFORM_QUERY_ACCOUNT_ERROR")
    public ResultObject<List<AccountResponse>> getAccountList(@RequestBody List<String> accountIdList) {
        return accountBusinessService.getAccountList(accountIdList);
    }


    @ApiOperation(value = "账户信息保存", notes = "账户信息保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "account/save")
    public ResultObject accountInfoPost(
            @RequestBody AccountRequest accountRequest
    ) {
        return accountBusinessService.saveAccount(accountRequest);
    }

    @ApiOperation(value = "账户信息保存", notes = "账户信息保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "account/info/save")
    public ResultObject saveAccountInfo(@RequestBody AccountRequest accountRequest) {
        return accountBusinessService.saveAccountInfo(accountRequest);
    }

    @ApiOperation(value = "账户信息批量保存", notes = "账户信息批量保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "account/save/batch")
    public ResultObject saveAccounts(@RequestBody List<AccountRequest> accountRequests) {
        return accountBusinessService.saveAccounts(accountRequests);
    }

    @ApiOperation(value = "账户信息回滚", notes = "账户信息回滚")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountId", value = "账户ID", example = "654324", paramType = "query", required = true)
    })
    @DeleteMapping(value = "account/rollback")
    public ResultObject accountInfoRollback(String accountId) {
        return accountBusinessService.accountRollback(accountId);
    }


    @ApiOperation(value = "根据用户ID获取账户信息", notes = "根据用户ID获取账户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "accounts")
    public ResultObject<List<AccountResponse>> accountGet(String userId) {
        return accountBusinessService.getAccountByUserId(userId);
    }

    @ApiOperation(value = "根据用户ID获取账户信息", notes = "根据用户ID获取账户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "accounts")
    public ResultObject<List<AccountResponse>> accountPost(@RequestBody List<String> userIds) {
        return accountBusinessService.getAccountByUserIds(userIds);
    }

}