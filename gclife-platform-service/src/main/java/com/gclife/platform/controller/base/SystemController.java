package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.model.response.SystemResponse;
import io.swagger.annotations.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "系统信息", description = "系统信息")
@RefreshScope
@RestController
@RequestMapping("v1/base/system/")
public class SystemController {


    @ApiOperation(value = "获取系统时间", notes = "获取系统时间")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type", value = "类型", example = "APPLY_WORK_FLOW_STATUS", paramType = "query", required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping()
    public ResultObject<SystemResponse> systemGet() {
        ResultObject<SystemResponse> resultObject = new ResultObject<>();
        SystemResponse systemResponse = new SystemResponse();
        String systemDate = DateUtils.timeStrToString(System.currentTimeMillis(), DateUtils.FORMATE6);
        systemResponse.setSystemDate(systemDate);
        resultObject.setData(systemResponse);
        return resultObject;
    }

}
