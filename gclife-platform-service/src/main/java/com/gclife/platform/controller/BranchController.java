package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.FinancialBranchPo;
import com.gclife.platform.model.response.BranchNameResponse;
import com.gclife.platform.service.business.BranchBusinessService;
import com.gclife.platform.vo.branch.BranchBankResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 * <AUTHOR>
 */

@Api(tags = "Branch", description = "机构API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class BranchController extends BaseController {

    @Autowired
    private BranchBusinessService branchBusinessService;

    @ApiOperation(value = "branch/1", notes = "根据机构ID查询机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/{id}")
    public ResultObject<BranchResponse> branchGet(@ApiParam(value = "机构ID",name = "id",example = "10010010000000")
                                                   @PathVariable(value = "id")String id) {
         /*TODO:
         根据机构ID查询机构信息
        */
        Users users  = this.getCurrentLoginUsers();
        return  branchBusinessService.loadBranchInfo(users,id);
    }

    @ApiOperation(value = "根据机构ID查询机构信息", notes = "根据机构ID查询机构信息,机构名称国际化")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/international/{id}")
    public ResultObject<BranchResponse> branchGet1(@PathVariable(value = "id")String id,
                                                   @RequestParam(value = "language")String language) {
        return  branchBusinessService.loadBranchInfo(id, language);
    }





    @ApiOperation(value = "branchs", notes = "根据机构ids 或 codes 查询机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branchs")
    public ResultObject<List<BranchResponse>> branchsPost(@RequestBody List<String> branchIds) {
        Users users  = this.getCurrentLoginUsers();
        return  branchBusinessService.loadBranchsByIds(users,branchIds);
    }



    @ApiOperation(value = "manager/branch/trees", notes = "查询当前用户管理的销售机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manager/branch/trees")
    public ResultObject<List<BranchTreeResponse>> userManagerBranchs() {

        return  branchBusinessService.loadUserBranchTree(this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "branch/trees", notes = "查询当前销售机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/trees")
    public ResultObject<List<BranchTreeResponse>> userTreeBranchs(String branchId) {

        return  branchBusinessService.loadBranchTreesByBranchId(this.getCurrentLoginUsers(),branchId);
    }

    @ApiOperation(value = "查询机构的所有机构树", notes = "查询机构的所有机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/trees")
    public ResultObject<List<BranchTreeResponse>> userTreesBranchs(@RequestBody List<String> branchIds) {

        return  branchBusinessService.loadBranchTreeByBranchIds(this.getCurrentLoginUsers(),branchIds);
    }


    @ApiOperation(value = "manager/all/branchs", notes = "查询当前用户管理的销售机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manager/all/branchs")
    public ResultObject<List<BranchResponse>> userManagerAllBranchs() {

        return  branchBusinessService.loadUserManagerAllBranchs(this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "manager/branchs", notes = "查询当前用户管理的销售机构列表(顶层节点集合)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manager/branchs")
    public ResultObject<List<BranchResponse>> userManagerBranchs2() {

        return  branchBusinessService.loadUserManagerRootBranchs(this.getCurrentLoginUsers());
    }




    @ApiOperation(value = "manager/leafbranchs", notes = "查询当前用户管理的销售机构的叶子机构(营业部)列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manager/leafbranchs")
    public ResultObject<List<BranchResponse>> userManagerLeafBranchs() {

        return  branchBusinessService.loadUserBranchTreeLeaf(this.getCurrentLoginUsers());
    }



    @ApiOperation(value = "branch/leafbranchs", notes = "查询机构的所有叶子机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/leafbranchs")
    public ResultObject<List<BranchResponse>> userManagerLeafBranchs(@RequestBody List<String> branchIds) {

        return  branchBusinessService.loadBranchAllBranchLeaf(this.getCurrentLoginUsers(),branchIds);
    }






    @ApiOperation(value = "branch/leafbranchs", notes = "查询机构的所有叶子机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/leafbranchs")
    public ResultObject<List<BranchResponse>> userManagerLeafBranchs(@RequestParam("branchId") String branchId) {

        return  branchBusinessService.loadBranchAllBranchLeaf(this.getCurrentLoginUsers(),branchId);
    }




    @ApiOperation(value = "parent/branchs", notes = "查询父类机构树列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "parent/branchs")
    public ResultObject<List<BranchResponse>> userParentBranchs(String branchId) {
        return  branchBusinessService.loadParentBranchs(this.getCurrentLoginUsers(),branchId);
    }



    @ApiOperation(value = "parent/branchs", notes = "查询父类机构列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "parent/branchs")
    public ResultObject<List<BranchResponse>> userParentBranchs(@RequestBody List<String> branchIds) {
        return  branchBusinessService.loadParentBranchs(this.getCurrentLoginUsers(),branchIds);
    }

    @ApiOperation(value = "area/name/{areaId}", notes = "根据叶子节点ID获取父类机构短名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "parent/branchname/{branchId}")
    public ResultObject<BranchNameResponse> branchNameGet(@PathVariable String branchId) {
        String language = null;
        try {
            language = this.getAppRequestHandler().getLanguage() ;
        } catch (Exception e) {
        }
        if(!AssertUtils.isNotEmpty(language)) {
            language = this.getCurrentLoginUsers().getLanguage();
        }
        return branchBusinessService.loadAllBranchLeafName(language,branchId);
    }

    @ApiOperation(value = "获取指定用户机构", notes = "获取指定用户机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/branch")
    public ResultObject<BranchResponse> getBreachByUserId(@RequestParam("userId") String userId) {
        return branchBusinessService.loadUserBranch(this.getCurrentLoginUsers(),userId);
    }

    @ApiOperation(value = "跟据机构编码获取机构信息", notes = "跟据机构编码获取机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/code")
    public ResultObject<List<BranchResponse>> getBreachByCode(@RequestParam("branchCodes") String... branchCodes) {
        return branchBusinessService.loadBreachByCode(this.getCurrentLoginUsers(),branchCodes);
    }


    @ApiOperation(value = "跟据机构ID获取银行网点的机构编号", notes = "跟据机构ID获取银行网点的机构编号")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "branch/bank/code")
    public ResultObject<List<BranchBankResponse>> getBranchBankCode(@RequestBody List<String> branchIds) {
        return branchBusinessService.getBranchBankCode(this.getCurrentLoginUsers(),branchIds);
    }

    @ApiOperation(value = "根据机构ID获取同层级的机构集合", notes = "根据机构ID获取同层级的机构集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/same/level")
    public ResultObject<List<BranchResponse>> getBranchSameLevel(@RequestParam("branchId") String branchId) {
        return branchBusinessService.getBranchSameLevel(this.getCurrentLoginUsers(),branchId);
    }

    @ApiOperation(value = "模糊查询贷款金融机构信息", notes = "模糊查询贷款金融机构信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/financial/fuzzy/list")
    public ResultObject<List<FinancialBranchPo>> queryFinancialBranchFuzzy(String keyword) {
        return branchBusinessService.queryFinancialBranchFuzzy(this.getCurrentLoginUsers(),keyword);
    }

}