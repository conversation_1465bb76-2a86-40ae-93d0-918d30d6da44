package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.workflow.RoleWfResponse;
import com.gclife.platform.model.response.workflow.UserWfResponse;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.service.business.UsersBusinessService;
import com.gclife.platform.service.business.UsersWorkFlowBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-16
 * \* Time: 下午4:13
 * \* To change this template use File | Settings | File Templates.
 * \* Description: activiti流程用户信息接口
 *
 * <AUTHOR>
 */
@Api(tags = "User_WorkFlow", description = "工作流用户API")
@RefreshScope
@RestController
@RequestMapping("v1/workflow/")
public class UsersWorkFlowController extends BaseController {

    @Autowired
    UsersWorkFlowBusinessService usersWorkFlowBusinessService;

    @Autowired
    UsersBusinessService usersBusinessService;


    @ApiOperation(value = "user", notes = "根据用户名称查询用户信息")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "username", value = "账户名", example = "wangchenglong", paramType = "query", required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user")
    public ResultObject<UserResponse> userByNameGet(String username) {
        Users users = this.getCurrentLoginUsers();
        return usersBusinessService.loadUserByUserName(users, username);
    }


    @ApiOperation(value = "user/1", notes = "查询工作流微服务用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/{userId}")
    public ResultObject<UserWfResponse> userGet(@ApiParam(value = "用户ID", defaultValue = "", name = "userId", example = "1") @PathVariable(value = "userId") String userId) {

        return usersWorkFlowBusinessService.loadWorkflowUser(userId);
    }


    @ApiOperation(value = "user/1/roles", notes = "查询工作流微服务用户角色信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/{userId}/roles")
    public ResultObject<List<RoleWfResponse>> userRolesGet(@ApiParam(value = "用户ID", defaultValue = "", name = "userId", example = "1") @PathVariable(value = "userId") String userId) {

        return usersWorkFlowBusinessService.loadWorkflowUserRoles(userId);
    }


    @ApiOperation(value = "role/1", notes = "查询工作流微服务角色信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "role/{roleId}")
    public ResultObject<RoleWfResponse> roleGet(@ApiParam(value = "角色ID", defaultValue = "", name = "roleId", example = "1") @PathVariable(value = "roleId") String roleId) {

        return usersWorkFlowBusinessService.loadWorkflowRole(roleId);
    }

    @ApiOperation(value = "users", notes = "根据节点ID和机构ID查看哪些用户拥有对应的操作权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "activityAuid", value = "资源编码code", example = "RECEIVE_TASK", paramType = "query", required = true),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "10020000000000", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "users")
    public ResultObject<UsersWfResponse> userWorkFlowGet(
            String activityAuid, String branchId
    ) {

        return usersWorkFlowBusinessService.loadWorkflowUsers(activityAuid, branchId);
    }


}