package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.service.business.UsersWorkFlowBusinessService;
import com.gclife.platform.service.business.base.UsersBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 * <AUTHOR>
 */

@Api(tags = "用户工作流基础接口(2018)", description = "用户工作流基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/workflow/")
public class UsersWorkFlowBaseController extends BaseController {

    @Autowired
    private UsersWorkFlowBusinessService usersWorkFlowBusinessService;

    @ApiOperation(value = "users", notes = "根据节点ID和机构ID查看哪些用户拥有对应的操作权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "activityAuid", value = "资源编码code", example = "RECEIVE_TASK", paramType = "query", required = true),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "10020000000000", paramType = "query"),
            @ApiImplicitParam(name = "branchMode", value = "机构模式(MANAGER:管理机构,SALES:销售机构)", example = "MANAGER", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "node/permission/users")
    public ResultObject<List<String>> userWorkFlowGet(@RequestParam(required = true,value = "activityAuid") String activityAuid,
                                                         @RequestParam(required = false,value = "branchId") String branchId,
                                                         @RequestParam(required = false,value = "branchMode") String branchMode) {

        return usersWorkFlowBusinessService.queryWorkflowNodePermissionUsers(activityAuid, branchId,branchMode);
    }

}