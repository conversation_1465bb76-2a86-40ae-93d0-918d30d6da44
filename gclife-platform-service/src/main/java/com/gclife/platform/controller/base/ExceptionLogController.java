package com.gclife.platform.controller.base;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.core.jooq.tables.pojos.ExceptionLogPo;
import com.gclife.platform.model.request.ExceptionLogRequest;
import com.gclife.platform.model.request.OperationLogRequest;
import com.gclife.platform.service.business.ExceptionLogBusinessService;
import com.gclife.platform.service.business.OperationLogBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 异常日志记录
 * @author: chenjinrong
 * @create: 2021-08-26
 **/
@Controller
@RequestMapping("v1/base/log/")
public class ExceptionLogController extends BaseController {

    @Autowired
    ExceptionLogBusinessService exceptionLogBusinessService;
    @Autowired
    OperationLogBusinessService operationLogBusinessService;

    @ApiOperation(value = "错误日志记录", notes = "错误日志记录")
    @PostMapping(value = "/exception")
    public ResultObject saveExceptionLog(@RequestBody ExceptionLogRequest logRequest){
        exceptionLogBusinessService.saveLog(logRequest);
        return ResultObject.success();
    }

}
