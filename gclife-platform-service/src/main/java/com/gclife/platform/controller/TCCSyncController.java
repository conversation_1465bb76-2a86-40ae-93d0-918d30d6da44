package com.gclife.platform.controller;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.service.business.TccSyncBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Api(tags = "Users", description = "用户")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class TCCSyncController extends BaseController {

    @Autowired
    private TccSyncBusinessService tccSyncBusinessService;

    @PostMapping(value = "tcc/sync")
    ResultObject syncTcc() {
        return tccSyncBusinessService.syncTcc();
    }


}