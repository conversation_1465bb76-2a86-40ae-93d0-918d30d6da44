package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.api.BranchBaseApi;
import com.gclife.platform.form.branch.UserBranchRequest;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.vo.branch.BranchParentResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchSimpleResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 *
 * <AUTHOR>
 */


@RefreshScope
@Controller
public class BranchBaseController extends BaseController implements BranchBaseApi{

    @Autowired
    private BranchBaseBusinessService branchBaseBusinessService;

    @Override
    public ResultObject<BranchResponse> queryOneBranchById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.queryOneBranchById(branchId,this.getLanguage(this.getCurrentLoginUsers().getLanguage()));
    }

    @Override
    public ResultObject<BranchResponse> queryOneBranchAndManagerById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.queryOneBranchAndManagerById(branchId,this.getLanguage(getCurrentLoginUsers().getLanguage()));
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchByIds(@RequestBody List<String> branchIds) {
        return branchBaseBusinessService.queryBranchByIds(branchIds);
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.queryBranchTreeBranchListById(branchId);
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListByIds(@RequestBody List<String> branchIds) {
        return branchBaseBusinessService.queryBranchTreeBranchListByIds(branchIds);
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(@RequestParam("branchId") String branchId) {

        return branchBaseBusinessService.queryBranchTreeLeafListById(branchId);
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(@RequestBody List<String> branchIds) {
        return branchBaseBusinessService.queryBranchTreeLeafListByIds(branchIds);
    }


    @Override
    public ResultObject<List<BranchResponse>> queryBranchParentListById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.queryBranchParentListById(branchId);
    }


    @Override
    public ResultObject<List<BranchParentResponse>> queryBranchParentListByIds(@RequestBody List<String> branchIds) {
        return branchBaseBusinessService.queryBranchParentListByIds(branchIds);
    }


    @Override
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.queryBranchTreeById(branchId);
    }


    @Override
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeByIds(@RequestBody List<String> branchIds) {
        return branchBaseBusinessService.queryBranchTreeByIds(branchIds);
    }


    @Override
    public ResultObject<List<BranchTreeResponse>> querySalesBranchTreeById(@RequestParam("branchId") String branchId) {
        return branchBaseBusinessService.querySalesBranchTreeById(branchId);
    }


    @Override
    public ResultObject<List<BranchTreeResponse>> querySalesAllBranchTree() {
        return branchBaseBusinessService.querySalesAllBranchTree();
    }

    @Override
    public ResultObject<BranchResponse> queryOneUserBranch(@RequestParam(value = "userId") String userId) {
        //AssertUtils.isNotEmpty(userId)
        return branchBaseBusinessService.queryOneUserBranch(userId);
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserBranchTree(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserBranchTree(userId);
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserBranchTreeList(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserBranchTreeList(userId);
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeList(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserOptionBranchTreeList(userId);
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeListByCode(@RequestParam(value = "channelTypeCode") String channelTypeCode) {
        return branchBaseBusinessService.queryUserOptionBranchTreeListByCode(channelTypeCode);
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeLeaf(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserOptionBranchTreeLeaf(userId);
    }


    @Override
    public ResultObject<List<BranchResponse>> loadUserOptionFirstLevelBranch(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.loadUserOptionFirstLevelBranch(userId);
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTreeFiterNoLeafBranch(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserOptionBranchTreeFiterNoLeafBranch(userId);
    }


    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTree(@RequestParam(value = "userId") String userId) {
        return branchBaseBusinessService.queryUserOptionBranchTree(userId);
    }

    @Override
    public ResultObject<BaseResponse> saveUserOptionBranch(@RequestBody UserBranchRequest userRequestRequest) {
        return branchBaseBusinessService.saveUserOptionBranch(userRequestRequest);
    }


    @Override
    public ResultObject saveUserOptionBranch(@RequestBody List<UserBranchRequest> userBranchRequests) {
        return branchBaseBusinessService.saveUserBranchByAgentIds(userBranchRequests);
    }


    @Override
    public ResultObject<List<BranchSimpleResponse>> queryLevelBranch(@RequestParam(value = "userId", required = true) String userId, @RequestParam(value = "levelNum", required = true) Long levelNum) {
        return branchBaseBusinessService.queryLevelBranch(userId, levelNum);
    }


    @Override
    public ResultObject queryBranchBelongLevel(@RequestParam(value = "branchId", required = true) String branchId) {
        return branchBaseBusinessService.queryBranchBelongLevel(branchId);
    }


    @Override
    public ResultObject queryBranchDefaultChannel(@RequestParam(value = "branchId", required = false) String branchId) {
        return branchBaseBusinessService.queryBranchDefaultChannel(branchId);
    }

    @Override
    public ResultObject<BranchResponse> queryOneTopBranchById(@RequestParam("branchId")String branchId) {
        return branchBaseBusinessService.queryOneTopBranchById(branchId);
    }

}