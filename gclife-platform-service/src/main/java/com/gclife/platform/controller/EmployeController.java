package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.request.EmployeeQueryRequest;
import com.gclife.platform.vo.branch.BranchTreeResponse;
import com.gclife.platform.model.response.ChannelsResponse;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.platform.service.business.BranchBusinessService;
import com.gclife.platform.service.business.EmployeeBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 上午10:02
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 职员信息控制器
 * <AUTHOR>
 */

@Api(tags = "Employe", description = "职员API")
@RefreshScope
@RestController
@RequestMapping("v1")
public class EmployeController extends BaseController {

    @Autowired
    EmployeeBusinessService employeeBusinessService;

    @Autowired
    BranchBusinessService branchBusinessService;

    @ApiOperation(value = "user/1/channels", notes = "查询职员管理的渠道集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/{id}/channels")
    public ResultObject<List<ChannelsResponse>> userChannelsGet(@ApiParam(value = "用户ID",name = "id",example = "1")
                                                                @PathVariable(value = "id")String id) {
         /*TODO:
         查询职员的管理渠道集合
         1.职员所属机构
         2.所属机构配置管理机构
         3.统计管理机构的渠道类型
        */
        Users users  = this.getCurrentLoginUsers();
        return  employeeBusinessService.loadUsersManagerChannels(users,id);
    }





    @ApiOperation(value = "user/1/employee", notes = "查询职员信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/{id}/employee")
    public ResultObject<EmployeResponse> employeGet(@ApiParam(value = "用户ID",name = "id",example = "1")
                                                                @PathVariable(value = "id")String id) {
         /*TODO:
         查询用户职员信息
         用户ID关联职员表
        */
        Users users  = this.getCurrentLoginUsers();
        return  employeeBusinessService.loadEmployeByUserId(users,id);
    }




    @ApiOperation(value = "user/employee", notes = "查询职员列表信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "user/employees"   )
    public ResultObject<List<EmployeResponse>> employeGet(@RequestBody EmployeeQueryRequest employeeQueryRequest) {
         /*TODO:
         查询用户职员信息
         用户ID关联职员表
         支持 其他微服务集合查询
        */
        Users users  = this.getCurrentLoginUsers();
        return  employeeBusinessService.loadEmployeByUserIds(users,employeeQueryRequest);
    }



    @ApiOperation(value = "user/1/employee/sale/branchs", notes = "查询当前用户管理的销售机构列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/{id}/employee/sale/branchs")
    public ResultObject<List<BranchTreeResponse>> employeeSaleBranchsGet(@ApiParam(defaultValue = "1", value = "用户ID",name = "id",example = "1")
                                                                                 @PathVariable(value = "id")String id) {
        return  branchBusinessService.loadUserBranchTreeLeaf2(this.getCurrentLoginUsers());
    }

}