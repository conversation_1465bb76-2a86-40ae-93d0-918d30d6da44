package com.gclife.platform.controller.middle;

import com.gclife.common.annotation.ErrorTip;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.model.request.AccountAuditRequest;
import com.gclife.platform.model.request.AccountQueryRequest;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountQueryResponse;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.service.business.AccountBusinessService;
import io.swagger.annotations.*;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息
 */
@Api(tags = "Account", description = "账户信息API")
@RefreshScope
@RestController
@RequestMapping("v1/middle/")
public class AccountMiddleController extends BaseController {

    @Autowired
    private AccountBusinessService accountBusinessService;

    @ApiOperation(value = "accounts/audit", notes = "获取待审核的银行卡列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "accounts/audit")
    @ErrorTip(classType = PlatformErrorConfigEnum.class, key = "PLATFORM_QUERY_AUDIT_ACCOUNT_ERROR")
    public ResultObject<BasePageResponse<AccountQueryResponse>> queryAccountAuditList(@RequestBody AccountQueryRequest accountQueryRequest) {
        return accountBusinessService.queryAccountAuditList(getCurrentLoginUsers(),accountQueryRequest);
    }

    @ApiOperation(value = "audit/banks", notes = "获取审核的银行卡列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "audit/banks")
    public ResultObject<List<AccountResponse>> queryAccountAuditBanksList(@RequestParam(name = "userId") String userId) {
        return accountBusinessService.queryAccountAuditBanksList(userId);
    }

    @ApiOperation(value = "audit/banks", notes = "获取审核的银行卡列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "audit/banks/new")
    public ResultObject<List<AccountResponse>> queryAccountAuditBanksListNew(@RequestParam(name = "userId") String userId) {
        return accountBusinessService.queryAccountAuditBanksListNew(userId);
    }

    @ApiOperation(value = "账户审核", notes = "账户审核")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "account/audit")
    @ErrorTip(classType = PlatformErrorConfigEnum.class, key = "PLATFORM_ACCOUNT_AUDIT_ERROR")
    public ResultObject accountAudit(
            @RequestBody AccountAuditRequest accountAuditRequest
    ) {
        return accountBusinessService.accountAudit(accountAuditRequest);
    }


    @ApiOperation(value = "结论信息列表", notes = "结论信息列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "account/audit/exceptions")
    @ErrorTip(classType = PlatformErrorConfigEnum.class, key = "PLATFORM_QUERY_AUDIT_ACCOUNT_EXCEPTION_ERROR")
    public ResultObject<List<InternationalDo>> queryAuditExceptions() {
        return accountBusinessService.queryAuditExceptions(this.getCurrentLoginUsers());
    }
}