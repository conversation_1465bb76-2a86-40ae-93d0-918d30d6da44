package com.gclife.platform.controller.middle;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.service.business.base.UsersBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 *
 * <AUTHOR>
 */

@Api(tags = "用户基础接口(2018)", description = "用户基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/middle/")
public class UsersMiddleController extends BaseController {

    @Autowired
    private UsersBaseBusinessService usersBaseBusinessService;

    @ApiOperation(value = "根据用户账号或姓名查询用户集合", notes = "根据用户手机号码查询用户集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名字", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "users/name")
    public ResultObject<List<UserResponse>> queryUsersPoByName(String name) {
        return usersBaseBusinessService.queryUsersPoByName(name, getCurrentLoginUsers());
    }
}
