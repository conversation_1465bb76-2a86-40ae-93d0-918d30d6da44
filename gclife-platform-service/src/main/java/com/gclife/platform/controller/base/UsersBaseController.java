package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.UserDingRelationResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.UserWeixinRelationResponse;
import com.gclife.platform.service.business.base.UsersBaseBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 *
 * <AUTHOR>
 */

@Api(tags = "用户基础接口(2018)", description = "用户基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class UsersBaseController extends BaseController {

    @Autowired
    private UsersBaseBusinessService usersBaseBusinessService;

    @ApiOperation(value = "根据用户Id查询用户信息", notes = "根据用户Id查询用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user")
    public ResultObject<UserResponse> queryOneUsersPoById(@RequestParam("userId") String userId) {
        return usersBaseBusinessService.queryOneUsersPoById(userId);
    }

    @ApiOperation(value = "根据用户ids查询用户集合", notes = "根据用户ids查询用户集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "users")
    public ResultObject<List<UserResponse>> queryUsersPoByIds(@RequestBody List<String> userIds) {
        return usersBaseBusinessService.queryUsersPoByIds(userIds);
    }

    @ApiOperation(value = "根据用户手机号码查询用户集合", notes = "根据用户手机号码查询用户集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "users/mobile")
    public ResultObject<List<UserResponse>> queryUsersPoByMobiles(@RequestBody String... mobiles) {
        return usersBaseBusinessService.queryUsersPoByMobiles(mobiles);
    }

    @ApiOperation(value = "根据openId查询用户信息", notes = "根据openId查询用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/wechat")
    public ResultObject<UserWeixinRelationResponse> queryOneUsersWechatById(@RequestParam("openId") String openId) {
        return usersBaseBusinessService.queryOneUsersWechatById(openId);
    }

    @ApiOperation(value = "根据userId查询用户信息", notes = "根据userId查询用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/ding")
    public ResultObject<UserDingRelationResponse> queryOneUsersDingById(@RequestParam("userId") String userId) {
        return usersBaseBusinessService.queryOneUsersDingById(userId);
    }

    @ApiOperation(value = "模糊查询用户(不止内勤)", notes = "模糊查询用户(不止内勤)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "users/keyword")
    public ResultObject<List<UserResponse>> queryUsersPoByKeyword(String keyword) {
        return usersBaseBusinessService.queryUsersPoByKeyword(keyword, getCurrentLoginUsers());
    }

    @ApiOperation(value = "赋予代理人团险权限", notes = "赋予代理人团险权限")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "granted/group/permission")
    public ResultObject grantedGroupPermission(@RequestParam("userId") String userId) {
        return usersBaseBusinessService.grantedGroupPermission(userId);
    }
}