package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.request.OperationLogRequest;
import com.gclife.platform.model.response.OperationLogResponse;
import com.gclife.platform.model.response.OperationRoleResponse;
import com.gclife.platform.model.response.OperationUserResponse;
import com.gclife.platform.service.business.OperationLogBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@Api(tags = "操作日志", description = "操作日志")
@RestController
@RequestMapping("v1")
public class OperationLogController extends BaseController {

    @Autowired
    private OperationLogBusinessService operationLogBusinessService;

    @ApiOperation(value = "获取操作日志列表", notes = "获取操作日志列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "list/operation/log")
    public ResultObject<BasePageResponse<OperationLogResponse>> listOperationLog(@RequestBody OperationLogRequest operationLogRequest) {
        return operationLogBusinessService.listOperationLog(operationLogRequest);
    }

    @ApiOperation(value = "获取操作人", notes = "获取操作人")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list/operation/user")
    public ResultObject<List<OperationUserResponse>> listOperationUser(String keyword) {
        return operationLogBusinessService.listOperationUser(keyword);
    }

    @ApiOperation(value = "获取报表操作日志", notes = "获取报表操作日志")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "list/report/operation/log")
    public ResultObject<List<OperationLogResponse>> listReportOperationLog(@RequestBody OperationLogRequest operationLogRequest) {
        return operationLogBusinessService.listReportOperationLog(operationLogRequest);
    }

    @ApiOperation(value = "获取所有操作角色", notes = "获取所有操作角色")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list/operation/role")
    public ResultObject<List<OperationRoleResponse>> listOperationRole() {
        return operationLogBusinessService.listOperationRole(getCurrentLoginUsers());
    }
}
