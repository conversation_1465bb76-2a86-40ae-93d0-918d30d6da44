package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.BranchLevelResponse;
import com.gclife.platform.service.business.base.BranchLevelBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 * <AUTHOR>
 */

@Api(tags = "机构基础接口(2018)", description = "机构基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class BranchLevelBaseController extends BaseController {

    @Autowired
    private BranchLevelBaseBusinessService branchLevelBaseBusinessService;

    @ApiOperation(value = "根据机构ID查询机构层级列表", notes = "根据机构ID查询机构层级列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", example = "0000", paramType = "query", required = false),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "PRO8888888888888", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "branch/level/list")
    public ResultObject<List<BranchLevelResponse>> queryBranchLevelByBranchId(@RequestParam("userId") String userId,@RequestParam("branchId") String branchId) {
        return  branchLevelBaseBusinessService.queryBranchLevel(userId,branchId);
    }
}