package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.request.TerminologyRequest;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.InternationalBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:55
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 国籍花信息接口
 * <AUTHOR>
 */
@Api(tags = "International", description = "国际化接口")
@RefreshScope
@RestController
@RequestMapping("v1/terminology")
public class InternationalController extends BaseController{

    @Autowired
    InternationalBusinessService internationalBusinessService;

    @ApiOperation(value = "language", notes = "国际化通用接口,获取单个code的国际化语言,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
                    @ApiImplicitParam(name = "key",value = "键值",example = "APPLY_WORK_FLOW_STATUS_NEW_TASK",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "language")
    public ResultObject<SyscodeResponse> languageGet(@RequestParam String type, @RequestParam String key) {
         /*TODO:
         传入术语类型，术语key，需要的语言，返回国际化术语对象
        */
        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadTerminology(users,type,key,null);
    }




    @ApiOperation(value = "languages", notes = "国际化通用接口,获取某个类型的国际化语言列表,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "languages")
    public ResultObject<List<SyscodeResponse>> languagesGet(@RequestParam String type) {
         /*TODO:
         传入术语类型，需要的语言，返回国际化术语集合
        */
        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadTerminologys(users,type,null);
    }





    @ApiOperation(value = "国际化通用接口", notes = "国际化通用接口,获取单个code的国际化语言,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
                    @ApiImplicitParam(name = "key",value = "键值",example = "APPLY_WORK_FLOW_STATUS_NEW_TASK",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "assign/language")
    public ResultObject<SyscodeResponse> languageGet(@RequestParam String type, @RequestParam String key,@RequestParam String language) {
         /*TODO:
         传入术语类型，术语key，需要的语言，返回国际化术语对象
        */
        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadTerminology(users,type,key,language);
    }




    @ApiOperation(value = "国际化通用接口", notes = "国际化通用接口,获取某个类型的国际化语言列表,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "assign/languages")
    public ResultObject<List<SyscodeResponse>> languagesGet(@RequestParam String type,@RequestParam String language) {
         /*TODO:
         传入术语类型，需要的语言，返回国际化术语集合
        */
        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadTerminologys(users,type,language);
    }



    @ApiOperation(value = "国际化通用接口", notes = "国际化通用接口,获取某个类型多个编码集合国际化语言列表,language不传会使用默认的语言")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "languages")
    public ResultObject<List<SyscodeResponse>> languagesGet(@RequestBody TerminologyRequest terminologyRequest) {
         /*TODO:
         传入术语类型，需要的语言，返回国际化术语集合
        */
        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadTerminologys(users,terminologyRequest);
    }


    @ApiOperation(value = "国际化通用接口", notes = "国际化通用接口,获取某个类型的国际化语言,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "international/type")
    public ResultObject<List<SyscodeResponse>> internationalTextGet(@RequestParam String type ) {

        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.internationalTextGet(users,type,null);
    }


    @ApiOperation(value = "国际化接口", notes = "国际化接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "international/type")
    public ResultObject<List<SyscodeResponse>> internationalsTextGet(@RequestBody TerminologyRequest terminologyRequest) {

        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.loadInternationalListByCodes(users,terminologyRequest);
    }




    @ApiOperation(value = "international/key", notes = "国际化通用接口,获取某个类型 的 某个Key 获取 国际化语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "ERROR",paramType = "query",required = true),
                    @ApiImplicitParam(name = "key",value = "文本Key",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "international/key")
    public ResultObject<SyscodeResponse> internationalTextGetOne(@RequestParam String key,@RequestParam String type ) {

        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.internationalTextGetOne(users,key,type,null);
    }


    @ApiOperation(value = "国际化通用接口", notes = "国际化通用接口,获取某个类型的国际化语言,language不传会使用默认的语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "assign/international/type")
    public ResultObject<List<SyscodeResponse>> internationalTextGet(@RequestParam String type ,@RequestParam String language) {

        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.internationalTextGet(users,type,language);
    }


    @ApiOperation(value = "international/key", notes = "国际化通用接口,获取某个类型 的 某个Key 获取 国际化语言")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type",value = "类型",example = "ERROR",paramType = "query",required = true),
                    @ApiImplicitParam(name = "key",value = "文本Key",example = "APPLY_WORK_FLOW_STATUS",paramType = "query",required = true),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "assign/international/key")
    public ResultObject<SyscodeResponse> internationalTextGetOne(@RequestParam String key,@RequestParam String type,@RequestParam String language ) {

        Users users  = this.getCurrentLoginUsers();
        return  internationalBusinessService.internationalTextGetOne(users,key,type,language);
    }

}