package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.platform.service.business.AreaBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:区域地址
 */
@Api(tags = "Area", description = "区域地址API")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class AreaController extends BaseController {
    @Autowired
    AreaBusinessService areaBusinessService;


    @ApiOperation(value = "area", notes = "获取区域地址")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area")
    public ResultObject<List<AreaResponse>> areaGet(String areaId) {
        return areaBusinessService.getAreaById(areaId, this.getCurrentLoginUsers(),this.getAppRequestHandler());
    }

    @ApiOperation(value = "area/tree", notes = "根据叶子节点ID获取区域地址树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/tree")
    public ResultObject<List<AreaTreeResponse>> areaTreeGet(String areaId, String sortType) {
        return areaBusinessService.getAreaTreeById(areaId, this.getCurrentLoginUsers(), sortType);
    }

    @ApiOperation(value = "根据叶子节点ID批量获取区域地址树", notes = "根据叶子节点ID批量获取区域地址树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "area/tree")
    public ResultObject<Map<String, List<AreaTreeResponse>>> areaTreeGetBatch(@RequestBody List<String> areaIds) {
        return areaBusinessService.getAreaTreeByIds(areaIds, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "area/name/{areaId}", notes = "根据叶子节点ID获取父类区域地址名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/name/{areaId}")
    public ResultObject<AreaNameResponse> areaNameGet(@PathVariable String areaId, String language, String sortType) {
        return areaBusinessService.getAreaNameById(areaId, this.getCurrentLoginUsers(), this.getLanguage(language), sortType);
    }

    @ApiOperation(value = "area/name/{areaId}", notes = "网销根据叶子节点ID获取父类区域地址名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/name/online/{areaId}")
    public ResultObject<AreaNameResponse> areaNameGetOnline(@PathVariable String areaId) {
        return areaBusinessService.getAreaNameByIdOnline(areaId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "area/name/{areaId}", notes = "网销根据叶子节点ID获取父类区域地址名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/name/online/normal/{areaId}")
    public ResultObject<AreaNameResponse> areaNameGetOnlineNormal(@PathVariable String areaId) {
        return areaBusinessService.getAreaNameByIdOnlineNormal(areaId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "area/name/{areaId}", notes = "根据叶子节点ID获取父类区域地址名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/name/get/{areaId}")
    public ResultObject<AreaNameResponse> areaNameGetNew(@PathVariable String areaId, String language) {
        return areaBusinessService.getAreaNameByIdNew(areaId, this.getCurrentLoginUsers(), this.getLanguage(language));
    }

    @ApiOperation(value = "根据叶子节点ID批量获取父类区域地址名称", notes = "根据叶子节点ID批量获取父类区域地址名称")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "area/name")
    public ResultObject<List<AreaNameResponse>> areaNameGetBatch(@RequestBody List<String> areaIds, String language, String sortType) {
        return areaBusinessService.areaNameGetBatch(areaIds, this.getCurrentLoginUsers(), this.getLanguage(language), sortType);
    }

    @ApiOperation(value = "area/info", notes = "根据ID获取区域地址信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaId", value = "地址id", example = "654324", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "area/info")
    public ResultObject<AreaResponse> areaInfoGet(String areaId) {
        return areaBusinessService.getAreaInfo(areaId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "area/info/list", notes = "根据ID集合获取区域地址信息集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaId", value = "地址id", example = "654324", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "area/info/list")
    public ResultObject<List<AreaResponse>> areaInfoGet(@RequestBody List<String> areaIds) {
        return areaBusinessService.getAreaList(areaIds, this.getCurrentLoginUsers());
    }
}