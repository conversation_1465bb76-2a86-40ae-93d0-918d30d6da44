package com.gclife.platform.controller.app.manage;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.service.business.app.manage.UserManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 18-8-1
 * \* Time: 下午1:58
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Api(tags = "app用户管理", description = "app用户管理")
@RefreshScope
@RestController
@RequestMapping("v1/app/user/manage")
public class UserManageController extends BaseController {


    @Autowired
    private UserManageService userManageService;

    @ApiOperation(value = "启用禁用用户", notes = "启用禁用用户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "/enabled")
    public ResultObject updateUserEnabled(@RequestParam(value = "updateUserId") String updateUserId, @RequestParam(value = "enabled") String enabled) {
        return userManageService.updateUserEnabled(updateUserId, enabled, this.getCurrentLoginUsers());
    }


}
