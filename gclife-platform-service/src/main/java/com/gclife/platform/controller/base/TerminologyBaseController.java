package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.api.TerminologyBaseApi;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.vo.StatusClassResponse;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:29
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构信息接口
 * <AUTHOR>
 */

@Api(tags = "术语基础接口(2018)", description = "术语基础接口(2018)")
@RefreshScope
@RestController
@Slf4j
public class TerminologyBaseController extends BaseController implements TerminologyBaseApi {

    @Autowired
    private TerminologyBaseBusinessService terminologyBaseBusinessService;

    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<List<SyscodeResponse>> queryTerminology(@RequestParam("codeType") String codeType) {
        return  terminologyBaseBusinessService.querySyscode(codeType);
    }


    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<SyscodeResponse> queryOneTerminology(@RequestParam("codeType") String codeType,@RequestParam("codeKey") String codeKey) {
        return  terminologyBaseBusinessService.queryOneSyscode(codeType,codeKey);
    }



    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<List<SyscodeResponse>> queryTerminologyInternation(@RequestParam("codeType") String codeType,@RequestParam(value = "language",required = false) String language) {
        return  terminologyBaseBusinessService.queryInternationSyscode(codeType,this.getLanguage(language));
    }


    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<List<SyscodeResponse>> queryTerminologyInternation(@RequestBody SyscodeForm syscodeReqFc) {
        syscodeReqFc.setLang(this.getLanguage(syscodeReqFc.getLang()));
        return  terminologyBaseBusinessService.queryInternationSyscodeByCodeKeys(syscodeReqFc);
    }


    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<SyscodeResponse> queryOneTerminologyInternation(@RequestParam("codeType") String codeType,@RequestParam("codeKey") String codeKey,@RequestParam(value = "language",required = false) String language) {
        return  terminologyBaseBusinessService.queryOneInternationSyscode(codeType,codeKey,this.getLanguage(language));
    }


    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<List<StatusClassResponse>> ClientsSysCodeStatusGet(@RequestParam String type) {
         /*TODO:
         传入术语类型，需要的语言，返回术语展示集合
        */
        Users users  = this.getCurrentLoginUsers();
        return  terminologyBaseBusinessService.queryStatusClassTerminologys(users,type);
    }



    @Override
    @ApiVersion(1)
    @AutoCheckPermissions
    public ResultObject<List<SyscodeResponse>> SysCodeStatusGet(@RequestParam String type,@RequestParam String statusClassCode) {
         /*TODO:
         传入术语类型，需要的语言，返回术语展示集合
        */
        Users users  = this.getCurrentLoginUsers();
        return  terminologyBaseBusinessService.querySyscodeTerminologys(users,type,statusClassCode);
    }

}