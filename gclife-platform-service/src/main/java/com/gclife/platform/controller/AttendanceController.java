package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.AttendanceResponse;
import com.gclife.platform.model.response.AttendancesMessageResponse;
import com.gclife.platform.service.business.AttendanceService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 17-11-3
 * description:
 */
@Api(tags = "Attendance", description = "场景签到")
@RefreshScope
@RestController
@RequestMapping("v1/attendance/")
public class AttendanceController extends BaseController {

    @Autowired
    private AttendanceService attendanceService;

    @ApiOperation(value = "attendance/save", notes = "签到")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "attendance/save")
    public ResultObject attendanceSave(){

        return attendanceService.attendanceSave(this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "attendance", notes = "获取当天是否签到  签到返回当天签到信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "attendance")
    public ResultObject<AttendanceResponse> getAttendance(){

        return attendanceService.getAttendance(this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "attendances/message", notes = "获取当月的打卡信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "attendances/message")
    public ResultObject<AttendancesMessageResponse> getAttendancesMessage(){

        return attendanceService.getAttendancesMessage(this.getCurrentLoginUsers());
    }


}