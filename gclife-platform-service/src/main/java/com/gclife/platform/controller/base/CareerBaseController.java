package com.gclife.platform.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.CareerBaseTreeResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.CareerTreeResponse;
import com.gclife.platform.service.business.CareerBusinessService;
import com.gclife.platform.service.business.base.CareerBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:获取职业代码和职业类别
 */
@Api(tags = "职业基础接口(2018)", description = "职业基础接口(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class CareerBaseController extends BaseController {
    @Autowired
    private CareerBaseBusinessService careerBaseBusinessService;

    @ApiOperation(value = "获取保险公司某职业下的(第一层职业类型)", notes = "获取保险公司某职业下的(第一层职业类型)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerId", value = "职业ID", example = "0000", paramType = "query", required = false),
            @ApiImplicitParam(name = "providerId", value = "保险公司ID", example = "PRO8888888888888", paramType = "query", required = false)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/first/level/childs")
    public ResultObject<List<CareerResponse>> queryCareerChilds(@RequestParam(value = "careerId", required = false) String careerId, @RequestParam(value = "providerId", required = false) String providerId) {
        return careerBaseBusinessService.queryCareerChilds(careerId, providerId);
    }


    @ApiOperation(value = "从底层往上找职级列表", notes = "从底层往上找职级列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/tree/parent/list")
    public ResultObject<List<CareerResponse>> careerParentGet(@RequestParam(value = "careerId", required = false) String careerId) {
        return careerBaseBusinessService.queryCareerParentTreeList(careerId);
    }


    @ApiOperation(value = "根据Id或者code获取职业信息", notes = "根据Id或者code获取职业信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerId", value = "职业id", example = "职业id", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career")
    public ResultObject<CareerResponse> queryOneCareerPo(@RequestParam(value = "careerId", required = true) String careerId) {
        return careerBaseBusinessService.queryOneCareerPo(careerId);
    }

    @ApiOperation(value = "根据职业ids或者职业codes获取职业详情", notes = "根据职业ids或者职业codes获取职业详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "careerIds", value = "职业ids或者职业codes", example = "职业ids或者职业codes", paramType = "query", required = true),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "career")
    public ResultObject<List<CareerResponse>> queryCareerPoByIdsOrCodes(@RequestBody List<String> careerIds) {
        return careerBaseBusinessService.queryCareerPoByIdsOrCodes(careerIds);
    }


    @ApiOperation(value = "查询职业树(所有)", notes = "查询职业树(所有)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "career/tree/all")
    public ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(@RequestParam("providerId") String providerId) {
        return careerBaseBusinessService.queryCareerTreeById(providerId);
    }

}