package com.gclife.platform.controller;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.request.UserThirdPartyRequest;
import com.gclife.platform.model.response.UserBaseResponse;
import com.gclife.platform.model.response.UserThirdPartyResponse;
import com.gclife.platform.service.business.UsersThirdPartyBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
@Api(tags = "第三方平台用户", value = "第三方平台用户")
@Controller
@RequestMapping("v1/user/third/party/")
public class UsersThirdPartyController extends BaseController {
    @Autowired
    private UsersThirdPartyBusinessService thirdPartyBusinessService;

    @ApiOperation(value = "查询第三方用户信息", notes = "查询第三方用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "user/info")
    public ResultObject<UserBaseResponse> queryUserInfo(@RequestParam("openId") String openId) {
        return thirdPartyBusinessService.queryUserInfo(openId);
    }

    @ApiOperation(value = "保存第三方用户信息", notes = "保存第三方用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "save")
    public ResultObject<UserBaseResponse> saveInfo(@RequestBody UserThirdPartyRequest userThirdPartyRequest) {
        return thirdPartyBusinessService.saveInfo(userThirdPartyRequest, getAppRequestHandler());
    }

    @ApiOperation(value = "查询第三方信息", notes = "查询第三方信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "info")
    public ResultObject<List<UserThirdPartyResponse>> listThirdPartyInfo(@RequestParam("userId") String userId) {
        return thirdPartyBusinessService.listThirdPartyInfo(userId);
    }

    @ApiOperation(value = "第三方信息绑定", notes = "第三方信息绑定")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "bind")
    public ResultObject bind(@RequestBody UserThirdPartyRequest userThirdPartyRequest) {
        return thirdPartyBusinessService.bind(userThirdPartyRequest);
    }

    @ApiOperation(value = "第三方信息解绑", notes = "第三方信息解绑")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "unbind")
    public ResultObject unbind(@RequestParam(name = "userId") String userId,
                               @RequestParam(name = "thirdPartyCode") String thirdPartyCode) {
        return thirdPartyBusinessService.unbind(userId, thirdPartyCode);
    }

}