package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.request.SharedLinkSaveRequest;
import com.gclife.platform.service.business.SharedLinkBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 17-12-15
 * description: 链接分享控制器
 */
@Api(tags = "SharedLink", description = "链接分享")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class SharedLinkController extends BaseController {

    @Autowired
    SharedLinkBusinessService sharedLinkBusinessService;

    @ApiOperation(value = "sharedLink/signature", notes = "根据signature获取链接地址")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "signature", value = "签名", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions()
    @GetMapping(value = "sharedLink/signature")
    public ResultObject retrieveSharedLinkBySignature(String signature) {
        return sharedLinkBusinessService.retriveSharedLinkBySignature(signature);
    }

    @ApiOperation(value = "sharedLink/save", notes = "保存链接")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sharedLinkSaveRequest", value = "共享链接保存请求", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions()
    @PostMapping(value = "sharedLink/save")
    public ResultObject saveSharedLink(@RequestBody SharedLinkSaveRequest sharedLinkSaveRequest) {
        return sharedLinkBusinessService.saveSharedLink(getCurrentLoginUsers().getUserId(),sharedLinkSaveRequest);
    }


}
