package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.platform.model.response.UserLoginCheckResponse;
import com.gclife.platform.model.response.UserLoginLogResponse;
import com.gclife.platform.service.business.UserLoginLogBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 17-12-13
 * description:用户登录日志表控制层
 */
@Api(tags = "用户登录日志", description = "用户登录日志")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class UserLoginLogController extends BaseController {
    @Autowired
    private UserLoginLogBusinessService userLoginLogBusinessService;

    @ApiOperation(value = "保存用户登录日志", notes = "保存用户登录日志")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "user/login/success/log")
    public ResultObject userLoginLogPost() {
        return userLoginLogBusinessService.postUserLoginLog(this.getAppRequestHandler(), this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "获取用户最新登录信息", notes = "获取用户最新登录信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "user/app/newest/login/log")
    public ResultObject<UserLoginLogResponse> getUserNewestLoginLog(String userId) {
        return userLoginLogBusinessService.loadUserAppNewestLoginLog(userId);
    }

    @ApiOperation(value = "user/app/login/check", notes = "用户登录信息校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "countryCode", value = "手机区号", example = "86", paramType = "query", required = true),
            @ApiImplicitParam(name = "mobile", value = "手机号", example = "***********", paramType = "query", required = true),
            @ApiImplicitParam(name = "deviceChannel", value = "设备渠道", example = "gclife_agent_app", paramType = "query", required = true),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @GetMapping(value = "user/app/login/check")
    public ResultObject<UserLoginCheckResponse> appUserLoginCheck(String countryCode, String mobile, String deviceChannel) {
        return userLoginLogBusinessService.appUserLoginCheck(countryCode, mobile, deviceChannel);
    }


    @ApiOperation(value = "用户退出登录", notes = "用户退出登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "app/user/logout")
    public ResultObject userLogout() {
        userLoginLogBusinessService.postUserLogout(this.getAppRequestHandler(), this.getCurrentLoginUsers());
        return new ResultObject();
    }

    @ApiOperation(value = "指定业务员用户退出登录", notes = "指定业务员用户退出登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "app/user/agent/logout")
    public ResultObject userAgentLogout(@RequestParam (value = "userId") String userId) {
        userLoginLogBusinessService.postUserAgentLogout(this.getAppRequestHandler(), this.getCurrentLoginUsers(),userId);
        return new ResultObject();
    }

    @ApiOperation(value = "短暂业务员用户登录校验", notes = "短暂业务员用户登录校验")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "user/valid/check")
    public ResultObject userValidCheck(String userName) {
        return userLoginLogBusinessService.userValidCheck(userName);
    }

}
