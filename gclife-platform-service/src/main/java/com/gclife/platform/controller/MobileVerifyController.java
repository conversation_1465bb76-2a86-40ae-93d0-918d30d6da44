package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.service.business.MobileVerifyBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:用户注册的手机号验证
 */
@Api(tags = "MobileVerify", description = "手机号验证")
@RefreshScope
@RestController
@RequestMapping("v1")
public class MobileVerifyController extends BaseController {

    @Autowired
    private MobileVerifyBusinessService mobileVerifyBusinessService;

    @ApiOperation(value = "mobile/verify", notes = "用户注册的手机号验证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "countryCode", value = "手机区号", example = "86", paramType = "query", required = true),
            @ApiImplicitParam(name = "mobile", value = "手机号", example = "***********", paramType = "query", required = true),
            @ApiImplicitParam(name = "deviceChannel", value = "设备渠道", example = "gclife_agent_app", paramType = "query", required = true),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "mobile/verify")
    public ResultObject mobileVerifyGet(
            String countryCode, String mobile, String deviceChannel
    ) {
        return mobileVerifyBusinessService.getMobileVerify(countryCode, mobile, deviceChannel);
    }

    @ApiOperation(value = "recommend/mobile", notes = "用户注册时推荐人手机号验证")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "推荐人手机号", example = "***********", paramType = "query", required = true),
            @ApiImplicitParam(name = "deviceChannel", value = "设备渠道", example = "gclife_agent_app", paramType = "query", required = true),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "recommend/mobile")
    public ResultObject mobileVerifyGet(String mobile, String deviceChannel) {
        return mobileVerifyBusinessService.getRecommendMobile(mobile, deviceChannel);
    }


}