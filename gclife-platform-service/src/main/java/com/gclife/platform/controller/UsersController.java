package com.gclife.platform.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.SaveUserRequest;
import com.gclife.platform.model.request.SmsVerifyCodeCheckRequest;
import com.gclife.platform.model.request.UserInfoRequest;
import com.gclife.platform.model.request.UserLoginPasswordResetRequest;
import com.gclife.platform.model.response.SaveUserResponse;
import com.gclife.platform.model.response.UserBaseResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.service.business.UsersBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Api(tags = "Users", description = "用户")
@RefreshScope
@RestController
@RequestMapping("v1/user/")
public class UsersController extends BaseController {
    @Autowired
    private UsersBusinessService usersBusinessService;


    @ApiOperation(value = "根据当前登录用户信息", notes = "根据当前登录用户ID获取用户详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", example = "1", paramType = "query", required = true),
    })
    @GetMapping(value = "info")
    public ResultObject getUsersByUserId(String userId) {
        return usersBusinessService.getUserById(userId);
    }


    @ApiOperation(value = "mobile", notes = "根据手机号码获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "手机号码", example = "***********", paramType = "query", required = true),
            @ApiImplicitParam(name = "deviceChannel", value = "设备渠道", example = "gclife_agent_app", paramType = "query", required = true),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "mobile")
    public ResultObject<UsersBo> usersByMobileGet(String mobile, String deviceChannel) {
        return usersBusinessService.getUserByMobile(mobile, deviceChannel);
    }

    @ApiOperation(value = "info/edit", notes = "修改用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "info/edit")
    public ResultObject userInfoPut(@RequestBody UserInfoRequest userInfoRequest) {
        return usersBusinessService.putUserInfo(this.getCurrentLoginUsers().getUserId(), userInfoRequest, this.getAppRequestHandler());
    }

    @ApiOperation(value = "info/edit", notes = "无需登录 修改用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "base/info/edit")
    public ResultObject baseUserInfoPut(@RequestBody UserInfoRequest userInfoRequest, @RequestParam String userId, @RequestParam String deviceChannel) {
        return usersBusinessService.baseUserInfoPut(userInfoRequest, userId, deviceChannel);
    }

    @ApiOperation(value = "users", notes = "根据代理人ids查询代理人用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @PostMapping(value = "users")
    public ResultObject<List<UserResponse>> agentsPost(@RequestBody List<String> userIds) {
        /*
          根据代理人编码集合，获取代理人集合信息，支持其他微服务的调用
          由于编码集合数据可能会很大，因此采用post方式提交
        */
        return usersBusinessService.postAgents(userIds);
    }


    @ApiOperation(value = "查询用户业务信息", notes = "查询用户业务信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @GetMapping(value = "business/detail")
    public ResultObject<UserResponse> agentsPost(String userId) {
        return usersBusinessService.loadBusinessUserDetailById(userId);
    }

    @ApiOperation(value = "查询用户业务详细信息", notes = "查询用户业务详细信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @PostMapping(value = "info/detail")
    public ResultObject<List<UserResponse>> userInfoDetail(@RequestBody List<String> userIds) {
        return usersBusinessService.userInfoDetailGet(userIds);
    }

    @ApiOperation(value = "users", notes = "根据用户userId回滚注册相关用户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", example = "***********", paramType = "query", required = true)
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @DeleteMapping(value = "rollback")
    public ResultObject userRollback(String userId) {
        /*
          根据用户id回滚用户注册信息
        */
        return usersBusinessService.rollbackRegister(userId);
    }

    @ApiOperation(value = "agent/save/users", notes = "创建用户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @PostMapping(value = "agent/save/users")
    public ResultObject<List<SaveUserResponse>> agentSaveUsers(@RequestBody List<SaveUserRequest> userRequestList) {
        return usersBusinessService.agentSaveUsers(userRequestList, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "change/userpwd", notes = "修改用户密码")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "change/userpwd")
    public ResultObject changeUserPwd(@RequestBody UserLoginPasswordResetRequest userLoginPasswordResetRequest) {
        return usersBusinessService.changeUserPwd(userLoginPasswordResetRequest);
    }

    @ApiOperation(value = "check/userpwd", notes = "判断用户是否首次登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "check/userpwd")
    public ResultObject checkUserPwd(@RequestParam(name = "username") String username) {
        return usersBusinessService.checkUserPwd(username);
    }

    @ApiOperation(value = "agent/save/user", notes = "创建用户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @PostMapping(value = "agent/save/user")
    public ResultObject<SaveUserResponse> agentSaveUsers(@RequestBody SaveUserRequest userRequest) {
        return usersBusinessService.agentSaveUsers(userRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "获取有该业务的用户", notes = "获取有该业务配置的角色对应用户(根据管理机构往上找,管理最近机构的用户)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "业务编码code", example = "RECEIVE_TASK", paramType = "query", required = true),
            @ApiImplicitParam(name = "branchId", value = "机构ID", example = "10020000000000", paramType = "query"),
            @ApiImplicitParam(name = "branchMode", value = "机构模式(MANAGER:管理机构,SALES:销售机构)", example = "MANAGER", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "business/recent/users")
    public ResultObject<List<String>> getBusinessUsers(String businessCode, String branchId, @RequestParam(value = "branchMode", required = false) String branchMode) {
        return usersBusinessService.loadBusinessUserRecentList(businessCode, branchId, branchMode);
    }

    @ApiOperation(value = "获取有该业务的用户", notes = "获取有该业务配置的角色对应用户(只通过业务类型查找)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "业务编码code", example = "RECEIVE_TASK", paramType = "query", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "business/recent/users/new")
    public ResultObject<List<String>> getBusinessUsersNew(String businessCode) {
        return usersBusinessService.loadBusinessUserRecentListNew(businessCode);
    }


    @ApiOperation(value = "校验验证码", notes = "校验验证码")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "verify/code/check")
    public ResultObject verifyCodeCheck(SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest) {
        return usersBusinessService.getVerifyCodeCheck(smsVerifyCodeCheckRequest);
    }

    @ApiOperation(value = "检验用户信息是否存在", notes = "检验用户信息是否存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", example = "Alice", paramType = "query", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "login/interface/check/user/exist")
    public ResultObject checkUserWhetherExist(String userName) {
        return usersBusinessService.checkUserWhetherExist(userName, this.getAppRequestHandler());
    }

    @ApiOperation(value = "获取验证码", notes = "获取验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", example = "Alice", paramType = "query", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "login/interface/verify/code/generate")
    public ResultObject verifyCodeGenerator(String userName) {
        return usersBusinessService.getFindPasswordVerifyCodeGenerator(userName, this.getAppRequestHandler());
    }

    @ApiOperation(value = "重置用户登录密码", notes = "重置用户登录密码")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @ApiParam(value = "版本", name = "version", example = "v1")
    @PostMapping(value = "login/interface/password/reset")
    public ResultObject verifyCodeCheck(@RequestBody UserLoginPasswordResetRequest request) {
        return usersBusinessService.resetPassword(request);
    }

    @ApiOperation(value = "根据手机号码和设备渠道获取用户信息", notes = "根据手机号码和设备渠道获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "手机号码", example = "***********", paramType = "query", required = true),
            @ApiImplicitParam(name = "deviceChannel", value = "设备渠道", example = "gclife_agent_app", paramType = "query", required = true),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "mobile/device")
    public ResultObject<UserResponse> getUserByMobileAndDevice(String mobile, String deviceChannel) {
        return usersBusinessService.getUserByMobileAndDevice(mobile, deviceChannel);
    }

    @ApiOperation(value = "绑定手机号", notes = "绑定手机号")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功")})
    @PostMapping(value = "bind/mobile")
    public ResultObject<UserBaseResponse> bindMobile(String mobile) {
        return usersBusinessService.bindMobile(mobile, this.getCurrentLoginUsers(), this.getAppRequestHandler());
    }

    @ApiOperation(value = "绑定手机号", notes = "绑定手机号")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功")})
    @PostMapping(value = "base/bind/mobile")
    public ResultObject<UserBaseResponse> baseBindMobile(String mobile, String userId, String deviceChannel) {
        return usersBusinessService.baseBindMobile(mobile, userId, deviceChannel);
    }

    @ApiOperation(value = "绑定邮箱", notes = "绑定邮箱")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功")})
    @PostMapping(value = "bind/email")
    public ResultObject bindEmail(String email) {
        return usersBusinessService.bindEmail(email, this.getCurrentLoginUsers(), this.getAppRequestHandler());
    }

    @ApiOperation(value = "无需登录 绑定邮箱", notes = "无需登录 绑定邮箱")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功")})
    @PostMapping(value = "base/bind/email")
    public ResultObject baseBindEmail(String email, String userId, String deviceChannel) {
        return usersBusinessService.baseBindEmail(email, userId, deviceChannel);
    }

    @ApiOperation(value = "处理暂时用户到期状态为禁用", notes = "处理暂时用户到期状态为禁用")
    @ApiResponses({@ApiResponse(code = 200, message = "请求成功")})
    @PostMapping(value = "temporarily/status/update")
    public ResultObject temporarilyUserUpdate() {
        return usersBusinessService.temporarilyUserUpdate();
    }
}