package com.gclife.platform.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.request.EventRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午9:13
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Component
public class EventParameterValidate {

    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(EventParameterValidate.class);


    public void validateEventParameter(EventRequest eventRequest) {
        AssertUtils.isNotNull(LOGGER, eventRequest.getBusinessType(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, eventRequest.getUserId(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_ID_IS_NOT_NULL);
        if (PlatformTermEnum.EVENT_BUSINESS_TYPE.HAPPY_NEWS.name().equals(eventRequest.getBusinessType())) {
            //喜报
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getInsuranceName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_INSURANCE_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getPremium(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PREMIUM_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getProductName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PRODUCT_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getApproveDate(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_APPROVE_DATE_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getApplicantName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_APPLICANT_NAME_IS_NOT_NULL);
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INCREASE.name().equals(eventRequest.getBusinessType())) {
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getRecommendAgentName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_RECOMMEND_AGENT_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getRefereeAgentName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_REFEREE_AGENT_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getTime(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_INCREASE_DATE_IS_NOT_NULL);
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.PLAN.name().equals(eventRequest.getBusinessType())) {
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getProductName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PRODUCT_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getCustomerName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_CUSTOMER_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getProductId(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PRODUCT_ID_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getTime(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PLAN_DATE_IS_NOT_NULL);
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INSURE.name().equals(eventRequest.getBusinessType())) {
            //交单
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getInsuranceName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_INSURANCE_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getPremium(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PREMIUM_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, eventRequest.getProductName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_PRODUCT_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getTime(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_INSURE_DATE_IS_NOT_NULL);
            AssertUtils.isNotNull(LOGGER, eventRequest.getApplicantName(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_APPLICANT_NAME_IS_NOT_NULL);
        } else {
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_TYPE_IS_NOT_EXIST);
        }
    }
}