package com.gclife.platform.validate.parameter.transform;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.MD5Utils;
import com.gclife.platform.model.bo.SharedLinkBo;
import com.gclife.platform.model.bo.SharedLinkConfigBo;
import com.gclife.platform.model.request.SharedLinkSaveRequest;
import com.gclife.platform.model.response.SharedLinkResponse;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 17-12-16
 * description:链接分享数据转换
 */
@Component
public class SharedLinkTransData extends BaseBusinessServiceImpl {

    public SharedLinkResponse transSharedLinkResponse(SharedLinkBo sharedLinkBo) {
        SharedLinkResponse sharedLinkResponse = new SharedLinkResponse();
        sharedLinkResponse.setLinkParameters(sharedLinkBo.getLinkParameters());
        sharedLinkResponse.setPseudoLink(sharedLinkBo.getPseudoLink());
        sharedLinkResponse.setActualLink(sharedLinkBo.getActualLink());
        sharedLinkResponse.setLinkType(sharedLinkBo.getLinkType());
        sharedLinkResponse.setSignature(sharedLinkBo.getSignature());
        return sharedLinkResponse;
    }

    public SharedLinkBo transSharedLinkBo(String userId, SharedLinkConfigBo sharedLinkConfigBo, SharedLinkSaveRequest sharedLinkSaveRequest) {
        SharedLinkBo sharedLinkBo = new SharedLinkBo();
        sharedLinkBo.setSharedLinkConfigId(sharedLinkConfigBo.getSharedLinkConfigId());
        sharedLinkBo.setLinkParameters(sharedLinkSaveRequest.getLinkParameters());
        sharedLinkBo.setCreatedDate(DateUtils.getCurrentTime());
        sharedLinkBo.setCreateUserId(userId);
        String signature = MD5Utils.MD5(String.valueOf(System.currentTimeMillis()));
        sharedLinkBo.setSignature(signature);
        return sharedLinkBo;
    }

}
