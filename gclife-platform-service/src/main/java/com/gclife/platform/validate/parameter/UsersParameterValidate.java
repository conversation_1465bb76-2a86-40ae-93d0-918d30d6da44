package com.gclife.platform.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.SmsVerifyCodeCheckRequest;
import com.gclife.platform.model.request.UserRequest;
import com.gclife.platform.model.request.UserWeixinRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午9:13
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
@Component
public class UsersParameterValidate {

    /**日志*/
    private static final Logger LOGGER = LoggerFactory.getLogger(UsersParameterValidate.class);

    /**
     * 参数验证
     * @param userRequest
     * @throws RequestException
     */
    public  void validParameterAddUser(UserRequest userRequest)  throws RequestException {
        AssertUtils.isNotEmpty(LOGGER,userRequest.getUsername(), PlatformErrorConfigEnum.USERS_PARAMETER_USER_USERNAME_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER,userRequest.getEmail(), PlatformErrorConfigEnum.USERS_PARAMETER_USER_EMAIL_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER,userRequest.getEnabled(), PlatformErrorConfigEnum.USERS_PARAMETER_USER_ENABLED_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER,userRequest.getName(), PlatformErrorConfigEnum.USERS_PARAMETER_USER_USERNAME_IS_NOT_NULL);
    }


    /**
     * 参数验证
     * @param userId
     * @throws RequestException
     */
    public  void validParameterAddUser(String  userId)  throws  RequestException{
        AssertUtils.isNotEmpty(LOGGER,userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
    }

    /**
     * 校验注册或登录用户对象业务数据
     *
     * @param smsVerifyCodeCheckRequest
     */
    public void validBusinessLoginUser(SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest) throws RequestException {
        AssertUtils.isNotEmpty(LOGGER, smsVerifyCodeCheckRequest.getCountryCode(), PlatformErrorConfigEnum.PLATFORM_USER_AREA_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, smsVerifyCodeCheckRequest.getMobile(), PlatformErrorConfigEnum.PLATFORM_USER_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, smsVerifyCodeCheckRequest.getTypeCode(), PlatformErrorConfigEnum.PLATFORM_USER_SMS_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, smsVerifyCodeCheckRequest.getVerifyCode(), PlatformErrorConfigEnum.PLATFORM_USER_VERIFY_CODE_IS_NOT_NULL);
    }


    /**
     * 参数验证
     * @param userWeixinRequest 微信用户请求对象
     */
    public  void validParameterSaveUserWeixin(UserWeixinRequest userWeixinRequest) {
        AssertUtils.isNotEmpty(LOGGER,userWeixinRequest.getOpenId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_WEIXIN_OPENID_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER,userWeixinRequest.getWechatAppId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_WEIXIN_APP_ID_S_NOT_NULL);
    }

    /** 检验验证码请求参数是否完整
     * @param countryCode 区号
     * @param mobile      手机号
     * @throws RequestException
     */
    public void validBusinessVerifyCodeGenerator(String countryCode, String mobile) throws RequestException {
        AssertUtils.isNotEmpty(LOGGER, countryCode, PlatformErrorConfigEnum.PLATFORM_USER_AREA_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, mobile, PlatformErrorConfigEnum.PLATFORM_USER_MOBILE_IS_NOT_NULL);
    }
}