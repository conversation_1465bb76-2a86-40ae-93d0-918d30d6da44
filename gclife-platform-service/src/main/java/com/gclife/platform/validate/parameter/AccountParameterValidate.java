package com.gclife.platform.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.model.request.AccountAuditRequest;
import com.gclife.platform.model.request.AccountRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息参数验证
 */
@Component
public class AccountParameterValidate {
    @Autowired
    private InternationalBaseService internationalBaseService;
    /**日志*/
    private static final Logger LOGGER = LoggerFactory.getLogger(InternationalParameterValidate.class);

    public void validParameterAddAccount(AccountRequest accountRequest) throws RequestException {
        System.out.println("accountRequest---------------------"+accountRequest.toString());

        AssertUtils.isNotEmpty(LOGGER, accountRequest.getAccountNo(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_NO_IS_NOT_NULL);

    }

    public void validParameterAccountAudit(AccountAuditRequest accountAuditRequest) {
        AssertUtils.isNotEmpty(LOGGER,accountAuditRequest.getAccountId(),PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_ID_ERROR);
        AssertUtils.isNotEmpty(LOGGER,accountAuditRequest.getAuditStatus(),PlatformErrorConfigEnum.PLATFORM_ACCOUNT_AUDIT_STATUS_IS_NOT_NULL);
        if(accountAuditRequest.getAuditStatus().equals(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_FAILED.name())){
            AssertUtils.isNotEmpty(LOGGER,accountAuditRequest.getFailureReasonCode(),PlatformErrorConfigEnum.PLATFORM_ACCOUNT_AUDIT_EXCEPTION_CODE_IS_NOT_NULL);
        }

        validParameterFormatAccountAudit(accountAuditRequest);
    }

    private void validParameterFormatAccountAudit(AccountAuditRequest accountAuditRequest) {
        if(!(accountAuditRequest.getAuditStatus().equals(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_PASS.name())||
                accountAuditRequest.getAuditStatus().equals(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_FAILED.name()))){
             throw new RequestException(PlatformErrorConfigEnum.PLATFORM_ACCOUNT_AUDIT_STATUS_FORMAT_ERROR);
        }
        if(accountAuditRequest.getAuditStatus().equals(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_FAILED.name())&&AssertUtils.isNotEmpty(accountAuditRequest.getFailureReasonCode())){
            InternationalDo internationalDo=internationalBaseService.queryOneInternational(InternationalTypeEnum.ACCOUNT_AUDIT_EXCEPTION_QUESTION.name(),accountAuditRequest.getFailureReasonCode(),null);
            AssertUtils.isNotNull(LOGGER,internationalDo,PlatformErrorConfigEnum.PLATFORM_ACCOUNT_AUDIT_EXCEPTION_ERROR);
        }
    }
}