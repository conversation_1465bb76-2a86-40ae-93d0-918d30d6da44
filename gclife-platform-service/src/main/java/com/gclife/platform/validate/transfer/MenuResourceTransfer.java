package com.gclife.platform.validate.transfer;

import com.gclife.common.model.base.Resources;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.model.bo.MenuResourcesBo;
import org.jooq.Record;
import org.springframework.stereotype.Component;

import static com.gclife.platform.core.jooq.Tables.RESOURCES;
import static com.gclife.platform.core.jooq.tables.BaseInternationalText.BASE_INTERNATIONAL_TEXT;

/**
 * <AUTHOR>
 *         create 17-12-22
 *         description:
 */
@Component
public class MenuResourceTransfer extends BaseBusinessServiceImpl{

    /******************************************************************************
     *                              资源转换
     * *****************************************************************************/

    /**
     * 数据转换，资源转换
     * @param resources  资源对象
     * @param menuResourcesBo　菜单资源对象
     */
//    public void transMenuResourcesBo(Resources resources, MenuResourcesBo menuResourcesBo){
//        menuResourcesBo.setCode(resources.getCode());
//        menuResourcesBo.setInternationalText(resources.getInternationalText());
//        menuResourcesBo.setVisible(resources.getVisible());
//        menuResourcesBo.setLogo(resources.getLogo());
//        menuResourcesBo.setEffective(resources.getEffective());
//        menuResourcesBo.setName(resources.getName());
//        menuResourcesBo.setIndex(resources.getIndex());
//        menuResourcesBo.setParentResourceId(resources.getParentResourceId());
//        menuResourcesBo.setResourceId(resources.getResourceId());
//        menuResourcesBo.setType(resources.getType());
//        menuResourcesBo.setValue(resources.getValue());
//        menuResourcesBo.setDescription(resources.getDescription());
//        menuResourcesBo.setCreatedDate(resources.getCreatedDate());
//        menuResourcesBo.setUpdatedDate(resources.getUpdatedDate());
//        menuResourcesBo.setServiceName(resources.getServiceName());
//        menuResourcesBo.setHttpMethod(resources.getHttpMethod());
//        menuResourcesBo.setAccessControlType(resources.getAccessControlType());
//        menuResourcesBo.setHasChildDomain(false);
//    }

    public MenuResourcesBo transMenuResourcesBo(Record record) {

        MenuResourcesBo menuResourcesBo = new MenuResourcesBo();
        menuResourcesBo.setVisible(record.get(RESOURCES.VISIBLE));
        menuResourcesBo.setLogo(record.get(RESOURCES.LOGO));
        menuResourcesBo.setEffective(record.get(RESOURCES.EFFECTIVE));
        menuResourcesBo.setName(record.get(RESOURCES.NAME));
        menuResourcesBo.setIndex(record.get(RESOURCES.INDEX));
        menuResourcesBo.setParentResourceId((String)record.getValue("parentresourceid"));
        menuResourcesBo.setResourceId(record.get(RESOURCES.RESOURCE_ID));
        menuResourcesBo.setType(record.get(RESOURCES.TYPE));
        menuResourcesBo.setValue(record.get(RESOURCES.VALUE));
        menuResourcesBo.setDescription(record.get(RESOURCES.DESCRIPTION));
        menuResourcesBo.setCreatedDate(record.get(RESOURCES.CREATED_DATE));
        menuResourcesBo.setUpdatedDate(record.get(RESOURCES.UPDATED_DATE));
        if (AssertUtils.isNotNull(record.get(RESOURCES.SERVICE_NAME))) {
            menuResourcesBo.setServiceName(getConfigValue(record.get(RESOURCES.SERVICE_NAME), "resources.service_name."));
        }
        menuResourcesBo.setHttpMethod(record.get(RESOURCES.HTTP_METHOD));
        menuResourcesBo.setAccessControlType(record.get(RESOURCES.ACCESS_CONTROL_TYPE));
        menuResourcesBo.setCode(record.get(RESOURCES.CODE));
        menuResourcesBo.setInternationalText(record.getValue(BASE_INTERNATIONAL_TEXT.VALUE));
        menuResourcesBo.setSubmenuVisibleOnDashboard(record.get(RESOURCES.SUBMENU_VISIBLE_ON_DASHBOARD));

        return menuResourcesBo;
    }

}