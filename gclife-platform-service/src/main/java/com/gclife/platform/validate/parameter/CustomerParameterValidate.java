package com.gclife.platform.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.EndorseCustomerRequest;
import com.gclife.platform.model.request.UserCustomerBusinessRequest;
import com.gclife.platform.model.request.UserCustomerRequest;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.AreaBusinessService;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
@Component
public class CustomerParameterValidate {

 @Autowired
 private TerminologyBaseBusinessService terminologyBaseBusinessService;

 @Autowired
 private AreaBusinessService areaBusinessService;
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerParameterValidate.class);

    public void validParameterCustomer(UserCustomerRequest userCustomerRequest) {
        AssertUtils.isNotNull(LOGGER, userCustomerRequest, PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR);
        AssertUtils.isNotEmpty(LOGGER, userCustomerRequest.getName(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, userCustomerRequest.getSex(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, userCustomerRequest.getBirthday(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, userCustomerRequest.getIdType(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, userCustomerRequest.getIdNo(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, verifyCodeNameByKey(TerminologyTypeEnum.GENDER.name(), userCustomerRequest.getSex()), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_SEX_FORMAT_ERROR);
        if (!AssertUtils.isTimestamp(userCustomerRequest.getBirthday())) {
            throw new RequestException(PlatformErrorConfigEnum.CUSTOMER_PARAMETER_BIRTHDAY_FORMAT_ERROR);
        }
        if(AssertUtils.isNotEmpty(userCustomerRequest.getPhone())) {
            AssertUtils.isNotPureDigital(LOGGER, userCustomerRequest.getPhone(), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_PHONE_FORMAT_ERROR);
        }
        if(AssertUtils.isNotEmpty(userCustomerRequest.getIdType())) {
            AssertUtils.isNotEmpty(LOGGER, verifyCodeNameByKey(TerminologyTypeEnum.ID_TYPE.name(), userCustomerRequest.getIdType()), PlatformErrorConfigEnum.CUSTOMER_PARAMETER_ID_TYPE_FORMAT_ERROR);
        }

        if (AssertUtils.isNotEmpty(userCustomerRequest.getEmail())) {
            AssertUtils.isEmail(LOGGER,userCustomerRequest.getEmail(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_EMAIL_FORMAT_ERROR);
        }
    }

    public String verifyCodeNameByKey(String type, String key) throws RequestException {
        ResultObject<SyscodeResponse> resultObject= terminologyBaseBusinessService.queryOneSyscode(type,key);
        if (!AssertUtils.isNotNull(resultObject.getData())) {
            return resultObject.getData().getCodeName();
        }
        return null;
    }

    public void validCustomerData(EndorseCustomerRequest endorseCustomerRequest,Users users) {
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getCustomerId(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getMobile(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getHomeAreaCode(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_HOME_AREA_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getHomeAddress(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_HOME_ADDRESS_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getEmail(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_EMAIL_IS_NOT_NULL);

        validCustomerDataFormat(endorseCustomerRequest,users);
    }

    private void validCustomerDataFormat(EndorseCustomerRequest endorseCustomerRequest,Users users) {
        if(AssertUtils.isNotEmpty(endorseCustomerRequest.getHomePhone())) {
            AssertUtils.isNotPureDigital(LOGGER, endorseCustomerRequest.getHomePhone(), PlatformErrorConfigEnum.PLATFORM_CUSTOMER_HOME_PHONE_FORMAT_ERROR);
        }
        AssertUtils.isNotPureDigital(LOGGER,endorseCustomerRequest.getMobile(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_MOBILE_FORMAT_ERROR);
        ResultObject<AreaResponse> homeAreaResultObject = areaBusinessService.getAreaInfo(endorseCustomerRequest.getHomeAreaCode(),users);
        AssertUtils.isResultObjectDataNull(LOGGER, homeAreaResultObject, PlatformErrorConfigEnum.PLATFORM_CUSTOMER_HOME_AREA_CODE_IS_NOT_EXIST);
        AssertUtils.isEmail(LOGGER,endorseCustomerRequest.getEmail(),PlatformErrorConfigEnum.PLATFORM_CUSTOMER_EMAIL_FORMAT_ERROR);
    }

    public void validateCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness) {
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getIdType(),PlatformErrorConfigEnum.CUSTOMER_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getIdNo(),PlatformErrorConfigEnum.CUSTOMER_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getName(),PlatformErrorConfigEnum.CUSTOMER_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getUserId(),PlatformErrorConfigEnum.CUSTOMER_PARAMETER_USER_ID_IS_NOT_NULL);
    }
}