package com.gclife.platform.validate.parameter.transform;

import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.message.model.request.SmsVerifyCodeGeneratorRequest;
import com.gclife.platform.model.request.SmsVerifyCodeCheckRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 2018/8/13
 * description:
 */

@Component
public class SmsVerifyReqFcTransData {
    public SmsVerifyCodeGeneratorRequest transSmsVerifyCodeGenerator(String countryCode, String mobile, String smsType, AppRequestHeads appRequestHeads) {
        SmsVerifyCodeGeneratorRequest smsVerifyCodeGeneratorReqFc = new SmsVerifyCodeGeneratorRequest();
        smsVerifyCodeGeneratorReqFc.setCountryCode(countryCode);
        smsVerifyCodeGeneratorReqFc.setMobile(mobile);
        smsVerifyCodeGeneratorReqFc.setTypeCode(smsType);
        smsVerifyCodeGeneratorReqFc.setCallbackUrl("/v1/verify/code/generate");
        smsVerifyCodeGeneratorReqFc.setChannelType(appRequestHeads.getDeviceChannel());
        return smsVerifyCodeGeneratorReqFc;
    }

    public com.gclife.message.model.request.SmsVerifyCodeCheckRequest transSmsVerifyCodeCheck(SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest) {
        com.gclife.message.model.request.SmsVerifyCodeCheckRequest smsVerifyCodeCheckReqFc = new com.gclife.message.model.request.SmsVerifyCodeCheckRequest();
        smsVerifyCodeCheckReqFc.setCountryCode(smsVerifyCodeCheckRequest.getCountryCode());
        smsVerifyCodeCheckReqFc.setMobile(smsVerifyCodeCheckRequest.getMobile());
        smsVerifyCodeCheckReqFc.setTypeCode(smsVerifyCodeCheckRequest.getTypeCode());
        smsVerifyCodeCheckReqFc.setVerifyCode(smsVerifyCodeCheckRequest.getVerifyCode());
        smsVerifyCodeCheckReqFc.setSubmitTime(String.valueOf(System.currentTimeMillis()));
        return smsVerifyCodeCheckReqFc;
    }
}
