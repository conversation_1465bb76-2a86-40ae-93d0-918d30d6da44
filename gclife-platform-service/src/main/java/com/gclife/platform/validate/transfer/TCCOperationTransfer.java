package com.gclife.platform.validate.transfer;

import com.gclife.common.model.TCCDetail;
import com.gclife.common.model.TCCEntry;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.core.jooq.tables.TccDetail;
import com.gclife.platform.core.jooq.tables.TccEntry;
import com.gclife.platform.core.jooq.tables.pojos.TccDetailPo;
import com.gclife.platform.core.jooq.tables.pojos.TccEntryPo;
import com.gclife.platform.model.bo.BranchLevelBo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *         create 17-12-22
 *         description:
 */
@Component
public class TCCOperationTransfer extends BaseBusinessServiceImpl{



    public TccDetailPo transferTCCDetail(TCCDetail tccDetail) {

        TccDetailPo tccDetailPo = new TccDetailPo();
        tccDetailPo.setDetailId(tccDetail.getTccDetailId());
        tccDetailPo.setTccInterfaceBean(tccDetail.getTccInterfaceBean());
        tccDetailPo.setTccInterfaceMethod(tccDetail.getTccInterfaceMethod());
        tccDetailPo.setTccBean(tccDetail.getTccBean());
        tccDetailPo.setTccOperation(tccDetail.getTccOperation());
        tccDetailPo.setCancelMethod(tccDetail.getCancelMethod());
        tccDetailPo.setConfirmMethod(tccDetail.getConfirmMethod());
        tccDetailPo.setConfirmData(tccDetail.getConfirmData());
        tccDetailPo.setCancelData(tccDetail.getCancelData());
        tccDetailPo.setTccEntryId(tccDetail.getTccEntryId());
        tccDetailPo.setErrorCode(tccDetail.getErrorCode());
        tccDetailPo.setErrorMessage(tccDetail.getErrorMessage());
        tccDetailPo.setResult(tccDetail.getResult());
        tccDetailPo.setTccStatus(tccDetail.getTccStatus());
        tccDetailPo.setCreateDate(tccDetail.getCreatedDate());

        return tccDetailPo;
    }


    public TccEntryPo transferTCCEntry(TCCEntry tccEntry) {

        TccEntryPo tccEntryPo = new TccEntryPo();
        tccEntryPo.setEntryId(tccEntry.getEntryId());
        tccEntryPo.setEntryParams(tccEntry.getEntryParams());
        tccEntryPo.setEntryBean(tccEntry.getEntryBean());
        tccEntryPo.setEntryRequestUrl(tccEntry.getEntryRequestUrl());
        tccEntryPo.setEntryMethod(tccEntry.getEntryMethod());
        tccEntryPo.setCreateUser(tccEntry.getEntryUserId());
        tccEntryPo.setErrorCode(tccEntry.getErrorCode());
        tccEntryPo.setErrorMessage(tccEntry.getErrorMessage());
        tccEntryPo.setResult(tccEntry.getResult());
        tccEntryPo.setCreateDate(tccEntry.getCreatedDate());

        return tccEntryPo;
    }

}