package com.gclife.platform.validate.transfer;

import com.gclife.agent.api.AgentTeamApi;
import com.gclife.agent.model.response.AgentTeamDetailResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.base.model.bo.EventBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.EventBaseService;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.EventRequest;
import com.gclife.platform.model.response.EventResponse;
import com.gclife.platform.service.business.UsersBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 19-2-27
 * description:
 */
@Component
public class EventTransfer extends BaseBusinessServiceImpl {

    @Autowired
    private UsersBaseService usersBaseService;

    @Autowired
    private UsersBusinessService usersBusinessService;

    @Autowired
    private EventBaseService eventBaseService;

    @Autowired
    private AgentTeamApi agentTeamApi;

    public List<EventResponse> transferEventData(List<EventBo> eventBos) {
        if (!AssertUtils.isNotEmpty(eventBos)) {
            return null;
        }
        List<EventResponse> eventResponses = new ArrayList<>();
        eventBos.forEach(eventBo ->
                eventResponses.add(transferEventBo(eventBo))
        );
        return eventResponses;
    }

    public EventResponse transferEventBo(EventBo eventBo) {
        EventResponse eventResponse = (EventResponse) this.converterObject(eventBo, EventResponse.class);
        AssertUtils.isNotNull(getLogger(), eventBo.getEventTemplatePo(), PlatformErrorConfigEnum.PLATFORM_EVENT_TEMPLATE_IS_NULL_ERROR);
        eventResponse.setTemplateCode(eventBo.getEventTemplatePo().getTemplateCode());
        eventResponse.setTemplateImageUrl(eventBo.getEventTemplatePo().getTemplateImageUrl());
        if (AssertUtils.isNotNull(eventBo.getEventHappyNewsPo())) {
            eventResponse.setInsuranceName(eventBo.getEventHappyNewsPo().getInsuranceName());
            eventResponse.setTeamLeader(eventBo.getEventHappyNewsPo().getTeamLeader());
            eventResponse.setPremium(AssertUtils.isNotNull(eventBo.getEventHappyNewsPo().getPremium()) ? eventBo.getEventHappyNewsPo().getPremium().toString() : null);
            eventResponse.setProductName(eventBo.getEventHappyNewsPo().getProductName());
            eventResponse.setApproveDate(AssertUtils.isNotNull(eventBo.getEventHappyNewsPo().getApproveDate()) ? DateUtils.timeStrToString(eventBo.getEventHappyNewsPo().getApproveDate(), DateUtils.FORMATE3) : null);
        } else if (AssertUtils.isNotNull(eventBo.getEventIncreasePo())) {
            eventResponse.setRecommendAgentName(eventBo.getEventIncreasePo().getRecommendAgentName());
            eventResponse.setRefereeAgentName(eventBo.getEventIncreasePo().getRefereeAgentName());
            eventResponse.setTeamLeader(eventBo.getEventIncreasePo().getTeamLeader());
            eventResponse.setIncreaseDate(AssertUtils.isNotNull(eventBo.getEventIncreasePo().getTime()) ? DateUtils.timeStrToString(eventBo.getEventIncreasePo().getTime(), DateUtils.FORMATE3) : null);
        } else if (AssertUtils.isNotNull(eventBo.getEventPlanPo())) {
            eventResponse.setTeamLeader(eventBo.getEventPlanPo().getTeamLeader());
            eventResponse.setProductName(eventBo.getEventPlanPo().getProductName());
            eventResponse.setCustomerName(eventBo.getEventPlanPo().getCustomerName());
            eventResponse.setPlanDate(AssertUtils.isNotNull(eventBo.getEventPlanPo().getTime()) ? DateUtils.timeStrToString(eventBo.getEventPlanPo().getTime(), DateUtils.FORMATE3) : null);
        } else if (AssertUtils.isNotNull(eventBo.getEventInsurePo())) {
            eventResponse.setInsuranceName(eventBo.getEventInsurePo().getInsuranceName());
            eventResponse.setTeamLeader(eventBo.getEventInsurePo().getTeamLeader());
            eventResponse.setPremium(AssertUtils.isNotNull(eventBo.getEventInsurePo().getPremium()) ? eventBo.getEventInsurePo().getPremium().toString() : null);
            eventResponse.setProductName(eventBo.getEventHappyNewsPo().getProductName());
            eventResponse.setInsureDate(AssertUtils.isNotNull(eventBo.getEventHappyNewsPo().getApproveDate()) ? DateUtils.timeStrToString(eventBo.getEventHappyNewsPo().getApproveDate(), DateUtils.FORMATE3) : null);
        }
        //事件动态
        List<EventDynamicPo> eventDynamicPos = eventBo.getEventDynamicPoList();
        List<String> eventDynamicList = new ArrayList<>();
        List<String> likeHeadUrls = new ArrayList<>();
        if (AssertUtils.isNotEmpty(eventDynamicPos)) {
            eventDynamicPos.forEach(eventDynamicPo -> {
                if (PlatformTermEnum.EVENT_DYNAMIC_TYPE.LIKE.name().equals(eventDynamicPo.getType())) {
                    //点赞
                    eventDynamicList.add("@" + eventDynamicPo.getNickName() + "为Ta点赞");
                    if (AssertUtils.isNotEmpty(eventDynamicPo.getHeadUrl())) {
                        likeHeadUrls.add(eventDynamicPo.getHeadUrl());
                    }
                } else if (PlatformTermEnum.EVENT_DYNAMIC_TYPE.FORWARD.name().equals(eventDynamicPo.getType())) {
                    //转发
                    eventDynamicList.add("@" + eventDynamicPo.getNickName() + "为Ta转发");
                }
            });
            eventResponse.setEventContents(eventDynamicList);
            eventResponse.setLikeHeadUrls(likeHeadUrls);
        }
        //团队成员
        ResultObject<AgentTeamDetailResponse> respFcResultObject = agentTeamApi.queryTeamDetail();
        if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
            eventResponse.setTeamNumber(String.valueOf(respFcResultObject.getData().getTeamNumber()));
            eventResponse.setHeadUrls(respFcResultObject.getData().getTeamHeadAttachIdList());
        }
        return eventResponse;
    }

    public EventPo transferEvent(EventPo eventPo, String dynamicType) {
        if (PlatformTermEnum.EVENT_DYNAMIC_TYPE.LIKE.name().equals(dynamicType)) {
            eventPo.setLikeNumber(AssertUtils.isNotNull(eventPo.getLikeNumber()) ? eventPo.getLikeNumber() + 1 : 1);
        } else if (PlatformTermEnum.EVENT_DYNAMIC_TYPE.FORWARD.name().equals(dynamicType)) {
            eventPo.setForwardNumber(AssertUtils.isNotNull(eventPo.getForwardNumber()) ? eventPo.getForwardNumber() + 1 : 1);
        }
        return eventPo;
    }

    public EventDynamicPo transferEventDynamic(EventPo eventPo, String dynamicType, Users users) {
        UserWeixinPo userWeixinPo = usersBaseService.queryOneUserWeixinPoById(users.getUserId());
        EventDynamicPo eventDynamicPo = new EventDynamicPo();
        eventDynamicPo.setEventId(eventPo.getEventId());
        if (AssertUtils.isNotNull(userWeixinPo)) {
            eventDynamicPo.setHeadUrl(userWeixinPo.getHeadImgUrl());
        }
        eventDynamicPo.setNickName(users.getNickName());
        eventDynamicPo.setTime(DateUtils.getCurrentTime());
        eventDynamicPo.setType(dynamicType);
        eventDynamicPo.setUserId(users.getUserId());
        return eventDynamicPo;
    }

    public EventPo transferEventPo(EventRequest eventRequest) {
        UsersBo usersBo = usersBusinessService.getUserById(eventRequest.getUserId()).getData();
        AssertUtils.isNotNull(getLogger(), usersBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
        EventPo eventPo = new EventPo();
        eventPo.setBusinessType(eventRequest.getBusinessType());
        eventPo.setUserId(eventRequest.getUserId());
        eventPo.setHeadUrl(usersBo.getUrl());
        eventPo.setNickName(usersBo.getNickName());
        //获取模板
        List<EventTemplatePo> eventTemplatePos = eventBaseService.queryEventTemplateByBusinessType(eventRequest.getBusinessType());
        AssertUtils.isNotEmpty(getLogger(), eventTemplatePos, PlatformErrorConfigEnum.PLATFORM_EVENT_TEMPLATE_IS_NULL_ERROR);
        if (PlatformTermEnum.EVENT_BUSINESS_TYPE.PLAN.name().equals(eventRequest.getBusinessType())) {
            //计划书匹配对应模板
            eventTemplatePos.forEach(eventTemplatePo -> {
                if (eventTemplatePo.getProductId().equals(eventRequest.getProductId())) {
                    eventPo.setTemplateId(eventTemplatePo.getEventTemplateId());
                }
            });
        }

        if (!AssertUtils.isNotEmpty(eventPo.getTemplateId())) {
            EventTemplatePo templatePo = eventTemplatePos.get((int) (Math.random() * (eventTemplatePos.size())));
            eventPo.setTemplateId(templatePo.getEventTemplateId());
        }
        return eventPo;
    }

    public EventHappyNewsPo transferEventHappyNews(EventRequest eventRequest, EventPo eventPo) {
        EventHappyNewsPo eventHappyNewsPo = (EventHappyNewsPo) this.converterObject(eventRequest, EventHappyNewsPo.class);
        eventHappyNewsPo.setEventId(eventPo.getEventId());
        return eventHappyNewsPo;
    }

    public EventIncreasePo transferEventIncrease(EventRequest eventRequest, EventPo eventPo) {
        EventIncreasePo eventIncreasePo = (EventIncreasePo) this.converterObject(eventRequest, EventIncreasePo.class);
        eventIncreasePo.setEventId(eventPo.getEventId());
        return eventIncreasePo;
    }

    public EventPlanPo transferEventPlan(EventRequest eventRequest, EventPo eventPo) {
        EventPlanPo eventPlanPo = (EventPlanPo) this.converterObject(eventRequest, EventPlanPo.class);
        eventPlanPo.setEventId(eventPo.getEventId());
        return eventPlanPo;
    }

    public EventInsurePo transferEventInsure(EventRequest eventRequest, EventPo eventPo) {
        EventInsurePo eventInsurePo = (EventInsurePo) this.converterObject(eventRequest, EventInsurePo.class);
        eventInsurePo.setEventId(eventPo.getEventId());
        eventInsurePo.setInsureDate(eventRequest.getTime());
        return eventInsurePo;
    }
}
