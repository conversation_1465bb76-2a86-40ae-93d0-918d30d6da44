package com.gclife.platform.validate.parameter.transform;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.model.request.AccountAuditRequest;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountResponse;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息数据转换
 */
@Component
public class AccountTransData extends BaseBusinessServiceImpl {

    public AccountBo transAccountBo(AccountBo accountBo, AccountRequest accountRequest) {
        //账户用途类: I付费，O退费
        accountBo.setUseType(accountRequest.getUseType());
        //账户号码
        accountBo.setAccountNo(accountRequest.getAccountNo());
        //银行代码
        accountBo.setBankCode(accountRequest.getBankCode());
        //支行名称
        accountBo.setSubbranch(accountRequest.getSubbranch());
        //账户持有人
        accountBo.setAccountOwner(accountRequest.getAccountOwner());
        //账户持有人证件类型
        accountBo.setIdTypeCode(accountRequest.getIdType());
       //账户持有人证件号码
        accountBo.setIdNo(accountRequest.getIdNo());
        //账户类型【活期储蓄账户/借记卡】
        accountBo.setAccountTypeCode(accountRequest.getAccountType());
        //账户持有人签名是否一致
        accountBo.setAcctuserSignStatus(accountRequest.getAcctuserSignStatus());
        //授权日期
        accountBo.setAuthorizedDate(accountRequest.getAuthorizedDate());
        //开户行所在城市
        accountBo.setAreaCode(accountRequest.getAreaCode());
        accountBo.setUserId(accountRequest.getUserId());
        accountBo.setPrimaryFlag(accountRequest.getPrimaryFlag());
        accountBo.setBankFrontAttachId(accountRequest.getBankFrontAttachId());
        accountBo.setBankBackAttachId(accountRequest.getBankBackAttachId());
        return accountBo;
    }

    public void transferAccountAudit(AccountBo accountBo, AccountResponse accountResponse) {
          if(AssertUtils.isNotNull(accountBo.getAccountAuditPo())){
              accountResponse.setAuditStatus(accountBo.getAccountAuditPo().getAuditStatus());
              accountResponse.setFailureReason(accountBo.getAccountAuditPo().getFailureReason());
              accountResponse.setFailureReasonCode(accountBo.getAccountAuditPo().getFailureReasonCode());
          }
    }

    public AccountAuditPo transferAccountAudit(AccountAuditPo accountAuditPo,AccountBo accountBo) {
        if(!AssertUtils.isNotNull(accountAuditPo)){
            accountAuditPo=new AccountAuditPo();
            accountAuditPo.setAccountId(accountBo.getAccountId());
        }else{
            accountAuditPo.setAuditDate(null);
            accountAuditPo.setAuditUserId(null);
            accountAuditPo.setFailureReason(null);
            accountAuditPo.setFailureReasonCode(null);
            accountAuditPo.setCreatedDate(DateUtils.getCurrentTime());
        }
        accountAuditPo.setAuditStatus(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.UNDER_REVIEW.name());
        return accountAuditPo;
    }

    public AccountAuditPo transferAccountAudit(AccountAuditPo accountAuditPo,AccountAuditRequest accountAuditRequest) {
        accountAuditPo.setFailureReasonCode(accountAuditRequest.getFailureReasonCode());
        accountAuditPo.setFailureReason(accountAuditRequest.getFailureReason());
        accountAuditPo.setAuditStatus(accountAuditRequest.getAuditStatus());
        accountAuditPo.setAuditDate(DateUtils.getCurrentTime());
        return accountAuditPo;
    }
}