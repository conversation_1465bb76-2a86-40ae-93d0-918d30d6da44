package com.gclife.platform.validate.transfer;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinPo;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinRelationPo;
import com.gclife.platform.model.bo.BranchLevelBo;
import com.gclife.platform.model.request.UserWeixinRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-12-22
 * description:
 */
@Component
public class UserWeixinTransfer extends BaseBusinessServiceImpl {


    /******************************************************************************
     *                              微信用户转换
     * *****************************************************************************/

    /**
     * 数据转换，微信用户转换
     *
     * @param userWeixinRequest    请求对象
     * @param userWeixinRelationPo 　数据库保存对象
     */
    public void transferWeixinRelation(UserWeixinRequest userWeixinRequest, UserWeixinRelationPo userWeixinRelationPo) {
        userWeixinRelationPo.setOpenid(userWeixinRequest.getOpenId());
        userWeixinRelationPo.setUnionid(userWeixinRequest.getUnionId());
        userWeixinRelationPo.setWechatAppId(userWeixinRequest.getWechatAppId());
        userWeixinRelationPo.setUserId(userWeixinRequest.getUserId());
    }


    /**
     * 数据转换，微信用户转换
     *
     * @param userWeixinRequest 请求对象
     * @param userWeixinPo      　数据库保存对象
     */
    public void transferWeixin(UserWeixinRequest userWeixinRequest, UserWeixinPo userWeixinPo, String userId) {
        userWeixinPo.setUserId(userId);
        userWeixinPo.setSubscribe(userWeixinRequest.getSubscribe());
        userWeixinPo.setSex(userWeixinRequest.getSex());
        userWeixinPo.setCity(userWeixinRequest.getCity());
        userWeixinPo.setCountry(userWeixinRequest.getCountry());
        userWeixinPo.setGroupid(userWeixinRequest.getGroupId() + "");
        userWeixinPo.setProvince(userWeixinRequest.getProvince());
        userWeixinPo.setRemark(userWeixinRequest.getRemark());
        userWeixinPo.setHeadImgUrl(userWeixinRequest.getHeadImgUrl());
        userWeixinPo.setLanguage(userWeixinRequest.getLanguage());
        userWeixinPo.setNickname(userWeixinRequest.getNickname());
    }


}