package com.gclife.platform.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午9:13
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
@Component
public class InternationalParameterValidate {

    /**日志*/
    private static final Logger LOGGER = LoggerFactory.getLogger(InternationalParameterValidate.class);

    /**
     * 参数验证
     * @param type
     * @param key
     * @throws RequestException
     */
    public  void validParameterLoadTerminologyLanguage(String type,String key)  throws RequestException {
        AssertUtils.isNotEmpty(LOGGER,type, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER,key, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
    }



}