package com.gclife.platform.validate.transfer;

import com.gclife.platform.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerResemblePo;
import com.gclife.platform.model.request.EndorseCustomerRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 18-9-11
 * description:
 */
@Component
public class CustomerBaseTransfer {

    public void transformCustomerBase(CustomerPo customerPo, EndorseCustomerRequest endorseCustomerRequest) {
        customerPo.setHomePhone(endorseCustomerRequest.getHomePhone());
        customerPo.setHomeAddress(endorseCustomerRequest.getHomeAddress());
        customerPo.setHomeZipCode(endorseCustomerRequest.getHomeZipCode());
        customerPo.setEmail(endorseCustomerRequest.getEmail());
        customerPo.setMobile(endorseCustomerRequest.getMobile());
        customerPo.setHomeAreaCode(endorseCustomerRequest.getHomeAreaCode());
    }

    public CustomerResemblePo transformCustomerResemble(CustomerPo customerPo, CustomerAgentPo customerAgentPo) {
        CustomerResemblePo customerResemblePo=new CustomerResemblePo();
        customerResemblePo.setCustomerAgentId(customerAgentPo.getCustomerAgentId());
        customerResemblePo.setCustomerAgentNo(customerAgentPo.getCustomerNo());
        customerResemblePo.setCustomerId(customerPo.getCustomerId());
        customerResemblePo.setCustomerNo(customerPo.getCustomerNo());
        return customerResemblePo;
    }
}
