package com.gclife.platform.validate.transfer;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.Branch;
import com.gclife.platform.model.bo.BranchLevelBo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *         create 17-12-22
 *         description:
 */
@Component
public class BranchTransfer extends BaseBusinessServiceImpl{



    /******************************************************************************
     *                              机构列表转机构树
     * *****************************************************************************/

    /**
     * 数据转换，转换机构树
     * @param branchLevelBos  树机构所有机构集合
     * @return  List<BranchLevelBo>　机构树
     */
    public List<BranchLevelBo> transferBranchTree(List<BranchLevelBo> branchLevelBos){
        //获取树跟节点
        List<BranchLevelBo> rootBranchList = branchLevelBos.stream().filter(branchLevelBo -> branchLevelBo.getLevel()==1).collect(Collectors.toList());
        //移除根节点
        branchLevelBos.removeAll(rootBranchList);
        //匹配
        if(AssertUtils.isNotEmpty(rootBranchList)){
            //递归
            recursion(rootBranchList,branchLevelBos);
        }
        return rootBranchList;
    }

    /**
     * 递归函数
     * @param rootBranchList      根节点机构
     * @param remainBranchList 　剩余需要匹配的机构
     */
    private void recursion(List<BranchLevelBo> rootBranchList, List<BranchLevelBo> remainBranchList) {
        if(AssertUtils.isNotEmpty(remainBranchList)){
            rootBranchList.forEach(rootBranch->{
                //获取下一层机构
                List<BranchLevelBo> childs = remainBranchList.stream().filter(childBranch->childBranch.getParentBranchId().equals(rootBranch.getBranchId())).collect(Collectors.toList());
                if(AssertUtils.isNotEmpty(childs)){
                    //移除数据
                    remainBranchList.removeAll(childs);
                    //递归
                    recursion(childs,remainBranchList);
                    //设置孩子
                    rootBranch.setChilds(childs);
                }
            });
        }
    }





    /******************************************************************************
     *                              机构列表筛选叶子机构
     * *****************************************************************************/

    /**
     * 机构列表筛选叶子机构
     * @param branchLevelBos  树机构所有机构集合
     * @return  List<BranchLevelBo>　叶子机构列表
     */
    public List<BranchLevelBo> transferBranchFilterLeaf(List<BranchLevelBo> branchLevelBos){
        List<BranchLevelBo> leafBranchList = new ArrayList<>();
        if(AssertUtils.isNotEmpty(branchLevelBos)){
            branchLevelBos.forEach(branchLevelBo -> {
                List<BranchLevelBo> branchLevelBoList = branchLevelBos.stream()
                        .filter(branchLevelBo1 -> branchLevelBo1.getParentBranchId().equals(branchLevelBo.getBranchId())).collect(Collectors.toList());
                if(!AssertUtils.isNotEmpty(branchLevelBoList)){
                    leafBranchList.add(branchLevelBo);
                }
            });
        }
        return leafBranchList;
    }

}