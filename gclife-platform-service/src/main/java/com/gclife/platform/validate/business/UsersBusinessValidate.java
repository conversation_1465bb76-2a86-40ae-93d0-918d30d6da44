package com.gclife.platform.validate.business;

import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.validate.parameter.UsersParameterValidate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午9:13
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */

@Component
public class UsersBusinessValidate {

    /**日志*/
    private static final Logger LOGGER = LoggerFactory.getLogger(UsersParameterValidate.class);


    /**
     * 校验用户对象业务数据
     * @param usersPo
     */
    public void validBusinessUpdateUser(UsersPo usersPo)throws RequestException {

        //用户验证
        AssertUtils.isNotNull(LOGGER,usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);

    }
}