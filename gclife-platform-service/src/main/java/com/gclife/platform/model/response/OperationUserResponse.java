package com.gclife.platform.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/4/24
 */
@Getter
@Setter
@ApiModel(value = "操作用户响应", description = "操作用户响应")
public class OperationUserResponse extends BaseResponse {
    @ApiModelProperty(value = "操作用户ID", example = "用户ID")
    private String userId;
    @ApiModelProperty(value = "操作用户名", example = "操作用户名")
    private String username;
    @ApiModelProperty(value = "操作用户名称", example = "操作用户名称")
    private String name;
    @ApiModelProperty(value = "操作用户性别", example = "操作用户性别")
    private String gender;
    @Internation(filed = "gender", codeType = "GENDER")
    @ApiModelProperty(value = "操作用户性别国际化", example = "操作用户性别国际化")
    private String genderName;
    @ApiModelProperty(value = "操作用户邮箱", example = "操作用户邮箱")
    private String email;
    @ApiModelProperty(value = "操作用户手机号码", example = "操作用户手机号码")
    private String mobile;
    @ApiModelProperty(value = "操作用户昵称", example = "操作用户昵称")
    private String nickName;
}
