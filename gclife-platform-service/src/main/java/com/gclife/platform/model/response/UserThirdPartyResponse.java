package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-19
 */
@Data
public class UserThirdPartyResponse {
    @ApiModelProperty(example = "开放标识")
    private String openId;
    @ApiModelProperty(example = "appId")
    private String appId;
    @ApiModelProperty(example = "第三方平台编码")
    private String thirdPartyCode;
    @ApiModelProperty(example = "绑定状态")
    private String bindStatus;
    @ApiModelProperty(example = "昵称")
    private String nickname;
    @ApiModelProperty(example = "手机号")
    private String mobile;
    @ApiModelProperty(example = "姓")
    private String familyName;
    @ApiModelProperty(example = "名")
    private String givenName;
    @ApiModelProperty(example = "性别")
    private String sex;
    @ApiModelProperty(example = "电子邮箱")
    private String email;
    @ApiModelProperty(example = "语言")
    private String language;
    @ApiModelProperty(example = "头像url")
    private String headImgUrl;
}
