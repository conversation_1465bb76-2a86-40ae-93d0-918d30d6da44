package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 2018/8/13
 * description:
 */

public class SmsVerifyCodeCheckRequest {

    @ApiModelProperty(value = "国家区号", example = "86", required = true)
    private String countryCode;

    @ApiModelProperty(value = "手机号码", example = "18898617356", required = true)
    private String mobile;

    @ApiModelProperty(value = "短信类型(注册:REGISTER,登录:LOGIN,身份验证:AUTHENTICATION,投保:APPLY)", example = "LOGIN", required = true)
    private String typeCode;

    @ApiModelProperty(value = "验证码", example = "6754", required = true)
    private String verifyCode;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }
}
