package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-10-22
 * description:
 */
public class BaseFactorConfigResponse {
    @ApiModelProperty(example = "配置Id")
    private String configId;
    @ApiModelProperty(example = "配置编码")
    private String configCode;
    @ApiModelProperty(example = "配置名称")
    private String configName;
    @ApiModelProperty(example = "配置描述,需描术不同的值对应的操作")
    private String configDescription;
    @ApiModelProperty(example = "配置值")
    private String configValue;

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigDescription() {
        return configDescription;
    }

    public void setConfigDescription(String configDescription) {
        this.configDescription = configDescription;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }
}
