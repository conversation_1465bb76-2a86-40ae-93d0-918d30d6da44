package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:32
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构返回信息
 * <AUTHOR>
 */
@ApiModel(value = "Employe",description = "职员")
public class EmployeResponse extends BaseResponse {

    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "职员ID")
    private String employeId;
    @ApiModelProperty(example = "机构ID")
    private String branchId;
    @ApiModelProperty(example = "职员编码")
    private String employeCode;
    @ApiModelProperty(example = "职员职级")
    private String employeLevel;
    @ApiModelProperty(example = "职员名称")
    private String employeName;
    @ApiModelProperty(example = "证件类型")
    private String idType;
    @ApiModelProperty(example = "证件号")
    private String idNo;
    @ApiModelProperty(example = "手机号码")
    private String mobile;

    public String getEmployeId() {
        return employeId;
    }

    public void setEmployeId(String employeId) {
        this.employeId = employeId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getEmployeCode() {
        return employeCode;
    }

    public void setEmployeCode(String employeCode) {
        this.employeCode = employeCode;
    }

    public String getEmployeLevel() {
        return employeLevel;
    }

    public void setEmployeLevel(String employeLevel) {
        this.employeLevel = employeLevel;
    }

    public String getEmployeName() {
        return employeName;
    }

    public void setEmployeName(String employeName) {
        this.employeName = employeName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}