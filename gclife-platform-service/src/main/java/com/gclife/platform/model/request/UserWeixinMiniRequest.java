package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Auther: chenjinrong
 * @Date: 19-2-28 10:40
 * @Description:根据openid和appid查询用户是否已经注册
 */
public class UserWeixinMiniRequest {
    @ApiModelProperty(value = "appid", example = "小程序appid")
    private String appid;
    @ApiModelProperty(value = "openid", example = "小程序获取到的用户openid")
    private String openid;
    private String unionId;
    @ApiModelProperty(value = "branchId", example = "机构Id")
    private String branchId;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
}
