package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-12-5
 * description:
 */
public class BranchNameTreeResponse {
    @ApiModelProperty(example = "机构ID")
    private String branchId;
    @ApiModelProperty(example = "机构编码")
    private String branchCode;
    @ApiModelProperty(example = "机构名称")
    private String branchName;
    @ApiModelProperty(example = "机构短名称")
    private String branchShortname;
    @ApiModelProperty(example = "层级")
    private String depth;

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchShortname() {
        return branchShortname;
    }

    public void setBranchShortname(String branchShortname) {
        this.branchShortname = branchShortname;
    }

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }
}