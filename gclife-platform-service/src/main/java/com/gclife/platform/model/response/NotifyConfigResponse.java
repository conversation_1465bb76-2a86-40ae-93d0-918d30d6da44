package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-6-11
 * description:
 */
public class NotifyConfigResponse {
    @ApiModelProperty(example = "支付通知id")
    private String notifyConfigId;
    @ApiModelProperty(example = "缴费业务类型")
    private String businessType;
    @ApiModelProperty(example = "支付通知url")
    private String notifyUrl;

    public String getNotifyConfigId() {
        return notifyConfigId;
    }

    public void setNotifyConfigId(String notifyConfigId) {
        this.notifyConfigId = notifyConfigId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}
