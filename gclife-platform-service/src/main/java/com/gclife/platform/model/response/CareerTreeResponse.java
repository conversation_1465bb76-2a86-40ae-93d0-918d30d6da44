package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-1-6
 * description:
 */
public class CareerTreeResponse {
    @ApiModelProperty(example = "职业ID")
    private String careerId;
    @ApiModelProperty(example = "职业名称")
    private String careerName;
    @ApiModelProperty(example = "职业ID名称")
    private String careerIdName;
    @ApiModelProperty(example = "父级职业ID")
    private String parentCareerId;
    @ApiModelProperty(example = "层级")
    private String depth;

    public String getCareerId() {
        return careerId;
    }

    public void setCareerId(String careerId) {
        this.careerId = careerId;
    }

    public String getCareerName() {
        return careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getParentCareerId() {
        return parentCareerId;
    }

    public void setParentCareerId(String parentCareerId) {
        this.parentCareerId = parentCareerId;
    }

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }

    public String getCareerIdName() {
        return careerIdName;
    }

    public void setCareerIdName(String careerIdName) {
        this.careerIdName = careerIdName;
    }
}
