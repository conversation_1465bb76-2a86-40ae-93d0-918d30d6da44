package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Auther: chenjinrong
 * @Date: 19-2-28 15:54
 * @Description: 开放平台注册后的用户信息
 */
public class UserOpenPlatformRespone {
    @ApiModelProperty(example = "用户ID")
    public  String userId;

    @ApiModelProperty(example = "用户昵称")
    public String nickName;

    @ApiModelProperty(example = "用户头像")
    public String headImgUrl;

    @ApiModelProperty(value = "微信APPID", example = "gh_12333232")
    private String wechatAppId;

    @ApiModelProperty(value = "openid", example = "MALE")
    private String openid;

    @ApiModelProperty(value = "unionid", example = "MALE")
    private String unionid;

    @ApiModelProperty(value = "branchId", example = "机构Id")
    private String branchId;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public String getWechatAppId() {
        return wechatAppId;
    }

    public void setWechatAppId(String wechatAppId) {
        this.wechatAppId = wechatAppId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
}
