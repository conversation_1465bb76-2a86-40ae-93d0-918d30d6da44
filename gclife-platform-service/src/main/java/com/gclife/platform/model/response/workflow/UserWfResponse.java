package com.gclife.platform.model.response.workflow;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "User",description = "系统用户")
public class UserWfResponse extends BaseResponse {
    @ApiModelProperty(example = "用户ID")
    private  String userId;
    @ApiModelProperty(example = "账户名称")
    private String username;
    @ApiModelProperty(example = "用户姓名")
    private String name;
    @ApiModelProperty(example = "用户邮箱")
    private String email;
    @ApiModelProperty(example = "用户密码")
    private String password;

    /**角色集合*/
    private List<RoleWfResponse> listRoles=null;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public List<RoleWfResponse> getListRoles() {
        return listRoles;
    }

    public void setListRoles(List<RoleWfResponse> listRoles) {
        this.listRoles = listRoles;
    }
}
