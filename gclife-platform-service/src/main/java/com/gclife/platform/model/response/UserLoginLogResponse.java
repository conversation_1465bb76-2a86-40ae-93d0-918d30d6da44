package com.gclife.platform.model.response;

import com.gclife.platform.core.jooq.tables.pojos.UserLoginLogPo;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-12-17
 *         description:
 */
public class UserLoginLogResponse{
    @ApiModelProperty(example = "ID")
    private String userDeviceId;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "设备渠道ID")
    private String deviceChannelId;
    @ApiModelProperty(example = "设备ID")
    private String deviceTokens;
    @ApiModelProperty(example = "登录时间")
    private Long   loginDate;
    @ApiModelProperty(example = "设备类型")
    private String deviceTypeCode;

    public String getUserDeviceId() {
        return userDeviceId;
    }

    public void setUserDeviceId(String userDeviceId) {
        this.userDeviceId = userDeviceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceChannelId() {
        return deviceChannelId;
    }

    public void setDeviceChannelId(String deviceChannelId) {
        this.deviceChannelId = deviceChannelId;
    }

    public String getDeviceTokens() {
        return deviceTokens;
    }

    public void setDeviceTokens(String deviceTokens) {
        this.deviceTokens = deviceTokens;
    }

    public Long getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Long loginDate) {
        this.loginDate = loginDate;
    }

    public String getDeviceTypeCode() {
        return deviceTypeCode;
    }

    public void setDeviceTypeCode(String deviceTypeCode) {
        this.deviceTypeCode = deviceTypeCode;
    }
}