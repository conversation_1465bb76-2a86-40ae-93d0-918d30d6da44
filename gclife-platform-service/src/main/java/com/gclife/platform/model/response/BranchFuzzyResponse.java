package com.gclife.platform.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-12-5
 * description:
 */
public class BranchFuzzyResponse {
    @ApiModelProperty(example = "大中华人寿中国分公司深圳市公司")
    private String branchId;
    @ApiModelProperty(example = "大中华人寿中国分公司深圳市公司")
    @Internation(codeType = "BRANCH_NAME",filed = "branchId")
    private String branchName;

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
}