package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 5/20/2022
 */
@Getter
@Setter
@ApiModel(value = "操作角色", description = "操作角色")
public class OperationRoleResponse extends BaseResponse {
    @ApiModelProperty(value = "角色ID", example = "OSS_MANAGER_ROLE11")
    private String roleId;
    @ApiModelProperty(value = "角色名称编号", example = "ROLE_OSS_MANAGER")
    private String code;
    @ApiModelProperty(value = "角色名称国际化", example = "管理员")
    private String codeName;

    private String parentRoleId;
    private String name;
    private String description;
    private String createdUserId;
    private Long createdDate;
    private String updatedUserId;
    private Long updatedDate;
    private String enabled;
    private String baseSign;
}
