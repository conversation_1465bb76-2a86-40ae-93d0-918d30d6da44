package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 微信用户保存请求对象
 */
public class UserWeixinRelationResponse {

    @ApiModelProperty(value = "微信APPID", example = "gh_12333232")
    private String wechatAppId;
    @ApiModelProperty(value = "openid", example = "MALE")
    private String openid;
    @ApiModelProperty(value = "openid", example = "MALE")
    private String unionid;
    @ApiModelProperty(value = "用户ID", example = "MALE")
    private String userId;
    @ApiModelProperty(value = "微信昵称", example = "微信昵称")
    private String weixinName;

    public String getWechatAppId() {
        return wechatAppId;
    }

    public void setWechatAppId(String wechatAppId) {
        this.wechatAppId = wechatAppId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getWeixinName() {
        return weixinName;
    }

    public void setWeixinName(String weixinName) {
        this.weixinName = weixinName;
    }
}
