package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-13
 * description:
 */
public class AreaTreeResponse {
    @ApiModelProperty(example = "地区ID")
    private String areaId;
    @ApiModelProperty(example = "地区名称")
    private String areaName;
    @ApiModelProperty(example = "父级地区ID")
    private String parentAreaId;
    @ApiModelProperty(example = "层级")
    private String depth;

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getParentAreaId() {
        return parentAreaId;
    }

    public void setParentAreaId(String parentAreaId) {
        this.parentAreaId = parentAreaId;
    }

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }
}