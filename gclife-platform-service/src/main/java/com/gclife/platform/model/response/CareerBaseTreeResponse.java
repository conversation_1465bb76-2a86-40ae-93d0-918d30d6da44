package com.gclife.platform.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-7-18
 * description:
 */
@Data
public class CareerBaseTreeResponse {
    @ApiModelProperty(example = "职业ID")
    private String careerId;
    @ApiModelProperty(example = "职业名称")
    @Internation(filed = "careerId",codeType = "CAREER")
    private String careerName;
    @ApiModelProperty(example = "职业ID名称")
    private String careerIdName;
    @ApiModelProperty(example = "职业类别")
    private String careerType;
    @ApiModelProperty(example = "父级职业ID")
    private String parentCareerId;
    @ApiModelProperty(example = "层级")
    private String depth;
    private String providerId;
    @ApiModelProperty(example = "描述编码")
    private String describe;
    @ApiModelProperty(example = "描述名称")
    @Internation(filed = "describe",codeType = "CAREER_DESC")
    private String describeName;

    /**
     * 子职业
     */
    private List<CareerBaseTreeResponse> childs;

}
