package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-3
 *         description:
 */
public class BannerResponse{
    @ApiModelProperty(example = "海报ID",value = "001")
    private String bannerId;
    @ApiModelProperty(example = "海报名称",value = "广告")
    private String bannerName;
    @ApiModelProperty(example = "海报索引",value = "1")
    private String bannerIndex;
    @ApiModelProperty(example = "海报链接地址",value = "https://www.baidu.com/")
    private String bannerLinkUrl;
    @ApiModelProperty(example = "海报图片",value = "这是一幅图片")
    private String bannerImg;

    public String getBannerId() {
        return bannerId;
    }

    public void setBannerId(String bannerId) {
        this.bannerId = bannerId;
    }

    public String getBannerName() {
        return bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public String getBannerIndex() {
        return bannerIndex;
    }

    public void setBannerIndex(String bannerIndex) {
        this.bannerIndex = bannerIndex;
    }

    public String getBannerLinkUrl() {
        return bannerLinkUrl;
    }

    public void setBannerLinkUrl(String bannerLinkUrl) {
        this.bannerLinkUrl = bannerLinkUrl;
    }

    public String getBannerImg() {
        return bannerImg;
    }

    public void setBannerImg(String bannerImg) {
        this.bannerImg = bannerImg;
    }
}