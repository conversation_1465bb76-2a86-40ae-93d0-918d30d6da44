package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-9
 * description:
 */
public class AttendanceStatusRequest implements Comparable<AttendanceStatusRequest>{
    @ApiModelProperty(example = "天 ")
    private int day;
    @ApiModelProperty(example = "当月每天签到状态  已经签到 ALREADY_PUNCH_CARD  未签到 NOT_PUNCH_CARD ")
    private String status = "NOT_PUNCH_CARD";

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getDay() {

        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    @Override
    public int compareTo(AttendanceStatusRequest o) {
        return this.day-o.day;
    }
}