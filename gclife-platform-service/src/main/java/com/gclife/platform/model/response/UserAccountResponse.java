package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 17-11-9
 *         description:
 */
public class UserAccountResponse {
    @ApiModelProperty(example = "账户ID")
    private String userAccountId;
    @ApiModelProperty(example = "账户总额")
    private BigDecimal totalAmount;
    @ApiModelProperty(example = "已使用额度")
    private BigDecimal useAmount;
    @ApiModelProperty(example = "剩余金额")
    private BigDecimal residueAmount;
    @ApiModelProperty(example = "币种")
    private String currencyCode;
    @ApiModelProperty(example = "账户类型")
    private String userAccountTypeCode;
    @ApiModelProperty(example = "账户状态(解冻，冻结)")
    private String accountStatus;
    @ApiModelProperty(example = "冻结金额")
    private BigDecimal frozenAmount;
    @ApiModelProperty(example = "提现金额")
    private BigDecimal extractAmount;


    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public BigDecimal getResidueAmount() {
        return residueAmount;
    }

    public void setResidueAmount(BigDecimal residueAmount) {
        this.residueAmount = residueAmount;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getUserAccountTypeCode() {
        return userAccountTypeCode;
    }

    public void setUserAccountTypeCode(String userAccountTypeCode) {
        this.userAccountTypeCode = userAccountTypeCode;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(String userAccountId) {
        this.userAccountId = userAccountId;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }

    public BigDecimal getExtractAmount() {
        return extractAmount;
    }

    public void setExtractAmount(BigDecimal extractAmount) {
        this.extractAmount = extractAmount;
    }
}
