package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-28
 * description:
 */
public class AreaNameResponse {
    @ApiModelProperty(example = "地址ID")
    private String areaId;
    @ApiModelProperty(example = "广东省深圳市南山区")
    private String areaName;
    @ApiModelProperty(example = "广东省深圳市(省会/城市）")
    private String capitalName;

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCapitalName() {
        return capitalName;
    }

    public void setCapitalName(String capitalName) {
        this.capitalName = capitalName;
    }
}