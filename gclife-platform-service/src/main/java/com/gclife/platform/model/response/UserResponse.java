package com.gclife.platform.model.response;


import com.gclife.common.annotation.DateFormat;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.config.DateFormatPatternEnum;
import com.gclife.platform.core.jooq.tables.pojos.UserThirdPartyPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 *
 * <AUTHOR>
 */
@ApiModel(value = "User", description = "系统用户")
public class UserResponse extends BaseResponse {
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "账户名称")
    private String username;
    @ApiModelProperty(example = "用户姓名")
    private String name;
    @ApiModelProperty(example = "用户邮箱")
    private String email;
    @ApiModelProperty(example = "使用的语言")
    private String language;
    @ApiModelProperty(example = "手机区号")
    private String countryCode;
    @ApiModelProperty(example = "手机号码")
    private String mobile;
    @ApiModelProperty(example = "注册渠道")
    private String deviceChannelId;
    @ApiModelProperty(example = "用户状态")
    private String enabled;
    /**
     * 用户状态 启用禁用 国际化名称
     */
    @ApiModelProperty(example = "用户状态 启用禁用 国际化名称")
    private String enabledName;

    /**
     * 登录次数
     */
    @ApiModelProperty(example = "登录次数")
    private Long loginCount;

    @ApiModelProperty(example = "所属机构")
    private String branchId;

    @ApiModelProperty(example = "用户所属机构")
    private String branchName;
    /**
     * 最近登录时间
     */
    @ApiModelProperty(example = "最近登录时间")
    private Long loginLast;
    @ApiModelProperty(example = "最近登录时间")
    @DateFormat(filed = "loginLast", pattern = DateFormatPatternEnum.FORMATE20)
    private String loginLastFormat;

    @ApiModelProperty(example = "第一次登录时间")
    private Long firstLogin;

    @ApiModelProperty(example = "创建时间")
    private Long createdDate;


    /**
     * 微信信息
     **/
    private UserWeixinRelationResponse userWeixin;
    /**
     * APP信息
     **/
    private UserAppDeviceResponse userAppDevice;

    /**
     * 钉钉信息
     **/
    private UserDingRelationResponse userDing;

    /**
     * 第三方账户信息
     */
    private List<UserThirdPartyResponse> listThirdParty;

    public String getEnabledName() {
        return enabledName;
    }

    public void setEnabledName(String enabledName) {
        this.enabledName = enabledName;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Long getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Long loginCount) {
        this.loginCount = loginCount;
    }

    public Long getLoginLast() {
        return loginLast;
    }

    public void setLoginLast(Long loginLast) {
        this.loginLast = loginLast;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public UserWeixinRelationResponse getUserWeixin() {
        return userWeixin;
    }

    public void setUserWeixin(UserWeixinRelationResponse userWeixin) {
        this.userWeixin = userWeixin;
    }

    public UserAppDeviceResponse getUserAppDevice() {
        return userAppDevice;
    }

    public void setUserAppDevice(UserAppDeviceResponse userAppDevice) {
        this.userAppDevice = userAppDevice;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getDeviceChannelId() {
        return deviceChannelId;
    }

    public void setDeviceChannelId(String deviceChannelId) {
        this.deviceChannelId = deviceChannelId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }


    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public UserDingRelationResponse getUserDing() {
        return userDing;
    }

    public void setUserDing(UserDingRelationResponse userDing) {
        this.userDing = userDing;
    }

    public Long getFirstLogin() {
        return firstLogin;
    }

    public void setFirstLogin(Long firstLogin) {
        this.firstLogin = firstLogin;
    }

    public Long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Long createdDate) {
        this.createdDate = createdDate;
    }

    public String getLoginLastFormat() {
        return loginLastFormat;
    }

    public void setLoginLastFormat(String loginLastFormat) {
        this.loginLastFormat = loginLastFormat;
    }

    public List<UserThirdPartyResponse> getListThirdParty() {
        return listThirdParty;
    }

    public void setListThirdParty(List<UserThirdPartyResponse> listThirdParty) {
        this.listThirdParty = listThirdParty;
    }
}
