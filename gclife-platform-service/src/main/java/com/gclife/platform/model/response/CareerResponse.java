package com.gclife.platform.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码返回信息
 */
@ApiModel(value = "Career",description = "职业代码")
public class CareerResponse extends BaseResponse{
    @ApiModelProperty(example = "职业类别ID")
    private String careerId;
    @ApiModelProperty(example = "职业类别编码")
    private String careerCode;
    @ApiModelProperty(example = "职业类别名称")
    @Internation(filed = "careerId",codeType = "CAREER")
    private String careerName;
    @ApiModelProperty(example = "职业ID名称")
    private String careerIdName;
    @ApiModelProperty(example = "父职级类别ID")
    private String parentCareerId;
    @ApiModelProperty(example = "职业类别")
    private String careerType;
    @ApiModelProperty(example = "职业类别索引")
    private String careerIndex;
    @ApiModelProperty(example = "保险公司ID")
    private String providerId;
    @ApiModelProperty(example = "下一层级数量")
    private String existChild;
    @ApiModelProperty(example = "层级")
    private String depth;

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }

    public String getProviderId() {
        return providerId;
    }

    public void setProviderId(String providerId) {
        this.providerId = providerId;
    }

    public String getCareerId() {
        return careerId;
    }

    public void setCareerId(String careerId) {
        this.careerId = careerId;
    }

    public String getCareerCode() {
        return careerCode;
    }

    public void setCareerCode(String careerCode) {
        this.careerCode = careerCode;
    }

    public String getCareerName() {
        return careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getParentCareerId() {
        return parentCareerId;
    }

    public void setParentCareerId(String parentCareerId) {
        this.parentCareerId = parentCareerId;
    }

    public String getCareerType() {
        return careerType;
    }

    public void setCareerType(String careerType) {
        this.careerType = careerType;
    }

    public String getCareerIndex() {
        return careerIndex;
    }

    public void setCareerIndex(String careerIndex) {
        this.careerIndex = careerIndex;
    }

    public String getExistChild() {
        return existChild;
    }

    public void setExistChild(String existChild) {
        this.existChild = existChild;
    }

    public String getCareerIdName() {
        return careerIdName;
    }

    public void setCareerIdName(String careerIdName) {
        this.careerIdName = careerIdName;
    }
}