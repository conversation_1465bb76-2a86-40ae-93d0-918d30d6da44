package com.gclife.platform.model.response.workflow;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 *         create 17-10-26
 *         description:
 */
@ApiModel(value = "Users", description = "筛选用户")
public class UsersWfResponse extends BaseResponse {
    @ApiModelProperty(example = "用户ID")
    private List<String> userId;

    public List<String> getUserId() {
        return userId;
    }

    public void setUserId(List<String> userId) {
        this.userId = userId;
    }
}