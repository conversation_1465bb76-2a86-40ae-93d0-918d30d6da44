package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:授权账户信息表返回数据
 */
@Data
@ApiModel(value = "Account", description = "授权账户信息")
public class AccountResponse extends BaseResponse {
    @ApiModelProperty(example = "授权帐号ID")
    private String accountId;
    @ApiModelProperty(example = "账户用途类: I付费，O退费")
    private String useType;
    @ApiModelProperty(example = "账户号码")
    private String accountNo;
    @ApiModelProperty(example = "银行代码")
    private String bankCode;
    @ApiModelProperty(example = "支行名称")
    private String subbranch;
    @ApiModelProperty(example = "账户持有人")
    private String accountOwner;
    @ApiModelProperty(example = "账户持有人证件类型")
    private String idType;
    @ApiModelProperty(example = "账户持有人证件号码")
    private String idNo;
    @ApiModelProperty(example = "账户类型【活期储蓄账户/借记卡】")
    private String accountType;
    @ApiModelProperty(example = "账户持有人签名是否一致")
    private String acctuserSignStatus;
    @ApiModelProperty(example = "授权日期")
    private Long authorizedDate;
    @ApiModelProperty(example = "有效标识(effective:有效，invalid:失效)")
    private String validFlag;
    @ApiModelProperty(example = "创建人")
    private String createdUserId;
    @ApiModelProperty(example = "创建时间")
    private Long createdDate;
    @ApiModelProperty(example = "更新人")
    private String updatedUserId;
    @ApiModelProperty(example = "更新时间")
    private Long updatedDate;
    @ApiModelProperty(example = "开户行所在地区编码")
    private String areaCode;
    @ApiModelProperty(example = "主附卡标识")
    private String primaryFlag;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "银行卡类型")
    private String accountTypeCode;
    @ApiModelProperty(example = "银行卡审核状态")
    private String auditStatus;
    @ApiModelProperty(example = "银行卡审核状态名称")
    private String auditStatusName;
    @ApiModelProperty(example = "银行卡附件正面")
    private String bankFrontAttachId;
    @ApiModelProperty(example = "银行卡附件反面")
    private String bankBackAttachId;
    @ApiModelProperty(example = "失败原因code")
    private String failureReasonCode;
    @ApiModelProperty(example = "失败原因")
    private String failureReason;

}