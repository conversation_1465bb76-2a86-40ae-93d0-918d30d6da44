package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 2018/8/14
 * description:
 */

public class UserLoginPasswordResetRequest {
    @ApiModelProperty(value = "用户名", example = "Alice", required = true)
    private String userName;

    @ApiModelProperty(value = "新设的密码", example = "alice1234", required = true)
    private String password;

    @ApiModelProperty(value = "验证码", example = "1234", required = true)
    private String verifyCode;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }
}
