package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * author deep
 * create 17-11-11
 * description
 */

@ApiModel(value = "AccountDetail",description = "账户明细")
public class UserAccountDetailResponse extends BaseResponse {
    @ApiModelProperty(example = "账户ID",value="********")
    private String userAccountId;

    @ApiModelProperty(example = "金额",value = "200")
    private String  amount;

    @ApiModelProperty(example = "更新时间",value = "********")
    private String  updatedDate;

    @ApiModelProperty(example = "状态",value = "提现")
    private String  status;

    @ApiModelProperty(example = "业务类型",value = "业务类型")
    private String  businessTypeCode;

    @ApiModelProperty(example = "利益相关",value = "利益相关")
    private String  interestRelate;

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    public String getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(String userAccountId) {
        this.userAccountId = userAccountId;
    }

    public String getBusinessTypeCode() {
        return businessTypeCode;
    }

    public void setBusinessTypeCode(String businessTypeCode) {
        this.businessTypeCode = businessTypeCode;
    }

    public String getInterestRelate() {
        return interestRelate;
    }

    public void setInterestRelate(String interestRelate) {
        this.interestRelate = interestRelate;
    }

}