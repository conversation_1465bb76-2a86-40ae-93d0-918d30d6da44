package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
@Data
public class UserBaseResponse extends BaseResponse {
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "用户名")
    private String username;
    @ApiModelProperty(example = "启用标识")
    private String enabled;
    @ApiModelProperty(example = "姓名")
    private String name;
    @ApiModelProperty(example = "性别")
    private String gender;
    @ApiModelProperty(example = "电子邮箱")
    private String email;
    @ApiModelProperty(example = "最后登录时间")
    private Long   lastLogin;
    @ApiModelProperty(example = "语言")
    private String language;
    @ApiModelProperty(example = "手机号")
    private String mobile;
    @ApiModelProperty(example = "昵称")
    private String nickName;
    @ApiModelProperty(example = "区号")
    private String countryCode;
    @ApiModelProperty(example = "注册渠道")
    private String deviceChannelId;
    @ApiModelProperty(example = "密码修改标识")
    private String passwordFlag;
}
