package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccountQueryResponse{
    @ApiModelProperty(example = "授权帐号ID")
    private String accountId;
    @ApiModelProperty(value = "所属机构ID", example = "*********")
    private String branchId;
    @ApiModelProperty(value = "所属机构", example = "*********")
    private String branchName;
    @ApiModelProperty(value = "业务员姓名", example = "*********")
    private String agentName;
    @ApiModelProperty(value = "业务员手机号", example = "*********")
    private String agentMobile;
    @ApiModelProperty(value = "开户行编码", example = "*********")
    private String bankCode;
    @ApiModelProperty(value = "开户行名称", example = "*********")
    private String bankName;
    @ApiModelProperty(example = "账户持有人")
    private String accountOwner;
    @ApiModelProperty(example = "账户号码")
    private String accountNo;
    @ApiModelProperty(example = "申请时间")
    private String auditDate;
    @ApiModelProperty(example = "主附卡标识")
    private String primaryFlag;
    @ApiModelProperty(example = "银行卡附件正面")
    private String bankFrontAttachId;
    @ApiModelProperty(example = "银行卡附件反面")
    private String bankBackAttachId;
}
