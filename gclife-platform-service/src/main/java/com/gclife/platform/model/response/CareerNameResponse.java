package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-1-6
 * description:
 */
public class CareerNameResponse {
    @ApiModelProperty(example = "职业id")
    private String careerId;
    @ApiModelProperty(example = "IT/系统工程师")
    private String careerName;
    @ApiModelProperty(example = "职业ID名称")
    private String careerIdName;

    public String getCareerName() {
        return careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getCareerId() {
        return careerId;
    }

    public void setCareerId(String careerId) {
        this.careerId = careerId;
    }

    public String getCareerIdName() {
        return careerIdName;
    }

    public void setCareerIdName(String careerIdName) {
        this.careerIdName = careerIdName;
    }
}
