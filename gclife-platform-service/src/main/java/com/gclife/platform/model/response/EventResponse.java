package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-2-26
 * description:
 */
public class EventResponse {
    @ApiModelProperty(example = "事件ID")
    private String eventId;
    @ApiModelProperty(example = "事件关联人昵称")
    private String nickName;
    @ApiModelProperty(example = "事件关联人头像")
    private String headUrl;
    @ApiModelProperty(example = "点赞数")
    private long likeNumber;
    @ApiModelProperty(example = "转发数")
    private long forwardNumber;
    @ApiModelProperty(example = "模板编码")
    private String templateCode;
    @ApiModelProperty(example = "模板url")
    private String templateImageUrl;


    @ApiModelProperty(example = "产品名称")
    private String productName;
    @ApiModelProperty(example = "团队长")
    private String teamLeader;
    @ApiModelProperty(example = "保险公司")
    private String insuranceName;
    @ApiModelProperty(example = "成交金额")
    private String premium;
    @ApiModelProperty(example = "承保时间")
    private String approveDate;
    @ApiModelProperty(example = "推荐人")
    private String recommendAgentName;
    @ApiModelProperty(example = "被推荐人")
    private String refereeAgentName;
    @ApiModelProperty(example = "增员时间")
    private String increaseDate;
    @ApiModelProperty(example = "计划书时间")
    private String planDate;
    @ApiModelProperty(example = "客户名称")
    private String customerName;
    @ApiModelProperty(example = "交单时间")
    private String insureDate;

    @ApiModelProperty(example = "团队成员人数")
    private String teamNumber;
    @ApiModelProperty(example = "团队成员头像列表(List<String>)")
    private List<String> headUrls;

    @ApiModelProperty(example = "事件动态列表(List<String>)")
    private List<String> eventContents;
    @ApiModelProperty(example = "点赞人头像列表(List<String>)")
    private List<String> likeHeadUrls;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public long getLikeNumber() {
        return likeNumber;
    }

    public void setLikeNumber(long likeNumber) {
        this.likeNumber = likeNumber;
    }

    public long getForwardNumber() {
        return forwardNumber;
    }

    public void setForwardNumber(long forwardNumber) {
        this.forwardNumber = forwardNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTeamLeader() {
        return teamLeader;
    }

    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }

    public String getPremium() {
        return premium;
    }

    public void setPremium(String premium) {
        this.premium = premium;
    }

    public String getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(String approveDate) {
        this.approveDate = approveDate;
    }

    public String getRecommendAgentName() {
        return recommendAgentName;
    }

    public void setRecommendAgentName(String recommendAgentName) {
        this.recommendAgentName = recommendAgentName;
    }

    public String getRefereeAgentName() {
        return refereeAgentName;
    }

    public void setRefereeAgentName(String refereeAgentName) {
        this.refereeAgentName = refereeAgentName;
    }

    public String getIncreaseDate() {
        return increaseDate;
    }

    public void setIncreaseDate(String increaseDate) {
        this.increaseDate = increaseDate;
    }

    public String getPlanDate() {
        return planDate;
    }

    public void setPlanDate(String planDate) {
        this.planDate = planDate;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getTeamNumber() {
        return teamNumber;
    }

    public void setTeamNumber(String teamNumber) {
        this.teamNumber = teamNumber;
    }

    public List<String> getHeadUrls() {
        return headUrls;
    }

    public void setHeadUrls(List<String> headUrls) {
        this.headUrls = headUrls;
    }

    public List<String> getEventContents() {
        return eventContents;
    }

    public void setEventContents(List<String> eventContents) {
        this.eventContents = eventContents;
    }

    public List<String> getLikeHeadUrls() {
        return likeHeadUrls;
    }

    public void setLikeHeadUrls(List<String> likeHeadUrls) {
        this.likeHeadUrls = likeHeadUrls;
    }

    public String getInsuranceName() {
        return insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    public String getInsureDate() {
        return insureDate;
    }

    public void setInsureDate(String insureDate) {
        this.insureDate = insureDate;
    }

    public String getTemplateImageUrl() {
        return templateImageUrl;
    }

    public void setTemplateImageUrl(String templateImageUrl) {
        this.templateImageUrl = templateImageUrl;
    }
}
