package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version v2.0
 * Description: 客户同步
 * @date 18-10-8
 */
public class CustomerSynchResponse {
    @ApiModelProperty(example = "客户ID")
    private String customerId;
    @ApiModelProperty(example = "旧客户版本号")
    private String oldVersionNo;
    @ApiModelProperty(example = "新客户版本号")
    private String newVersionNo;

    public CustomerSynchResponse() {}

    public CustomerSynchResponse(String customerId, String oldVersionNo, String newVersionNo) {
        this.customerId = customerId;
        this.oldVersionNo = oldVersionNo;
        this.newVersionNo = newVersionNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOldVersionNo() {
        return oldVersionNo;
    }

    public void setOldVersionNo(String oldVersionNo) {
        this.oldVersionNo = oldVersionNo;
    }

    public String getNewVersionNo() {
        return newVersionNo;
    }

    public void setNewVersionNo(String newVersionNo) {
        this.newVersionNo = newVersionNo;
    }
}
