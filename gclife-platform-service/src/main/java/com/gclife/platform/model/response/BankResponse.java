package com.gclife.platform.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:银行信息返回数据
 */
@Data
@ApiModel(value = "Bank", description = "银行信息")
public class BankResponse extends BaseResponse {
    @ApiModelProperty(example = "银行主键ID")
    private String bankId;
    @ApiModelProperty(example = "银行编码")
    private String bankCode;
    @ApiModelProperty(example = "银行名称")
    @Internation(codeType = "BANK", filed = "bankCode")
    private String bankName;
    @ApiModelProperty(example = "银行简称")
    private String bankShortname;
    @ApiModelProperty(example = "排序值")
    private String bankIndex;
    @ApiModelProperty(example = "备注")
    private String remark;
    //增加相应的公司银行账号名称及号码(sprint-v4.1.0.********)
    @ApiModelProperty(example = "银行账号")
    private String accountNo;
    @ApiModelProperty(example = "账号名称")
    private String accountName;
}