package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.base.Resources;
import com.gclife.platform.model.bo.MenuResourcesBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "GrantedSubMenu",description = "查看有权限的子菜单")
public class GrantedSubMenuResponse extends BaseResponse {

    @ApiModelProperty(example = "有权限的子菜单集合")
    List<MenuResourcesBo> grantedSubmenu;

    @ApiModelProperty(example = "服务名")
    String serviceName;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public List<MenuResourcesBo> getGrantedSubmenu() {
        return grantedSubmenu;
    }

    public void setGrantedSubmenu(List<MenuResourcesBo> grantedSubmenu) {
        this.grantedSubmenu = grantedSubmenu;
    }

}
