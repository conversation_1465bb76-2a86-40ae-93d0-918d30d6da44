package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public class CustomerMessagesResponse {

    /**
     * 客户ID
     */
    @ApiModelProperty(example = "客户ID")
    private String customerId;
    /**
     * 头像
     */
    @ApiModelProperty(example = "头像")
    private String avatar;
    /**
     * 客户姓名
     */
    @ApiModelProperty(example = "客户姓名")
    private String name;
    /**
     * 准客户
     */
    @ApiModelProperty(example = "准客户 QUASI_CUSTOMER")
    private String quasiCustomer;
    /**
     * 客户
     */
    @ApiModelProperty(example = "客户 CUSTOMER")
    private String customer;
    /**
     * 客户手机号
     */
    @ApiModelProperty(example = "客户手机号")
    private String phone;
    /**
     * 受益人
     */
    @ApiModelProperty(example = "受益人 BENEFICIARY")
    private String beneficiary;
    /**
     * 被保人
     */
    @ApiModelProperty(example = "被保人 INSURED")
    private String insured;
    /**
     * 投保人
     */
    @ApiModelProperty(example = "投保人 APPLICANT")
    private String applicant;
    /**
     * 分组  编码
     */
    @ApiModelProperty(example = "分组编码")
    private String groupCode;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getQuasiCustomer() {
        return quasiCustomer;
    }

    public void setQuasiCustomer(String quasiCustomer) {
        this.quasiCustomer = quasiCustomer;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public String getInsured() {
        return insured;
    }

    public void setInsured(String insured) {
        this.insured = insured;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
}