package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import com.gclife.platform.core.jooq.tables.pojos.BranchLevelPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:32
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构返回信息
 * <AUTHOR>
 */
@ApiModel(value = "BranchLevel",description = "机构层级")
public class BranchLevelResponse extends BaseResponse {
    @ApiModelProperty(example = "层级ID")
    private String branchLevelId;
    @ApiModelProperty(example = "层级编码")
    private String branchLevelCode;
    @ApiModelProperty(example = "层级num")
    private Long   branchLevelNum;
    @ApiModelProperty(example = "层级名称")
    private String branchLevelName;
    @ApiModelProperty(example = "最大值")
    private Long   maxValue;

    public String getBranchLevelId() {
        return branchLevelId;
    }

    public void setBranchLevelId(String branchLevelId) {
        this.branchLevelId = branchLevelId;
    }

    public String getBranchLevelCode() {
        return branchLevelCode;
    }

    public void setBranchLevelCode(String branchLevelCode) {
        this.branchLevelCode = branchLevelCode;
    }

    public Long getBranchLevelNum() {
        return branchLevelNum;
    }

    public void setBranchLevelNum(Long branchLevelNum) {
        this.branchLevelNum = branchLevelNum;
    }

    public String getBranchLevelName() {
        return branchLevelName;
    }

    public void setBranchLevelName(String branchLevelName) {
        this.branchLevelName = branchLevelName;
    }

    public Long getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Long maxValue) {
        this.maxValue = maxValue;
    }
}