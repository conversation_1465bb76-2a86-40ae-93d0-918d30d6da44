/*
 * This file is generated by jOOQ.
*/
package com.gclife.platform.model.response;


import com.gclife.common.model.pojo.BasePojo;
import io.swagger.annotations.ApiModelProperty;

import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 用户设备tokens表
 */
public class UserAppDeviceResponse{

    @ApiModelProperty(example = "主键")
    private String userAppDeviceId;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "消息token")
    private String deviceTokens;
    @ApiModelProperty(example = "是否在线")
    private String isOnline;
    @ApiModelProperty(example = "设备类型(IOS,ANDROID)")
    private String deviceTypeCode;
    @ApiModelProperty(example = "设备渠道")
    private String deviceChannelId;

    public String getUserAppDeviceId() {
        return userAppDeviceId;
    }

    public void setUserAppDeviceId(String userAppDeviceId) {
        this.userAppDeviceId = userAppDeviceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceTokens() {
        return deviceTokens;
    }

    public void setDeviceTokens(String deviceTokens) {
        this.deviceTokens = deviceTokens;
    }

    public String getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(String isOnline) {
        this.isOnline = isOnline;
    }

    public String getDeviceTypeCode() {
        return deviceTypeCode;
    }

    public void setDeviceTypeCode(String deviceTypeCode) {
        this.deviceTypeCode = deviceTypeCode;
    }

    public String getDeviceChannelId() {
        return deviceChannelId;
    }

    public void setDeviceChannelId(String deviceChannelId) {
        this.deviceChannelId = deviceChannelId;
    }
}
