package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午9:17
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 渠道bo
 * <AUTHOR>
 */
@ApiModel(value = "Channel",description = "系统销售渠道")
public class ChannelsResponse {

    @ApiModelProperty(example = "渠道编码")
    private String channelTypeCode;

    @ApiModelProperty(example = "渠道名称")
    private String channelTypeName;

    public String getChannelTypeCode() {
        return channelTypeCode;
    }

    public void setChannelTypeCode(String channelTypeCode) {
        this.channelTypeCode = channelTypeCode;
    }

    public String getChannelTypeName() {
        return channelTypeName;
    }

    public void setChannelTypeName(String channelTypeName) {
        this.channelTypeName = channelTypeName;
    }
}