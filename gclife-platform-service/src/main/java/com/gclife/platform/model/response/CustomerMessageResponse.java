package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
public class CustomerMessageResponse {

    @ApiModelProperty(example = "客户主键ID")
    private String customerId;
    @ApiModelProperty(example = "客户版本")
    private String versionNo;
    @ApiModelProperty(example = "客户号")
    private String customerNo;
    @ApiModelProperty(example = "姓名")
    private String name;
    @ApiModelProperty(example = "证件类型")
    private String idType;
    @ApiModelProperty(example = "证件类型名称")
    private String idTypeName;
    @ApiModelProperty(example = "性别")
    private String sex;
    @ApiModelProperty(example = "性别名称")
    private String sexName;
    @ApiModelProperty(example = "出生日期")
    private Long birthday;
    @ApiModelProperty(example = "证件号码")
    private String idNo;
    @ApiModelProperty(example = "证件有效期")
    private Long idExpDate;
    @ApiModelProperty(example = "通讯地址")
    private String postalAddress;
    @ApiModelProperty(example = "通讯邮编")
    private String zipCode;
    @ApiModelProperty(example = "通讯电话")
    private String phone;
    @ApiModelProperty(example = "通讯传真")
    private String fax;
    @ApiModelProperty(example = "家庭地址")
    private String homeAddress;
    @ApiModelProperty(example = "家庭邮编")
    private String homeZipCode;
    @ApiModelProperty(example = "家庭电话")
    private String homePhone;
    @ApiModelProperty(example = "家庭传真")
    private String homeFax;
    @ApiModelProperty(example = "国籍")
    private String nationality;
    @ApiModelProperty(example = "民族")
    private String nations;
    @ApiModelProperty(example = "户口所在地")
    private String registerAddress;
    @ApiModelProperty(example = "婚姻状况")
    private String marriage;
    @ApiModelProperty(example = "健康状况")
    private String health;
    @ApiModelProperty(example = "身高")
    private String stature;
    @ApiModelProperty(example = "体重")
    private String avoirdupois;
    @ApiModelProperty(example = "学历")
    private String degree;
    @ApiModelProperty(example = "信用等级")
    private String creditGrade;
    @ApiModelProperty(example = "是否吸烟标志")
    private String smokeFlag;
    @ApiModelProperty(example = "身体指标")
    private String bmi;
    @ApiModelProperty(example = "驾照")
    private String license;
    @ApiModelProperty(example = "驾照类型")
    private String licenseType;
    @ApiModelProperty(example = "职业类别")
    private String occupationType;
    @ApiModelProperty(example = "职业代码")
    private String occupationCode;
    @ApiModelProperty(example = "职业（工种）")
    private String workType;
    @ApiModelProperty(example = "兼职（工种）")
    private String pluralityType;
    @ApiModelProperty(example = "工资")
    private String salary;
    @ApiModelProperty(example = "是否有社保标志")
    private String socialSecurity;
    @ApiModelProperty(example = "单位地址")
    private String belongsCompanyAddress;
    @ApiModelProperty(example = "单位邮编")
    private String belongsCompanyZipCode;
    @ApiModelProperty(example = "单位电话")
    private String belongsCompanyPhone;
    @ApiModelProperty(example = "单位传真")
    private String belongsCompanyFax;
    @ApiModelProperty(example = "入司日期")
    private Long joinCompanyDate;
    @ApiModelProperty(example = "参加工作日期")
    private Long startWorkDate;
    @ApiModelProperty(example = "职位")
    private String position;
    @ApiModelProperty(example = "年收入")
    private String income;
    @ApiModelProperty(example = "年收入来源")
    private String incomeSource;
    @ApiModelProperty(example = "家庭收入")
    private String familyIncome;
    @ApiModelProperty(example = "家庭收入来源")
    private String familyIncomeSource;
    @ApiModelProperty(example = "银行编码")
    private String bankCode;
    @ApiModelProperty(example = "银行帐号")
    private String bankAccountNo;
    @ApiModelProperty(example = "银行帐户名")
    private String bankAccountName;
    @ApiModelProperty(example = "单位名词")
    private String companyName;
    @ApiModelProperty(example = "单位证件类型")
    private String companyIdType;
    @ApiModelProperty(example = "单位证件号码")
    private String companyIdNo;
    @ApiModelProperty(example = "单位地址")
    private String companyAddress;
    @ApiModelProperty(example = "单位邮编")
    private String companyZipCode;
    @ApiModelProperty(example = "单位电话")
    private String companyPhone;
    @ApiModelProperty(example = "单位传真")
    private String companyFax;
    @ApiModelProperty(example = "单位联系人")
    private String companyContractName;
    @ApiModelProperty(example = "单位联系人手机")
    private String companyContractMobile;
    @ApiModelProperty(example = "单位联系人地址")
    private String companyContractAddress;
    @ApiModelProperty(example = "其它电话号码")
    private String otherPhone;
    @ApiModelProperty(example = "客户主键ID")
    private String email;
    @ApiModelProperty(example = "邮件地址")
    private String mobile;
    @ApiModelProperty(example = "单位联系人电话")
    private String companyContractPhone;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "头像")
    private String avatar;
    @ApiModelProperty(example = "家庭地址地区编码")
    private String homeAreaCode;
    @ApiModelProperty(example = "家庭地址地区名称")
    private String homeAreaCodeName;
    @ApiModelProperty(example = "单位地址地区编码")
    private String belongsCompanyAreaCode;
    @ApiModelProperty(example = "单位地址地区编码")
    private String companyAreaCode;
    @ApiModelProperty(example = "分组编号")
    private String groupCode;
    @ApiModelProperty(example = "公司类型")
    private String companyType;
    @ApiModelProperty(value = "微信号",example = "微信号")
    private String wechatNo;

    @ApiModelProperty(example = "脸书号",required = true)
    private String facebookNo;

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getWechatNo() {
        return wechatNo;
    }

    public void setWechatNo(String wechatNo) {
        this.wechatNo = wechatNo;
    }

    public String getFacebookNo() {
        return facebookNo;
    }

    public void setFacebookNo(String facebookNo) {
        this.facebookNo = facebookNo;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Long getBirthday() {
        return birthday;
    }

    public void setBirthday(Long birthday) {
        this.birthday = birthday;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public Long getIdExpDate() {
        return idExpDate;
    }

    public void setIdExpDate(Long idExpDate) {
        this.idExpDate = idExpDate;
    }

    public String getPostalAddress() {
        return postalAddress;
    }

    public void setPostalAddress(String postalAddress) {
        this.postalAddress = postalAddress;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getHomeZipCode() {
        return homeZipCode;
    }

    public void setHomeZipCode(String homeZipCode) {
        this.homeZipCode = homeZipCode;
    }

    public String getHomePhone() {
        return homePhone;
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    public String getHomeFax() {
        return homeFax;
    }

    public void setHomeFax(String homeFax) {
        this.homeFax = homeFax;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNations() {
        return nations;
    }

    public void setNations(String nations) {
        this.nations = nations;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getHealth() {
        return health;
    }

    public void setHealth(String health) {
        this.health = health;
    }

    public String getStature() {
        return stature;
    }

    public void setStature(String stature) {
        this.stature = stature;
    }

    public String getAvoirdupois() {
        return avoirdupois;
    }

    public void setAvoirdupois(String avoirdupois) {
        this.avoirdupois = avoirdupois;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getCreditGrade() {
        return creditGrade;
    }

    public void setCreditGrade(String creditGrade) {
        this.creditGrade = creditGrade;
    }

    public String getSmokeFlag() {
        return smokeFlag;
    }

    public void setSmokeFlag(String smokeFlag) {
        this.smokeFlag = smokeFlag;
    }

    public String getBmi() {
        return bmi;
    }

    public void setBmi(String bmi) {
        this.bmi = bmi;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getOccupationType() {
        return occupationType;
    }

    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    public String getOccupationCode() {
        return occupationCode;
    }

    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    public String getWorkType() {
        return workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }

    public String getPluralityType() {
        return pluralityType;
    }

    public void setPluralityType(String pluralityType) {
        this.pluralityType = pluralityType;
    }

    public String getSalary() {
        return salary;
    }

    public void setSalary(String salary) {
        this.salary = salary;
    }

    public String getSocialSecurity() {
        return socialSecurity;
    }

    public void setSocialSecurity(String socialSecurity) {
        this.socialSecurity = socialSecurity;
    }

    public String getBelongsCompanyAddress() {
        return belongsCompanyAddress;
    }

    public void setBelongsCompanyAddress(String belongsCompanyAddress) {
        this.belongsCompanyAddress = belongsCompanyAddress;
    }

    public String getBelongsCompanyZipCode() {
        return belongsCompanyZipCode;
    }

    public void setBelongsCompanyZipCode(String belongsCompanyZipCode) {
        this.belongsCompanyZipCode = belongsCompanyZipCode;
    }

    public String getBelongsCompanyPhone() {
        return belongsCompanyPhone;
    }

    public void setBelongsCompanyPhone(String belongsCompanyPhone) {
        this.belongsCompanyPhone = belongsCompanyPhone;
    }

    public String getBelongsCompanyFax() {
        return belongsCompanyFax;
    }

    public void setBelongsCompanyFax(String belongsCompanyFax) {
        this.belongsCompanyFax = belongsCompanyFax;
    }

    public Long getJoinCompanyDate() {
        return joinCompanyDate;
    }

    public void setJoinCompanyDate(Long joinCompanyDate) {
        this.joinCompanyDate = joinCompanyDate;
    }

    public Long getStartWorkDate() {
        return startWorkDate;
    }

    public void setStartWorkDate(Long startWorkDate) {
        this.startWorkDate = startWorkDate;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public String getIncomeSource() {
        return incomeSource;
    }

    public void setIncomeSource(String incomeSource) {
        this.incomeSource = incomeSource;
    }

    public String getFamilyIncome() {
        return familyIncome;
    }

    public void setFamilyIncome(String familyIncome) {
        this.familyIncome = familyIncome;
    }

    public String getFamilyIncomeSource() {
        return familyIncomeSource;
    }

    public void setFamilyIncomeSource(String familyIncomeSource) {
        this.familyIncomeSource = familyIncomeSource;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyIdType() {
        return companyIdType;
    }

    public void setCompanyIdType(String companyIdType) {
        this.companyIdType = companyIdType;
    }

    public String getCompanyIdNo() {
        return companyIdNo;
    }

    public void setCompanyIdNo(String companyIdNo) {
        this.companyIdNo = companyIdNo;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyZipCode() {
        return companyZipCode;
    }

    public void setCompanyZipCode(String companyZipCode) {
        this.companyZipCode = companyZipCode;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyFax() {
        return companyFax;
    }

    public void setCompanyFax(String companyFax) {
        this.companyFax = companyFax;
    }

    public String getCompanyContractName() {
        return companyContractName;
    }

    public void setCompanyContractName(String companyContractName) {
        this.companyContractName = companyContractName;
    }

    public String getCompanyContractMobile() {
        return companyContractMobile;
    }

    public void setCompanyContractMobile(String companyContractMobile) {
        this.companyContractMobile = companyContractMobile;
    }

    public String getCompanyContractAddress() {
        return companyContractAddress;
    }

    public void setCompanyContractAddress(String companyContractAddress) {
        this.companyContractAddress = companyContractAddress;
    }

    public String getOtherPhone() {
        return otherPhone;
    }

    public void setOtherPhone(String otherPhone) {
        this.otherPhone = otherPhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCompanyContractPhone() {
        return companyContractPhone;
    }

    public void setCompanyContractPhone(String companyContractPhone) {
        this.companyContractPhone = companyContractPhone;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getHomeAreaCode() {
        return homeAreaCode;
    }

    public void setHomeAreaCode(String homeAreaCode) {
        this.homeAreaCode = homeAreaCode;
    }

    public String getBelongsCompanyAreaCode() {
        return belongsCompanyAreaCode;
    }

    public void setBelongsCompanyAreaCode(String belongsCompanyAreaCode) {
        this.belongsCompanyAreaCode = belongsCompanyAreaCode;
    }

    public String getCompanyAreaCode() {
        return companyAreaCode;
    }

    public void setCompanyAreaCode(String companyAreaCode) {
        this.companyAreaCode = companyAreaCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getIdTypeName() {
        return idTypeName;
    }

    public void setIdTypeName(String idTypeName) {
        this.idTypeName = idTypeName;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getHomeAreaCodeName() {
        return homeAreaCodeName;
    }

    public void setHomeAreaCodeName(String homeAreaCodeName) {
        this.homeAreaCodeName = homeAreaCodeName;
    }
}