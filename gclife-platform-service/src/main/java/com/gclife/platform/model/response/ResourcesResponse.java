package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "Resource",description = "系统资源")
public class ResourcesResponse extends BaseResponse {
    @ApiModelProperty(example = "资源ID")
    public  String resourceId;

    @ApiModelProperty(example = "资源值")
    public String resourceValue;

    @ApiModelProperty(example = "资源类型")
    public String resourceType;

    @ApiModelProperty(example = "资源URL")
    public String resourceUrl;

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceValue() {
        return resourceValue;
    }

    public void setResourceValue(String resourceValue) {
        this.resourceValue = resourceValue;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }
}
