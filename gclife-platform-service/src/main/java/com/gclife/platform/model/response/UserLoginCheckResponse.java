package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-12-17
 *         description:
 */
public class UserLoginCheckResponse {
    @ApiModelProperty(example = "用户ID")
    private String userId;

    @ApiModelProperty(example = "连续登录失败次数")
    private int failedCount;

    @ApiModelProperty(example = "是否允许登录")
    private boolean allowLogin;

    public boolean isAllowLogin() {
        return allowLogin;
    }

    public void setAllowLogin(boolean allowLogin) {
        this.allowLogin = allowLogin;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }
}