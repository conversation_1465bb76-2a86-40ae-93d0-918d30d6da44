package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-12-15
 * description: 链接分享返回的响应
 */

@ApiModel(value = "SharedLink",description = "链接分享")
public class SharedLinkResponse extends BaseResponse {

    @ApiModelProperty(example = "链接参数，JSON字符串")
    private String linkParameters;

    @ApiModelProperty(example = "伪链接")
    private String pseudoLink;

    @ApiModelProperty(example = "实际链接")
    private String actualLink;

    @ApiModelProperty(example = "链接类型")
    private String linkType;

    public String getLinkParameters() {
        return linkParameters;
    }

    public void setLinkParameters(String linkParameters) {
        this.linkParameters = linkParameters;
    }

    public String getPseudoLink() {
        return pseudoLink;
    }

    public void setPseudoLink(String pseudoLink) {
        this.pseudoLink = pseudoLink;
    }

    public String getActualLink() {
        return actualLink;
    }

    public void setActualLink(String actualLink) {
        this.actualLink = actualLink;
    }

    public String getLinkType() {
        return linkType;
    }

    public void setLinkType(String linkType) {
        this.linkType = linkType;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @ApiModelProperty(example = "签名")
    private String signature;
}
