package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-3
 * description:
 */
public class AttendanceResponse {


    @ApiModelProperty(example = "签到时间")
    private  String attendanceDate;
    @ApiModelProperty(example = "签到状态 UNREAD_PUNCH_CARD   用户未打卡   ALREADY_PUNCH_CARD  用户已经打卡 ")
    private  String attendanceStatus;

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }
}