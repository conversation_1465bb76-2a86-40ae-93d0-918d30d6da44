package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-4
 *         description:
 */
public class InformationListResponse {

    @ApiModelProperty(example = "信息ID")
    private String informationId;
    @ApiModelProperty(example = "标题")
    private String title;
    @ApiModelProperty(example = "描述")
    private String description;
    @ApiModelProperty(example = "图片链接")
    private String picUrl;
    @ApiModelProperty(example = "阅读量")
    private Long   readNumber;
    @ApiModelProperty(example = "评论量")
    private Long   comments;
    @ApiModelProperty(example = "跳转链接")
    private String jumpLink;
    @ApiModelProperty(example = "视频地址")
    private String videoLink;

    public String getInformationId() {
        return informationId;
    }

    public void setInformationId(String informationId) {
        this.informationId = informationId;
    }

    public String getTitle() {
        return title;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }



    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public Long getReadNumber() {
        return readNumber;
    }

    public void setReadNumber(Long readNumber) {
        this.readNumber = readNumber;
    }

    public Long getComments() {
        return comments;
    }

    public void setComments(Long comments) {
        this.comments = comments;
    }

    public String getJumpLink() {
        return jumpLink;
    }

    public void setJumpLink(String jumpLink) {
        this.jumpLink = jumpLink;
    }

    public String getVideoLink() {
        return videoLink;
    }

    public void setVideoLink(String videoLink) {
        this.videoLink = videoLink;
    }
}
