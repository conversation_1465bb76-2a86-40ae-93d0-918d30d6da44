package com.gclife.platform.model.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/6
 */
@RefreshScope
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "system-operation-log")
public class SystemOperationLogProperties {
    private String[] urlToDisplay;
    private String[] urlNotDisplayed;
}
