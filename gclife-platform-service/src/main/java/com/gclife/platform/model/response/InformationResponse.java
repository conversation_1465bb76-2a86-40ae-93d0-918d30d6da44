package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-4
 *         description:
 */
public class InformationResponse {
    @ApiModelProperty(example = "消息标题")
    private String title;
    @ApiModelProperty(example = "内容")
    private String content;
    @ApiModelProperty(example = "描述")
    private String description;
    @ApiModelProperty(example = "图片链接")
    private String picUrl;
    @ApiModelProperty(example = "阅读量")
    private Long   readNumber;
    @ApiModelProperty(example = "评论量")
    private Long   comments;
    @ApiModelProperty(example = "跳转链接")
    private String jumpLink;
    @ApiModelProperty(example = "视频地址")
    private String videoLink;
    @ApiModelProperty(example = "大图片地址")
    private String bigPicUrl;
    @ApiModelProperty(example = "发布时间")
    private Long   publishDate;
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public Long getReadNumber() {
        return readNumber;
    }

    public void setReadNumber(Long readNumber) {
        this.readNumber = readNumber;
    }

    public Long getComments() {
        return comments;
    }

    public void setComments(Long comments) {
        this.comments = comments;
    }

    public String getJumpLink() {
        return jumpLink;
    }

    public void setJumpLink(String jumpLink) {
        this.jumpLink = jumpLink;
    }

    public String getVideoLink() {
        return videoLink;
    }

    public void setVideoLink(String videoLink) {
        this.videoLink = videoLink;
    }

    public String getBigPicUrl() {
        return bigPicUrl;
    }

    public void setBigPicUrl(String bigPicUrl) {
        this.bigPicUrl = bigPicUrl;
    }

    public Long getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(Long publishDate) {
        this.publishDate = publishDate;
    }
}
