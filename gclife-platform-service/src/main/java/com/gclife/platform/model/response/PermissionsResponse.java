package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "Permissions",description = "系统权限")
public class PermissionsResponse extends BaseResponse {
    @ApiModelProperty(example = "权限ID")
    public  String permissionId;

    @ApiModelProperty(example = "父权限编码")
    public String  parentPermissionId;

    @ApiModelProperty(example = "权限名称")
    public String  name;

    @ApiModelProperty(value = "资源数据集")
    public List<ResourcesResponse> listResource;


    public String getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(String permissionId) {
        this.permissionId = permissionId;
    }

    public String getParentPermissionId() {
        return parentPermissionId;
    }

    public void setParentPermissionId(String parentPermissionId) {
        this.parentPermissionId = parentPermissionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<ResourcesResponse> getListResource() {
        return listResource;
    }

    public void setListResource(List<ResourcesResponse> listResource) {
        this.listResource = listResource;
    }
}
