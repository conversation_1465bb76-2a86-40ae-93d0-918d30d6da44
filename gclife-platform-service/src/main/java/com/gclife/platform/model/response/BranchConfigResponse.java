package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-1-23
 * description:
 */
public class BranchConfigResponse {
    @ApiModelProperty(example = "顶级机构配置ID")
    private String topBranchConfigId;
    @ApiModelProperty(example = "顶级机构ID")
    private String topBranchId;
    @ApiModelProperty(example = "顶级机构编码")
    private String branchCode;
    @ApiModelProperty(example = "配置类型")
    private String configType;
    @ApiModelProperty(example = "配置值")
    private String configValue;



    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }
    public String getTopBranchConfigId() {
        return topBranchConfigId;
    }

    public void setTopBranchConfigId(String topBranchConfigId) {
        this.topBranchConfigId = topBranchConfigId;
    }

    public String getTopBranchId() {
        return topBranchId;
    }

    public void setTopBranchId(String topBranchId) {
        this.topBranchId = topBranchId;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }
}
