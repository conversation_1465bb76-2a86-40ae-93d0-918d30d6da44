package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import com.gclife.platform.core.jooq.tables.Branch;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:32
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构返回信息
 * <AUTHOR>
 */
@ApiModel(value = "BranchType",description = "系统机构类型")
public class BranchTypeResponse extends BaseResponse {


    @ApiModelProperty(example = "机构类型ID")
    public  String branchType;

    @ApiModelProperty(example = "机构类型名称")
    public String  branchTypeName;

    public String getBranchType() {
        return branchType;
    }

    public void setBranchType(String branchType) {
        this.branchType = branchType;
    }

    public String getBranchTypeName() {
        return branchTypeName;
    }

    public void setBranchTypeName(String branchTypeName) {
        this.branchTypeName = branchTypeName;
    }
}