package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:区域地址返回信息
 */
public class AreaResponse extends BaseResponse {
    @ApiModelProperty(example = "地区ID")
    private String areaId;
    @ApiModelProperty(example = "地区名称")
    private String areaName;
    @ApiModelProperty(example = "父级地区ID")
    private String parentAreaId;
    @ApiModelProperty(example = "地区排序值")
    private String areaIndex;
    @ApiModelProperty(example = "地区层级")
    private String areaLevel;
    @ApiModelProperty(example = "下一层级数量")
    private String existChild;
    @ApiModelProperty(example = "下一层级数量")
    private String existGrandson;

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getParentAreaId() {
        return parentAreaId;
    }

    public void setParentAreaId(String parentAreaId) {
        this.parentAreaId = parentAreaId;
    }

    public String getAreaIndex() {
        return areaIndex;
    }

    public void setAreaIndex(String areaIndex) {
        this.areaIndex = areaIndex;
    }

    public String getAreaLevel() {
        return areaLevel;
    }

    public void setAreaLevel(String areaLevel) {
        this.areaLevel = areaLevel;
    }

    public String getExistChild() {
        return existChild;
    }

    public void setExistChild(String existChild) {
        this.existChild = existChild;
    }

    public String getExistGrandson() {
        return existGrandson;
    }

    public void setExistGrandson(String existGrandson) {
        this.existGrandson = existGrandson;
    }
}