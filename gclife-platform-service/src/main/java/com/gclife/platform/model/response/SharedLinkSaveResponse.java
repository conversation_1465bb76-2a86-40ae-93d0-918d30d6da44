package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:链接保存后的响应信息
 */
@ApiModel(value = "SharedLinkSave",description = "链接保存")
public class SharedLinkSaveResponse extends BaseResponse {
    @ApiModelProperty(example = "signature")
    private String signature;

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
