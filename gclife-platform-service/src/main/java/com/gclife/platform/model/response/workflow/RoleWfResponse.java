package com.gclife.platform.model.response.workflow;


import com.gclife.common.model.BaseResponse;
import com.gclife.platform.core.jooq.tables.pojos.RolesPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "Role",description = "系统用户组")
public class RoleWfResponse extends BaseResponse {
    @ApiModelProperty(example = "用户组姓名")
    private String roleId;
    @ApiModelProperty(example = "用户组名称")
    private String name;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
