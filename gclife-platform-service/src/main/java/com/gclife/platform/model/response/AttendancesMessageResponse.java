package com.gclife.platform.model.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取当月的打卡信息  以及  累计签到天数
 * <AUTHOR>
 * create 17-11-8
 * description:
 */
public class AttendancesMessageResponse {

    @ApiModelProperty(example = "累计签到天数")
    private Long continuouSignDays;
    @ApiModelProperty(example = "用户总计积分")
    private String totalAmount;

    private List<AttendanceStatusRequest> daysStatus;


    public Long getContinuouSignDays() {
        return continuouSignDays;
    }

    public void setContinuouSignDays(Long continuouSignDays) {
        this.continuouSignDays = continuouSignDays;
    }

    public List<AttendanceStatusRequest> getDaysStatus() {
        return daysStatus;
    }

    public void setDaysStatus(List<AttendanceStatusRequest> daysStatus) {
        this.daysStatus = daysStatus;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }
}