package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "User",description = "系统用户")
public class UserOauthResponse extends BaseResponse {
    @ApiModelProperty(example = "用户ID")
    public  String userId;

    @ApiModelProperty(example = "用户账号")
    public String userName;

    @ApiModelProperty(example = "用户短姓名")
    public String name;


    @ApiModelProperty(example = "创建时间")
    public  long ctime;


    @ApiModelProperty(example = "更新时间")
    public long mtime;

    @ApiModelProperty(value = "角色数据集")
    private List<RolesResponse> listRoles =null;



    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    public long getMtime() {
        return mtime;
    }

    public void setMtime(long mtime) {
        this.mtime = mtime;
    }

    public List<RolesResponse> getListRoles() {
        return listRoles;
    }

    public void setListRoles(List<RolesResponse> listRoles) {
        this.listRoles = listRoles;
    }
}
