package com.gclife.platform.model.response;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@Getter
@Setter
@ApiModel(value = "操作日志响应", description = "操作日志响应")
public class OperationLogResponse extends BaseResponse {
    @ApiModelProperty(value = "日志id", example = "日志id")
    private String operationLogId;
    @ApiModelProperty(value = "用户ID", example = "用户ID")
    private String userId;
    @ApiModelProperty(value = "用户名称", example = "用户名称")
    private String username;
    @ApiModelProperty(value = "操作名称", example = "操作名称")
    private String operationName;
    @ApiModelProperty(value = "请求接口Key", example = "请求接口Key")
    private String requestNoteKey;
    @Internation(filed = "requestNoteKey", codeType = "OPERATION_LOG")
    @ApiModelProperty(value = "请求接口说明", example = "请求接口说明")
    private String requestNote;
    @ApiModelProperty(value = "请求开始时间", example = "请求开始时间")
    private Long requestDate;
    @DateFormat(filed = "requestDate", pattern = DateFormatPatternEnum.FORMATE6)
    @ApiModelProperty(value = "请求开始时间格式化", example = "请求开始时间格式化")
    private String requestDateFormat;
    @ApiModelProperty(value = "接口tagKey", example = "接口tagKey")
    private String requestTagKey;
    @Internation(filed = "requestTagKey", codeType = "OPERATION_LOG")
    @ApiModelProperty(value = "接口tag说明", example = "接口tag说明")
    private String requestTag;
    private Integer totalLine;
}
