package com.gclife.platform.model.response;


import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
@ApiModel(value = "Role",description = "系统角色")
public class RolesResponse extends BaseResponse {
    @ApiModelProperty(example = "角色ID")
    public  String roleId;

    @ApiModelProperty(example = "角色名称")
    public String roleName;

    @ApiModelProperty(value = "权限数据集")
    public List<PermissionsResponse> listPermissions;


    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }


    public List<PermissionsResponse> getListPermissions() {
        return listPermissions;
    }

    public void setListPermissions(List<PermissionsResponse> listPermissions) {
        this.listPermissions = listPermissions;
    }
}
