package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.dao.SharedLinkConfigExtDao;
import com.gclife.platform.dao.SharedLinkExtDao;
import com.gclife.platform.model.bo.SharedLinkBo;
import com.gclife.platform.model.bo.SharedLinkConfigBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.SharedLinkSaveRequest;
import com.gclife.platform.model.response.SharedLinkResponse;
import com.gclife.platform.model.response.SharedLinkSaveResponse;
import com.gclife.platform.service.business.SharedLinkBusinessService;
import com.gclife.platform.service.data.SharedLinkService;
import com.gclife.platform.validate.parameter.transform.SharedLinkTransData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * <AUTHOR>
 * create 17-12-15
 * description: 分享链接的实现类
 */
@Service
public class SharedLinkBusinessServiceImpl extends BaseBusinessServiceImpl implements SharedLinkBusinessService {

    @Autowired
    SharedLinkExtDao sharedLinkExtDao;

    @Autowired
    SharedLinkConfigExtDao sharedLinkConfigExtDao;

    @Autowired
    SharedLinkService sharedLinkService;

    @Autowired
    SharedLinkTransData sharedLinkTransData;

    @Override
    public ResultObject retriveSharedLinkBySignature(String signature) {
        ResultObject<SharedLinkResponse> resultObject = new ResultObject<>();

        try {
            AssertUtils.isNotEmpty(this.getLogger(), signature, PlatformErrorConfigEnum.PLATFORM_PARAMETER_SIGNATURE_IS_NOT_NULL);
            SharedLinkBo sharedLinkBo = sharedLinkExtDao.loadSharedLinkBySignature(signature);

            if (null != sharedLinkBo) {
                resultObject.setData(sharedLinkTransData.transSharedLinkResponse(sharedLinkBo));
            }

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_ERROR);
            }
        }

        return resultObject;
    }

    @Transactional
    @Override
    public ResultObject saveSharedLink(String userId, SharedLinkSaveRequest sharedLinkSaveRequest) {
        ResultObject<SharedLinkSaveResponse> resultObject = new ResultObject<>();

        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), sharedLinkSaveRequest.getLinkType(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_LINKTYPE_IS_NOT_NULL);
            // 根据链接类型获取链接配置信息
            SharedLinkConfigBo sharedLinkConfigBo = sharedLinkConfigExtDao.loadSharedLinkConfigByType(sharedLinkSaveRequest.getLinkType());

            SharedLinkBo sharedLinkBo = sharedLinkTransData.transSharedLinkBo(userId, sharedLinkConfigBo, sharedLinkSaveRequest);
            sharedLinkService.saveSharedLink(sharedLinkBo);

            SharedLinkSaveResponse sharedLinkSaveResponse = new SharedLinkSaveResponse();
            sharedLinkSaveResponse.setSignature(sharedLinkBo.getSignature());
            resultObject.setData(sharedLinkSaveResponse);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SAVE_SHARED_LINK_ERROR);
            }
        }

        return resultObject;
    }
}
