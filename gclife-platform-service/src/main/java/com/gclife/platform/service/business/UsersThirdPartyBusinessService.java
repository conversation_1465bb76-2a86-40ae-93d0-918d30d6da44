package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.UserThirdPartyRequest;
import com.gclife.platform.model.response.UserBaseResponse;
import com.gclife.platform.model.response.UserThirdPartyResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
public interface UsersThirdPartyBusinessService extends BaseBusinessService {

    /**
     * 查询第三方用户信息
     * @param openId 开放标识
     * @return
     */
    ResultObject<UserBaseResponse> queryUserInfo(String openId);

    /**
     * 保存第三方用户信息
     * @param userThirdPartyRequest
     * @param appRequestHeads
     * @return
     */
    ResultObject<UserBaseResponse> saveInfo(UserThirdPartyRequest userThirdPartyRequest, AppRequestHeads appRequestHeads);

    /**
     * 查询第三方信息
     * @param userId 用户ID
     * @return
     */
    ResultObject<List<UserThirdPartyResponse>> listThirdPartyInfo(String userId);

    /**
     * 第三方信息绑定
     * @param userThirdPartyRequest
     * @return
     */
    ResultObject bind(UserThirdPartyRequest userThirdPartyRequest);

    /**
     * 第三方信息解绑
     * @param userId 用户ID
     * @param thirdPartyCode 第三方平台编码
     * @return
     */
    ResultObject unbind(String userId, String thirdPartyCode);
}
