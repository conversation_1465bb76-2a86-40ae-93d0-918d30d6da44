package com.gclife.platform.service.business.app.manage;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;

public interface UserManageService {
    /**
     * 修改用户启用禁用
     *
     * @param updateUserId
     * @param enabled
     * @param currentLoginUsers
     * @return
     */
    ResultObject updateUserEnabled(String updateUserId, String enabled, Users currentLoginUsers);
}
