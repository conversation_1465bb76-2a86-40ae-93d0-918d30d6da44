package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.UserAccountDetailRequest;
import com.gclife.platform.model.response.UserAccountDetailResponse;
import com.gclife.platform.model.response.UserAccountResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-9
 * description:
 */
public interface UserInfoBusinessService extends BaseBusinessService {
    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return
     */
    ResultObject getUserInfoById(String userId);

    /**
     * 获取账户信息
     *
     * @param userId      请求参数
     * @param accountType 账户类型
     * @return
     */
    ResultObject<UserAccountResponse> getUserAccount(String userId, String accountType);

    /**
     * 根据用户添加积分
     *
     * @param users
     * @param integral
     * @return
     */
    ResultObject saveUserIntegral(Users users, BigDecimal integral);


    /**
     * 根据用户id、账户类型、流水类型获取账户信息
     *
     * @param userId,accountTypeCode,streamCode
     * @return
     */
    ResultObject<List<UserAccountDetailResponse>> getAccountDetail(String userId,String accountTypeCode, String streamCode);


    /**
     * 保存提现记录信息
     *
     * @param userAccountDetailRequest 请求参数
     * @return
     */
    ResultObject saveUserAccountDetail(UserAccountDetailRequest userAccountDetailRequest);
}