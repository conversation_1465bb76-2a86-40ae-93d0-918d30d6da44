package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.AuthItemConfigEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.MenuResourcesBo;
import com.gclife.platform.model.response.GrantedSubMenuResponse;
import com.gclife.platform.service.business.ResourcesBusinessService;
import com.gclife.platform.validate.transfer.MenuResourceTransfer;
import com.google.common.collect.Collections2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.Tables.RESOURCES;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * create 17-12-22
 * description: 资源业务接口实现类
 */
@Service
public class ResourcesBusinessServiceImpl extends BaseBusinessServiceImpl implements ResourcesBusinessService {

    @Autowired
    private MenuResourceTransfer menuResourceTransfer;

    @Autowired
    private UsersExtDao usersExtDao;

    /**
     * 查询菜单的子菜单
     *
     * @param currentUser     当前用户上下文信息
     * @param parentMenuValue 父菜单值
     * @param serviceName     域名或服务名
     * @param httpMethod      http请求方式GET或POST
     * @param parentType      资源类型
     * @param childType       子资源类型
     * @param language        语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    @Override
    public ResultObject<GrantedSubMenuResponse> retriveGrantedSubmenu(Users currentUser,
                                                                      String parentMenuValue,
                                                                      String httpMethod,
                                                                      String serviceName,
                                                                      String parentType,
                                                                      String childType,
                                                                      String language) {

        AssertUtils.isNotNull(this.getLogger(), parentMenuValue, PlatformErrorConfigEnum.PLATFORM_PARAMETER_PARENT_MENU_VALUE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), httpMethod, PlatformErrorConfigEnum.PLATFORM_PARAMETER_HTTP_METHOD_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), serviceName, PlatformErrorConfigEnum.PLATFORM_PARAMETER_SERVICE_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), parentType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_RESOURCE_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), childType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_RESOURCE_TYPE_IS_NOT_NULL);
        // 从上下文中去取菜单信息
        ResultObject<GrantedSubMenuResponse> resultObject = new ResultObject<>();
        GrantedSubMenuResponse grantedSubMenuResponse = new GrantedSubMenuResponse();
//        List<Resources> resourcesList = currentUser.getResourcesPoList();
        List<MenuResourcesBo> resourcesList = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), language == null ? TerminologyConfigEnum.LANGUAGE.ZH_CN.name() : language);
        // 直接匹配，无需使用正则表达式
        List<MenuResourcesBo> resourcesPos = resourcesList.stream().filter(a -> null != a.getValue() &&
                parentType.equals(a.getType()) &&
                serviceName.equals(a.getServiceName()) &&
                parentMenuValue.equals(a.getValue())).collect(toList());


        List<MenuResourcesBo> grantedResources = new ArrayList<>();
        if (resourcesPos.size() > 0) {
            String parentResourceId = resourcesPos.get(0).getResourceId();
            grantedSubMenuResponse.setServiceName(resourcesPos.get(0).getInternationalText());
            grantedResources = resourcesList.stream().filter(a -> null != a.getValue() &&
//                    childType.equals(a.getType()) &&
                    httpMethod.equals(a.getHttpMethod()) &&
                    PlatformTermEnum.FLAG.VISIBLE.desc().equals(a.getVisible()) &&
                    parentResourceId.equals(a.getParentResourceId())).collect(toList());
        }

        processMenuResources(resourcesList, grantedResources);
//        List<MenuResourcesBo> menuResourcesBos = getMenuResourcesBos(resourcesList, grantedResources);

        grantedSubMenuResponse.setGrantedSubmenu(grantedResources);
        resultObject.setData(grantedSubMenuResponse);
        return resultObject;
    }

    /**
     * @param currentUser      当前用户上下文信息
     * @param parentResourceId 父菜单ID
     * @param language         语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    @Override
    public ResultObject<GrantedSubMenuResponse> retriveByParentResourceId(Users currentUser, String parentResourceId, String language) {

        AssertUtils.isNotNull(this.getLogger(), parentResourceId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_PARENT_RESOURCE_ID_IS_NOT_NULL);
        // 从上下文中去取菜单信息
        ResultObject<GrantedSubMenuResponse> resultObject = new ResultObject<>();
        GrantedSubMenuResponse grantedSubMenuResponse = new GrantedSubMenuResponse();
//        List<Resources> resourcesList = currentUser.getResourcesPoList();
        List<MenuResourcesBo> resourcesList = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), language == null ? TerminologyConfigEnum.LANGUAGE.ZH_CN.name() : language);

        resultObject.setData(retriveByParentId(parentResourceId, grantedSubMenuResponse, resourcesList));
        return resultObject;
    }

    private GrantedSubMenuResponse retriveByParentId(String parentResourceId, GrantedSubMenuResponse grantedSubMenuResponse, List<MenuResourcesBo> resourcesList) {
        // 获取parentResourceId对应的资源
        List<MenuResourcesBo> parentResources = resourcesList.stream().filter(a -> null != a.getValue() &&
                a.getResourceId().equals(parentResourceId)).collect(toList());
        if (parentResources.size() > 0) {

            grantedSubMenuResponse.setServiceName(parentResources.get(0).getInternationalText());
            // 直接匹配，无需使用正则表达式
            List<MenuResourcesBo> resourcesPos = resourcesList.stream().filter(a -> null != a.getValue() &&
                    PlatformTermEnum.FLAG.VISIBLE.desc().equals(a.getVisible()) &&
                    parentResourceId.equals(a.getParentResourceId())).collect(toList());

            processMenuResources(resourcesList, resourcesPos);


            grantedSubMenuResponse.setGrantedSubmenu(resourcesPos);
        }
        return grantedSubMenuResponse;
    }

    private void processMenuResources(List<MenuResourcesBo> allResourcesList, List<MenuResourcesBo> grantedResourcesPos) {
        grantedResourcesPos.forEach(a -> {
            if (a.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue())) {
                a.setHasChildUrl(hasChildUrl(a, allResourcesList));
                a.setHasChildDomain(hasChildDomain(a, allResourcesList));
            }
        });

        //排序
        grantedResourcesPos.sort(Comparator.comparing(MenuResourcesBo::getVisible, (o1, o2) -> o1.compareTo(o2)).reversed()
                .thenComparing(MenuResourcesBo::getIndex, (o1, o2) -> (o1.intValue() - o2.intValue())));
    }

    /**
     * 根据当前的url地址获取界面上应显示哪些菜单
     *
     * @param currentUser 当前用户上下文信息
     * @param currentUrl  当前url地址
     * @param language    语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    @Override
    public ResultObject<GrantedSubMenuResponse> retriveByCurrentUrl(Users currentUser, String currentUrl, String httpMethod, String language) {
        ResultObject<GrantedSubMenuResponse> resultObject = new ResultObject<>();
        // 解析url
        try {
            URL url = new URL(currentUrl.replaceFirst("/#", ""));
            StringBuilder sb = new StringBuilder(url.getProtocol());
            sb.append("://").append(url.getHost());
            String serviceName = sb.toString();
            String currentMenuValue = url.getPath();

            // 正则表达式匹配
            List<MenuResourcesBo> allResourcesPos = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), language == null ? TerminologyConfigEnum.LANGUAGE.ZH_CN.name() : language);

            List<MenuResourcesBo> serviceResourcesPos = (List) allResourcesPos.stream().filter((a) -> {
                return null != a.getValue() &&
                        AuthItemConfigEnum.FRONTEND_URL.getValue().equals(a.getType()) &&
                        httpMethod.equals(a.getHttpMethod()) &&
                        serviceName.equals(a.getServiceName());
            }).collect(Collectors.toList());

            //如果当前访问路径为/
            if (currentMenuValue.equals("/")) {
                Optional<MenuResourcesBo> resourcesOptional = serviceResourcesPos.stream().filter(
                        a -> a.getValue().equals("/")
                ).findFirst();
                if (AssertUtils.isNotNull(resourcesOptional.get()) &&
                        AssertUtils.isNotEmpty(resourcesOptional.get().getParentResourceId())) {
                    resultObject.setData(retriveBySpecRes(resourcesOptional.get(), allResourcesPos));
                }
            } else {
                Collection<MenuResourcesBo> resourcesCollection = Collections2.filter(serviceResourcesPos, new com.google.common.base.Predicate<MenuResourcesBo>() {
                    public boolean apply(MenuResourcesBo resources) {
                        String url = resources.getValue();
                        boolean result = false;
                        String uri = url.replaceAll(":[^/]+", "");
//                    String regEx = "^" + uri + "$";

                        //统计uri和currentMenuValue中/出现的次数
                        String uriRep = uri.replaceAll("/", "");
                        String currentMenuValueRep = currentMenuValue.replaceAll("/", "");

                        int uriLenDiff = uri.length() - uriRep.length();
                        int currMenuLenDiff = currentMenuValue.length() - currentMenuValueRep.length();

                        if (uriLenDiff == currMenuLenDiff) {
                            result = resources.getServiceName().equals(serviceName) &&
                                    resources.getHttpMethod().equals(httpMethod);
                            if (uriLenDiff == 1) {
                                result = (result && uriRep.equals(currentMenuValueRep));
                            } else {
                                result = (result && (currentMenuValue.contains(uri) || currentMenuValue.contains(uri.replaceAll("[/]$", ""))));
                            }
                        } else {
                            result = false;
                        }
                        return result;
                    }
                });

                if (resourcesCollection.size() > 0) {
                    MenuResourcesBo[] resArray = resourcesCollection.toArray(new MenuResourcesBo[0]);
                    resultObject.setData(retriveBySpecRes(resArray[0], allResourcesPos));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_RETRIVE_RESOURCE_ERROR);
            }
        }

        return resultObject;
    }

    /**
     * 根据当前的url地址获取界面上应显示哪些按钮
     *
     * @param currentUser 当前用户上下文信息
     * @param serviceName 前端项目名
     * @return ResultObject<Map < String, Boolean>>
     */
    @Override
    public ResultObject<Map<String, Boolean>> retriveGrantedButton(Users currentUser, String serviceName) {
        ResultObject<Map<String, Boolean>> resultObject = new ResultObject<>();
        Map<String, Boolean> buttonMap = new HashMap<>();

        try {

            List<MenuResourcesBo> allResourcesPos = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), TerminologyConfigEnum.LANGUAGE.ZH_CN.name());

            allResourcesPos.stream().forEach(a -> {
                if (a.getServiceName() != null &&
                        a.getServiceName().equals(serviceName) &&
                        a.getType() != null &&
                        AuthItemConfigEnum.FRONTEND_BUTTON.getValue().equals(a.getType())) {
                    buttonMap.put(a.getName(), true);
                }
            });
            resultObject.setData(buttonMap);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_RETRIVE_RESOURCE_ERROR);
            }
        }

        return resultObject;
    }

    @Override
    public ResultObject<String> accsResCheck(Users currentUser, String requestUrl, String httpMethod, String serviceName, String type) {
        boolean checkResult = false;
        ResultObject<String> resultObject = new ResultObject<>();
        if (AuthItemConfigEnum.FULL_ACCESS.getValue().equals(currentUser.getResourcesAccessType())) {
            checkResult = true;
        } else {
            List<MenuResourcesBo> resourcesPoList = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
            // 直接匹配，无需使用正则表达式
            List<MenuResourcesBo> resourcesPos = resourcesPoList.stream().filter(a -> null != a.getValue() &&
                    AuthItemConfigEnum.FRONTEND_URL.getValue().equals(a.getType()) &&
                    httpMethod.equals(a.getHttpMethod()) &&
                    requestUrl.equals(a.getValue()) &&
                    serviceName.equals(a.getServiceName())).collect(toList());
            checkResult = resourcesPos.size() > 0;
        }
        resultObject.setData(String.valueOf(checkResult));
        return resultObject;
    }

    //获取前端项目的URL列表
    public GrantedSubMenuResponse retriveBySpecRes(MenuResourcesBo srcResource, List<MenuResourcesBo> allResourcesPoList) {
        //获取前端项目根资源
        Optional<MenuResourcesBo> serviceRootResource = allResourcesPoList
                .stream()
                .filter(m -> m.getParentResourceId().equals("root") &&
                        m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()))
                .findFirst();
        String parentResourceId = null;
        String searchAgainResourceId = null;
        List<MenuResourcesBo> resList = new ArrayList<>();
        boolean searchAgain = true;

        if (serviceRootResource.isPresent()) {
            //查找直接下级，最多找两层
            //第一次查找

            Optional<MenuResourcesBo> resourcesOptional = allResourcesPoList
                    .stream()
                    .filter(m -> m.getParentResourceId() != null &&
                            m.getServiceName() != null &&
                            m.getParentResourceId().equals(serviceRootResource.get().getResourceId()) &&
                            m.getServiceName().equals(srcResource.getServiceName()) &&
                            m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()))
                    .findFirst();

            // 第一次查找到的，如果下面有子菜单，则无需继续查找
            // 如果submenu_visible_on_dashboard为INVISIBLE，则无需继续查找
            if (resourcesOptional.isPresent()) {
                if (hasChildUrl(resourcesOptional.get(), allResourcesPoList) ||
                        !resourcesOptional.get().getSubmenuVisibleOnDashboard().equals(AuthItemConfigEnum.VISIBLE.getValue())) {
                    parentResourceId = resourcesOptional.get().getResourceId();
                    searchAgain = false;
                } else {
                    searchAgain = true;
                    searchAgainResourceId = resourcesOptional.get().getResourceId();
                }
            }

            //再次查找
            if (searchAgain && searchAgainResourceId != null) {
                for (MenuResourcesBo m : allResourcesPoList) {
                    if (m.getParentResourceId().equals(searchAgainResourceId) &&
                            m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()) &&
                            m.getServiceName().equals(srcResource.getServiceName()) &&
                            isSuperior(srcResource, m, allResourcesPoList)) {
                        parentResourceId = m.getResourceId();
                        break;
                    }
                }
            }
        }

        GrantedSubMenuResponse grantedSubMenuResponse = new GrantedSubMenuResponse();

        return parentResourceId == null ? null : retriveByParentId(parentResourceId, grantedSubMenuResponse, allResourcesPoList);
    }


    /**
     * 查找资源是否有上下级关系
     *
     * @param urlRes          菜单资源
     * @param domainRes       域名资源
     * @param resourcesPoList 资源记录集
     * @return boolean
     */
    public boolean isSuperior(MenuResourcesBo urlRes, MenuResourcesBo domainRes, List<MenuResourcesBo> resourcesPoList) {

        Optional<MenuResourcesBo> parentResOptional = resourcesPoList
                .stream()
                .filter(m -> m.getResourceId().equals(urlRes.getParentResourceId()) &&
                        m.getParentResourceId() != null &&
                        m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()))
                .findFirst();

        MenuResourcesBo parentRes = parentResOptional.get();
        if (parentRes == null) {
            return false;
        }

        if (parentRes.getResourceId().equals(domainRes.getResourceId())) {
            return true;
        }

        while (parentRes != null &&
                parentRes.getResourceId() != domainRes.getResourceId() &&
                !parentRes.getParentResourceId().equals("root")) {
            String parentResourceId = parentRes.getParentResourceId();
            parentResOptional = resourcesPoList
                    .stream()
                    .filter(m -> m.getResourceId().equals(parentResourceId) &&
                            m.getParentResourceId() != null &&
                            m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()))
                    .findFirst();
            parentRes = parentResOptional.get();
        }


        return parentRes == null ? false : (parentRes.getResourceId().equals(domainRes.getResourceId()) ? true : false);
    }

    /**
     * 查找资源是否有子域名
     *
     * @param a               资源
     * @param resourcesPoList 资源记录集
     * @return boolean
     */
    public boolean hasChildDomain(MenuResourcesBo a, List<MenuResourcesBo> resourcesPoList) {
        return resourcesPoList
                .stream()
                .anyMatch(m -> m.getParentResourceId().equals(a.getResourceId()) &&
                        m.getType().equals(AuthItemConfigEnum.FRONTEND_DOMAIN.getValue()));
    }

    /**
     * 查找资源是否有子菜单
     *
     * @param a               资源
     * @param resourcesPoList 资源记录集
     * @return boolean
     */
    public boolean hasChildUrl(MenuResourcesBo a, List<MenuResourcesBo> resourcesPoList) {
        return resourcesPoList
                .stream()
                .anyMatch(m -> m.getParentResourceId().equals(a.getResourceId()) &&
                        m.getType().equals(AuthItemConfigEnum.FRONTEND_URL.getValue()));
    }

    /**
     * @param currentUser      当前用户上下文信息
     * @param parentResourceId 父菜单ID
     * @return ResultObject<GrantedSubMenuResponse>
     */
    @Override
    public ResultObject<MenuResourcesBo> retriveRedirectUrl(Users currentUser, String parentResourceId, String language) {

        AssertUtils.isNotNull(this.getLogger(), parentResourceId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_PARENT_RESOURCE_ID_IS_NOT_NULL);
        // 从上下文中去取菜单信息
        ResultObject<MenuResourcesBo> resultObject = new ResultObject<>();

        if (!AssertUtils.isNotNullString(language)) {
            language = currentUser.getLanguage();
            if (!AssertUtils.isNotNullString(language)) {
                language = TerminologyConfigEnum.LANGUAGE.ZH_CN.name();
            }
        }

        List<MenuResourcesBo> resourcesList = usersExtDao.loadFrontendResourcesList(currentUser.getUserId(), language);

        //取可见的下级菜单
        List<MenuResourcesBo> resourcesPos = resourcesList.stream().filter(a -> null != a.getValue() &&
                PlatformTermEnum.FLAG.VISIBLE.desc().equals(a.getVisible()) &&
                parentResourceId.equals(a.getParentResourceId())).collect(toList());

        processMenuResources(resourcesList, resourcesPos);

        //取第一个类型为FRONTEND_URL的记录
        Optional<MenuResourcesBo> menuResourcesBoOptional = resourcesPos.stream().findFirst();

        //全部为FRONTEND_DOMAIN类型，取第一项的第一个可见子菜单
        if (menuResourcesBoOptional.isPresent()&&AuthItemConfigEnum.FRONTEND_URL.getValue().equals(menuResourcesBoOptional.get().getType())) {
            resultObject.setData(menuResourcesBoOptional.get());

        } else {
            Optional<MenuResourcesBo> childOptional = resourcesList.stream().filter(a -> null != a.getValue() &&
                    PlatformTermEnum.FLAG.VISIBLE.desc().equals(a.getVisible()) &&
                    a.getType().equals(AuthItemConfigEnum.FRONTEND_URL.getValue()) &&
                    resourcesPos.get(0).getResourceId().equals(a.getParentResourceId()))
                    .findFirst();

            resultObject.setData(childOptional.get());
        }

        return resultObject;
    }

    @Override
    public ResultObject<Boolean> accsResCheckBackend(Users currentUser, String requestUrl, String httpMethod) {
        Boolean checkResult = false;
        ResultObject<Boolean> resultObject = new ResultObject<>();
        if (AuthItemConfigEnum.FULL_ACCESS.getValue().equals(currentUser.getResourcesAccessType())) {
            checkResult = true;
        } else {
            List<MenuResourcesBo> resourcesPoList = usersExtDao.loadBackendResourcesList(currentUser.getUserId());

            // 再使用正则表达式过滤
            if (resourcesPoList.size() > 0) {
                checkResult = resourcesPoList
                        .stream()
                        .anyMatch(resources -> {
                            String url = resources.getValue();
                            boolean result = false;
                            String uri = url.replaceAll("\\{[^/]+\\}", "[^\\\\s]+");
                            String regEx = "^" + uri + "$";
                            result = resources.getHttpMethod().equals(httpMethod) &&
                                    (Pattern.compile(regEx).matcher(requestUrl).find() ||
                                            requestUrl.startsWith(url + "/"));
                            return result;
                        });
            }
        }
        resultObject.setData(checkResult);
        return resultObject;
    }

}
