package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.bo.MenuResourcesBo;
import com.gclife.platform.model.response.GrantedSubMenuResponse;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-12-22
 * description: 资源业务接口
 */

public interface ResourcesBusinessService {

    /**
     *
     * @param currentUser 当前用户上下文信息
     * @param parentMenuValue 父菜单名
     * @param serviceName 域名或服务名
     * @param httpMethod http请求方式GET或POST
     * @param parentType 资源类型
     * @param childType 子资源类型
     * @param language 语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    ResultObject<GrantedSubMenuResponse> retriveGrantedSubmenu(Users currentUser,
                                                               String parentMenuValue,
                                                               String httpMethod,
                                                               String serviceName,
                                                               String parentType,
                                                               String childType,
                                                               String language);
    /**
     *
     * @param currentUser 当前用户上下文信息
     * @param parentResourceId 父菜单ID
     * @param language 语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    ResultObject<GrantedSubMenuResponse> retriveByParentResourceId(Users currentUser,
                                                               String parentResourceId,
                                                                   String language);

    /**
     *
     * 根据当前的url地址获取界面上应显示哪些菜单
     *
     * @param currentUser 当前用户上下文信息
     * @param currentUrl 当前url地址
     * @param language 语言
     * @return ResultObject<GrantedSubMenuResponse>
     */
    ResultObject<GrantedSubMenuResponse> retriveByCurrentUrl(Users currentUser,
                                                             String currentUrl,
                                                             String httpMethod,
                                                             String language);

    /**
     *
     * 根据当前的url地址获取界面上应显示哪些菜单
     *
     * @param currentUser 当前用户上下文信息
     * @param serviceName 前端项目名
     * @return ResultObject<Map<String, Boolean>>
     */
    ResultObject<Map<String, Boolean>> retriveGrantedButton(Users currentUser, String serviceName);

    /**
     *
     * 根据当前的url地址获取界面上应显示哪些菜单
     *
     * @param currentUser 当前用户上下文信息
     * @param requestUrl 请求url地址
     * @param httpMethod http请求方法
     * @param serviceName 请求服务名
     * @param type 类型
     * @return String
     */
    ResultObject<String> accsResCheck(Users currentUser, String requestUrl, String httpMethod, String serviceName, String type);

    /**
     *
     * @param currentUser 当前用户上下文信息
     * @param parentResourceId 父菜单ID
     * @return ResultObject<MenuResourcesBo>
     */
    ResultObject<MenuResourcesBo> retriveRedirectUrl(Users currentUser,
                                                     String parentResourceId,
                                                     String language);


    /**
     *
     * 根据当前的url地址获取界面上应显示哪些菜单
     *
     * @param currentUser 当前用户上下文信息
     * @param requestUrl 请求url地址
     * @param httpMethod http请求方法
     * @return ResultObject<Boolean>
     */
    ResultObject<Boolean> accsResCheckBackend(Users currentUser, String requestUrl, String httpMethod);

}
