package com.gclife.platform.service.business.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.platform.dao.RoleExtDao;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.workflow.RoleWfResponse;
import com.gclife.platform.service.business.UserAuthBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
@Service
public class UserAuthBusinessServiceImpl extends BaseBusinessServiceImpl implements UserAuthBusinessService {

    @Autowired
    UsersExtDao usersExtDao;

    @Autowired
    RoleExtDao roleExtDao;


    /**
     * 加载用户权限信息
     * @param userId
     * @return
     */
    @Override
    public ResultObject loadUserAuths(String userId) {
        ResultObject<RoleWfResponse> resultObject=new ResultObject<RoleWfResponse>();
        try {

        }catch (Exception e){
            if(e instanceof RequestException){
                RequestException error  =(RequestException)e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            }else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
        }
        return  resultObject;
    }
}