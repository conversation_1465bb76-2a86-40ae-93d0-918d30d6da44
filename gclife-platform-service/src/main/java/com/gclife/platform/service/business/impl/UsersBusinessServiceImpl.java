package com.gclife.platform.service.business.impl;


import com.alibaba.fastjson.JSON;
import com.gclife.auth.api.UserBaseApi;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.common.util.UUIDUtils;
import com.gclife.message.api.MessageSmsApi;
import com.gclife.message.model.request.SmsVerifyCodeGeneratorRequest;
import com.gclife.message.model.respone.SmsVerifyCodeCheckResponse;
import com.gclife.platform.base.model.bo.BranchDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.model.vo.UsersBranchVo;
import com.gclife.platform.base.model.vo.UsersVo;
import com.gclife.platform.base.service.BranchBaseService;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.base.service.UsersBranchBaseService;
import com.gclife.platform.core.jooq.tables.daos.UserBranchDao;
import com.gclife.platform.core.jooq.tables.daos.UsersDao;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.dao.*;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.model.request.*;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.AccountBusinessService;
import com.gclife.platform.service.business.MobileVerifyBusinessService;
import com.gclife.platform.service.business.UsersBusinessService;
import com.gclife.platform.service.data.UsersService;
import com.gclife.platform.validate.business.UsersBusinessValidate;
import com.gclife.platform.validate.parameter.UsersParameterValidate;
import com.gclife.platform.validate.parameter.transform.SmsVerifyReqFcTransData;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.gclife.platform.base.model.config.PlatformErrorConfigEnum.PLATFORM_SAVE_USER_MOBILE_REPEAT_IS_NOT_NULL;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class UsersBusinessServiceImpl extends BaseBusinessServiceImpl implements UsersBusinessService {


    @Autowired
    private UsersExtDao usersDetailDao;

    @Autowired
    private UsersService usersService;

    @Autowired
    private UsersParameterValidate usersParameterValidate;

    @Autowired
    private UsersBusinessValidate usersBusinessValidate;

    @Autowired
    private UsersExtDao usersExtDao;

    @Autowired
    private UsersWeixinExtDao usersWeixinExtDao;

    @Autowired
    private UserAppDeviceExtDao userAppDeviceExtDao;

    @Autowired
    private BranchExtDao branchExtDao;

    @Autowired
    private UserBranchDao userBranchDao;

    @Autowired
    private UsersDao usersDao;
    @Autowired
    private UsersBaseService usersBaseService;

    @Autowired
    private UsersBranchBaseService usersBranchBaseService;

    @Autowired
    private BranchBaseService branchBaseService;

    @Autowired
    private AccountBusinessService accountBusinessService;

    @Autowired
    private MobileVerifyBusinessService mobileVerifyBusinessService;

    @Autowired
    private MessageSmsApi messageSmsApi;

    @Autowired
    private SmsVerifyReqFcTransData smsVerifyReqFcTransData;

    @Autowired
    private UserDingExDao userDingExDao;
    @Autowired
    private UserBaseApi userBaseApi;

    /**
     * 查询用户角色列表
     *
     * @param roleId
     * @return
     */
    @Override
    public ResultObject loadUserRoles(String roleId) {

        return null;
    }

    /**
     * 查询用户列表
     *
     * @param userQueryRequest
     * @return
     */
    @Override
    public ResultObject loadUserList(UserQueryRequest userQueryRequest) {

        ResultObject<BasePageResponse> resultObject = new ResultObject<BasePageResponse>();
        try {

            /**
             * 分页查询数据
             */
            List<UsersPo> usersPos = usersDetailDao.loadUserList(userQueryRequest);

            /**
             * 数据转换
             */
            List<UserOauthResponse> userOauthResponse = (List<UserOauthResponse>) this.converterList(usersPos, new TypeToken<List<UsersPo>>() {
            }.getType());

            /**
             * 数据返回
             */
            BasePageResponse basePageResponse = new BasePageResponse();

            basePageResponse.setData(userOauthResponse);

            resultObject.setData(basePageResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }
        return resultObject;

    }

    /**
     * 查询用户详细信息
     *
     * @param userId
     * @return
     */
    @Override
    public ResultObject loadUserDetailById(String userId) {

        ResultObject<UserOauthResponse> resultObject = new ResultObject<UserOauthResponse>();
        try {
            /**
             * 数据验证
             */
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);

            /**
             * 查询业务对象数据
             */
            UsersOauthBo usersOauthBo = usersDetailDao.loadUserDetailById(userId);


            //数据转换
            UserOauthResponse userOauthResponse = (UserOauthResponse) this.converterObject(usersOauthBo, UserOauthResponse.class);

            /**
             * 设置返回数据
             */
            resultObject.setData(userOauthResponse);


        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }
        return resultObject;
    }


    /**
     * 新增用户
     *
     * @param userRequest
     * @return
     */
    @Override
    @Transactional
    public ResultObject addUser(UserRequest userRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<BaseResponse>();
        try {

            /**
             * 数据验证
             */
            usersParameterValidate.validParameterAddUser(userRequest);
            /**
             * 数据转换
             */
            UsersPo usersPo = (UsersPo) this.converterObject(userRequest, UsersPo.class);

            conversionAddUser(usersPo, userRequest);
            /**
             * 数据保存
             */
            usersService.saveUsersPo(usersPo);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }

        return resultObject;
    }

    /**
     * 修改用户
     *
     * @param userRequest
     * @return
     */
    @Override
    @Transactional
    public ResultObject updateUser(UserRequest userRequest, String userId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<BaseResponse>();
        try {
            UsersPo usersPo = null;
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询数据
            usersPo = usersDetailDao.loadUserPo(userId);
            //校验userpo对象
            usersBusinessValidate.validBusinessUpdateUser(usersPo);

            conversionAddUser(usersPo, userRequest);

            usersService.saveUsersPo(usersPo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 根据用户名查询用户信息
     *
     * @param username
     * @return
     */
    @Override
    public ResultObject loadUserByUserName(Users users, String username) {

        ResultObject<UserResponse> resultObject = new ResultObject<UserResponse>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), username, PlatformErrorConfigEnum.USERS_PARAMETER_USER_USERNAME_IS_NOT_NULL);

            //查询业务对象数据
            UsersPo usersPo = usersDetailDao.loadUserByUsersName(username);

            //业务校验
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);

            //数据转换
            UserResponse userResponse = (UserResponse) this.converterObject(usersPo, UserResponse.class);

            /**
             * 设置返回数据
             */
            resultObject.setData(userResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<UsersBo> getUserById(String userId) {
        ResultObject<UsersBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            UsersBo usersBo = (UsersBo) this.converterObject(usersExtDao.loadUserPo(userId), UsersBo.class);
            if (null == usersBo) {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
                return resultObject;
            }
            UserWeixinPo userWeixinPo = usersBaseService.queryOneUserWeixinPoById(userId);
            if (AssertUtils.isNotNull(userWeixinPo)) {
                usersBo.setUrl(userWeixinPo.getHeadImgUrl());
                if (!AssertUtils.isNotEmpty(usersBo.getName())) {
                    usersBo.setName(userWeixinPo.getNickname());
                }
            }
            resultObject.setData(usersBo);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<UsersBo> getUserByMobile(String mobile, String deviceChannel) {
        ResultObject<UsersBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), mobile, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
            UsersBo usersBo = usersExtDao.loadMobileVerify(null, mobile, deviceChannel);
            resultObject.setData(usersBo);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject putUserInfo(String userId, UserInfoRequest userInfoRequest, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject();
        try {
            if (AssertUtils.isNotEmpty(userInfoRequest.getUserId())) {
                userId = userInfoRequest.getUserId();
            }
            UsersPo usersPo = usersExtDao.loadUserPo(userId);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            if (AssertUtils.isNotEmpty(userInfoRequest.getEmail())) {
                usersPo.setEmail(userInfoRequest.getEmail());
            }
            if (AssertUtils.isNotEmpty(userInfoRequest.getMobile())) {
                usersPo.setMobile(userInfoRequest.getMobile());
                if (!AssertUtils.isNotEmpty(usersPo.getCountryCode())) {
                    usersPo.setCountryCode("855");
                }
            }
            if (AssertUtils.isNotEmpty(userInfoRequest.getGender())) {
                usersPo.setGender(userInfoRequest.getGender());
            }
            if (AssertUtils.isNotEmpty(userInfoRequest.getNikeName())) {
                usersPo.setNickName(userInfoRequest.getNikeName());
                if (!AssertUtils.isNotEmpty(usersPo.getName())) {
                    usersPo.setName(userInfoRequest.getNikeName());
                }
            }
            if ("gclife_client_app".equals(appRequestHeads.getDeviceChannel()) && AssertUtils.isNotEmpty(userInfoRequest.getName())) {
                usersPo.setName(userInfoRequest.getName());
            }
            if ("gclife_client_app".equals(appRequestHeads.getDeviceChannel()) && AssertUtils.isNotEmpty(userInfoRequest.getPassword())) {
                usersPo.setPassword(new BCryptPasswordEncoder().encode(userInfoRequest.getPassword()));
                usersPo.setPasswordFlag(PlatformTermEnum.PASSWORD_CHANGE_STATUS.MODIFIED.code());
                // 改了密码就清除旧token
                userBaseApi.deleteToken(usersPo.getUserId());
            }
            if (AssertUtils.isNotEmpty(userInfoRequest.getLanguage())) {
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(userInfoRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());
                } else if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(userInfoRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                } else if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(userInfoRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.KM_KH.name());
                } else {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                }
            }
            usersService.saveUsersPo(usersPo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_SAVE_USER_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject changeUserPwd(UserLoginPasswordResetRequest userLoginPasswordResetRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            UsersPo usersPo = usersExtDao.loadUserByUsersName(userLoginPasswordResetRequest.getUserName());
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            String regexSpecChars = "^(?=.*[`~!@#\\$%\\^&\\*\\(\\)\\-=_\\+\\{\\}\\[\\]\\|\\\\:\";'\\,\\.\\?\\/<>])";
            String regexNum = "^(?=.*[\\d])";
            String regexLetter = "^(?=.*[a-zA-Z])";
            String regexNull = "^(?=.*[\\s]+)";

            Pattern pattern = Pattern.compile(regexSpecChars);
            Matcher matcher = pattern.matcher(userLoginPasswordResetRequest.getPassword());

            Pattern pattern1 = Pattern.compile(regexNum);
            Matcher matcher1 = pattern1.matcher(userLoginPasswordResetRequest.getPassword());

            Pattern pattern2 = Pattern.compile(regexLetter);
            Matcher matcher2 = pattern2.matcher(userLoginPasswordResetRequest.getPassword());

            Pattern pattern3 = Pattern.compile(regexNull);
            Matcher matcher3 = pattern3.matcher(userLoginPasswordResetRequest.getPassword());

            int result = 0;
            if (matcher.find()) {
                result += 1;
            }
            if (matcher1.find()) {
                result += 1;
            }
            if (matcher2.find()) {
                result += 1;
            }
            if (matcher3.find()) {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_PASSWORD_FORMAT_ERROR);
                return resultObject;
            }
            if (userLoginPasswordResetRequest.getPassword().length() >= 8 && userLoginPasswordResetRequest.getPassword().length() <= 16) {
                if (result >= 2) {
                    usersPo.setPassword(new BCryptPasswordEncoder().encode(userLoginPasswordResetRequest.getPassword()));
                    usersPo.setPasswordFlag(PlatformTermEnum.PASSWORD_CHANGE_STATUS.MODIFIED.code());
                    usersDao.update(usersPo);
                    // 改了密码就清除旧token
                    userBaseApi.deleteToken(usersPo.getUserId());
                } else {
                    resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_PASSWORD_FORMAT_ERROR);
                }
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_PASSWORD_FORMAT_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_UPDATE_PASSWORD_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject checkUserPwd(String username) {
        ResultObject resultObject = new ResultObject();
        try {
            UsersPo usersPo = usersExtDao.loadUserByUsersName(username);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            if (AssertUtils.isNullString(usersPo.getPasswordFlag())) {
                resultObject.setData("欢迎您首次使用本系统，请先修改密码。");
            } else {
                return resultObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_USER_CHECK_PASSWORD_FAIL);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject<List<UserResponse>> postAgents(List<String> userIds) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<UserResponse> userResponses = new ArrayList<>();
            userIds.forEach(s -> {
                ResultObject<UserResponse> userResponseResultObject = this.loadBusinessUserDetailById(s);
                if (!AssertUtils.isResultObjectDataNull(userResponseResultObject)) {
                    userResponses.add(userResponseResultObject.getData());
                }
            });
            resultObject.setData(userResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    /**
     * 加载用户业务信息
     *
     * @param userId
     * @return
     */
    @Override
    public ResultObject<UserResponse> loadBusinessUserDetailById(String userId) {
        ResultObject<UserResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            UsersPo usersBo = usersExtDao.loadUserPo(userId);
            if (!AssertUtils.isNotNull(usersBo)) {
                return resultObject;
            }
            UserResponse userResponse = (UserResponse) this.converterObject(usersBo, UserResponse.class);
            //查询微信用户
            UserWeixinRelationPo userWeixinRelationPo = usersWeixinExtDao.loadUserWeixinRelationPoByUserId(userId);
            if (AssertUtils.isNotNull(userWeixinRelationPo)) {
                UserWeixinRelationResponse userWeixinRelationResponse = (UserWeixinRelationResponse) this.converterObject(userWeixinRelationPo, UserWeixinRelationResponse.class);
                userResponse.setUserWeixin(userWeixinRelationResponse);
            }
            //查询userAppDevice
            UserAppDevicePo userAppDevicePo = userAppDeviceExtDao.queryUserAppDevicePo(userId, userResponse.getDeviceChannelId());
            if (AssertUtils.isNotNull(userAppDevicePo)) {
                UserAppDeviceResponse userAppDeviceResponse = (UserAppDeviceResponse) this.converterObject(userAppDevicePo, UserAppDeviceResponse.class);
                userResponse.setUserAppDevice(userAppDeviceResponse);
            }
            //查询钉钉用户
            UserDingRelationPo userDingRelationPo = userDingExDao.loadUserDingRelationPoByUserId(userId);
            if (AssertUtils.isNotNull(userDingRelationPo)) {
                UserDingRelationResponse userDingRelationResponse = (UserDingRelationResponse) this.converterObject(userDingRelationPo, UserDingRelationResponse.class);
                userResponse.setUserDing(userDingRelationResponse);
            }

            resultObject.setData(userResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<UserResponse>> userInfoDetailGet(List<String> userIds) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<UsersBo> usersBos = usersExtDao.loadUserDetailBos(userIds);

            if (!AssertUtils.isNotEmpty(usersBos)) {
                return resultObject;
            }
            List<UserResponse> userResponses = (List<UserResponse>) this.converterList(usersBos, new TypeToken<List<UserResponse>>() {
                }.getType());

            resultObject.setData(userResponses);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<String>> loadResourceUserRecentList(String resourceCode, String branchId) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), resourceCode, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);
            //资源用户列表
            List<ResourceUserBo> resourceUserBoList = usersExtDao.loadResourceUserList(resourceCode, branchId);

            List<String> userIds = new ArrayList<>();
            //是否为空
            if (AssertUtils.isNotEmpty(resourceUserBoList)) {
                //查询机构
                List<BranchLevelBo> branchLevelBos = branchExtDao.loadParentBranchs(branchId);
                if (AssertUtils.isNotEmpty(branchLevelBos)) {
                    for (BranchLevelBo branchLevelBo : branchLevelBos) {
                        List<ResourceUserBo> resourceUserBos = resourceUserBoList.stream().filter(resourceUserBo -> resourceUserBo.getBranchId().equals(branchLevelBo.getBranchId())).collect(Collectors.toList());
                        if (AssertUtils.isNotEmpty(resourceUserBos)) {
                            resourceUserBos.forEach(resourceUserBo -> {
                                if (!userIds.contains(resourceUserBo.getUserId())) {
                                    userIds.add(resourceUserBo.getUserId());
                                }
                            });
                            break;
                        }
                    }
                }
                resultObject.setData(userIds);
            }
            System.out.println(resourceUserBoList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<String>> loadResourceUserList(String resourceCode, String branchId) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), resourceCode, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);
            //资源用户列表
            List<ResourceUserBo> resourceUserBoList = usersExtDao.loadResourceUserList(resourceCode, branchId);
            //是否为空
            if (AssertUtils.isNotEmpty(resourceUserBoList)) {
                List<String> userIds = resourceUserBoList.stream().map(ResourceUserBo::getUserId).collect(Collectors.toList());
                resultObject.setData(userIds);
            }
            System.out.println(resourceUserBoList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject rollbackRegister(String userId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //回滚用户机构
            List<UserBranchPo> listUserBranchPo = userBranchDao.fetchByUserId(userId);
            if (AssertUtils.isNotEmpty(listUserBranchPo)) {
                listUserBranchPo.forEach(userBranchPo -> userBranchDao.delete(userBranchPo));
            }
            //回滚用户
            usersDao.deleteById(userId);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_USERS_INFO_ROLLBACK_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 数据转换
     *
     * @param usersPo
     * @throws RequestException
     */
    public void conversionAddUser(UsersPo usersPo, UserRequest userRequest) throws RequestException {
        try {
            if (!StringUtil.isNullString(userRequest.getUsername())) {
                usersPo.setUsername(userRequest.getUsername());
            }
            if (StringUtil.isNullString(usersPo.getUserId())) {
                usersPo.setPassword(new BCryptPasswordEncoder().encode("88888888"));
            }
            if (!StringUtil.isNullString(userRequest.getEnabled())) {
                usersPo.setEnabled("ENABLED");
            }
            if (!StringUtil.isNullString(userRequest.getName())) {
                usersPo.setName(userRequest.getName());
            }
            if (!StringUtil.isNullString(userRequest.getSex())) {
                usersPo.setGender("FMALE");
            }
            if (!StringUtil.isNullString(userRequest.getEmail())) {
                usersPo.setEmail(userRequest.getEmail());
            }

            if (AssertUtils.isNotEmpty(userRequest.getLanguage())) {

                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(userRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());
                } else if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(userRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                } else if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(userRequest.getLanguage().toUpperCase())) {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.KM_KH.name());
                } else {
                    usersPo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                }
            }

            usersPo.setCreatedDate(System.currentTimeMillis());
            usersPo.setUpdatedDate(System.currentTimeMillis());
            usersPo.setLastLogin(System.currentTimeMillis());
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_CONVERSION_USEROAUTHRESPONSE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_CONVERSION_USEROAUTHRESPONSE_ERROR);
        }
    }


    @Override
    @Transactional
    public ResultObject<List<SaveUserResponse>> agentSaveUsers(List<SaveUserRequest> userRequestList, Users currentLoginUsers) {
        ResultObject<List<SaveUserResponse>> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(userRequestList)) {
                return resultObject;
            }
            List<SaveUserResponse> saveUserResponseList = new ArrayList<>();
            List<UsersVo> usersVoList = new ArrayList<>();
            List<UsersBranchVo> usersBranchVoList = new ArrayList<>();
            List<String> mobileList = userRequestList.stream().map(saveUserRequest -> saveUserRequest.getMobile()).collect(Collectors.toList());
            List<UsersPo> usersPoList = usersBaseService.queryUsersPoByMobiles(mobileList.stream().toArray(String[]::new));
            if (AssertUtils.isNotEmpty(usersPoList)) {
                throw new RequestException(PLATFORM_SAVE_USER_MOBILE_REPEAT_IS_NOT_NULL);
            }

            List<String> branchCodeList = userRequestList.stream().map(saveUserRequest -> saveUserRequest.getBranchCode()).collect(Collectors.toList());
            List<BranchDo> branchDoList = branchBaseService.queryBranchByCode(branchCodeList);
            userRequestList.forEach(saveUserRequest -> {
                String userId = saveUserRequest.getUserId();
                SaveUserResponse saveUserResponse = new SaveUserResponse();
                saveUserResponse.setAgentMobile(saveUserRequest.getMobile());
                saveUserResponse.setUserId(userId);
                saveUserResponseList.add(saveUserResponse);
                //用户
                UsersVo usersVo = new UsersVo();
                usersVo.setUserId(userId);
                usersVo.setUsername(saveUserRequest.getDeviceChannelId() + "_" + saveUserRequest.getMobile());
                usersVo.setPassword(new BCryptPasswordEncoder().encode("88888888"));
                usersVo.setEnabled("ENABLED");
                usersVo.setName(saveUserRequest.getAgentName());
                usersVo.setGender("FEMALE");
                usersVo.setMobile(saveUserRequest.getMobile());
                usersVo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                usersVo.setCreatedDate(System.currentTimeMillis());
                usersVo.setUpdatedDate(System.currentTimeMillis());
                usersVo.setLastLogin(System.currentTimeMillis());
                usersVo.setCountryCode(currentLoginUsers.getCountryCode());
                usersVo.setDeviceChannelId(saveUserRequest.getDeviceChannelId());
                usersVoList.add(usersVo);
                //机构
                UsersBranchVo usersBranchVo = new UsersBranchVo();
                usersBranchVo.setUserBranchId(UUIDUtils.getUUIDShort());
                Optional<BranchDo> firstBranchDo = branchDoList.stream().filter(branchDo -> branchDo.getBranchCode().equals(saveUserRequest.getBranchCode())).findFirst();
                if (firstBranchDo.isPresent()) {
                    BranchDo branchDo = firstBranchDo.get();
                    usersBranchVo.setBranchId(branchDo.getBranchId());
                }
                usersBranchVo.setUserId(userId);
                usersBranchVo.setCreatedUserId(currentLoginUsers.getUserId());
                usersBranchVo.setCreatedDate(System.currentTimeMillis());
                usersBranchVo.setUpdatedDate(System.currentTimeMillis());
                usersBranchVoList.add(usersBranchVo);
            });
            usersBaseService.insertUsersVo(usersVoList);
            usersBranchBaseService.insertUsersBranchVo(usersBranchVoList);
            resultObject.setData(saveUserResponseList);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_USERS_INFO_ROLLBACK_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<SaveUserResponse> agentSaveUsers(SaveUserRequest userRequest, Users currentLoginUsers) {
        ResultObject<SaveUserResponse> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotNull(userRequest)) {
                return resultObject;
            }
            userRequest.setCreatedUserId(currentLoginUsers.getUserId());
            userRequest.setCountryCode(currentLoginUsers.getCountryCode());
            UsersPo usersPo = usersBaseService.queryOneUsersPoById(userRequest.getUserId());
            if (AssertUtils.isNotNull(usersPo)) {
                usersBaseService.updateUsersVo(userRequest);
            } else {
                usersBaseService.insertUsersVo(userRequest);
            }
            if (AssertUtils.isNotNull(userRequest.getAccount())) {
                ResultObject accountResultObject = accountBusinessService.saveAccount(userRequest.getAccount());
                AssertUtils.isResultObjectError(this.getLogger(), accountResultObject);
            }
            // 保存账户信息
            if (AssertUtils.isNotEmpty(userRequest.getAccounts())) {
                ResultObject accountResultObject = accountBusinessService.saveAccount(userRequest.getAccounts());
                AssertUtils.isResultObjectError(this.getLogger(), accountResultObject);
            } else {
                // 删除账户
                ResultObject accountResultObject = accountBusinessService.deleteAccount(userRequest.getUserId());
                AssertUtils.isResultObjectError(this.getLogger(), accountResultObject);
            }
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_USERS_INFO_ROLLBACK_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    /**
     * 获取有该业务配置的角色对应用户
     *
     * @param businessCode 业务编码
     * @param branchId     机构ID
     * @return
     */
    @Override
    public ResultObject<List<String>> loadBusinessUserRecentList(String businessCode, String branchId, String branchMode) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        try {
            this.getLogger().info("businessCode:" + businessCode);
            this.getLogger().info("branchId:" + branchId);
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), businessCode, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BUSINESS_CODE_IS_NOT_NULL);
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            BranchPo branchPo = branchBaseService.queryOneBranchById(branchId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //机构模式为空，取机构对应的渠道
            if (!AssertUtils.isNotEmpty(branchMode)) {
                branchMode = PlatformTermEnum.BRANCH_MODE.SALES.name();
                if (PlatformTermEnum.BRANCH_MODE.MANAGER.name().equals(branchPo.getChannelTypeCode())) {
                    branchMode = PlatformTermEnum.BRANCH_MODE.MANAGER.name();
                }
            }
            //业务用户列表
            List<BusinessUserBo> businessUserBoList = usersExtDao.loadBusinessUserList(businessCode, branchId, branchMode);
            System.out.println("businessUserBoList:" + JSON.toJSONString(businessUserBoList));

            List<String> userIds = new ArrayList<>();
            //是否为空
            if (AssertUtils.isNotEmpty(businessUserBoList)) {
//                //查询机构
//                List<BranchLevelBo> branchLevelBos = branchExtDao.loadParentBranchs(branchId);
//                if (AssertUtils.isNotEmpty(branchLevelBos)) {
//                    for (BranchLevelBo branchLevelBo : branchLevelBos) {
//                        List<BusinessUserBo> businessUserBos = businessUserBoList.stream()
//                                .filter(businessUserBo -> businessUserBo.getBranchId().equals(branchLevelBo.getBranchId()))
//                                .collect(Collectors.toList());
//                        if (AssertUtils.isNotEmpty(businessUserBos)) {
//                            businessUserBos.forEach(businessUserBo -> {
//                                if (!userIds.contains(businessUserBo.getUserId())) {
//                                    userIds.add(businessUserBo.getUserId());
//                                }
//                            });
//                            break;
//                        }
//                    }
//                }
                userIds = businessUserBoList.stream().map(BusinessUserBo::getUserId).distinct().collect(Collectors.toList());
                resultObject.setData(userIds);
            }
            System.out.println("userIds:" + JSON.toJSONString(userIds));
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<String>> loadBusinessUserRecentListNew(String businessCode) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        try {
            this.getLogger().info("businessCode:" + businessCode);
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), businessCode, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BUSINESS_CODE_IS_NOT_NULL);
            //机构模式为空，取机构对应的渠道
            //业务用户列表
            List<BusinessUserBo> businessUserBoList = usersExtDao.loadBusinessUserListNew(businessCode);
            System.out.println("businessUserBoList:" + JSON.toJSONString(businessUserBoList));

            List<String> userIds = new ArrayList<>();
            //是否为空
            if (AssertUtils.isNotEmpty(businessUserBoList)) {
                userIds = businessUserBoList.stream().map(BusinessUserBo::getUserId).collect(Collectors.toList());
            }
            resultObject.setData(userIds);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
        }
        return resultObject;
    }

    /**
     * 检测用户是否存在
     *
     * @param userName
     * @param appRequestHeads
     * @return
     */
    @Override
    public ResultObject checkUserWhetherExist(String userName, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject();
        try {
            UsersPo usersPo = usersExtDao.loadUserByUsersName(userName);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_THIS_USER_IS_NOT_EXIST);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getMobile(), PlatformErrorConfigEnum.PLATFORM_USER_MOBILE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getCountryCode(), PlatformErrorConfigEnum.PLATFORM_USER_AREA_CODE_IS_NOT_NULL);
            resultObject.setData(true);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 获取找回密码验证码
     *
     * @param userName
     * @param appRequestHeads
     * @return
     */
    @Override
    public ResultObject getFindPasswordVerifyCodeGenerator(String userName, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject();
        try {
            UsersPo usersPo = usersExtDao.loadUserByUsersName(userName);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_THIS_USER_IS_NOT_EXIST);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getMobile(), PlatformErrorConfigEnum.PLATFORM_USER_MOBILE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getCountryCode(), PlatformErrorConfigEnum.PLATFORM_USER_AREA_CODE_IS_NOT_NULL);

            //参数转换
            SmsVerifyCodeGeneratorRequest smsVerifyCodeGeneratorReqFc = smsVerifyReqFcTransData.transSmsVerifyCodeGenerator(usersPo.getCountryCode(), usersPo.getMobile(), PlatformTermEnum.SMS_TYPE.CHANGE_PASSWORD.name(), appRequestHeads);

            //调用获取验证码服务接口
            resultObject = messageSmsApi.smsVerifyCodeGenerator(smsVerifyCodeGeneratorReqFc);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_VERIFY_CODE_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 校验验证码
     *
     * @param smsVerifyCodeCheckRequest 校验请求参数
     * @return 校验结果
     */
    @Override
    public ResultObject<SmsVerifyCodeCheckResponse> getVerifyCodeCheck(SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest) {
        ResultObject<SmsVerifyCodeCheckResponse> resultObject = new ResultObject();
        try {
            //参数验证
            usersParameterValidate.validBusinessLoginUser(smsVerifyCodeCheckRequest);
            //参数转换
            com.gclife.message.model.request.SmsVerifyCodeCheckRequest smsVerifyCodeCheckReqFc = smsVerifyReqFcTransData.transSmsVerifyCodeCheck(smsVerifyCodeCheckRequest);
//            SmsVerifyCodeCheckRespFc smsVerifyCodeCheckRespFc = new SmsVerifyCodeCheckRespFc();
            //调用校验验证码服务接口
            SmsVerifyCodeCheckResponse smsVerifyCodeCheckRespFc = messageSmsApi.smsVerifyCodeCheck(smsVerifyCodeCheckReqFc).getData();
            resultObject.setData(smsVerifyCodeCheckRespFc);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_VERIFY_CODE_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 找回密码
     *
     * @param request
     * @return
     */
    @Override
    public ResultObject resetPassword(UserLoginPasswordResetRequest request) {
        ResultObject resultObject = new ResultObject();
        try {
            AssertUtils.isNotNull(this.getLogger(), request.getUserName(), PlatformErrorConfigEnum.PLATFORM_USER_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), request.getPassword(), PlatformErrorConfigEnum.PLATFORM_USER_PASSWORD_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), request.getVerifyCode(), PlatformErrorConfigEnum.PLATFORM_USER_VERTIFY_CODE_IS_NOT_NULL);
            UsersPo usersPo = usersExtDao.loadUserByUsersName(request.getUserName());
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getMobile(), PlatformErrorConfigEnum.PLATFORM_QUERY_USER_NOT_FOUND);
            AssertUtils.isNotEmpty(this.getLogger(), usersPo.getCountryCode(), PlatformErrorConfigEnum.PLATFORM_USER_AREA_CODE_IS_NOT_NULL);
            SmsVerifyCodeCheckRequest smsRequest = new SmsVerifyCodeCheckRequest();
            smsRequest.setTypeCode(PlatformTermEnum.SMS_TYPE.CHANGE_PASSWORD.name());
            smsRequest.setMobile(usersPo.getMobile());
            smsRequest.setCountryCode(usersPo.getCountryCode());
            smsRequest.setVerifyCode(request.getVerifyCode());

            // 检测验证码
            SmsVerifyCodeCheckResponse smsVerifyCodeCheckRespFc = this.getVerifyCodeCheck(smsRequest).getData();
            if (!smsVerifyCodeCheckRespFc.getIsPass().equals("Y")) {
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_USER_VERIFY_CODE_IS_NOT_EFFECTIVE);
            }
            // 执行修改密码
            resultObject = this.changeUserPwd(request);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_RESET_PASSWORD_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<UserResponse> getUserByMobileAndDevice(String mobile, String deviceChannel) {
        AssertUtils.isNotEmpty(this.getLogger(), mobile, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
        ResultObject<UserResponse> resultObject = new ResultObject<>();

        UsersBo usersBo = usersExtDao.loadMobileVerify(null, mobile, deviceChannel);
        if (!AssertUtils.isNotNull(usersBo)) {
            return resultObject;
        }
        String userId = usersBo.getUserId();
        UserResponse userResponse = (UserResponse) this.converterObject(usersBo, UserResponse.class);
        //查询微信用户
        UserWeixinRelationPo userWeixinRelationPo = usersWeixinExtDao.loadUserWeixinRelationPoByUserId(userId);
        if (AssertUtils.isNotNull(userWeixinRelationPo)) {
            UserWeixinRelationResponse userWeixinRelationResponse = (UserWeixinRelationResponse) this.converterObject(userWeixinRelationPo, UserWeixinRelationResponse.class);
            userResponse.setUserWeixin(userWeixinRelationResponse);
        }
        //查询userAppDevice
        UserAppDevicePo userAppDevicePo = userAppDeviceExtDao.queryUserAppDevicePo(userId, userResponse.getDeviceChannelId());
        if (AssertUtils.isNotNull(userAppDevicePo)) {
            UserAppDeviceResponse userAppDeviceResponse = (UserAppDeviceResponse) this.converterObject(userAppDevicePo, UserAppDeviceResponse.class);
            userResponse.setUserAppDevice(userAppDeviceResponse);
        }
        //查询钉钉用户
        UserDingRelationPo userDingRelationPo = userDingExDao.loadUserDingRelationPoByUserId(userId);
        if (AssertUtils.isNotNull(userDingRelationPo)) {
            UserDingRelationResponse userDingRelationResponse = (UserDingRelationResponse) this.converterObject(userDingRelationPo, UserDingRelationResponse.class);
            userResponse.setUserDing(userDingRelationResponse);
        }
        resultObject.setData(userResponse);
        return resultObject;
    }

    /**
     * 绑定手机号
     * 1.查询该手机号是否注册用户
     * (1).已注册用户：删除当前用户，更新该手机号对应用户并返回
     * (2).未注册用户：更新当前用户并返回
     *
     * @param mobile          手机号
     * @param users           当前用户
     * @param appRequestHeads
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<UserBaseResponse> bindMobile(String mobile, Users users, AppRequestHeads appRequestHeads) {
        AssertUtils.isNotEmpty(this.getLogger(), mobile, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
        // 查询当前用户
        UsersPo usersPo = usersExtDao.loadUserPo(users.getUserId());
        AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
        // 查询当前用户是否绑定第三方
        List<UserThirdPartyPo> userThirdPartyPos = usersBaseService.listUserThirdParty(usersPo.getUserId());
        // 根据手机号查询用户
        UsersBo usersBo = usersExtDao.loadMobileVerify("855", mobile, appRequestHeads.getDeviceChannel());
        UserBaseResponse userBaseResponse = new UserBaseResponse();
        if (AssertUtils.isNotNull(usersBo)) {
            if (AssertUtils.isNotEmpty(userThirdPartyPos)) {
                // 当前用户第三方登录，查询手机号对应用户是否绑定相应第三方
                UserThirdPartyPo userThirdPartyPo = usersBaseService.queryUserThirdParty(usersBo.getUserId(), userThirdPartyPos.get(0).getThirdPartyCode());
                if (AssertUtils.isNotNull(userThirdPartyPo)) {
                    // 手机号对应用户已绑定第三方
                    throwsException(getLogger(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_EMAIL_BIND_MOBILE_ERROR);
                }
                usersBo.setPasswordFlag(null);
            } else {
                // 当前用户未绑定第三方，必是邮箱登录
                if (AssertUtils.isNotEmpty(usersBo.getEmail())) {
                    // 手机号对应用户已绑定邮箱
                    throwsException(getLogger(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_EMAIL_BIND_MOBILE_ERROR);
                }
                usersBo.setEmail(usersPo.getEmail());
                usersBo.setPassword(usersPo.getPassword());
                if (!AssertUtils.isNotEmpty(usersBo.getName())) {
                    usersBo.setName(usersPo.getName());
                }
                if (!AssertUtils.isNotEmpty(usersBo.getNickName())) {
                    usersBo.setNickName(usersPo.getNickName());
                }
                usersService.saveUsersPo(usersBo);
            }
            // 删除当前用户
            usersDao.delete(usersPo);
            ClazzUtils.copyPropertiesIgnoreNull(usersBo, userBaseResponse);
        } else {
            usersPo.setCountryCode("855");
            usersPo.setMobile(mobile);
            usersService.saveUsersPo(usersPo);
            ClazzUtils.copyPropertiesIgnoreNull(usersPo, userBaseResponse);
        }
        ResultObject<UserBaseResponse> resultObject = new ResultObject<>();
        resultObject.setData(userBaseResponse);
        return resultObject;
    }

    /**
     * 绑定邮箱
     * 1.查询该邮箱是否绑定用户
     * (1).已绑定用户：
     * a.绑定用户无手机号，删除该用户并更新当前用户
     * b.绑定用户有手机号，抛出错误提示信息
     * (2).未绑定用户：更新当前用户
     *
     * @param email           邮箱
     * @param users           当前用户
     * @param appRequestHeads
     * @return
     */
    @Override
    public ResultObject bindEmail(String email, Users users, AppRequestHeads appRequestHeads) {
        AssertUtils.isNotEmpty(this.getLogger(), email, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
        // 查询当前用户
        UsersPo usersPo = usersExtDao.loadUserPo(users.getUserId());
        AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
        // 根据邮箱查询用户
        UsersBo usersBo = usersExtDao.loadMobileVerify(null, email, appRequestHeads.getDeviceChannel());
        if (AssertUtils.isNotNull(usersBo)) {
            if (AssertUtils.isNotEmpty(usersBo.getMobile())) {
                // 邮箱对应用户已绑定手机号
                throwsException(getLogger(), PlatformErrorConfigEnum.PLATFORM_BUSINESS_MOBILE_BIND_EMAIL_ERROR);
            }
            if (!AssertUtils.isNotEmpty(usersPo.getName())) {
                usersPo.setName(usersBo.getName());
            }
            if (!AssertUtils.isNotEmpty(usersPo.getNickName())) {
                usersPo.setNickName(usersBo.getNickName());
            }
            // 删除邮箱对应用户
            usersDao.delete(usersBo);
        }
        usersPo.setEmail(email);
        usersService.saveUsersPo(usersPo);
        return ResultObject.success();
    }

    @Override
    public ResultObject<UserBaseResponse> baseBindMobile(String mobile, String userId, String deviceChannel) {
        Users users = new Users();
        users.setUserId(userId);
        AppRequestHeads appRequestHeads = new AppRequestHeads();
        appRequestHeads.setDeviceChannel(deviceChannel);
        return bindMobile(mobile, users, appRequestHeads);
    }

    @Override
    public ResultObject baseBindEmail(String email, String userId, String deviceChannel) {
        Users users = new Users();
        users.setUserId(userId);
        AppRequestHeads appRequestHeads = new AppRequestHeads();
        appRequestHeads.setDeviceChannel(deviceChannel);
        return bindEmail(email, users, appRequestHeads);
    }

    @Override
    public ResultObject baseUserInfoPut(UserInfoRequest userInfoRequest, String userId, String deviceChannel) {
        Users users = new Users();
        users.setUserId(userId);
        AppRequestHeads appRequestHeads = new AppRequestHeads();
        appRequestHeads.setDeviceChannel(deviceChannel);
        return putUserInfo(userId, userInfoRequest, appRequestHeads);
    }

    @Transactional
    @Override
    public ResultObject temporarilyUserUpdate() {
        List<UsersPo> usersPos = usersExtDao.loadTemporarilyOverTimeUsers();
        if (AssertUtils.isNotEmpty(usersPos)) {
            usersPos.forEach(usersPo -> {
                usersPo.setEnabled(TerminologyConfigEnum.ENABLED.DISABLED.name());
                usersService.saveUsersPo(usersPo);
            });
        }
        return ResultObject.success();
    }


}