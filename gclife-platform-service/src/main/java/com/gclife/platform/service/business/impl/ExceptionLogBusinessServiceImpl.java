package com.gclife.platform.service.business.impl;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.common.util.http.HttpUtils;
import com.gclife.platform.base.service.PlatformConfigBaseService;
import com.gclife.platform.core.jooq.tables.daos.ExceptionLogDao;
import com.gclife.platform.core.jooq.tables.pojos.ExceptionLogPo;
import com.gclife.platform.core.jooq.tables.pojos.NotifyConfigPo;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.request.DingRobotSendRequest;
import com.gclife.platform.model.request.ExceptionLogRequest;
import com.gclife.platform.service.business.ExceptionLogBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @description:
 * @author: chenjinrong
 * @create: 2021-08-26
 **/
@Service
@Slf4j
public class ExceptionLogBusinessServiceImpl  extends BaseBusinessServiceImpl implements ExceptionLogBusinessService {

    @Autowired
    ExceptionLogDao exceptionLogDao;
    @Autowired
    UsersExtDao usersExtDao;
    @Autowired
    Environment env;
    @Autowired
    PlatformConfigBaseService platformConfigBaseService;
    /**
     * 钉钉机器人密钥
     */
    private String DING_SECRET;
    /**
     * 钉钉机器人webhook
     */
    private String SERVER_URL;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLog(ExceptionLogRequest logRequest) {
        if (AssertUtils.isNotNull(logRequest)) {
            if (AssertUtils.isNotEmpty(logRequest.getExceptionContent())
                    && logRequest.getExceptionContent().contains("org.apache.catalina.connector.ClientAbortException")) {
                log.info("忽略异常通知:org.apache.catalina.connector.ClientAbortException: java.io.IOException: 断开的管道");
                return;
            }
            ExceptionLogPo exceptionLogPo = (ExceptionLogPo) this.converterObject(logRequest, ExceptionLogPo.class);
            exceptionLogPo.setExceptionLogId(UUIDUtils.getUUIDShort());
            exceptionLogPo.setCreatedDate(System.currentTimeMillis());
            exceptionLogPo.setLogDate(LocalDateTime.now());
            if (StringUtils.isNotEmpty(logRequest.getUserId())) {
                UsersPo usersPo = usersExtDao.loadUserPo(logRequest.getUserId());
                if (AssertUtils.isNotNull(usersPo)) {
                    exceptionLogPo.setUsername(usersPo.getName());
                }
            }
            exceptionLogDao.insert(exceptionLogPo);
            sendDingTalkMsg(exceptionLogPo);
        }
    }

    /**
     * 发送钉钉群机器人消息
     * @param exceptionLogPo
     */
    private void sendDingTalkMsg(ExceptionLogPo exceptionLogPo){

        try {
            String activeProfile = env.getProperty("spring.cloud.config.profile")+env.getProperty("spring.profiles.active");
            System.out.println(activeProfile);
            if(!activeProfile.contains("prod")){
                return;
            }
            if(StringUtils.isEmpty(DING_SECRET)){
                NotifyConfigPo notifyConfig = platformConfigBaseService.getNotifyConfig("EXCEPTION_DING_SECRET");
                DING_SECRET = notifyConfig.getNotifyUrl();
            }
            if(StringUtils.isEmpty(SERVER_URL)){
                NotifyConfigPo notifyConfig = platformConfigBaseService.getNotifyConfig("EXCEPTION_DING_URL");
                SERVER_URL = notifyConfig.getNotifyUrl();
            }

            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + DING_SECRET;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(DING_SECRET.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)),"UTF-8");

            DingRobotSendRequest param = new DingRobotSendRequest();
            param.setMsgtype("markdown");
            DingRobotSendRequest.Markdown markdown = new DingRobotSendRequest.Markdown();
            markdown.setTitle("## ["+exceptionLogPo.getServiceName()+"]");

            String text = "* `traceId：`TRACEID\n" +
                    "* `异常时间：`OPERATORTIME\n" +
                    "* `操作者：`OPERATOR\n" +
                    "* `异常信息：`EXCEPTIONMSG";
            String traceId = StringUtils.isNotEmpty(exceptionLogPo.getTraceId())?exceptionLogPo.getTraceId():"";
            String userName = StringUtils.isNotEmpty(exceptionLogPo.getUsername())?exceptionLogPo.getUsername():"";
            markdown.setText(text.replace("TRACEID",traceId).replace("OPERATORTIME",DateUtils.dateToString(new Date(),DateUtils.FORMATE6)).replace("OPERATOR",userName).replace("EXCEPTIONMSG",exceptionLogPo.getExceptionContent()));
            param.setMarkdown(markdown);

            String res = HttpUtils.getInstance().post(SERVER_URL+"&timestamp="+timestamp+"&sign="+sign)
                    .setParameterJson(param).execute().getString();

        }catch ( Exception e){
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
    }
}
