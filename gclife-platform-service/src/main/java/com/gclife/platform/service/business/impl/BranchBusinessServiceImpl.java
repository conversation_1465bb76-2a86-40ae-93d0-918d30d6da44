package com.gclife.platform.service.business.impl;


import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.platform.base.model.bo.BranchDo;
import com.gclife.platform.base.model.bo.BranchTreeDo;
import com.gclife.platform.base.service.BranchBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BranchBankPo;
import com.gclife.platform.core.jooq.tables.pojos.FinancialBranchPo;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.dao.BranchExtDao;
import com.gclife.platform.model.bo.BranchBo;
import com.gclife.platform.model.bo.BranchLevelBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.BranchBusinessService;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import com.gclife.platform.validate.transfer.BranchTransfer;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.vo.branch.BranchBankResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class BranchBusinessServiceImpl extends BaseBusinessServiceImpl implements BranchBusinessService {

    @Autowired
    private BranchExtDao branchExtDao;

    @Autowired
    private BranchTransfer branchTransfer;

    @Autowired
    private InternationalBaseBusinessService internationalBaseBusinessService;

    @Autowired
    private TerminologyBaseBusinessService terminologyBaseBusinessService;

    @Autowired
    private BaseSysCodeExtDao baseSysCodeExtDao;

    @Autowired
    private BranchBaseService branchBaseService;

    @Autowired
    private AttachmentApi attachmentApi;

    /**
     * 根据机构ID加载机构信息
     *
     * @param branchId 机构Id
     * @param users    当前用户信息
     * @return
     */
    @Override
    public ResultObject loadBranchInfo(Users users, String branchId) {

        ResultObject<BranchResponse> resultObject = new ResultObject<BranchResponse>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            String language = TerminologyConfigEnum.LANGUAGE.ZH_CN.name();
            if (AssertUtils.isNotNull(users)) {
                language = users.getLanguage();
            }
            //查询机构
            BranchBo branchBo = branchExtDao.loadBranchsById(branchId, language);
            //机构国际化
            this.internationalBranch(language, branchBo);
            //机构验证
            AssertUtils.isNotNull(this.getLogger(), branchBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //机构名称国际化

            //数据转换
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchBo, BranchResponse.class);
            branchResponse.setBranchBank((BranchBankResponse) this.converterObject(branchBaseService.queryOneBranchBankById(branchId), BranchBankResponse.class));
            //设置返回值
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据机构ID加载机构信息,根据语言国际化机构名称
     *
     * @param branchId 机构Id
     * @param language 国际化语言
     * @return ResultObject
     */
    @Override
    public ResultObject<BranchResponse> loadBranchInfo(String branchId, String language) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            if (!AssertUtils.isNotEmpty(language) || !(TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)
                    || TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)
                    || TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language))) {
                // 语言为空或格式错误,默认打印英语
                language = TerminologyConfigEnum.LANGUAGE.EN_US.name();
            }

            //查询机构
            BranchBo branchBo = branchExtDao.loadBranchsById(branchId, language);
            //机构国际化
            if (!TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language) && AssertUtils.isNotNull(branchBo)) {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(branchBo.getBranchCode(), TerminologyTypeEnum.BRANCH_NAME.name(), language);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    branchBo.setBranchName(syscodeBo.getCodeName());
                    branchBo.setBranchShortname(syscodeBo.getCodeName());
                }
            }
            //机构验证
            AssertUtils.isNotNull(this.getLogger(), branchBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);

            //数据转换
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchBo, BranchResponse.class);
            //设置返回值
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 根据机构ID加载机构信息
     *
     * @param branchIds 机构查询参数
     * @param users     当前用户信息
     * @return
     */
    @Override
    public ResultObject<List<BranchResponse>> loadBranchsByIds(Users users, List<String> branchIds) {

        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            //查询机构
            List<BranchBo> branchBos = branchExtDao.loadBranchsByIds(branchIds, users.getLanguage());
            //机构验证
            AssertUtils.isNotEmpty(this.getLogger(), branchBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //机构名称国际化
            this.internationalBranchList(users, branchBos);
            //数据转换
            List<BranchResponse> branchResponses = (List<BranchResponse>) this.converterList(branchBos, new TypeToken<List<BranchResponse>>() {
            }.getType());
            List<BranchBankPo> branchBankPos = branchBaseService.queryBranchBankById(branchIds);
            if (AssertUtils.isNotEmpty(branchBankPos)) {
                branchBankPos.forEach(branchBankPo -> {
                    branchResponses.stream().filter(branchResponse -> AssertUtils.isNotEmpty(branchBankPo.getBranchId())
                            && branchBankPo.getBranchId().equals(branchResponse.getBranchId())).findFirst().ifPresent(branchResponse -> {
                        BranchBankResponse branchBank = new BranchBankResponse();
                        ClazzUtils.copyPropertiesIgnoreNull(branchBankPo, branchBank);
                        branchResponse.setBranchBank(branchBank);
                    });
                });
            }
            //设置返回值
            resultObject.setData(branchResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 根据当前用户管理的销售机构查询机构树
     *
     * @param users 当前用户信息
     * @return ResultObject<List < BranchTreeResponse>>
     */
    @Override
    public ResultObject loadUserBranchTree(Users users) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<List<BranchTreeResponse>>();
        try {
            //查询机构
            //查询所有的管理机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadUserManagerBranchList(users.getUserId());
            //名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            //转换机构树
            List<BranchLevelBo> branchTrees = branchTransfer.transferBranchTree(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchTrees, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchTreeResponse> branchTreeRespons = (List<BranchTreeResponse>) this.converterList(branchTrees, new TypeToken<List<BranchTreeResponse>>() {
            }.getType());

            resultObject.setData(branchTreeRespons);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject loadBranchTreesByBranchId(Users users, String branchId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<List<BranchTreeResponse>>();
        try {
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadBranchAllChildsBranchList(branchId);
            //名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            //转换树
            List<BranchLevelBo> branchTrees = branchTransfer.transferBranchTree(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchTrees, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchTreeResponse> branchTreeRespons = (List<BranchTreeResponse>) this.converterList(branchTrees, new TypeToken<List<BranchTreeResponse>>() {
            }.getType());

            resultObject.setData(branchTreeRespons);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject loadUserManagerAllBranchs(Users users) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadUserManagerBranchList(users.getUserId());
            //名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchLevelBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchDownlistResponses = (List<BranchResponse>) this.converterList(branchLevelBos, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchDownlistResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 查询用户管理的机构
     *
     * @param users 当前用户信息
     * @return
     */
    @Override
    public ResultObject<List<BranchResponse>> loadUserManagerRootBranchs(Users users) {

        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchBo> branchBos = branchExtDao.loadManagerRootBranchs(users.getUserId());
            //机构名称国际化
            this.internationalBranchList(users, branchBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchDownlistResponses = (List<BranchResponse>) this.converterList(branchBos, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchDownlistResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 根据当前用户管理的销售机构查询机构树叶子节点(营业部)
     *
     * @param users 当前用户信息
     * @return
     */
    @Override
    public ResultObject loadUserBranchTreeLeaf(Users users) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询管理的所有机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadUserManagerBranchList(users.getUserId());
            //机构名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            List<BranchLevelBo> leafBranchList = branchTransfer.transferBranchFilterLeaf(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), leafBranchList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchLeafResponses = (List<BranchResponse>) this.converterList(leafBranchList, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchLeafResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 根据当前用户管理的销售机构查询机构树 叶子节点
     *
     * @param users 当前用户信息
     * @return ResultObject<List < BranchTreeResponse>>
     */
    @Override
    public ResultObject loadUserBranchTreeLeaf2(Users users) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<List<BranchTreeResponse>>();
        try {
            //查询机构
            //查询所有的管理机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadUserManagerBranchList(users.getUserId());
            //名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            List<BranchLevelBo> leafBranchList = branchTransfer.transferBranchFilterLeaf(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), leafBranchList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchTreeResponse> branchTreeRespons = (List<BranchTreeResponse>) this.converterList(leafBranchList, new TypeToken<List<BranchTreeResponse>>() {
            }.getType());
            resultObject.setData(branchTreeRespons);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据机构列表查询机构叶子节点
     *
     * @param branchIds 机构集合
     * @return
     */
    @Override
    public ResultObject loadBranchAllBranchLeaf(Users users, List<String> branchIds) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadBranchAllChildsBranchList(branchIds);
            //机构名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);
            //数据筛选
            List<BranchLevelBo> leafBranchList = branchTransfer.transferBranchFilterLeaf(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), leafBranchList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchLeafResponses = (List<BranchResponse>) this.converterList(leafBranchList, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchLeafResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据机构查询机构叶子节点
     *
     * @param branchId 机构集合
     * @return
     */
    @Override
    public ResultObject<List<BranchResponse>> loadBranchAllBranchLeaf(Users users, String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadBranchAllChildsBranchList(branchId);
            //机构名称国际化
            this.internationalBranchLevelList(users, branchLevelBos);

            //筛选叶子子节点
            List<BranchLevelBo> leafBranchBos = branchTransfer.transferBranchFilterLeaf(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), leafBranchBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchLeafResponses = (List<BranchResponse>) this.converterList(leafBranchBos, new TypeToken<List<BranchResponse>>() {
            }.getType());
            resultObject.setData(branchLeafResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 查询机构树
     *
     * @param branchIds 机构IDS
     * @return
     */
    @Override
    public ResultObject<List<BranchTreeResponse>> loadBranchTreeByBranchIds(Users users, List<String> branchIds) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<List<BranchTreeResponse>>();
        try {
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadBranchAllChildsBranchList(branchIds);
            this.internationalBranchLevelList(users, branchLevelBos);
            //转换树
            List<BranchLevelBo> branchTrees = branchTransfer.transferBranchTree(branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchTrees, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchTreeResponse> branchTreeRespons = (List<BranchTreeResponse>) this.converterList(branchTrees, new TypeToken<List<BranchTreeResponse>>() {
            }.getType());

            resultObject.setData(branchTreeRespons);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 查询父机构树集合
     *
     * @param branchId 　销售机构
     * @return
     */
    @Override
    public ResultObject loadParentBranchs(Users users, String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadParentBranchs(branchId);
            this.internationalBranchLevelList(users, branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchLevelBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchLeafResponses = (List<BranchResponse>) this.converterList(branchLevelBos, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchLeafResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 查询父机构树集合
     *
     * @param branchIds 　销售机构集合
     * @return
     */
    @Override
    public ResultObject loadParentBranchs(Users users, List<String> branchIds) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<List<BranchResponse>>();
        try {
            //查询机构
            List<BranchLevelBo> branchLevelBos = branchExtDao.loadParentBranchs(branchIds);
            this.internationalBranchLevelList(users, branchLevelBos);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchLevelBos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<BranchResponse> branchLeafResponses = (List<BranchResponse>) this.converterList(branchLevelBos, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(branchLeafResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BranchNameResponse> loadAllBranchLeafName(String language, String branchId) {
        ResultObject<BranchNameResponse> resultObject = new ResultObject<>();
        BranchNameResponse branchNameResponse = new BranchNameResponse();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            //查询机构
            List<BranchNameTreeResponse> branchNameTreeResponses = branchExtDao.loadParentBranchName(branchId);
            //机构名称国际化
            if (!PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name().equals(language) && AssertUtils.isNotEmpty(branchNameTreeResponses)) {
                List<SyscodeBo> syscodeBoList = baseSysCodeExtDao.loadInternationalListByCodes(branchNameTreeResponses.stream().map(BranchNameTreeResponse::getBranchId).collect(Collectors.toList()), TerminologyTypeEnum.BRANCH_NAME.name(), language);
                if (AssertUtils.isNotNull(syscodeBoList)) {
                    branchNameTreeResponses.forEach(branchBo -> {
                        syscodeBoList.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(branchBo.getBranchId())).findFirst().ifPresent(syscodeBo -> {
                            branchBo.setBranchName(syscodeBo.getCodeName());
                            branchBo.setBranchShortname(syscodeBo.getCodeName());
                        });
                    });
                }
            }
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), branchNameTreeResponses, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<String> branchNames = new ArrayList<>();
            branchNameTreeResponses.forEach(branchNameTreeResponse -> branchNames.add(branchNameTreeResponse.getBranchShortname()));
            String branchName = StringUtils.join(branchNames.toArray(), "");
            branchNameResponse.setBranchName(branchName);

            resultObject.setData(branchNameResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BranchResponse> loadUserBranch(Users users, String userId) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            BranchBo branchBo = branchExtDao.getBranchByUserId(userId);
            //机构国际化
            String language = TerminologyConfigEnum.LANGUAGE.ZH_CN.name();
            if (AssertUtils.isNotNull(users)) {
                language = users.getLanguage();
            }
            this.internationalBranch(language, branchBo);
            if (AssertUtils.isNotNull(branchBo)) {
                //数据转换
                BranchResponse branchResponse = (BranchResponse) this.converterObject(branchBo, BranchResponse.class);
                //设置返回值
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 机构国际化
     *
     * @param language 用户信息
     * @param branchBo 机构信息
     */
    private void internationalBranch(String language, BranchBo branchBo) {
        //机构名称国际化
        if (!PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name().equals(language) && AssertUtils.isNotNull(branchBo)) {
            SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(branchBo.getBranchCode(), TerminologyTypeEnum.BRANCH_NAME.name(), language);
            if (AssertUtils.isNotNull(syscodeBo)) {
                branchBo.setBranchName(syscodeBo.getCodeName());
                branchBo.setBranchShortname(syscodeBo.getCodeName());
            }
        }
    }

    /**
     * 机构国际化
     *
     * @param users          用户信息
     * @param branchLevelBos 层级机构
     */
    private void internationalBranchLevelList(Users users, List<BranchLevelBo> branchLevelBos) {
        //机构名称国际化
        if (!PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name().equals(users.getLanguage()) && AssertUtils.isNotEmpty(branchLevelBos)) {
            List<SyscodeBo> syscodeBoList = baseSysCodeExtDao.loadInternationalListByCodes(branchLevelBos.stream().map(BranchLevelBo::getBranchId).collect(Collectors.toList()), TerminologyTypeEnum.BRANCH_NAME.name(), users.getLanguage());
            if (AssertUtils.isNotNull(syscodeBoList)) {
                branchLevelBos.forEach(branchBo -> {
                    syscodeBoList.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(branchBo.getBranchId())).findFirst().ifPresent(syscodeBo -> {
                        branchBo.setBranchName(syscodeBo.getCodeName());
                        branchBo.setBranchShortname(syscodeBo.getCodeName());
                    });
                });
            }
        }
    }

    /**
     * 机构国际化
     *
     * @param users     用户信息
     * @param branchBos 层级机构
     */
    private void internationalBranchList(Users users, List<BranchBo> branchBos) {
        //机构名称国际化
        if (!PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name().equals(users.getLanguage()) && AssertUtils.isNotEmpty(branchBos)) {
            List<SyscodeBo> syscodeBoList = baseSysCodeExtDao.loadInternationalListByCodes(branchBos.stream().map(BranchBo::getBranchId).collect(Collectors.toList()), TerminologyTypeEnum.BRANCH_NAME.name(), users.getLanguage());
            if (AssertUtils.isNotNull(syscodeBoList)) {
                branchBos.forEach(branchBo -> {
                    syscodeBoList.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(branchBo.getBranchId())).findFirst().ifPresent(syscodeBo -> {
                        branchBo.setBranchName(syscodeBo.getCodeName());
                        branchBo.setBranchShortname(syscodeBo.getCodeName());
                    });
                });
            }
        }
    }

    @Override
    public ResultObject<List<BranchResponse>> loadBreachByCode(Users currentLoginUsers, String... branchCodes) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchDo> branchDoList = branchBaseService.queryBranchByCode(Arrays.asList(branchCodes));
            if (!AssertUtils.isNotEmpty(branchDoList)) {
                return null;
            }

            //数据转换
            List<BranchResponse> careerResponses = (List<BranchResponse>) this.converterList(branchDoList, new TypeToken<List<BranchResponse>>() {
            }.getType());

            resultObject.setData(careerResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * @param response
     * @param users
     */
    @Override
    public void exportBranchs(HttpServletResponse response, Users users) {
        try {
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(PlatformTermEnum.IMPORT_EXPORT_TEMPLATE.BRANCH_EXPORT_TEMPLATE.name());
            String urlPath = attachmentRespFcResultObject.getData().getUrl();
            URL url = new URL(urlPath);
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            List<BranchTreeDo> branchTreeDos = branchBaseService.querySalesBranchLeafByUserId(users.getUserId());
            if (AssertUtils.isNotEmpty(branchTreeDos)) {
                try {
                    int startRow = 4;
                    int rowNum = sheet.getLastRowNum();
                    for (int i = 0; i < branchTreeDos.size(); i++) {
                        BranchTreeDo branchTreeDo = branchTreeDos.get(i);
                        XSSFRow writeRow = null;
                        if (i + startRow < rowNum) {
                            writeRow = sheet.getRow(i + startRow);
                        } else {
                            writeRow = sheet.createRow(i + startRow);
                            // 以内容首行样式作为参考
                            writeRow.setRowStyle(sheet.getRow(startRow).getRowStyle());
                            for (int c = 0; c <= 8; c++) {
                                writeRow.createCell(c);
                                writeRow.getCell(c).setCellStyle(sheet.getRow(startRow).getCell(c).getCellStyle());
                            }
                            // 序号
                            writeRow.getCell(0).setCellValue(i + 1);
                        }

                        String branchCode = branchTreeDo.getBranchCode();
                        String branchName = branchTreeDo.getBranchName();
                        if (AssertUtils.isNotNull(branchTreeDo.getBranchName())) {
                            ResultObject<SyscodeResponse> syscodeResponseResultObjectBranchName = internationalBaseBusinessService.queryOneInternational(TerminologyTypeEnum.BRANCH_NAME.name(), branchTreeDo.getBranchName(), users.getLanguage());
                            if (AssertUtils.isNotNull(syscodeResponseResultObjectBranchName.getData()) && AssertUtils.isNotNull(syscodeResponseResultObjectBranchName.getData().getCodeName())) {
                                branchName = syscodeResponseResultObjectBranchName.getData().getCodeName();
                            }
                        }
                        String branchChannel = "";
                        if (AssertUtils.isNotNull(branchTreeDo.getChannelTypeCode())) {
                            ResultObject<SyscodeResponse> syscodeResponseResultObjectChannelType = internationalBaseBusinessService.queryOneInternational(TerminologyTypeEnum.CHANNEL_TYPE.name(), branchTreeDo.getChannelTypeCode(), users.getLanguage());
                            if (AssertUtils.isNotNull(syscodeResponseResultObjectChannelType.getData())) {
                                branchChannel = syscodeResponseResultObjectChannelType.getData().getCodeName();
                            }
                        }
                        String principal = branchTreeDo.getBranchContactAdminName();
                        String principalPhone = branchTreeDo.getBranchContactAdminMobile();
                        String branchAddr = branchTreeDo.getBranchAddress();
                        String branchStatus = "";
                        if (AssertUtils.isNotNull(branchTreeDo.getBranchStatus())) {
                            ResultObject<SyscodeResponse> syscodeResponseResultObjectBranchStatus = internationalBaseBusinessService.queryOneInternational(TerminologyTypeEnum.BRANCH_STATUS.name(), branchTreeDo.getBranchStatus(), users.getLanguage());
                            if (AssertUtils.isNotNull(syscodeResponseResultObjectBranchStatus.getData())) {
                                branchStatus = syscodeResponseResultObjectBranchStatus.getData().getCodeName();
                            }
                        }
                        writeRow.getCell(1).setCellValue(branchCode);
                        writeRow.getCell(2).setCellValue(branchName);
                        writeRow.getCell(3).setCellValue(branchChannel);
                        writeRow.getCell(4).setCellValue(principal);
                        writeRow.getCell(5).setCellValue(principalPhone);
                        writeRow.getCell(6).setCellValue(branchAddr);
                        writeRow.getCell(7).setCellValue(branchStatus);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            response.addHeader("content-Disposition", "attachment;filename=" + URLEncoder.encode("机构清单.xlsx", "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_EXPORT_BRANCH_ERROR);
        }
    }

    /**
     * 跟据机构ID获取银行网点的机构编号
     *
     * @param users
     * @param branchIds
     * @return
     */
    @Override
    public ResultObject<List<BranchBankResponse>> getBranchBankCode(Users users, List<String> branchIds) {
        ResultObject<List<BranchBankResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(branchIds)) {
            return resultObject;
        }
        List<BranchBankPo> branchBankPos = branchBaseService.queryBranchBankById(branchIds);
        if (!AssertUtils.isNotEmpty(branchBankPos)) {
            return resultObject;
        }
        //数据转换
        List<BranchBankResponse> branchBankResponses = (List<BranchBankResponse>) this.converterList(branchBankPos, new TypeToken<List<BranchBankResponse>>() {
        }.getType());
        resultObject.setData(branchBankResponses);
        return resultObject;
    }

    /**
     * 根据机构ID获取同层级的机构集合
     *
     * @param users
     * @param branchId
     * @return
     */
    @Override
    public ResultObject<List<BranchResponse>> getBranchSameLevel(Users users, String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        List<BranchDo> branchDoList = branchBaseService.getBranchSameLevel(branchId);
        if (!AssertUtils.isNotEmpty(branchDoList)) {
            return resultObject;
        }

        //数据转换
        List<BranchResponse> branchResponses = (List<BranchResponse>) this.converterList(branchDoList, new TypeToken<List<BranchResponse>>() {
        }.getType());
        resultObject.setData(branchResponses);
        return resultObject;
    }

    /**
     * 模糊查询贷款金融机构信息
     *
     * @param users
     * @param keyword
     * @return
     */
    @Override
    public ResultObject<List<FinancialBranchPo>> queryFinancialBranchFuzzy(Users users, String keyword) {
        ResultObject<List<FinancialBranchPo>> resultObject = new ResultObject<>();
        List<FinancialBranchPo> financialBranchPos = branchBaseService.queryFinancialBranchFuzzy(keyword);
        resultObject.setData(financialBranchPos);
        return resultObject;
    }
}