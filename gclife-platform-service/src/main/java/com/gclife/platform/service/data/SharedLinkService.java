package com.gclife.platform.service.data;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.BaseService;
import com.gclife.platform.core.jooq.tables.pojos.SharedLinkPo;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:用于向shared_link插入和删除数据
 */

public interface SharedLinkService extends BaseService{

    void saveSharedLink(SharedLinkPo sharedLinkPo) throws RequestException;
}
