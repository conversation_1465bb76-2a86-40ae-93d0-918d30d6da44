package com.gclife.platform.service.data;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.BaseService;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.model.request.AccountQueryRequest;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:
 */
public interface AccountBoService extends BaseService {
    /**
     * 保存账户信息
     *
     * @param accountBo
     */
     void saveAccountPo(AccountBo accountBo);

    /**
     * 保存账户审核信息
     *
     * @param accountAuditPo
     */
     void saveAccountAuditPo(AccountAuditPo accountAuditPo);


}