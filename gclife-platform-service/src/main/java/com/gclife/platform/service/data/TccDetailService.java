package com.gclife.platform.service.data;

import com.gclife.common.service.BaseService;
import com.gclife.platform.core.jooq.tables.pojos.TccDetailPo;
import com.gclife.platform.core.jooq.tables.pojos.TccEntryPo;

;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午11:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public interface TccDetailService extends BaseService {

    /**
     * 保存TCC操作详细信息
     * @param tccDetailPo
     */
    void saveTccDetailPo(TccDetailPo tccDetailPo);

}