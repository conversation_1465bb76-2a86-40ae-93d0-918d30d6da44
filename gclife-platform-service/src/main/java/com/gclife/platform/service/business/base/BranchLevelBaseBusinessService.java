package com.gclife.platform.service.business.base;


import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.BranchLevelResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BranchLevelBaseBusinessService extends BaseBusinessService {


    /**
     * 根据机构ID信息
     * @param userId 用户ID
     * @param branchId 机构ID
     * @return  ResultObject<List<BranchLevelResponse>>
     */
    ResultObject<List<BranchLevelResponse>> queryBranchLevel(String userId,String branchId);

}
