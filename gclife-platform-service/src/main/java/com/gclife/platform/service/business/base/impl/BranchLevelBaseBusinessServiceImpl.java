package com.gclife.platform.service.business.base.impl;


import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.BranchLevelBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BranchLevelPo;
import com.gclife.platform.model.response.BranchLevelResponse;
import com.gclife.platform.service.business.base.BranchLevelBaseBusinessService;
import com.gclife.platform.service.data.UsersService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class BranchLevelBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements BranchLevelBaseBusinessService {

    @Autowired
    private BranchLevelBaseService branchLevelBaseService;

    @Autowired
    private UsersService usersService;
    /**
     * 根据机构ID信息
     * @param branchId 机构ID
     * @return  ResultObject<List<BranchLevelResponse>>
     */
    @Override
    public ResultObject<List<BranchLevelResponse>> queryBranchLevel(String userId,String branchId) {
        ResultObject<List<BranchLevelResponse>> resultObject = new ResultObject<>();
        List<BranchLevelPo> branchPos=new ArrayList<>();
        try {
            if(!AssertUtils.isNotEmpty(branchId)){
                AssertUtils.isNotEmpty(this.getLogger(),userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            }else if(!AssertUtils.isNotEmpty(userId)){
                AssertUtils.isNotEmpty(this.getLogger(),branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            }
            if(AssertUtils.isNotEmpty(branchId)){
                 branchPos = branchLevelBaseService.queryBranchLevelByBranchId(branchId);
            }else {
                 branchPos = branchLevelBaseService.queryBranchLevelByUserId(userId);
            }
            if(AssertUtils.isNotEmpty(branchPos)){
                List<BranchLevelResponse> branchResponse = (List<BranchLevelResponse>)this.converterList(branchPos,new TypeToken<List<BranchLevelResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(),resultObject,e,PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }
}