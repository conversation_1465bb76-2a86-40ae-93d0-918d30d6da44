package com.gclife.platform.service.data.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.TCCEntry;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.core.jooq.tables.daos.*;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.model.bo.UserLoginLogBo;
import com.gclife.platform.service.data.TccEntryService;
import com.gclife.platform.service.data.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午10:45
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Service
public class TccEntryServiceImpl extends BaseServiceImpl implements TccEntryService {

    @Autowired
    TccEntryDao tccEntryDao;

    @Override
    public void saveTccEntryPo(TccEntryPo tccEntryPo) {
        try {
            tccEntryDao.insert(tccEntryPo);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_TCC_ENTRY_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_TCC_ENTRY_ERROR);
        }
    }
}