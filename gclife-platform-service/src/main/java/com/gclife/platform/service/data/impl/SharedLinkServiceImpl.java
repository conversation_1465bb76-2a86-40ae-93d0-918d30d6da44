package com.gclife.platform.service.data.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.core.jooq.tables.daos.SharedLinkDao;
import com.gclife.platform.core.jooq.tables.pojos.SharedLinkPo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.service.data.SharedLinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:
 */
@Service
public class SharedLinkServiceImpl extends BaseServiceImpl implements SharedLinkService{

    @Autowired
    SharedLinkDao sharedLinkDao;

    @Override
    @Transactional
    public void saveSharedLink(SharedLinkPo sharedLinkPo) throws RequestException{
        try {
            if(!AssertUtils.isNotEmpty(sharedLinkPo.getSharedLinkId())){
                //执行新增
                sharedLinkPo.setSharedLinkId(UUIDUtils.getUUIDShort());
                sharedLinkDao.insert(sharedLinkPo);
            }else{
                //执行修改
                sharedLinkDao.update(sharedLinkPo);
            }
        }catch (Exception e){
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_SHARED_LINK_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_SHARED_LINK_ERROR);
        }
    }
}
