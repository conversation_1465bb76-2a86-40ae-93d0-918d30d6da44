package com.gclife.platform.service.business;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.OperationLogRequest;
import com.gclife.platform.model.response.OperationLogResponse;
import com.gclife.platform.model.response.OperationRoleResponse;
import com.gclife.platform.model.response.OperationUserResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
public interface OperationLogBusinessService extends BaseBusinessService {

    /**
     * 消费日志
     */
    void consumeLog();
    /**
     * 获取操作日志列表
     *
     * @param operationLogRequest
     * @return
     */
    ResultObject<BasePageResponse<OperationLogResponse>> listOperationLog(OperationLogRequest operationLogRequest);

    /**
     * 获取操作用户
     *
     * @param keyword
     * @return
     */
    ResultObject<List<OperationUserResponse>> listOperationUser(String keyword);

    /**
     * 获取报表操作日志
     *
     * @param operationLogRequest
     * @return
     */
    ResultObject<List<OperationLogResponse>> listReportOperationLog(OperationLogRequest operationLogRequest);

    /**
     * 获取所有操作角色
     *
     * @return
     */
    ResultObject<List<OperationRoleResponse>> listOperationRole(Users users);
}
