package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.GB2Alpha;
import com.gclife.finance.api.FinanceUserAccountApi;
import com.gclife.finance.model.request.UserAccountRequest;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.CustomerBaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.dao.CustomerManageDao;
import com.gclife.platform.model.bo.CustomerMessageBo;
import com.gclife.platform.model.bo.CustomerMessagesBo;
import com.gclife.platform.model.bo.MedalBo;
import com.gclife.platform.model.request.CustomerMessagesRequest;
import com.gclife.platform.model.request.UserCustomerBusinessRequest;
import com.gclife.platform.model.request.UserCustomerRequest;
import com.gclife.platform.model.response.CustomerMessageResponse;
import com.gclife.platform.model.response.CustomerMessagesResponse;
import com.gclife.platform.model.response.UserCustomerResponse;
import com.gclife.platform.service.business.CustomerManageService;
import com.gclife.platform.validate.parameter.CustomerParameterValidate;
import com.gclife.platform.validate.transfer.CustomerBaseTransfer;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;


/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
@Service
public class CustomerManageServiceImpl extends BaseBusinessServiceImpl implements CustomerManageService {

    @Autowired
    private CustomerManageDao customerManageDao;
    @Autowired
    private CustomerParameterValidate customerParameterValidate;

    @Autowired
    private FinanceUserAccountApi financeUserAccountApi;

    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;

    @Override
    public ResultObject<BasePageResponse<CustomerMessagesResponse>> getCustomerMessages(Users users, CustomerMessagesRequest customerMessagesRequest) {

        ResultObject<BasePageResponse<CustomerMessagesResponse>> resultObject = new ResultObject<>();
        try {


            List<CustomerMessagesBo> customerMessagesBoList = customerManageDao.getCustomerMessages(users, customerMessagesRequest);

            customerMessagesBoList.forEach(customerMessagesBo -> {
                customerMessagesBo.setPhone(customerMessagesBo.getMobile());
                //一个勋章 没有为准客户
                if (AssertUtils.isNotNull(customerMessagesBo.getApplicant())
                        || AssertUtils.isNotNull(customerMessagesBo.getInsured())
                        || AssertUtils.isNotNull(customerMessagesBo.getBeneficiary())) {
                    customerMessagesBo.setCustomer(PlatformTermEnum.MedalEnum.CUSTOMER.name());
                } else {
                    customerMessagesBo.setQuasiCustomer(PlatformTermEnum.MedalEnum.QUASI_CUSTOMER.name());
                }

            });

            //数据转换
            List<CustomerMessagesResponse> careerResponses = (List<CustomerMessagesResponse>) this.converterList(customerMessagesBoList, new TypeToken<List<CustomerMessagesResponse>>() {
            }.getType());

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(customerMessagesBoList) ? customerMessagesBoList.get(0).getTotalLine() : null;

            BasePageResponse basePageResponse = BasePageResponse.getData(customerMessagesRequest.getCurrentPage(), customerMessagesRequest.getPageSize(), totalLine, careerResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> saveCustomerMessage(UserCustomerRequest userCustomerRequest, Users users) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        try {

            customerParameterValidate.validParameterCustomer(userCustomerRequest);


            //查询客户  是否存在 根据 客户  手机号or身份证号
            List<CustomerAgentPo> customerPoList = customerManageDao.findByIdNo(userCustomerRequest.getCustomerId(), userCustomerRequest.getIdType(), userCustomerRequest.getIdNo(), users.getUserId());

            if (!AssertUtils.isNotEmpty(userCustomerRequest.getCustomerId())&&AssertUtils.isNotEmpty(customerPoList)) {
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_REPEAT_ERROR);
            }
            //查询 是否是 准客户 只有 准客户可以修改,客户信息 禁止修改
            if (AssertUtils.isNotEmpty(userCustomerRequest.getCustomerId())) {
                List<MedalBo> medalList = customerManageDao.getMedalList(userCustomerRequest.getCustomerId());
                if (AssertUtils.isNotEmpty(medalList)) {
                    throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_NO_UPDATE);
                }
            }
            CustomerAgentPo customerAgentPo = new CustomerAgentPo();
            if (AssertUtils.isNotEmpty(userCustomerRequest.getCustomerId())) {
                customerAgentPo = customerBaseService.queryOneCustomerAgent(userCustomerRequest.getCustomerId());
                if (!AssertUtils.isNotNull(customerAgentPo)) {
                    throw new RequestException(PlatformErrorConfigEnum.PLATFORM_UPDATE_CUSTOMER_IS_NULL);
                }
            }
            // 手机号 替换
            String mobile = userCustomerRequest.getPhone();
            userCustomerRequest.setPhone(null);
            ClazzUtils.copy(userCustomerRequest, customerAgentPo);
            customerAgentPo.setMobile(mobile);
            customerAgentPo.setUserId(users.getUserId());
            String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
            customerAgentPo.setGroupCode(groupNo);

            customerBaseService.saveCustomerAgent(customerAgentPo);

            //返回添加客户ID
            UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
            userCustomerResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            resultObject.setData(userCustomerResponse);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_AGENT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<CustomerMessageResponse> getCustomerMessage(String customerId, Users users) {
        ResultObject<CustomerMessageResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(),customerId,PlatformErrorConfigEnum.PLATFORM_CUSTOMER_ID_IS_NOT_NULL);
            CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerId);
            if (AssertUtils.isNotNull(customerAgentPo) && AssertUtils.isNotNull(customerAgentPo.getUserId())) {
                if (!users.getUserId().equals(customerAgentPo.getUserId())) {
                    throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
                }
            }
            CustomerMessageResponse customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerAgentPo, CustomerMessageResponse.class);
            customerMessageResponse.setCustomerId(customerId);
            resultObject.setData(customerMessageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> saveCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness, Users users) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        try {
            System.out.println("=======================保存用户信息:===========================");
            System.out.println("userCustomerBusiness:"+ JSON.toJSONString(userCustomerBusiness));
            System.out.println("=======================保存用户信息:===========================");
            //验证数据
            customerParameterValidate.validateCustomerBusiness(userCustomerBusiness);

            System.out.println("=======================保存用户信息1:===========================");
            List<CustomerAgentPo> customerPoList = customerManageDao.findByIdNo(null, userCustomerBusiness.getIdType(),userCustomerBusiness.getIdNo(), userCustomerBusiness.getUserId());
            CustomerAgentPo customerAgentPo=new CustomerAgentPo();
            if (AssertUtils.isNotEmpty(customerPoList)) {
                customerAgentPo = customerPoList.get(0);
            }
            System.out.println("=======================保存用户信息2:===========================");
            ClazzUtils.copy(userCustomerBusiness, customerAgentPo);
            System.out.println("=======================保存用户信息3:===========================");
            System.out.println("customerAgentPo:"+JSON.toJSONString(customerAgentPo));
            System.out.println("=======================保存用户信息3:===========================");
            String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
            System.out.println("=======================保存用户信息4:===========================");
            customerAgentPo.setGroupCode(groupNo);

            customerBaseService.saveCustomerAgent(customerAgentPo);
            System.out.println("=======================保存用户信息5:===========================");
            // 调用finance微服务，保存用户账户
            UserAccountRequest userAccountReqFc = new UserAccountRequest();
            userAccountReqFc.setUserId(customerAgentPo.getCustomerAgentId());
            userAccountReqFc.setUserAccountTypeCode(PlatformTermEnum.USER_ACCOUNT_TYPE.OVER_PAYMENT_ACCOUNT.name());
            userAccountReqFc.setAccountStatus(PlatformTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
            ResultObject userAccountResultObject = financeUserAccountApi.saveUserAccount(userAccountReqFc);
            AssertUtils.isResultObjectError(this.getLogger(), userAccountResultObject);
            System.out.println("=======================保存用户信息6:===========================");
            //添加  用户 勋章
            this.saveMedal(userCustomerBusiness.getMedalNo(), customerAgentPo.getCustomerAgentId(), userCustomerBusiness.getUserId(),customerAgentPo);
            System.out.println("=======================保存用户信息7:===========================");
            UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
            userCustomerResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            resultObject.setData(userCustomerResponse);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_AGENT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    /**
     * 添加 勋章
     *
     * @param medalNo
     * @param customerId
     */
    public void saveMedal(String medalNo, String customerId, String userId,CustomerAgentPo customerAgentPo) {
        if (AssertUtils.isNotNull(medalNo)) {
            CustomerMessageBo customerMessageBo = customerManageDao.getCustomerMessage(medalNo, userId, customerId);
            MedalBo medalBo = customerManageDao.getMedalIdByOn(medalNo);

            if (!AssertUtils.isNotNull(customerMessageBo) && AssertUtils.isNotNull(medalBo)) {

                CustomerAgentMedalPo customerAgentMedalPo = new CustomerAgentMedalPo();
                customerAgentMedalPo.setCustomerId(customerId);
                customerAgentMedalPo.setMedalId(medalBo.getMedalId());
                customerAgentMedalPo.setCreatedUserId(userId);
                customerBaseService.saveCustomerAgentMedal(customerAgentMedalPo);
            }
            //查询是否有疑似客户
            List<CustomerPo> customerPoList=customerBaseService.queryCustomerPoByMobileAndIdNo(customerAgentPo.getIdType(),customerAgentPo.getIdNo(),customerAgentPo.getMobile());
            if(AssertUtils.isNotEmpty(customerPoList)){
                //是疑似客户
                customerPoList.forEach(customerPo -> {
                    CustomerResemblePo customerResemblePo=customerBaseTransfer.transformCustomerResemble(customerPo,customerAgentPo);
                    customerBaseService.saveCustomerResemble(customerResemblePo);
                });
            }else{
                //非疑似客户，成为客户
                CustomerPo customerPo= (CustomerPo) converterObject(customerAgentPo,CustomerPo.class);
                customerBaseService.saveCustomerPo(customerPo);
            }
        }
    }

}