package com.gclife.platform.service.business.base;


import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.vo.SyscodeResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface InternationalBaseBusinessService extends BaseBusinessService {

    /**
     * 根据类型和语言查询国际化集合
     *
     * @param codeType 　国际化类型
     * @return ResultObject<List < SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> queryInternational(String codeType, String lang);

    /**
     * 根据类型,key值和语言查询国际化对象
     *
     * @param codeKey  国际化键
     * @param codeType 　国家化类型
     * @return ResultObject<SyscodeResponse>
     */
    ResultObject<SyscodeResponse> queryOneInternational(String codeType, String codeKey, String lang);

    /**
     * 根据类型,keys值和语言查询国际化集合
     *
     * @param syscodeReqFc 　国际化key集合
     * @return ResultObject<List < SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> queryInternationalByCodeKeys(SyscodeForm syscodeReqFc);

    /**
     * 根据id查询国际互对象id
     *
     * @param textId 　国际化id
     * @return ResultObject<SyscodeResponse>
     */
    public ResultObject<SyscodeResponse> queryOneInternationalById(String textId);

    /**
     * 批量查询国际化对象
     *
     * @param language  语言
     * @param codeTypes 类型集合
     * @return Map<String, List < SyscodeResponse>>
     */
    ResultObject<Map<String, List<SyscodeResponse>>> queryBatchInternationalByCodeKeys(String language, List<String> codeTypes);
}
