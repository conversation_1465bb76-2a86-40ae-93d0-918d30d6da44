package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.dao.BankExtDao;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.model.bo.BankBo;
import com.gclife.platform.model.bo.BankInfoBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.service.business.BankBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
@Service
public class BankBusinessServiceImpl extends BaseBusinessServiceImpl implements BankBusinessService {

    @Autowired
    BankExtDao bankExtDao;

    @Autowired
    private BaseSysCodeExtDao baseSysCodeExtDao;

    @Override
    public ResultObject<BankResponse> getBankById(String bankType, String bankCode, String language) {
        ResultObject<BankResponse> resultObject = new ResultObject<>();
        BankResponse bankResponse = null;
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), bankCode, PlatformErrorConfigEnum.PLATFORM_BANK_CODE_IN_NOT_NULL);
            //查询银行信息
            BankBo bankBo = bankExtDao.loadBankById(bankType, bankCode);
            //账户验证
            AssertUtils.isNotNull(this.getLogger(), bankBo, PlatformErrorConfigEnum.PLATFORM_BANK_IS_NOT_FOUND_OBJECT);
            //数据转换
            bankResponse = (BankResponse) this.converterObject(bankBo, BankResponse.class);

            //从国际化文本中取name
            if (AssertUtils.isNotNull(bankResponse)) {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(bankResponse.getBankCode(), InternationalTypeEnum.BANK.name(), language);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    bankResponse.setBankName(syscodeBo.getCodeName());
                }
            }

            //设置银行账号和账号名称
            if (AssertUtils.isNotNull(bankResponse.getBankId())) {
                BankInfoBo bankInfoBo = bankExtDao.loadBankInfoById(bankResponse.getBankId());
                if (AssertUtils.isNotNull(bankInfoBo)) {
                    bankResponse.setAccountNo(bankInfoBo.getAccountNo());
                    bankResponse.setAccountName(bankInfoBo.getAccountName());
                }

            }

            //设置返回值
            resultObject.setData(bankResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }

        return resultObject;
    }

    @Override
    public ResultObject<List<BankResponse>> getBanks(String language) {
        ResultObject<List<BankResponse>> resultObject = new ResultObject<>();
        try {
            //查询银行信息
            List<BankBo> bankBos = bankExtDao.loadBanks(PlatformTermEnum.BANK_TYPE.PAYMENT.name(), null);
            //数据转换
            List<BankResponse> bankResponses = (List<BankResponse>) this.converterList(bankBos, new TypeToken<List<BankResponse>>() {
            }.getType());

            resultObject.setData(bankResponses);

        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_BANK_ERROR);
        }

        return resultObject;
    }

    /**
     * 扣款银行列表
     * @param language 语言
     * @return
     */
    @Override
    public ResultObject<List<BankResponse>> getDeductionBanks(String language) {
        ResultObject<List<BankResponse>> resultObject = new ResultObject<>();
        try {
            // 查询银行信息
            List<BankBo> bankBos = bankExtDao.loadBanks(PlatformTermEnum.BANK_TYPE.DEDUCTION.name(), null);
            // 数据转换
            List<BankResponse> bankResponses = (List<BankResponse>) this.converterList(bankBos, new TypeToken<List<BankResponse>>() {
            }.getType());
            // 从国际化文本中取name
            if (AssertUtils.isNotEmpty(bankResponses)) {
                List<SyscodeBo> syscodeBos = baseSysCodeExtDao.internationalTextGet(InternationalTypeEnum.BANK.name(), language);
                if (AssertUtils.isNotEmpty(syscodeBos)) {
                    bankResponses.forEach(bankResponse -> {
                        syscodeBos.stream()
                                .filter(syscodeBo -> syscodeBo.getCodeKey().equals(bankResponse.getBankCode()))
                                .findFirst().ifPresent(syscode -> bankResponse.setBankName(syscode.getCodeName()));
                    });
                }
            }

            resultObject.setData(bankResponses);

        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_BANK_ERROR);
        }

        return resultObject;
    }

    /**
     * 根据银行类型查询银行列表
     * @param bankType 银行类型
     * @param validFlag 有效标识
     * @return
     */
    @Override
    public ResultObject<List<BankResponse>> listBank(String bankType, String validFlag) {
        ResultObject<List<BankResponse>> resultObject = new ResultObject<>();
        try {
            List<String> bankTypes = Arrays.asList("RECEIPT", "PAYMENT", "DEDUCTION");
            if (!AssertUtils.isNotEmpty(bankType)) {
                // 默认查收款银行
                bankType = PlatformTermEnum.BANK_TYPE.RECEIPT.name();
            } else if (!bankTypes.contains(bankType)) {
                throwsException(PlatformErrorConfigEnum.PLATFORM_PARAMETER_BANK_TYPE_ERROR);
            }
            // 查询银行信息
            List<BankBo> bankBos = bankExtDao.loadBanks(bankType, null);

            // 数据转换
            List<BankResponse> bankResponses = (List<BankResponse>) this.converterList(bankBos, new TypeToken<List<BankResponse>>() {
            }.getType());

            //设置银行账号和账号名称
            if (AssertUtils.isNotEmpty(bankResponses)) {
                bankResponses.forEach(bankResponse -> {
                    BankInfoBo bankInfoBo = bankExtDao.loadBankInfoById(bankResponse.getBankId());
                    if (AssertUtils.isNotNull(bankInfoBo)) {
                        bankResponse.setAccountNo(bankInfoBo.getAccountNo());
                        bankResponse.setAccountName(bankInfoBo.getAccountName());
                    }
                });
            }
            resultObject.setData(bankResponses);
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_BANK_ERROR);
        }

        return resultObject;
    }
}