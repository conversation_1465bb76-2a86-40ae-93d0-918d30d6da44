package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.model.response.InformationListResponse;
import com.gclife.platform.model.response.InformationResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:信息服务
 */
public interface InformationBusinessService extends BaseBusinessService {
    /**
     * 根据信息类型获取信息列表
     * @param  informationType 信息类型
     * @return 信息列表数据
     */
    ResultObject<List<InformationListResponse>> getInformationList(String informationType);

    /**
     * 根据主键获取信息详情
     *
     * @param informationId
     * @return  信息详情
     */
    ResultObject<InformationResponse> getInformationInfo(String  informationId);

}