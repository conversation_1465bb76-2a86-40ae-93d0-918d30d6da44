package com.gclife.platform.service.business.base.impl;


import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.platform.base.model.bo.*;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.vo.BranchPagingQueryVo;
import com.gclife.platform.base.service.BranchBaseService;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.base.service.UsersBranchBaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.form.branch.UserBranchRequest;
import com.gclife.platform.model.request.BranchPagingRequest;
import com.gclife.platform.model.response.BranchFuzzyResponse;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.service.data.UsersService;
import com.gclife.platform.vo.branch.*;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class BranchBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements BranchBaseBusinessService {

    @Autowired
    private BranchBaseService branchBaseService;

    @Autowired
    private UsersService usersService;

    @Autowired
    private UsersBaseService usersBaseService;
    @Autowired
    private UsersBranchBaseService usersBranchBaseService;

    @Autowired
    private InternationalBaseService internationalBaseService;

    /**
     * 根据机构ID信息
     *
     * @param branchId 机构ID
     * @return ResultObject<BranchResponse>
     */
    @Override
    public ResultObject<BranchResponse> queryOneBranchById(String branchId,String lang) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            BranchPo branchPo = branchBaseService.queryOneBranchById(branchId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchPo, BranchResponse.class);

            List<BranchPo> branchPos = branchBaseService.queryBranchParentListById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                branchPos.forEach(po -> {
                    BranchBankPo branchBankPo = branchBaseService.queryOneBranchBankById(po.getBranchId());
                    if (AssertUtils.isNotNull(branchBankPo) && !AssertUtils.isNotNull(branchResponse.getBranchBank())) {
                        branchResponse.setBranchBank((BranchBankResponse) this.converterObject(branchBankPo, BranchBankResponse.class));
                    }
                    BranchLicensePo branchLicensePo = branchBaseService.queryOneBranchLicenseById(po.getBranchId());
                    if (AssertUtils.isNotNull(branchLicensePo) && !AssertUtils.isNotNull(branchResponse.getBranchLicense())) {
                        branchResponse.setBranchLicense((BranchLicenseResponse) this.converterObject(branchLicensePo, BranchLicenseResponse.class));
                    }
                });
            }
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BranchResponse> queryOneBranchAndManagerById(String branchId, String lang) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            BranchDo branchDo = branchBaseService.queryOneBranchAndManagerById(branchId);
            AssertUtils.isNotNull(this.getLogger(), branchDo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchDo, BranchResponse.class);
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BranchResponse> queryOneTopBranchById(String branchId) {
        this.getLogger().info("开始查询机构的顶级机构:{}",branchId);
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            BranchPo branchPo = branchBaseService.queryOneTopBranchById(branchId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchPo, BranchResponse.class);
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            this.getLogger().info("开始查询机构的顶级机构错误:{}",branchId);
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID信息
     *
     * @param branchIds 机构IDs
     * @return ResultObject<BranchResponse>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchByIds(List<String> branchIds) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchByIds(branchIds);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponses = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                List<BranchBankPo> branchBankPos = branchBaseService.queryBranchBankById(branchIds);
                if (AssertUtils.isNotEmpty(branchBankPos)) {
                    branchBankPos.forEach(branchBankPo -> {
                        branchResponses.stream().filter(branchResponse -> AssertUtils.isNotEmpty(branchBankPo.getBranchId())
                                && branchBankPo.getBranchId().equals(branchResponse.getBranchId())).findFirst().ifPresent(branchResponse -> {
                            BranchBankResponse branchBank = new BranchBankResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(branchBankPo, branchBank);
                            branchResponse.setBranchBank(branchBank);
                        });
                    });
                }
                resultObject.setData(branchResponses);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树上的　机构集合
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListById(String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchTreeBranchListById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponses = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                List<String> branchIds = branchResponses.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
                List<BranchBankPo> branchBankPos = branchBaseService.queryBranchBankById(branchIds);
                if (AssertUtils.isNotEmpty(branchBankPos)) {
                    branchBankPos.forEach(branchBankPo -> {
                        branchResponses.stream().filter(branchResponse -> AssertUtils.isNotEmpty(branchBankPo.getBranchId())
                                && branchBankPo.getBranchId().equals(branchResponse.getBranchId())).findFirst().ifPresent(branchResponse -> {
                            BranchBankResponse branchBank = new BranchBankResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(branchBankPo, branchBank);
                            branchResponse.setBranchBank(branchBank);
                        });
                    });
                }
                resultObject.setData(branchResponses);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树上的叶子机构　机构集合
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchTreeLeafListById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询父机构集合 (按照从下往上排序)
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchParentListById(String branchId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchParentListById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponses = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                branchResponses.forEach(branchResponse -> {
                    branchResponse.setBranchLicense((BranchLicenseResponse) this.converterObject(branchBaseService.queryOneBranchLicenseById(branchResponse.getBranchId()), BranchLicenseResponse.class));
                    branchResponse.setBranchBank((BranchBankResponse) this.converterObject(branchBaseService.queryOneBranchBankById(branchId), BranchBankResponse.class));
                });
                resultObject.setData(branchResponses);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树上的　机构集合
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeBranchListByIds(List<String> branchIds) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchTreeBranchListByIds(branchIds);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树上的叶子机构　机构集合
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryBranchTreeLeafListByIds(List<String> branchIds) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchPo> branchPos = branchBaseService.queryBranchTreeLeafListByIds(branchIds);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询父机构集合 (按照从下往上排序)
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchParentResponse>> queryBranchParentListByIds(List<String> branchIds) {
        ResultObject<List<BranchParentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchParentDo> branchPos = branchBaseService.queryBranchParentListByIds(branchIds);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchParentResponse> branchResponse = (List<BranchParentResponse>) this.converterList(branchPos, new TypeToken<List<BranchParentResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeById(String branchId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchTreeDo> branchPos = branchBaseService.queryBranchTreeById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> querySalesBranchTreeById(String branchId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchTreeDo> branchPos = branchBaseService.querySalesBranchTreeById(branchId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> querySalesAllBranchTree() {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchTreeDo> branchPos = branchBaseService.querySalesAllBranchTree();
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构ID查询机构树
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<List<BranchTreeResponse>> queryBranchTreeByIds(List<String> branchIds) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), branchIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            List<BranchTreeDo> branchPos = branchBaseService.queryBranchTreeByIds(branchIds);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户机构信息
     *
     * @param userId 机构ID
     * @return ResultObject<BranchResponse>
     */
    @Override
    public ResultObject<BranchResponse> queryOneUserBranch(String userId) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            BranchPo branchPo = branchBaseService.queryOneUserBranch(userId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            BranchResponse branchResponse = (BranchResponse) this.converterObject(branchPo, BranchResponse.class);
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户所属机构树(管理机构)
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserBranchTree(String userId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            BranchPo branchPo = branchBaseService.queryOneUserBranch(userId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            List<BranchTreeDo> branchPos = branchBaseService.queryBranchTreeById(branchPo.getBranchId());
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserBranchTreeList(String userId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            BranchPo branchPo = branchBaseService.queryOneUserBranch(userId);
            AssertUtils.isNotNull(this.getLogger(), branchPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
            List<BranchPo> branchPos = branchBaseService.queryBranchTreeBranchListById(branchPo.getBranchId());
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeList(String userId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<BranchPo> branchPos = branchBaseService.queryUserOptionBranchTreeList(userId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeListByCode(String channelTypeCode) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchDo> branchDos = branchBaseService.queryUserOptionBranchTreeListByCode(channelTypeCode);
            if (AssertUtils.isNotEmpty(branchDos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchDos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户管理的机构叶子列表
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> queryUserOptionBranchTreeLeaf(String userId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<BranchPo> branchPos = branchBaseService.queryUserOptionBranchTreeLeaf(userId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户管理的机构第一层级的机构集合
     *
     * @return ResultObject<List<BranchResponse>>
     */
    @Override
    public ResultObject<List<BranchResponse>> loadUserOptionFirstLevelBranch(String userId) {
        ResultObject<List<BranchResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<BranchPo> branchPos = branchBaseService.queryUserOptionFirstLevelBranch(userId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchResponse> branchResponse = (List<BranchResponse>) this.converterList(branchPos, new TypeToken<List<BranchResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户管理的机构树
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTree(String userId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<BranchTreeDo> branchPos = branchBaseService.queryUserOptionBranchTree(userId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTreeFiterNoLeafBranch(String userId) {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<BranchTreeDo> branchPos = branchBaseService.queryUserOptionBranchTreeFiterNoLeafBranch(userId);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchTreeResponse> branchResponse = (List<BranchTreeResponse>) this.converterList(branchPos, new TypeToken<List<BranchTreeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<BaseResponse> saveUserOptionBranch(UserBranchRequest userBranchRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userBranchRequest.getUserId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), userBranchRequest.getBranchId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            UserBranchPo userBranchPo = (UserBranchPo) this.converterObject(userBranchRequest, UserBranchPo.class);
            usersService.saveUserBranchPo(userBranchPo);
        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据机构层级查询机构列表
     *
     * @param levelNum 层级
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<List<BranchSimpleResponse>> queryLevelBranch(String userId, Long levelNum) {
        ResultObject<List<BranchSimpleResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), levelNum, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_LEVEL_NUM_IS_NOT_NULL);
            List<BranchPo> branchPos = branchBaseService.queryLevelBranch(userId, levelNum);
            if (AssertUtils.isNotEmpty(branchPos)) {
                List<BranchSimpleResponse> branchResponse = (List<BranchSimpleResponse>) this.converterList(branchPos, new TypeToken<List<BranchSimpleResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            //事务回滚
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveUserBranchByAgentIds(List<UserBranchRequest> userBranchRequests) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //参数校验
            AssertUtils.isNotEmpty(this.getLogger(), userBranchRequests, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            userBranchRequests.forEach(userBranchRequest -> {
                AssertUtils.isNotEmpty(this.getLogger(), userBranchRequest.getUserId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), userBranchRequest.getBranchId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            });
            //校验机构和用户真实性
            List<String> branchIds = userBranchRequests.stream().map(UserBranchRequest::getBranchId).collect(Collectors.toList());
            List<String> userIds = userBranchRequests.stream().map(UserBranchRequest::getUserId).collect(Collectors.toList());
            List<BranchPo> branchPos = branchBaseService.queryBranchByIds(branchIds);
            AssertUtils.isNotEmpty(this.getLogger(), branchPos, PlatformErrorConfigEnum.PLATFORM_PARMETER_BRANCH_ID_FORMAT_ERROR);
            branchIds.forEach(s -> {
                if (!branchPos.stream().map(BranchPo::getBranchId).collect(Collectors.toList()).contains(s)) {
                    throw new RequestException(PlatformErrorConfigEnum.PLATFORM_PARMETER_BRANCH_ID_FORMAT_ERROR);
                }
            });

            List<UsersDo> usersPos = usersBaseService.queryUsersPoByIds(userIds);
            AssertUtils.isNotEmpty(this.getLogger(), usersPos, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            userIds.forEach(s -> {
                if (!usersPos.stream().map(UsersPo::getUserId).collect(Collectors.toList()).contains(s)) {
                    throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
                }
            });

            //数据保存
            userBranchRequests.forEach(userBranchRequest -> {
                UserBranchDo userBranchDo = usersBranchBaseService.queryUsersBranchDoByUserId(userBranchRequest.getUserId());
                UserBranchPo userBranchPo = new UserBranchPo();
                if (AssertUtils.isNotNull(userBranchDo)) {
                    userBranchPo = JSON.parseObject(JSON.toJSON(userBranchDo).toString(), UserBranchDo.class);

                }
                userBranchPo.setUserId(userBranchRequest.getUserId());
                userBranchPo.setBranchId(userBranchRequest.getBranchId());
                usersService.saveUserBranchPo(userBranchPo);
            });
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR);
        }
        return resultObject;
    }


    /**
     * 查询机构所属层级
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<Long> queryBranchBelongLevel(String branchId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数校验
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            BranchTreeDo branchPos = branchBaseService.queryBranchBelongLevel(branchId);
            resultObject.setData(branchPos.getLevel());
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR);
        }
        return resultObject;
    }
    /**
     * 查询默认渠道机构
     *
     * @return ResultObject<List<BranchTreeResponse>>
     */
    @Override
    public ResultObject<BranchResponse> queryBranchDefaultChannel(String branchId) {
        ResultObject<BranchResponse> resultObject = new ResultObject<>();
        BranchResponse branchResponse=null;
        try {
            //参数校验
            BranchPo branchPo = branchBaseService.queryBranchDefaultChannel(branchId);
            if(AssertUtils.isNotNull(branchPo)){
                branchResponse =(BranchResponse)this.converterObject(branchPo,BranchResponse.class);
            }
            resultObject.setData(branchResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR);
        }
        return resultObject;
    }

    /**
     *  机构模糊查询
     * @param branchPagingRequest 分页请求对象
     * @return  ResultObject
     */
    @Override
    public ResultObject<List<BranchFuzzyResponse>> queryBranchFuzzy(BranchPagingRequest branchPagingRequest,String branchType) {
        ResultObject<List<BranchFuzzyResponse>> resultObject = new ResultObject<>();
        try {
            BranchPagingQueryVo branchPagingQueryVo = (BranchPagingQueryVo)this.converterObject(branchPagingRequest, BranchPagingQueryVo.class);
            branchPagingQueryVo.setChannelTypeCode(branchType);
            List<BranchPagingDo> branchPagingDos = branchBaseService.queryBranchPagingFuzzy(branchPagingQueryVo);
            if(AssertUtils.isNotEmpty(branchPagingDos)){
                List<BranchFuzzyResponse> branchFuzzyResponses =(List<BranchFuzzyResponse>)this.converterList(branchPagingDos, new TypeToken<List<BranchFuzzyResponse>>() {
                }.getType());
                resultObject.setData(branchFuzzyResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR);
        }
        return resultObject;
    }
}