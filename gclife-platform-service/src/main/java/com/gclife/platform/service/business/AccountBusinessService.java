package com.gclife.platform.service.business;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.model.request.AccountAuditRequest;
import com.gclife.platform.model.request.AccountQueryRequest;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountQueryResponse;
import com.gclife.platform.model.response.AccountResponse;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息接口
 */
public interface AccountBusinessService extends BaseBusinessService {
    /**
     * 根据账户ID获取账户信息
     *
     * @param accountId
     * @return
     */
    ResultObject<AccountResponse> getAccountById(String accountId);

    /**
     * 账户信息保存
     *
     * @param accountRequest
     * @return
     */
    ResultObject saveAccount(AccountRequest accountRequest);

    /**
     * 账户信息保存
     * @param accountRequest
     * @return
     */
    ResultObject saveAccountInfo(AccountRequest accountRequest);

    /**
     * 账户信息批量保存
     * @param accountRequests
     * @return
     */
    ResultObject saveAccounts(List<AccountRequest> accountRequests);

    /**
     * 批量保存账户信息
     * @param accounts 账户信息集
     * @return
     */
    ResultObject saveAccount(List<AccountRequest> accounts);

    /**
     * 删除账户信息
     * @param userId 用户ID
     * @return
     */
    ResultObject deleteAccount(String userId);

    /**
     * 根据用户ID获取账户信息
     *
     * @param userId
     * @return
     */
    ResultObject<List<AccountResponse>> getAccountByUserId(String userId);

    /**
     * 回滚账户
     * @param accountId 账户ID
     * @return  ResultObject
     */
    ResultObject accountRollback(String accountId);

    /**
     * 查询list账户
     * @param accountIdList
     * @return
     */
    ResultObject<List<AccountResponse>> getAccountList(List<String> accountIdList);

    /**
     * 查询待审核账户
     *
     * @param bankType
     * @param users
     * @param accountQueryRequest
     * @return
     */
    ResultObject<BasePageResponse<AccountQueryResponse>> queryAccountAuditList(Users users,AccountQueryRequest accountQueryRequest);

    /**
     * 查询审核成功银行卡信息
     * @param userId
     * @return
     */
    ResultObject<List<AccountResponse>>  queryAccountAuditBanksList(String userId);

    /**
     * 查询审核成功银行卡信息
     * @param userId
     * @return
     */
    ResultObject<List<AccountResponse>>  queryAccountAuditBanksListNew(String userId);

    /**
     * 查询审核异常原因集合
     * @return
     */
    ResultObject<List<InternationalDo>> queryAuditExceptions(Users users);

    /**
     * 账户审核
     * @param accountAuditRequest
     * @return
     */
    ResultObject accountAudit(AccountAuditRequest accountAuditRequest);

    /**
     * 根据用户集合ID获取账户信息
     * @param userIds
     * @return
     */
    ResultObject<List<AccountResponse>> getAccountByUserIds(List<String> userIds);
}