package com.gclife.platform.service.business.base;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.base.model.bo.CareerDo;
import com.gclife.platform.core.jooq.tables.pojos.BaseCareerPo;
import com.gclife.platform.model.response.CareerBaseTreeResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.CareerTreeResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码接口
 */
public interface CareerBaseBusinessService extends BaseBusinessService {
    /**
     * 查询职业孩子集合
     *
     * @param careerId   　父职业ID
     * @param providerId 供应商ID
     * @return List<CareerDo>  ResultObject<List<CareerResponse>>
     */
    ResultObject<List<CareerResponse>> queryCareerChilds(String careerId, String providerId);

    /**
     * 查询职业信息
     *
     * @param careerId 职业ID
     * @return CareerBo ResultObject<CareerResponse>
     */
    ResultObject<CareerResponse> queryOneCareerPo(String careerId);

    /**
     * 根据职业ids或者职业codes获取职业详情
     *
     * @param careerIds 职业ID
     * @return ResultObject<List<CareerResponse>>
     */
    ResultObject<List<CareerResponse>> queryCareerPoByIdsOrCodes(List<String> careerIds);

    /**
     * 职业树集合
     *
     * @param careerId 　职业ID或职业编码
     * @return ResultObject<List<CareerResponse>>
     */
    ResultObject<List<CareerResponse>> queryCareerParentTreeList(String careerId);

    /**
     * 查询所有职业树
     *
     * @param providerId 供应商ID
     * @return CareerBaseTreeResponses
     */
    ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(String providerId);
}