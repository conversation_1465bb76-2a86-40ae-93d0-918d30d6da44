package com.gclife.platform.service.business.base;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.UserDingRelationResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.UserWeixinRelationResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UsersBaseBusinessService extends BaseBusinessService {

    /**
     * 根据机构ID信息
     *
     * @param branchId 机构ID
     * @return ResultObject<BranchResponse>
     */
    ResultObject<UserResponse> queryOneUsersPoById(String branchId);

    /**
     * 根据机构ID信息
     *
     * @param branchIds 机构IDs
     * @return ResultObject<BranchResponse>
     */
    ResultObject<List<UserResponse>> queryUsersPoByIds(List<String> branchIds);


    /**
     * 根据用户手机号码 查询 用户信息
     *
     * @param mobiles
     * @return
     */
    ResultObject<List<UserResponse>> queryUsersPoByMobiles(String... mobiles);

    /**
     * 根据账户或姓名模糊查询用户
     *
     * @param name
     * @return
     */
    ResultObject<List<UserResponse>> queryUsersPoByName(String name, Users users);

    /**
     * 根据openId查询用户信息
     *
     * @param openId 微信
     * @return UserWeixinRelationResponses
     */
    ResultObject<UserWeixinRelationResponse> queryOneUsersWechatById(String openId);

    /**
     * 根据userId查询用户信息
     *
     * @param userId 用户id
     * @return UserDingRelationResponse
     */
    ResultObject<UserDingRelationResponse> queryOneUsersDingById(String userId);

    /**
     * 根据账户或姓名模糊查询用户
     *
     * @param keyword
     * @return
     */
    ResultObject<List<UserResponse>> queryUsersPoByKeyword(String keyword, Users users);

    /**
     * 赋予用户团险权限
     *
     * @param userId 用户
     * @return ResultObject
     */
    ResultObject grantedGroupPermission(String userId);
}
