package com.gclife.platform.service.business.impl;

import com.gclife.common.configuration.redis.RedisDataTool;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.TCCDetail;
import com.gclife.common.model.TCCEntry;
import com.gclife.common.model.config.BaseTermEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.core.jooq.tables.pojos.TccDetailPo;
import com.gclife.platform.service.business.TccSyncBusinessService;
import com.gclife.platform.service.data.TccDetailService;
import com.gclife.platform.service.data.TccEntryService;
import com.gclife.platform.validate.transfer.TCCOperationTransfer;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * create 17-12-15
 * description: 同步redis中TCC操作数据接口的实现类
 */
@Service
public class TccSyncBusinessServiceImpl extends BaseBusinessServiceImpl implements TccSyncBusinessService {

    @Autowired
    TccEntryService tccEntryService;

    @Autowired
    TccDetailService tccDetailService;

    @Autowired
    RedisDataTool redisDataTool;

    @Autowired
    TCCOperationTransfer tccOperationTransfer;

    @Override
    @Transactional
    public ResultObject syncTcc() {

        ResultObject resultObject = new ResultObject();

        try {
            // 先查出key
            List<String> keys = redisDataTool.getKeysByPattern("TCC_ENTRY_*");

            for (String a : keys) {
                RMap<String, Object> dataMap = redisDataTool.getMapData(a);

                //判断是否数据齐全
                if(dataMap.containsKey(a)) {
                    Set<String> keySet = dataMap.keySet();
                    boolean shouldSync = true;

                    for (String m : keySet) {
                        if (m.indexOf("TCC_DETAIL_") == 0) {
                            TCCDetail detail = (TCCDetail) dataMap.get(m);
                            if (BaseTermEnum.TCC_STATUS.TCC_INITIALIZED.code().equals(detail.getTccStatus()))
                            {
                                shouldSync = false;
                                break;
                            }

                        }
                    }

                    if (shouldSync) {
                        for (String m : keySet) {
                            if (m.indexOf("TCC_DETAIL_") == 0) {
                                TCCDetail detail = (TCCDetail) dataMap.get(m);
                                tccDetailService.saveTccDetailPo(tccOperationTransfer.transferTCCDetail(detail));
                            }

                            if (m.indexOf("TCC_ENTRY_") == 0) {
                                TCCEntry tccEntry = (TCCEntry) dataMap.get(m);
                                tccEntryService.saveTccEntryPo(tccOperationTransfer.transferTCCEntry(tccEntry));
                            }
                        }

                        redisDataTool.deleteMap(a);
                    }

                }

            }

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            if (e instanceof RequestException) {
                RequestException err = (RequestException) e;
                resultObject.setIenum(err.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SYNC_TCC_OPERATION_ERROR);
            }
        }

        return resultObject;
    }
}
