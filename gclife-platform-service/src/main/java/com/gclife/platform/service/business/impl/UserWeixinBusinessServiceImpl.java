package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentRequest;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinPo;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinRelationPo;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.UsersWeixinExtDao;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.UserWeixinMiniRequest;
import com.gclife.platform.model.request.UserWeixinRequest;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.model.response.UserOpenPlatformRespone;
import com.gclife.platform.model.response.UserWeixinRelationResponse;
import com.gclife.platform.service.business.UserWeixinBusinessService;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.service.data.UsersService;
import com.gclife.platform.validate.parameter.UsersParameterValidate;
import com.gclife.platform.validate.transfer.UserWeixinTransfer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;


/**
 * <AUTHOR>
 * create 17-12-13
 * description:
 */
@Service
public class UserWeixinBusinessServiceImpl extends BaseBusinessServiceImpl implements UserWeixinBusinessService {

    @Autowired
    private UsersWeixinExtDao usersWeixinExtDao;

    @Autowired
    private UsersParameterValidate usersParameterValidate;

    @Autowired
    private UserWeixinTransfer userWeixinTransfer;

    @Autowired
    private UsersService usersService;

    @Autowired
    private AgentApi agentApi;

    @Autowired
    private BranchBaseBusinessService branchBaseBusinessService;

    @Override
    @Transactional
    public ResultObject<BaseResponse> saveUsersWechatInfo(UserWeixinRequest weixinRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<BaseResponse>();
        try {
            //参数验证
            usersParameterValidate.validParameterSaveUserWeixin(weixinRequest);
            //查询userID关联关系
            UserWeixinRelationPo userWeixinRelationPo = usersWeixinExtDao.loadUserWeixinRelationPoByOpenId(weixinRequest.getUserId());
            if (!AssertUtils.isNotNull(userWeixinRelationPo)) {
                userWeixinRelationPo = new UserWeixinRelationPo();
            }
            userWeixinTransfer.transferWeixinRelation(weixinRequest, userWeixinRelationPo);
            //保存数据
            usersService.saveUserWeixinRelationPo(userWeixinRelationPo);
            //是否绑定了用户
            if (AssertUtils.isNotEmpty(userWeixinRelationPo.getUserId())) {
                UserWeixinPo userWeixinPo = usersWeixinExtDao.loadUserWeixinPo(userWeixinRelationPo.getUserId());
                if (!AssertUtils.isNotNull(userWeixinPo)) {
                    userWeixinPo = new UserWeixinPo();
                }
                userWeixinTransfer.transferWeixin(weixinRequest, userWeixinPo, userWeixinRelationPo.getUserId());
                //保存数据
                usersService.saveUserWeixinPo(userWeixinPo);
            }
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_LOGIN_CHECK_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject<UserWeixinRelationResponse> queryUserWeixinRelationInfo(String userId) {
        ResultObject<UserWeixinRelationResponse> resultObject = new ResultObject<UserWeixinRelationResponse>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询open微信ID关联关系
            UserWeixinRelationPo userWeixinRelationPo = usersWeixinExtDao.loadUserWeixinRelationPoByUserId(userId);
            if (AssertUtils.isNotNull(userWeixinRelationPo)) {
                UserWeixinRelationResponse userWeixinRelationResponse = (UserWeixinRelationResponse) this.converterObject(userWeixinRelationPo, UserWeixinRelationResponse.class);
                UserWeixinPo userWeixinPo = usersWeixinExtDao.loadUserWeixinPo(userWeixinRelationPo.getUserId());
                if (AssertUtils.isNotNull(userWeixinPo) && AssertUtils.isNotEmpty(userWeixinPo.getNickname())) {
                    String nickname = userWeixinPo.getNickname();
                    userWeixinRelationResponse.setWeixinName(nickname);
                }
                resultObject.setData(userWeixinRelationResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_LOGIN_CHECK_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<UserOpenPlatformRespone> findUserWeixinRelationInfo(UserWeixinMiniRequest userWeixinMiniRequest) {
        ResultObject<UserOpenPlatformRespone> resultObject = ResultObject.success();
        //参数验证
        AssertUtils.isNotEmpty(this.getLogger(), userWeixinMiniRequest.getAppid(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_WXMINI_APPID_KEY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userWeixinMiniRequest.getOpenid(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_WXMINI_OPENID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userWeixinMiniRequest.getUnionId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_WXMINI_UNIONID_IS_NOT_NULL);
        //根据branchid转为channelid
        ResultObject<BranchResponse>  branchResponseResultObject = branchBaseBusinessService.queryBranchDefaultChannel(userWeixinMiniRequest.getBranchId());
        if(!AssertUtils.isResultObjectDataNull(branchResponseResultObject)){
            return new ResultObject<>(branchResponseResultObject.getError(),branchResponseResultObject.getErrorCode(),branchResponseResultObject.getMessage());
        }
        String channelId = branchResponseResultObject.getData().getBranchId();

        //根据uniodid和渠道查询是否已经注册过
        List<UserWeixinRelationPo> userWeixinRelationPoList = usersWeixinExtDao.findUserWeixinRelationPo(userWeixinMiniRequest.getUnionId(),channelId);

       if (AssertUtils.isNotNull(userWeixinRelationPoList)) {
           UserWeixinRelationPo userWeixinRelationPo = null;
           if(userWeixinRelationPoList.stream().filter(userWeixinRelationPoTemp -> userWeixinRelationPoTemp.getOpenid().equals(userWeixinMiniRequest.getOpenid()))
                   .findFirst().isPresent()){
               userWeixinRelationPo = userWeixinRelationPoList.get(0);
               UserOpenPlatformRespone userOpenPlatformRespone = (UserOpenPlatformRespone) this.converterObject(userWeixinRelationPo, UserOpenPlatformRespone.class);
               UserWeixinPo userWeixinPo = usersWeixinExtDao.loadUserWeixinPo(userWeixinRelationPo.getUserId());
               if (AssertUtils.isNotNull(userWeixinPo)) {
                   userOpenPlatformRespone.setNickName(userWeixinPo.getNickname());
                   userOpenPlatformRespone.setHeadImgUrl(userWeixinPo.getHeadImgUrl());
               }
               resultObject.setData(userOpenPlatformRespone);
           }

        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<UserOpenPlatformRespone> registerUsersWechatInfo(UserWeixinRequest weixinRequest) {
        ResultObject<UserOpenPlatformRespone> resultObject = ResultObject.success();
        try {
            //参数验证
            usersParameterValidate.validParameterSaveUserWeixin(weixinRequest);
            //TODO 根据branchid转为channelid
            //根据branchid转为channelid
            ResultObject<BranchResponse>  branchResponseResultObject = branchBaseBusinessService.queryBranchDefaultChannel(weixinRequest.getBranchId());
            if(!AssertUtils.isResultObjectDataNull(branchResponseResultObject)){
                return new ResultObject<>(branchResponseResultObject.getError(),branchResponseResultObject.getErrorCode(),branchResponseResultObject.getMessage());
            }
            String channelId = branchResponseResultObject.getData().getBranchId();

            AssertUtils.isNotEmpty(this.getLogger(),weixinRequest.getUnionId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_WEIXIN_UNION_ID_S_NOT_NULL);

            //根据uniodid和渠道查询是否已经注册过
            List<UserWeixinRelationPo> userWeixinRelationPoList = usersWeixinExtDao.findUserWeixinRelationPo(weixinRequest.getUnionId(),channelId);

            UserWeixinRelationPo userWeixinRelationPo = null;
            if (!AssertUtils.isNotNull(userWeixinRelationPoList)) {
                userWeixinRelationPo = new UserWeixinRelationPo();
            }else{
                //用户已经注册过，直接返回用户信息
                String userId = userWeixinRelationPoList.get(0).getUserId();
                if(userWeixinRelationPoList.stream().filter(userWeixinRelationPoTemp -> userWeixinRelationPoTemp.getOpenid().equals(weixinRequest.getOpenId()))
                        .findFirst().isPresent()){
                    userWeixinRelationPo = userWeixinRelationPoList.get(0);
                    System.out.println("userWeixinRelationPoList po:"+ JSON.toJSONString(userWeixinRelationPo));
                }else{
                    //直接再插入一条userWeixinRelationPo信息
                    this.getLogger().info("直接再插入一条userWeixinRelationPo信息");
                    userWeixinRelationPo = new UserWeixinRelationPo();
                    userWeixinTransfer.transferWeixinRelation(weixinRequest, userWeixinRelationPo);
                    userWeixinRelationPo.setUserId(userId);
                    userWeixinRelationPo.setChannelId(channelId);
                    usersService.saveUserWeixinRelationPo(userWeixinRelationPo);
                }

                UserOpenPlatformRespone userOpenPlatformRespone = (UserOpenPlatformRespone) this.converterObject(userWeixinRelationPo, UserOpenPlatformRespone.class);
                UserWeixinPo userWeixinPo = usersWeixinExtDao.loadUserWeixinPo(userWeixinRelationPo.getUserId());
                if (AssertUtils.isNotNull(userWeixinPo)) {
                    userOpenPlatformRespone.setNickName(userWeixinPo.getNickname());
                    userOpenPlatformRespone.setHeadImgUrl(userWeixinPo.getHeadImgUrl());
                }
                resultObject.setData(userOpenPlatformRespone);
                return resultObject;
            }

            //保存用户数据
            UsersPo usersVo = new UsersPo();
            usersVo.setUsername(UUIDUtils.getUUIDShort());
            usersVo.setPassword(new BCryptPasswordEncoder().encode("88888888"));
            usersVo.setEnabled("ENABLED");
            usersVo.setNickName(weixinRequest.getNickname());
            usersVo.setGender("FMALE");
            usersVo.setMobile(null);
            usersVo.setLanguage(TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
            usersVo.setCreatedDate(System.currentTimeMillis());
            usersVo.setUpdatedDate(System.currentTimeMillis());
            usersVo.setLastLogin(System.currentTimeMillis());
            usersVo.setCountryCode("86");
            usersVo.setDeviceChannelId("wx_xcx");
            usersService.saveUsersPo(usersVo);
            System.out.println("保存的userid:"+usersVo.getUserId());
            //保存数据
            userWeixinTransfer.transferWeixinRelation(weixinRequest, userWeixinRelationPo);
            userWeixinRelationPo.setUserId(usersVo.getUserId());
            userWeixinRelationPo.setChannelId(channelId);
            usersService.saveUserWeixinRelationPo(userWeixinRelationPo);

            UserWeixinPo userWeixinPo = usersWeixinExtDao.loadUserWeixinPo(userWeixinRelationPo.getUserId());
            if (!AssertUtils.isNotNull(userWeixinPo)) {
                userWeixinPo = new UserWeixinPo();
            }
            userWeixinTransfer.transferWeixin(weixinRequest, userWeixinPo, userWeixinRelationPo.getUserId());
            //保存数据
            usersService.saveUserWeixinPo(userWeixinPo);

            //创建代理人信息
            AgentRequest agentReqFc=new AgentRequest();
            agentReqFc.setUserId(usersVo.getUserId());
            agentReqFc.setAgentName(userWeixinPo.getNickname());
            AssertUtils.isResultObjectError(getLogger(),agentApi.saveAgent(agentReqFc),PlatformErrorConfigEnum.PLATFORM_BUSINESS_SAVE_AGENT_ERROR);
            //返回注册后的用户信息

            UserOpenPlatformRespone userOpenPlatformRespone = (UserOpenPlatformRespone) this.converterObject(userWeixinRelationPo, UserOpenPlatformRespone.class);
            userOpenPlatformRespone.setNickName(userWeixinPo.getNickname());
            userOpenPlatformRespone.setHeadImgUrl(userWeixinPo.getHeadImgUrl());
            resultObject.setData(userOpenPlatformRespone);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_WEIXIN_USER_REGISTER_ERROR);
            }
        }
        return resultObject;
    }
}
