package com.gclife.platform.service.data;

;
import com.gclife.common.exception.RequestException;
import com.gclife.common.service.BaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.model.bo.UserLoginLogBo;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午11:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public interface UsersService extends BaseService {

    /**
     * 保存用户信息
     * @param usersPo
     */
    void saveUsersPo(UsersPo usersPo);

    void saveUserLoginLogBo(UserLoginLogBo userLoginLogBo);

    void saveUserBranchPo(UserBranchPo userBranchPo);

    /**
     * 保存用户设备token信息(批量)
     * @param userAppDevicePos 用户设备token信息集
     */
    void saveUserAppDevice(List<UserAppDevicePo> userAppDevicePos);
    public void saveUserWeixinPo(UserWeixinPo userWeixinPo);

    public void saveUserWeixinRelationPo(UserWeixinRelationPo userWeixinRelationPo);

}