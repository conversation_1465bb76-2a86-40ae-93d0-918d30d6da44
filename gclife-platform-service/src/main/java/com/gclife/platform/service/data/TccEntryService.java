package com.gclife.platform.service.data;

import com.gclife.common.service.BaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;

import java.util.List;

;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-15
 * \* Time: 上午11:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public interface TccEntryService extends BaseService {

    /**
     * 保存TCC操作入口信息
     * @param tccEntryPo
     */
    void saveTccEntryPo(TccEntryPo tccEntryPo);

}