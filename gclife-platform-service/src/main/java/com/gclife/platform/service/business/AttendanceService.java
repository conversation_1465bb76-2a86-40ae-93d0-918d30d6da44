package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.response.AttendanceResponse;
import com.gclife.platform.model.response.AttendancesMessageResponse;
import org.apache.catalina.User;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
public interface AttendanceService {

    /**
     * 添加 签到信息
     *
     * @return
     */
    ResultObject attendanceSave(Users users);

    /**
     * 查询签到信息  只查当天的 签到信息
     *
     * @return
     */
    ResultObject<AttendanceResponse> getAttendance(Users users);


    /***
     * 根据用户获取当月的签到信息
     * @param users
     * @return
     */
    ResultObject<AttendancesMessageResponse> getAttendancesMessage(Users users);

}