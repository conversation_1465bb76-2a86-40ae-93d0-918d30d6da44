package com.gclife.platform.service.business;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.TerminologyRequest;
import com.gclife.platform.vo.SyscodeResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InternationalBusinessService extends BaseBusinessService {


    /**
     * 查询术语国际化值
     * @param type 术语类型
     * @param key 术语key
     * @param users 当前用户
     * @return
     */
    ResultObject loadTerminology(Users users,String type,String key,String language);


    /**
     * 查询术语国际化集合
     * @param type 类型
     * @param users 当前用户
     * @return
     */
    ResultObject loadTerminologys(Users users, String type,String language);


    /**
     * 查询术语国际化集合
     * @param terminologyRequest 类型
     * @param users 当前用户
     * @return
     */
    ResultObject loadTerminologys(Users users, TerminologyRequest terminologyRequest);

    /**
     * 根据类型 查询国际化 数据
     * @param users
     * @param type
     * @return
     */
    ResultObject<List<SyscodeResponse>> internationalTextGet(Users users,String type,String language);

    /**
     * 根据类型 key 查询数据
     * @param users
     * @param key
     * @param type
     * @return
     */
    ResultObject<SyscodeResponse> internationalTextGetOne(Users users, String key,String type,String language);


    /**
     * 根据类型 key 查询数据
     * @param users 当前用户
     * @param terminologyRequest　国际化请求类
     * @return
     */
    ResultObject<List<SyscodeResponse>> loadInternationalListByCodes(Users users, TerminologyRequest terminologyRequest);
}
