package com.gclife.platform.service.business.impl;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.base.service.PlatformConfigBaseService;
import com.gclife.platform.core.jooq.tables.daos.OperationLogDao;
import com.gclife.platform.core.jooq.tables.pojos.OperationLogPo;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.dao.OperationLogExtDao;
import com.gclife.platform.dao.ResourcesExtDao;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.OperationLogBo;
import com.gclife.platform.model.bo.OperationRoleBo;
import com.gclife.platform.model.bo.OperationUserBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.model.query.OperationLogQuery;
import com.gclife.platform.model.request.OperationLogRequest;
import com.gclife.platform.model.response.OperationLogResponse;
import com.gclife.platform.model.response.OperationRoleResponse;
import com.gclife.platform.model.response.OperationUserResponse;
import com.gclife.platform.service.business.OperationLogBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.modelmapper.TypeToken;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@Service
@Slf4j
public class OperationLogBusinessServiceImpl extends BaseBusinessServiceImpl implements OperationLogBusinessService {

    @Autowired
    private OperationLogExtDao operationLogExtDao;

    @Autowired
    OperationLogDao operationLogDao;
    @Autowired
    UsersExtDao usersExtDao;
    @Autowired
    Environment env;
    @Autowired
    PlatformConfigBaseService platformConfigBaseService;
    @Autowired
    RedissonClient redisson;
    private final String logKey = "log:op:que";
    @Autowired
    ResourcesExtDao resourcesExtDao;
    @Autowired
    BaseSysCodeExtDao baseSysCodeExtDao;

    @Transactional
    public void consumeLog(){
        try {
            RQueue<String> queue = redisson.getQueue(logKey);
            String logRequestJson = queue.poll();
            if(AssertUtils.isNotNullString(logRequestJson)){
                OperationLogPo operationLogPo = JackSonUtils.jsonStrToObject(logRequestJson,OperationLogPo.class);
                operationLogPo.setOperationLogId(UUIDUtils.getUUIDShort());
                operationLogPo.setCreatedDate(System.currentTimeMillis());
                operationLogPo.setLogDate(LocalDateTime.now());
                /*if(this.resourcesPoMap == null){
                    this.initResourcesMap();
                }
                if(this.resourcesPoMap != null){
                    ResourcesPo po = this.resourcesPoMap.get(operationLogPo.getRequestUrl());
                    if(AssertUtils.isNotNull(po)){
                        operationLogPo.setServiceName(po.getServiceName());
                        operationLogPo.setRequestNote(po.getDescription());
                    }
                }*/
                log.debug("插入数据:"+JackSonUtils.toJson(operationLogPo));
                operationLogDao.insert(operationLogPo);
            }
        }catch (Exception e){
            log.error("消费操作日志保存错误,{}",ExceptionUtils.getFullStackTrace(e));
        }

    }


    @Override
    public ResultObject<BasePageResponse<OperationLogResponse>> listOperationLog(OperationLogRequest operationLogRequest) {
        ResultObject<BasePageResponse<OperationLogResponse>> resultObject = new ResultObject<>();

        OperationLogQuery operationLogQuery = (OperationLogQuery) converterObject(operationLogRequest, OperationLogQuery.class);
        List<OperationLogBo> operationLogBos = operationLogExtDao.listOperationLog(operationLogQuery);
        List<OperationLogResponse> operationLogResponses = null;
        if (AssertUtils.isNotEmpty(operationLogBos)) {
            // 对象深拷贝
            operationLogResponses = (List<OperationLogResponse>) this.converterList(operationLogBos, new TypeToken<List<OperationLogResponse>>() {
            }.getType());
        }

        // 获取总页数
        Integer totalLine = AssertUtils.isNotNull(operationLogBos) ? operationLogBos.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(operationLogRequest.getCurrentPage(), operationLogRequest.getPageSize(), totalLine, operationLogResponses);

        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<List<OperationUserResponse>> listOperationUser(String keyword) {
        ResultObject<List<OperationUserResponse>> resultObject = new ResultObject<>();
        List<OperationUserResponse> operationUserResponses = null;

        List<OperationUserBo> operationUserBos = operationLogExtDao.listOperationUserByKeyword(keyword);
        if (AssertUtils.isNotEmpty(operationUserBos)) {
            operationUserResponses = (List<OperationUserResponse>) this.converterList(operationUserBos, new TypeToken<List<OperationUserResponse>>() {
            }.getType());
        }

        resultObject.setData(operationUserResponses);
        return resultObject;
    }

    @Override
    public ResultObject<List<OperationLogResponse>> listReportOperationLog(OperationLogRequest operationLogRequest) {
        ResultObject<List<OperationLogResponse>> resultObject = new ResultObject<>();

        OperationLogQuery operationLogQuery = (OperationLogQuery) converterObject(operationLogRequest, OperationLogQuery.class);
        List<OperationLogBo> operationLogBos = operationLogExtDao.listOperationLog(operationLogQuery);
        List<OperationLogResponse> operationLogResponses = null;
        if (AssertUtils.isNotEmpty(operationLogBos)) {
            // 对象深拷贝
            operationLogResponses = (List<OperationLogResponse>) this.converterList(operationLogBos, new TypeToken<List<OperationLogResponse>>() {
            }.getType());
        }

        resultObject.setData(operationLogResponses);
        return resultObject;
    }

    @Override
    public ResultObject<List<OperationRoleResponse>> listOperationRole(Users users) {
        ResultObject<List<OperationRoleResponse>> resultObject = new ResultObject<>();

        List<OperationRoleBo> operationRoleBos = operationLogExtDao.listOperationRole();
        List<OperationRoleResponse> operationRoleResponses = null;
        if (AssertUtils.isNotEmpty(operationRoleBos)) {
            // 对象深拷贝
            operationRoleResponses = (List<OperationRoleResponse>) this.converterList(operationRoleBos, new TypeToken<List<OperationRoleResponse>>() {
            }.getType());
        }

        // 角色名称国际化
        if (AssertUtils.isNotEmpty(operationRoleResponses)) {
            List<SyscodeBo> syscodeBos = baseSysCodeExtDao.internationalTextGet(InternationalTypeEnum.ROLE.name(), users.getLanguage());
            for (OperationRoleResponse operationRoleResponse : operationRoleResponses) {
                String code = operationRoleResponse.getCode();
                // 角色名称默认值 roles.name
                operationRoleResponse.setCodeName(operationRoleResponse.getName());
                for (SyscodeBo syscodeBo : syscodeBos) {
                    String codeKey = syscodeBo.getCodeKey();
                    if (AssertUtils.isNotEmpty(code) && code.equals(codeKey)) {
                        operationRoleResponse.setCodeName(syscodeBo.getCodeName());
                    }
                }
            }
        }

        resultObject.setData(operationRoleResponses);
        return resultObject;
    }
}
