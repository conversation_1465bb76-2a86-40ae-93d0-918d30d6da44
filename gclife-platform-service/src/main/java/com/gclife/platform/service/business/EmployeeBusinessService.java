package com.gclife.platform.service.business;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.EmployeeQueryRequest;
import com.gclife.platform.model.request.UserQueryRequest;
import com.gclife.platform.model.request.UserRequest;

/**
 * <AUTHOR>
 */
public interface EmployeeBusinessService extends BaseBusinessService {

    /**
     * 加载员工管理渠道列表
     * @param users 当前用户
     * @param userId 查询用户ID
     * @return
     */
    ResultObject loadUsersManagerChannels(Users users, String userId);



    /**
     * 加载职员信息
     * @param users 当前用户
     * @param userId 用户Id
     * @return
     */
    ResultObject loadEmployeByUserId(Users users,String userId);



    /**
     * 加载职员信息
     * @param users 当前用户
     * @param employeeQueryRequest  职员请求
     * @return
     */
    ResultObject loadEmployeByUserIds(Users users,EmployeeQueryRequest employeeQueryRequest);


}
