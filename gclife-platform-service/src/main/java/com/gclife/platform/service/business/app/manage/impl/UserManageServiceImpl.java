package com.gclife.platform.service.business.app.manage.impl;

import com.gclife.auth.api.UserBaseApi;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.base.model.bo.UsersDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.BranchBaseService;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.app.manage.UserManageService;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.service.data.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class UserManageServiceImpl extends BaseBusinessServiceImpl implements UserManageService {
    @Autowired
    private InternationalBaseBusinessService internationalBaseBusinessService;
    @Autowired
    private BranchBaseService branchBaseService;
    @Autowired
    private UsersBaseService usersBaseService;
    @Autowired
    private UsersService usersService;
    @Autowired
    private UserBaseApi userBaseApi;
    @Override
    public ResultObject updateUserEnabled(String updateUserId, String enabled, Users currentLoginUsers) {
        ResultObject resultObject = new ResultObject<>();
        try {

            ResultObject<SyscodeResponse> syscodeResponseResultObject = internationalBaseBusinessService.queryOneInternational(TerminologyConfigEnum.ENABLED.ENABLED.name(),enabled, null);
            SyscodeResponse syscodeResponse = syscodeResponseResultObject.getData();
            if(!AssertUtils.isNotNull(syscodeResponse)){
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_IS_NOT_USER_ENABLED_ERROR);
            }
            List<BranchPo> branchPos = branchBaseService.queryUserOptionBranchTreeLeaf(currentLoginUsers.getUserId());
            if (!AssertUtils.isNotEmpty(branchPos)) {
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_UPDATE_USER_ENABLED_ERROR);
            }
            List<String> branchIds = branchPos.stream().map(BranchPo::getBranchId).distinct().collect(Collectors.toList());
            UsersDo usersDo = usersBaseService.queryOneUsersPoById(updateUserId);
            Optional<String> branchIdOptional = branchIds.stream().filter(branchId -> branchId.equals(usersDo.getBranchId())).findFirst();
            if(!AssertUtils.isNotNull(usersDo)){
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            }
            if(!branchIdOptional.isPresent()){
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USERS_BRANCH_ID_IS_NOT_USER_ERROR);
            }
            usersDo.setEnabled(enabled);

            // 用户禁用就清除旧token
            boolean b = TerminologyConfigEnum.ENABLED.DISABLED.name().equals(enabled);
            if (b) {
                usersDo.setUserInvalidDate(DateUtils.getCurrentTime());
            }
            usersService.saveUsersPo(usersDo);
            if (b) {
                userBaseApi.deleteToken(usersDo.getUserId());
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_UPDATE_USER_ENABLED_ERROR);
            }
        }
        return resultObject;
    }
}
