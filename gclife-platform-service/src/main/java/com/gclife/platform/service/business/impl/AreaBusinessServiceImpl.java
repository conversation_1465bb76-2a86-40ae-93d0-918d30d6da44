package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.dao.AreaExtDao;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.model.bo.AreaBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.platform.service.business.AreaBusinessService;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:区域地址实现
 */
@Service
public class AreaBusinessServiceImpl extends BaseBusinessServiceImpl implements AreaBusinessService {

    @Autowired
    private AreaExtDao areaExtDao;

    @Autowired
    private BaseSysCodeExtDao baseSysCodeExtDao;

    @Override
    public ResultObject<List<AreaResponse>> getAreaById(String areaId, Users users, AppRequestHeads appRequestHeads) {
        ResultObject<List<AreaResponse>> resultObject = new ResultObject<>();
        try {
            if (StringUtil.isNullString(areaId)) {
                //设置根节点编码ID
                areaId = "ROOT";
            }

            List<AreaBo> areaBos = areaExtDao.loadArea(areaId);

            if (!AssertUtils.isNotEmpty(areaBos)) {
                return resultObject;
            }
            //数据转换
            List<AreaResponse> areaResponses = (List<AreaResponse>) this.converterList(areaBos, new TypeToken<List<AreaResponse>>() {
            }.getType());

            //从国际化文本中取name
            areaResponses.forEach(areaResponse -> {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaResponse.getAreaId(), InternationalTypeEnum.AREA.name(), appRequestHeads.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaResponse.setAreaName(syscodeBo.getCodeName());
                }
                //网销特殊判断只展示到镇
                List<AreaBo> areaBos1 = areaExtDao.loadArea(areaResponse.getAreaId());
                if (AssertUtils.isNotEmpty(areaBos1)) {
                    AreaBo areaBo = areaBos1.get(0);
                    if ("0".equals(areaBo.getExistChild())) {
                        areaResponse.setExistGrandson("NO");
                    }else {
                        areaResponse.setExistGrandson("YES");
                    }
                }
            });
            resultObject.setData(areaResponses);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AreaTreeResponse>> getAreaTreeById(String areaId, Users users, String sortType) {
        ResultObject<List<AreaTreeResponse>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);

            //查询地址树
            List<AreaTreeResponse> areaTreeResponses = areaExtDao.loadAreaTree(areaId, sortType);

            AssertUtils.isNotEmpty(this.getLogger(), areaTreeResponses, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            areaTreeResponses.forEach(areaTreeResponse -> {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaTreeResponse.getAreaId(), InternationalTypeEnum.AREA.name(), users.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaTreeResponse.setAreaName(syscodeBo.getCodeName());
                }
            });

            //返回数据
            resultObject.setData(areaTreeResponses);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaNameResponse> getAreaNameById(String areaId, Users users, String language, String sortType) {
        ResultObject<AreaNameResponse> resultObject = new ResultObject<>();
        AreaNameResponse areaNameResponse = new AreaNameResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);

            //查询地址树
            List<AreaTreeResponse> areaTreeResponses = areaExtDao.loadAreaTree(areaId, sortType);

            AssertUtils.isNotEmpty(this.getLogger(), areaTreeResponses, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            areaTreeResponses.forEach(areaTreeResponse -> {
                String newLanguage = "";
                if (AssertUtils.isNotEmpty(language)) {
                    newLanguage = language;
                } else {
                    newLanguage = users.getLanguage();
                }
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaTreeResponse.getAreaId(), InternationalTypeEnum.AREA.name(), newLanguage);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaTreeResponse.setAreaName(syscodeBo.getCodeName());
                }
            });

            List<String> provinceNames = new ArrayList<>();
            List<String> areaNames = new ArrayList<>();
            areaTreeResponses.forEach(areaTreeRespFc -> areaNames.add(areaTreeRespFc.getAreaName()));
            String areaName = StringUtils.join(areaNames.toArray(), " ");
            areaNameResponse.setAreaName(areaName);
            areaNameResponse.setAreaId(areaId);

            //过滤刷选出地址的省会和城市信息
            areaTreeResponses.stream().filter(areaTreeResponse -> "CAMBODIA".equals(areaTreeResponse.getParentAreaId()))
                    .findFirst().ifPresent(areaTreeResponse -> {
                        provinceNames.add(areaTreeResponse.getAreaName());
                        /*areaTreeResponses.stream().filter(areaTreeResponse1 -> areaTreeResponse.getAreaId().equals(areaTreeResponse1.getParentAreaId()))
                                .findFirst().ifPresent(areaTreeResponse1 -> {
                                    provinceNames.add(areaTreeResponse1.getAreaName());
                                });*/
                    });

            if (AssertUtils.isNotEmpty(provinceNames)) {
                String provinceName = StringUtils.join(provinceNames.toArray(), " ");
                areaNameResponse.setCapitalName(provinceName);
            }

            //返回数据
            resultObject.setData(areaNameResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaNameResponse> getAreaNameByIdOnline(String areaId,AppRequestHeads appRequestHeads) {
        ResultObject<AreaNameResponse> resultObject = new ResultObject<>();
        AreaNameResponse areaNameResponse = new AreaNameResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);

            //查询地址树
            List<AreaTreeResponse> areaTreeResponses = areaExtDao.loadAreaTree(areaId, null);

            AssertUtils.isNotEmpty(this.getLogger(), areaTreeResponses, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            areaTreeResponses.forEach(areaTreeResponse -> {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaTreeResponse.getAreaId(), InternationalTypeEnum.AREA.name(), appRequestHeads.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaTreeResponse.setAreaName(syscodeBo.getCodeName());
                }
            });

            List<String> areaNames = new ArrayList<>();
            areaTreeResponses.forEach(areaTreeRespFc -> areaNames.add(areaTreeRespFc.getAreaName()));
            String areaName = StringUtils.join(areaNames.toArray(), "/");
            areaNameResponse.setAreaName(areaName);
            areaNameResponse.setAreaId(areaId);
            //返回数据
            resultObject.setData(areaNameResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaNameResponse> getAreaNameByIdOnlineNormal(String areaId, AppRequestHeads appRequestHeads) {
        ResultObject<AreaNameResponse> resultObject = new ResultObject<>();
        AreaNameResponse areaNameResponse = new AreaNameResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);

            //查询地址树
            List<AreaTreeResponse> areaTreeResponses = areaExtDao.loadAreaTree(areaId, null);

            AssertUtils.isNotEmpty(this.getLogger(), areaTreeResponses, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            areaTreeResponses.forEach(areaTreeResponse -> {
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaTreeResponse.getAreaId(), InternationalTypeEnum.AREA.name(), appRequestHeads.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaTreeResponse.setAreaName(syscodeBo.getCodeName());
                }
            });

            List<String> areaNames = new ArrayList<>();
            areaTreeResponses.forEach(areaTreeRespFc -> areaNames.add(areaTreeRespFc.getAreaName()));
            String areaName = StringUtils.join(areaNames.toArray(), " ");
            areaNameResponse.setAreaName(areaName);
            areaNameResponse.setAreaId(areaId);
            //返回数据
            resultObject.setData(areaNameResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaNameResponse> getAreaNameByIdNew(String areaId, Users users, String language) {
        ResultObject<AreaNameResponse> resultObject = new ResultObject<>();
        AreaNameResponse areaNameResponse = new AreaNameResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);

            //查询地址树
            List<AreaTreeResponse> areaTreeResponses = areaExtDao.loadAreaTreeNew(areaId);

            AssertUtils.isNotEmpty(this.getLogger(), areaTreeResponses, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            areaTreeResponses.forEach(areaTreeResponse -> {
                String newLanguage = "";
                if (AssertUtils.isNotEmpty(language)) {
                    newLanguage = language;
                } else {
                    newLanguage = users.getLanguage();
                }
                SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaTreeResponse.getAreaId(), InternationalTypeEnum.AREA.name(), newLanguage);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    areaTreeResponse.setAreaName(syscodeBo.getCodeName());
                }
            });

            List<String> areaNames = new ArrayList<>();
            areaTreeResponses.forEach(areaTreeRespFc -> areaNames.add(areaTreeRespFc.getAreaName()));
            String areaName = StringUtils.join(areaNames.toArray(), " ");
            areaNameResponse.setAreaName(areaName);
            areaNameResponse.setAreaId(areaId);
            //返回数据
            resultObject.setData(areaNameResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaResponse> getAreaInfo(String areaId, Users users) {
        ResultObject<AreaResponse> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);
            AreaBo areaBo = areaExtDao.loadAreaInfo(areaId);
            AreaResponse areaResponse = (AreaResponse) this.converterObject(areaBo, AreaResponse.class);
            //从国际化文本中取name
            SyscodeBo syscodeBo = baseSysCodeExtDao.internationalTextGetOne(areaResponse.getAreaId(), InternationalTypeEnum.AREA.name(), users.getLanguage());
            if (AssertUtils.isNotNull(syscodeBo)) {
                areaResponse.setAreaName(syscodeBo.getCodeName());
            }
            resultObject.setData(areaResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AreaResponse>> getAreaList(List<String> areaIds, Users users) {
        ResultObject<List<AreaResponse>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), areaIds, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);
            List<AreaBo> areaBos = areaExtDao.loadAreaList(areaIds);
            List<AreaResponse> areaResponseList = (List<AreaResponse>) this.converterList(areaBos, new TypeToken<List<AreaResponse>>() {
            }.getType());
            //从国际化文本中取name
            List<SyscodeBo> syscodeBos = baseSysCodeExtDao.internationalTextGet(InternationalTypeEnum.AREA.name(), users.getLanguage());
            areaResponseList.forEach(areaResponse -> {
                syscodeBos.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(areaResponse.getAreaId())).findFirst().ifPresent(syscodeBo -> {
                    areaResponse.setAreaName(syscodeBo.getCodeName());
                });
            });
            resultObject.setData(areaResponseList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据叶子节点ID批量获取区域地址树
     *
     * @param areaIds
     * @param users
     * @return
     */
    @Override
    public ResultObject<Map<String, List<AreaTreeResponse>>> getAreaTreeByIds(List<String> areaIds, Users users) {
        ResultObject<Map<String, List<AreaTreeResponse>>> resultObject = ResultObject.success();
        if (!AssertUtils.isNotEmpty(areaIds)) {
            return resultObject;
        }
        Map<String, List<AreaTreeResponse>> map = new HashMap<>();
        areaIds.stream().distinct().collect(Collectors.toList()).forEach(areaId -> {
            ResultObject<List<AreaTreeResponse>> areaTreeById = this.getAreaTreeById(areaId, users, null);
            if (!AssertUtils.isResultObjectListDataNull(areaTreeById)) {
                map.put(areaId, areaTreeById.getData());
            }
        });
        resultObject.setData(map);
        return resultObject;
    }

    /**
     * 根据叶子节点ID批量获取父类区域地址名称
     *
     * @param areaIds
     * @param users
     * @param language
     * @param sortType
     * @return
     */
    @Override
    public ResultObject<List<AreaNameResponse>> areaNameGetBatch(List<String> areaIds, Users users, String language, String sortType) {
        ResultObject<List<AreaNameResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(areaIds)) {
            return resultObject;
        }
        List<AreaNameResponse> areaNameResponses = new ArrayList<>();
        areaIds.stream().distinct().collect(Collectors.toList()).forEach(areaId -> {
            ResultObject<AreaNameResponse> areaNameById = this.getAreaNameById(areaId, users, language, sortType);
            if (!AssertUtils.isResultObjectDataNull(areaNameById)) {
                areaNameResponses.add(areaNameById.getData());
            }
        });
        resultObject.setData(areaNameResponses);
        return resultObject;
    }
}