package com.gclife.platform.service.business.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.RoleExtDao;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.RolesBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.workflow.RoleWfResponse;
import com.gclife.platform.model.response.workflow.UserWfResponse;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.service.business.UsersWorkFlowBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class UsersWorkFlowBusinessServiceImpl extends BaseBusinessServiceImpl implements UsersWorkFlowBusinessService {

    @Autowired
    UsersExtDao usersExtDao;

    @Autowired
    RoleExtDao roleExtDao;

    /**
     * 加载workflow用户信息
     *
     * @param userId
     * @return
     */
    @Override
    public ResultObject loadWorkflowUser(String userId) {
        ResultObject<UserWfResponse> resultObject = new ResultObject<UserWfResponse>();
        try {
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            UsersPo usersPo = usersExtDao.loadUserPo(userId);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
            //数据转换
            UserWfResponse userWFResponse = (UserWfResponse) this.converterObject(usersPo, UserWfResponse.class);

            resultObject.setData(userWFResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 加载workflow用户角色列表
     *
     * @param userId
     * @return
     */
    @Override
    public ResultObject loadWorkflowUserRoles(String userId) {
        ResultObject<List<RoleWfResponse>> resultObject = new ResultObject<List<RoleWfResponse>>();
        try {
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);

            List<RolesBo> listRoles = usersExtDao.loadUserRoles(userId);

            AssertUtils.isNotNull(this.getLogger(), listRoles, PlatformErrorConfigEnum.PLATFORM_BUSINESS_ROLE_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<RoleWfResponse> roleWFResponses = (List<RoleWfResponse>) this.converterList(listRoles, new TypeToken<List<RoleWfResponse>>() {
            }.getType());

            resultObject.setData(roleWFResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 加载workflow角色列
     *
     * @param roleId 角色信息
     * @return
     */
    @Override
    public ResultObject loadWorkflowRole(String roleId) {
        ResultObject<RoleWfResponse> resultObject = new ResultObject<RoleWfResponse>();
        try {
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), roleId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ROLE_ID_IS_NOT_NULL);
            RolesBo rolesBo = roleExtDao.loadRolesBo(roleId);
            AssertUtils.isNotNull(this.getLogger(), rolesBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_ROLE_IS_NOT_FOUND_OBJECT);
            //数据转换
            RoleWfResponse roleWFResponse = (RoleWfResponse) this.converterObject(rolesBo, RoleWfResponse.class);

            resultObject.setData(roleWFResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<UsersWfResponse> loadWorkflowUsers(String activityAuid, String branchId) {
        ResultObject<UsersWfResponse> resultObject = new ResultObject<>();
        UsersWfResponse usersWfResponses = new UsersWfResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), activityAuid, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);

            usersWfResponses = usersExtDao.loadWorkflowUser(activityAuid, branchId);

            resultObject.setData(usersWfResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<String>> queryWorkflowNodePermissionUsers(String activityAuid, String branchId, String branchMode) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), activityAuid, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACTIVITYAUID_IS_NOT_NULL);

            List<String> userIds = usersExtDao.queryWorkflowNodePermissionUsers(activityAuid, branchId,branchMode);

            resultObject.setData(userIds);

        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(),resultObject,e,PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
        }
        return resultObject;
    }
}