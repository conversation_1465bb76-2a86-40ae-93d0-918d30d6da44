package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.request.SharedLinkSaveRequest;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:　分享链接的接口
 */

public interface SharedLinkBusinessService {

    /**
     *
     * 根据链接签名获取链接
     *
     * @param signature 链接签名
     * @return ResultObject
     */
    ResultObject retriveSharedLinkBySignature(String signature);

    /**
     *
     * 根据链接签名获取链接
     *
     * @param sharedLinkSaveRequest 链接保存请求
     * @return ResultObject
     */
    ResultObject saveSharedLink(String userId, SharedLinkSaveRequest sharedLinkSaveRequest);
}
