package com.gclife.platform.service.business.event;

import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.EventRequest;
import com.gclife.platform.model.response.EventResponse;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * create 19-2-26
 * description:
 */

public interface EventBusinessService extends BaseBusinessService {

    ResultObject<BasePageResponse<EventResponse>> getEventInfo(BasePageRequest basePageRequest);

    ResultObject likeOrForward(Users currentLoginUsers, String dynamicType, String eventId);

    /**
     * 查询单个事件
     * @param eventId
     * @return
     */
    ResultObject<EventResponse> queryOneEventInfo(String eventId);

    /**
     * 保存事件
     * @param eventRequest
     * @return
     */
    ResultObject saveEventInfo(EventRequest eventRequest);
}
