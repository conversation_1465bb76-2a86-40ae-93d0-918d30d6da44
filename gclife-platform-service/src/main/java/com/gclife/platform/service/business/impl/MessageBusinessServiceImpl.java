package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.message.api.BusinessMessageApi;
import com.gclife.message.model.request.BusinessMessagePushRequest;
import com.gclife.platform.base.model.bo.EventBo;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.service.business.MessageBusinessService;
import com.gclife.platform.service.business.UsersBusinessService;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.vo.branch.BranchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 消息处理
 * @date 18-4-13
 */
@Service
@Lazy
public class MessageBusinessServiceImpl extends BaseBusinessServiceImpl implements MessageBusinessService {

    @Autowired
    private BusinessMessageApi businessMessageApi;
    @Autowired
    private BranchBaseBusinessService branchBaseBusinessService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private UsersBusinessService usersBusinessService;

    /**
     * 喜报消息发送(单个用户)
     *
     * @param businessCode 消息类型
     * @param eventBo      喜报对象
     * @param userId       用户ID
     */
    @Override
    @Async
    public void pushEventMessageSingle(String businessCode, EventBo eventBo, String userId) {
        try {
            this.getLogger().error("消息发送入口 喜报对象eventBo:" + JSON.toJSONString(eventBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParam(eventBo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushEventMessageSingle(String businessCode, EventBo eventBo, String userId, Map<String, String> map) {
        try {
            this.getLogger().error("消息发送入口 喜报对象eventBo:" + JSON.toJSONString(eventBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageMapParam(eventBo, map));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送业务员银行卡审核提醒 批量用户
     *
     * @param businessCode 业务类型
     * @param userId       用户ID
     */
    @Override
    @Async
    public void sendBankCardReviewBatchMessage(String businessCode, String userId) {
        try {
            Map<String, String> messageParamMap = new HashMap<>();
            ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByIdGet(userId);
            AgentResponse agentResponse = agentResponseResultObject.getData();
            messageParamMap.put("agentName", agentResponse.getAgentName());
            messageParamMap.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE6));

            String jsonString = JSON.toJSONString(messageParamMap);
            this.getLogger().info("发送业务员银行卡审核提醒,参数:{}", jsonString);

            List<String> userIds = usersBusinessService.loadBusinessUserRecentList(businessCode, agentResponse.getBranchId(), null).getData();
            if (AssertUtils.isNotEmpty(userIds)) {
                this.pushBusinessMessage(businessCode, userIds, jsonString);
            }
        } catch (Exception e) {
            this.getLogger().info("发送业务员银行卡审核提醒失败,错误原因:{}", e.getMessage());
        }
    }

    /**
     * 消息发送
     *
     * @param businessCode 消息类型
     * @param userIds      用户IDS
     * @param messageParam 消息参数
     */
    @Override
    public void pushBusinessMessage(String businessCode, List<String> userIds, String messageParam) {
        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setUserIdList(userIds);
        businessMessagePushReqFc.setMessageParam(messageParam);

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
    }

    /**
     * 喜报消息发送参数转换
     *
     * @param eventBo 喜报对象
     */
    private String transferMessageParam(EventBo eventBo) {
        Map<String, String> map = new HashMap<>();
        transferMessageMapParam(map, eventBo);
        return JSON.toJSONString(map);
    }

    /**
     * 喜报消息发送参数转换
     *
     * @param eventBo 喜报对象
     */
    private String transferMessageMapParam(EventBo eventBo, Map<String, String> paramMap) {
        Map<String, String> map = new HashMap<>();
        transferMessageMapParam(map, eventBo);
        if (AssertUtils.isNotNull(paramMap)) {
            map.putAll(paramMap);
        }
        return JSON.toJSONString(map);
    }

    private void transferMessageMapParam(Map<String, String> map, EventBo eventBo) {
        if (AssertUtils.isNotNull(eventBo)) {
            map.put("eventId", eventBo.getEventId());
            map.put("count", AssertUtils.isNotNull(eventBo.getLikeNumber()) ? eventBo.getLikeNumber().toString() : "0");
            if (PlatformTermEnum.EVENT_BUSINESS_TYPE.HAPPY_NEWS.name().equals(eventBo.getBusinessType()) && AssertUtils.isNotNull(eventBo.getEventHappyNewsPo())) {
                map.put("applicantName", eventBo.getEventHappyNewsPo().getApplicantName());
                map.put("productName", eventBo.getEventHappyNewsPo().getProductName());
                if (AssertUtils.isNotNull(eventBo.getEventHappyNewsPo().getApproveDate())) {
                    map.put("time", DateUtils.timeStrToString(eventBo.getEventHappyNewsPo().getApproveDate(), DateUtils.FORMATE3));
                }
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INSURE.name().equals(eventBo.getBusinessType()) && AssertUtils.isNotNull(eventBo.getEventInsurePo())) {
                map.put("applicantName", eventBo.getEventInsurePo().getApplicantName());
                map.put("productName", eventBo.getEventInsurePo().getProductName());
                if (AssertUtils.isNotNull(eventBo.getEventInsurePo().getInsureDate())) {
                    map.put("time", DateUtils.timeStrToString(eventBo.getEventInsurePo().getInsureDate(), DateUtils.FORMATE3));
                }
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.PLAN.name().equals(eventBo.getBusinessType()) && AssertUtils.isNotNull(eventBo.getEventPlanPo())) {
                map.put("applicantName", eventBo.getEventPlanPo().getCustomerName());
                if (AssertUtils.isNotNull(eventBo.getEventPlanPo().getTime())) {
                    map.put("time", DateUtils.timeStrToString(eventBo.getEventPlanPo().getTime(), DateUtils.FORMATE3));
                }
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INCREASE.name().equals(eventBo.getBusinessType()) && AssertUtils.isNotNull(eventBo.getEventIncreasePo())) {
                map.put("agentName", eventBo.getEventIncreasePo().getRefereeAgentName());
                if (AssertUtils.isNotNull(eventBo.getEventIncreasePo().getTime())) {
                    map.put("time", DateUtils.timeStrToString(eventBo.getEventIncreasePo().getTime(), DateUtils.FORMATE3));
                }
            }
        }
    }
}
