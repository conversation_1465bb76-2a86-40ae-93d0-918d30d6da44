package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.PlatformConfigBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BaseFactorConfigPo;
import com.gclife.platform.core.jooq.tables.pojos.NotifyConfigPo;
import com.gclife.platform.core.jooq.tables.pojos.TopBranchConfigPo;
import com.gclife.platform.dao.BranchExtDao;
import com.gclife.platform.model.response.BaseFactorConfigResponse;
import com.gclife.platform.model.response.BranchConfigResponse;
import com.gclife.platform.model.response.NotifyConfigResponse;
import com.gclife.platform.service.business.PlatformConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create 18-1-23
 * description:
 */
@Service
public class PlatformConfigServiceImpl extends BaseBusinessServiceImpl implements PlatformConfigService {

    @Autowired
    private BranchExtDao branchExtDao;

    @Autowired
    private PlatformConfigBaseService platformConfigBaseService;

    @Override
    public ResultObject<BranchConfigResponse> getBranchConfig(String branchId, String configType) {
        ResultObject<BranchConfigResponse> resultObject = new ResultObject<>();
        try {
            //参数校验
            AssertUtils.isNotEmpty(this.getLogger(), branchId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_BRANCH_ID_ERROR);
            AssertUtils.isNotEmpty(this.getLogger(), configType, PlatformErrorConfigEnum.PLATFORM_QUERY_CONFIG_TYPE_ERROR);
            //数据返回
            TopBranchConfigPo topBranchConfigPo = branchExtDao.loadBranchConfig(branchId, configType);
            BranchConfigResponse branchConfigResponse = (BranchConfigResponse) this.converterObject(topBranchConfigPo, BranchConfigResponse.class);
            resultObject.setData(branchConfigResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_TOP_BRANCH_CONFIG_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<NotifyConfigResponse> getNotifyConfig(String businessType) {
        ResultObject<NotifyConfigResponse> resultObject = new ResultObject<>();
        try {
            //参数校验
            AssertUtils.isNotEmpty(this.getLogger(), businessType, PlatformErrorConfigEnum.PLATFORM_BUSINESS_TYPE_IS_NOT_NULL);
            //数据返回
            NotifyConfigPo notifyConfig = platformConfigBaseService.getNotifyConfig(businessType);
            NotifyConfigResponse notifyConfigResponse = (NotifyConfigResponse) this.converterObject(notifyConfig, NotifyConfigResponse.class);
            resultObject.setData(notifyConfigResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsException(this.getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_NOTIFY_CONFIG_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询基础要素配置
     *
     * @param configCode 配置编码
     * @return BaseFactorConfigResponse
     */
    @Override
    public ResultObject<BaseFactorConfigResponse> getBaseFactorConfig(String configCode) {
        ResultObject<BaseFactorConfigResponse> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(configCode)) {
                return resultObject;
            }
            BaseFactorConfigPo baseFactorConfig = platformConfigBaseService.getBaseFactorConfig(configCode);
            resultObject.setData((BaseFactorConfigResponse) this.converterObject(baseFactorConfig, BaseFactorConfigResponse.class));
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsException(this.getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_BASE_FACTOR_CONFIG_ERROR);
        }
        return resultObject;
    }

    /**
     * 内勤登录微信验证开关
     *
     * @return ResultObject
     */
    @Override
    public ResultObject editWeChatSwitch() {
        ResultObject resultObject = new ResultObject<>();
        try {
            BaseFactorConfigPo baseFactorConfig = platformConfigBaseService.getBaseFactorConfig(PlatformTermEnum.BASE_FACTOR_CONFIG.USER_EMPLOYEE_WECHAT_VERIFICATION.name());
            if (!AssertUtils.isNotNull(baseFactorConfig)) {
                return resultObject;
            }
            String returnMessage = "";
            if (baseFactorConfig.getConfigValue().equals(TerminologyConfigEnum.WHETHER.YES.name())) {
                returnMessage = "微信验证已关闭!";
                baseFactorConfig.setConfigValue(TerminologyConfigEnum.WHETHER.NO.name());
            } else {
                returnMessage = "微信验证已打开!";
                baseFactorConfig.setConfigValue(TerminologyConfigEnum.WHETHER.YES.name());
            }
            platformConfigBaseService.saveBaseFactorConfigPo(baseFactorConfig);
            resultObject.setData(returnMessage);
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsException(this.getLogger(), e, PlatformErrorConfigEnum.PLATFORM_QUERY_BASE_FACTOR_CONFIG_ERROR);
        }
        return resultObject;
    }
}
