package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.AttendanceResponse;
import com.gclife.platform.model.response.AttendancesMessageResponse;
import com.gclife.platform.service.business.AttendanceService;
import com.gclife.platform.service.data.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Service
public class AttendanceServiceImpl extends BaseBusinessServiceImpl implements AttendanceService {

//    @Autowired
//    private AttendanceExtDao attendanceExtDao;
    @Autowired
    private UsersService usersService;

    @Override
    public ResultObject attendanceSave(Users users) {
        ResultObject resultObject = new ResultObject();

        try {
//
//            AttendanceBo attendanceBo = attendanceExtDao.getAttendance(users, System.currentTimeMillis(), System.currentTimeMillis(),AttendanceTypeConfigEnum.PUNCH_CARD.name());
//            //判断用户 是否已经签到
//            if (attendanceBo != null) {
//                throw new RequestException(AttendanceTypeConfigEnum.ALREADY_PUNCH_CARD);
//            }
//            UserActiveScenesBo userActiveScenes = attendanceExtDao.getUserActiveScenes(AttendanceTypeConfigEnum.PUNCH_CARD.name());
//            //判断活动是否为空
//            if (!AssertUtils.isNotNull(userActiveScenes)) {
//                throw new RequestException(PlatformErrorConfigEnum.ATTENDANCE_SAVE_USER_ACTIVE_ERROR);
//            }
//
//            UserActiveLogPo userActiveLogPo = new UserActiveLogPo();
//            userActiveLogPo.setActiveScenesId(userActiveScenes.getActiveScenesId());
//            userActiveLogPo.setUserId(users.getUserId());
//            userActiveLogPo.setCreatedUserId(users.getUserId());
//            userActiveLogPo.setUpdatedUserId(users.getUserId());
//            userActiveLogPo.setContinuouSignDays(1L);
//            //获取前天 的 签到天数
//            Calendar calendar = new GregorianCalendar();
//            calendar.setTime(new Date());
//            calendar.add(Calendar.DATE, -1);
//            Long startTime = calendar.getTime().getTime();
//
//            AttendanceBo attendance = attendanceExtDao.getAttendance(users, startTime, startTime, AttendanceTypeConfigEnum.PUNCH_CARD.name());
//
//            if (AssertUtils.isNotNull(attendance)&&AssertUtils.isNotNull(attendance.getContinuouSignDays())) {
//                userActiveLogPo.setContinuouSignDays(attendance.getContinuouSignDays() + 1L);
//            }
//
//            usersService.saveAttendance(userActiveLogPo);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.ATTENDANCE_SAVE_USER_ACTIVE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    @Override
    public ResultObject<AttendanceResponse> getAttendance(Users users) {
        ResultObject<AttendanceResponse> resultObject = new ResultObject<>();

        try {

//            AttendanceBo attendanceBo = attendanceExtDao.getAttendance(users, System.currentTimeMillis(), System.currentTimeMillis(), AttendanceTypeConfigEnum.PUNCH_CARD.name());
//
//            AttendanceResponse attendanceResponse = new AttendanceResponse();
//            if (AssertUtils.isNotNull(attendanceBo)) {
//                attendanceResponse = (AttendanceResponse) this.converterObject(attendanceBo, AttendanceResponse.class);
//            }
//
//            attendanceResponse.setAttendanceStatus(
//                    AssertUtils.isNotNull(attendanceBo) ? AttendanceTypeConfigEnum.ALREADY_PUNCH_CARD.name() : AttendanceTypeConfigEnum.UNREAD_PUNCH_CARD.name()
//            );
//
//            resultObject.setData(attendanceResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.ATTENDANCE_QUERY_USER_ACTIVE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }


    @Override
    public ResultObject<AttendancesMessageResponse> getAttendancesMessage(Users users) {

        ResultObject<AttendancesMessageResponse> resultObject = new ResultObject<>();

        try {
//            // 获取前月的第一天
//            Calendar cale = Calendar.getInstance();
//            cale.add(Calendar.MONTH, 0);
//            cale.set(Calendar.DAY_OF_MONTH, 1);
//            Long startTime = cale.getTime().getTime();
//            // 获取前月的最后一天
//            cale = Calendar.getInstance();
//            cale.add(Calendar.MONTH, 1);
//            cale.set(Calendar.DAY_OF_MONTH, 0);
//            Long endTime = cale.getTime().getTime();
//            // 获取当月的天数
//            Calendar a = Calendar.getInstance();
//            a.set(Calendar.DATE, 1);
//            a.roll(Calendar.DATE, -1);
//            int maxDate = a.get(Calendar.DATE);
//            List<AttendancesMessageBo> boList = attendanceExtDao.getAttendancesMessage(users, startTime, endTime, AttendanceTypeConfigEnum.PUNCH_CARD.name());
//            // 初始化签到数据
//            List<AttendanceStatusRequest> daysStatus = new ArrayList<>();
//
//            for (int i = 1; i <= maxDate; i++) {
//                AttendanceStatusRequest asq = new AttendanceStatusRequest();
//                asq.setDay(i);
//                daysStatus.add(asq);
//            }
//
//            SimpleDateFormat sdf = new SimpleDateFormat("dd");
//
//            boList.forEach(bo -> {
//                int data = Integer.parseInt(sdf.format(bo.getCreateDate()));
//
//                daysStatus.forEach(dayStatus -> {
//                    if (data == dayStatus.getDay()) {
//                        dayStatus.setStatus(AttendanceTypeConfigEnum.ALREADY_PUNCH_CARD.name());
//                    }
//                });
//            });
//
//            AttendancesMessageResponse attendancesMessageResponse = new AttendancesMessageResponse();
//
//            // 获取 累计签到天数
//            startTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
//            List<AttendancesMessageBo> continuouSignDays = attendanceExtDao.getAttendancesMessage(users, startTime, System.currentTimeMillis(), AttendanceTypeConfigEnum.PUNCH_CARD.name());
//
//            continuouSignDays.forEach(con -> {
//
//                if (!AssertUtils.isNotNull(attendancesMessageResponse.getContinuouSignDays())
//                        && AssertUtils.isNotNull(con.getContinuouSignDays())) {
//                    attendancesMessageResponse.setContinuouSignDays(con.getContinuouSignDays());
//                }
//                if (AssertUtils.isNotNull(attendancesMessageResponse.getContinuouSignDays())
//                        && AssertUtils.isNotNull(con.getContinuouSignDays())
//                        && con.getContinuouSignDays() > attendancesMessageResponse.getContinuouSignDays()) {
//                    attendancesMessageResponse.setContinuouSignDays(con.getContinuouSignDays());
//                }
//
//            });
//
//            attendancesMessageResponse.setDaysStatus(daysStatus);
//
//            //获取用户总积分
//            UserAccountBo userAccountBo = userInfoExtDao.loadUserAccount(users.getUserId(),AttendanceTypeConfigEnum.POINT_ACCOUNT.name());
//            attendancesMessageResponse.setTotalAmount("0");
//            if (AssertUtils.isNotNull(userAccountBo) && AssertUtils.isNotNull(userAccountBo.getTotalAmount())) {
//                attendancesMessageResponse.setTotalAmount(userAccountBo.getTotalAmount().intValue()+"");
//            }
//
//            resultObject.setData(attendancesMessageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.ATTENDANCE_QUERY_ATTENDANCES_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;

    }

}