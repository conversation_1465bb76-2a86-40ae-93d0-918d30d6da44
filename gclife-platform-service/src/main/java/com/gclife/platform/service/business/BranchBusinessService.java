package com.gclife.platform.service.business;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.core.jooq.tables.pojos.FinancialBranchPo;
import com.gclife.platform.model.response.BranchNameResponse;
import com.gclife.platform.vo.branch.BranchBankResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BranchBusinessService extends BaseBusinessService {


    /**
     * 根据机构ID加载机构信息
     *
     * @param branchId 机构Id
     * @param users    当前用户信息
     * @return
     */
    ResultObject<BranchResponse> loadBranchInfo(Users users, String branchId);

    /**
     * 根据机构ID加载机构信息,根据语言国际化机构名称
     *
     * @param branchId 机构Id
     * @param language 国际化语言
     * @return ResultObject
     */
    ResultObject<BranchResponse> loadBranchInfo(String branchId, String language);


    /**
     * 根据机构ID加载机构信息
     *
     * @param branchIds 机构IdS
     * @param users     当前用户信息
     * @return
     */
    ResultObject<List<BranchResponse>> loadBranchsByIds(Users users, List<String> branchIds);


    /**
     * 根据当前用户管理的销售机构查询机构树
     *
     * @param users 当前用户信息
     * @return
     */
    ResultObject<List<BranchTreeResponse>> loadUserBranchTree(Users users);


    /**
     * 查询机构下面的树
     *
     * @param users    用户信息
     * @param branchId 　机构ID
     * @return ResultObject<List   <   BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> loadBranchTreesByBranchId(Users users, String branchId);


    /**
     * 根据当前用户管理的销售机构查询机构列表
     *
     * @param users 当前用户信息
     * @return
     */
    ResultObject loadUserManagerAllBranchs(Users users);


    /**
     * 根据当前用户管理的销售机构查询机构列表
     *
     * @param users 当前用户信息
     * @return
     */
    ResultObject<List<BranchResponse>> loadUserManagerRootBranchs(Users users);


    /**
     * 根据当前用户管理的销售机构查询机构树叶子节点(营业部)
     *
     * @param users 当前用户信息
     * @return ResultObject<List < BranchResponse>>
     */
    ResultObject loadUserBranchTreeLeaf(Users users);

    /**
     * 根据当前用户管理的销售机构查询机构树叶子节点(营业部)
     *
     * @param users 当前用户信息
     * @return ResultObject<List < BranchTreeResponse>>
     */
    ResultObject loadUserBranchTreeLeaf2(Users users);


    /**
     * 根据销售机构查询机构树叶子节点(营业部)
     *
     * @param branchIds 机构集合
     * @return
     */
    ResultObject loadBranchAllBranchLeaf(Users users, List<String> branchIds);


    /**
     * 根据销售机构查询机构树叶子节点(营业部)
     *
     * @param branchId 机构集合
     * @return
     */
    ResultObject loadBranchAllBranchLeaf(Users users, String branchId);


    /**
     * 根据销售机构IDS查询机构树
     *
     * @param branchIds 机构IDS
     * @return
     */
    ResultObject<List<BranchTreeResponse>> loadBranchTreeByBranchIds(Users users, List<String> branchIds);


    /**
     * 查询父机构树集合
     *
     * @param branchId 　销售机构
     * @return
     */
    public ResultObject loadParentBranchs(Users users, String branchId);


    /**
     * 查询父机构树集合
     *
     * @param branchIds 　销售机构
     * @return
     */
    public ResultObject loadParentBranchs(Users users, List<String> branchIds);


    /**
     * 根据叶子节点ID获取父类机构短名称
     *
     * @param language
     * @param branchId
     * @return
     */
    ResultObject<BranchNameResponse> loadAllBranchLeafName(String language, String branchId);

    /**
     * 根据用户Id 获取机构信息
     *
     * @param userId
     * @return
     */
    ResultObject<BranchResponse> loadUserBranch(Users users, String userId);

    /**
     * 跟据机构编码获取机构信息
     *
     * @param currentLoginUsers
     * @param branchCodes
     * @return
     */
    ResultObject<List<BranchResponse>> loadBreachByCode(Users currentLoginUsers, String... branchCodes);

    /**
     * 导出机构信息
     *
     * @param response
     * @param users
     */
    void exportBranchs(HttpServletResponse response, Users users);

    /**
     * 跟据机构ID获取银行网点的机构编号
     * @param users
     * @param branchIds
     * @return
     */
    ResultObject<List<BranchBankResponse>> getBranchBankCode(Users users, List<String> branchIds);

    /**
     * 根据机构ID获取同层级的机构集合
     * @param users
     * @param branchId
     * @return
     */
    ResultObject<List<BranchResponse>> getBranchSameLevel(Users users, String branchId);

    /**
     * 模糊查询贷款金融机构信息
     * @param users
     * @param keyword
     * @return
     */
    ResultObject<List<FinancialBranchPo>> queryFinancialBranchFuzzy(Users users, String keyword);
}
