package com.gclife.platform.service.business.base.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.base.model.bo.CareerDo;
import com.gclife.platform.base.model.bo.CareerTreeDo;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.CareerBaseService;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BaseCareerPo;
import com.gclife.platform.model.response.CareerBaseTreeResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.CareerTreeResponse;
import com.gclife.platform.service.business.CareerBusinessService;
import com.gclife.platform.service.business.base.CareerBaseBusinessService;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码接口实现
 */
@Service
public class CareerBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements CareerBaseBusinessService {
    @Autowired
    private InternationalBaseService internationalBaseService;
    @Autowired
    private CareerBaseService careerBaseService;

    /**
     * 查询职业孩子集合
     *
     * @param careerId   　父职业ID
     * @param providerId 供应商ID
     * @return List<CareerDo>  ResultObject<List<CareerResponse>>
     */
    @Override
    public ResultObject<List<CareerResponse>> queryCareerChilds(String careerId, String providerId) {
        ResultObject<List<CareerResponse>> resultObject = new ResultObject<>();
        try {
            List<CareerDo> careerDos = careerBaseService.queryCareerChilds(AssertUtils.isNotEmpty(careerId) ? careerId : "0000", providerId);
            //转换
            if (AssertUtils.isNotEmpty(careerDos)) {
                List<CareerResponse> careerResponses = (List<CareerResponse>) this.converterList(careerDos, new TypeToken<List<CareerResponse>>() {
                }.getType());
                resultObject.setData(careerResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    /**
     * 查询职业信息
     *
     * @param careerId 职业ID
     * @return CareerBo ResultObject<CareerResponse>
     */
    @Override
    public ResultObject<CareerResponse> queryOneCareerPo(String careerId) {
        ResultObject<CareerResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            BaseCareerPo careerPo = careerBaseService.queryOneCareerPo(careerId);
            AssertUtils.isNotNull(this.getLogger(), careerPo, PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            CareerResponse careerResponse = (CareerResponse) this.converterObject(careerPo, CareerResponse.class);
            resultObject.setData(careerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据职业ids或者职业codes获取职业详情
     *
     * @param careerIds 职业ID
     * @return ResultObject<List<CareerResponse>>
     */
    @Override
    public ResultObject<List<CareerResponse>> queryCareerPoByIdsOrCodes(List<String> careerIds) {
        ResultObject<List<CareerResponse>> resultObject = new ResultObject<>();
        try {
            List<BaseCareerPo> careerDos = careerBaseService.queryCareerPoByIdsOrCodes(careerIds);
            //转换
            if (AssertUtils.isNotEmpty(careerDos)) {
                List<CareerResponse> careerResponses = (List<CareerResponse>) this.converterList(careerDos, new TypeToken<List<CareerResponse>>() {
                }.getType());
                resultObject.setData(careerResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    /**
     * 职业树集合
     *
     * @param careerId 　职业ID或职业编码
     * @return ResultObject<List<CareerResponse>>
     */
    @Override
    public ResultObject<List<CareerResponse>> queryCareerParentTreeList(String careerId) {
        ResultObject<List<CareerResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            List<CareerDo> careerDos = careerBaseService.queryCareerParentTreeList(careerId);
            //转换
            if (AssertUtils.isNotEmpty(careerDos)) {
                List<CareerResponse> careerResponses = (List<CareerResponse>) this.converterList(careerDos, new TypeToken<List<CareerResponse>>() {
                }.getType());
                resultObject.setData(careerResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(String providerId) {
        ResultObject<List<CareerBaseTreeResponse>> resultObject = new ResultObject<>();
        try {
            List<CareerTreeDo> careerTreeDos = careerBaseService.queryCareerAllTree(providerId);
            if (AssertUtils.isNotEmpty(careerTreeDos)) {
                List<CareerBaseTreeResponse> careerResponses = (List<CareerBaseTreeResponse>) this.converterList(careerTreeDos, new TypeToken<List<CareerBaseTreeResponse>>() {
                }.getType());
                resultObject.setData(careerResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return resultObject;
    }
}