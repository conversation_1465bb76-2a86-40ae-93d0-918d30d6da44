package com.gclife.platform.service.business.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.model.request.TerminologyRequest;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.InternationalBusinessService;
import com.gclife.platform.validate.parameter.InternationalParameterValidate;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class InternationalBusinessServiceImpl extends BaseBusinessServiceImpl implements InternationalBusinessService {

    @Autowired
    BaseSysCodeExtDao baseSysCodeDetailDao;

    @Autowired
    InternationalParameterValidate internationalParameterValidate;


    /**
     * 查询术语国际化值
     *
     * @param type
     * @param users
     * @return
     */
    @Override
    public ResultObject loadTerminology(com.gclife.common.model.base.Users users, String type, String key,String language) {
        ResultObject<SyscodeResponse> resultObject = new ResultObject<SyscodeResponse>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(language)){
                lang=language;
            }
            // 数据验证
            internationalParameterValidate.validParameterLoadTerminologyLanguage(type, key);
            //查询数据
            SyscodeBo syscodeBo = baseSysCodeDetailDao.loadBaseSyscode(type, key, lang);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), syscodeBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_SYSCODE_IS_NOT_FOUND_OBJECT);
            //数据转换
            SyscodeResponse syscodeResponse = (SyscodeResponse) this.converterObject(syscodeBo, SyscodeResponse.class);

            resultObject.setData(syscodeResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 查询术语国际化集合
     *
     * @param type
     * @param users
     * @return
     */
    @Override
    public ResultObject loadTerminologys(Users users, String type,String language) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<List<SyscodeResponse>>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(language)){
                lang=language;
            }
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), type, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);

            List<SyscodeBo> syscodeBoList = baseSysCodeDetailDao.loadBaseSyscodes(type, lang);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), syscodeBoList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_SYSCODE_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<SyscodeResponse> syscodeResponse = (List<SyscodeResponse>) this.converterList(syscodeBoList, new TypeToken<List<SyscodeResponse>>() {
            }.getType());

            resultObject.setData(syscodeResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject loadTerminologys(Users users, TerminologyRequest terminologyRequest) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<List<SyscodeResponse>>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(terminologyRequest.getLanguage())){
                lang=terminologyRequest.getLanguage();
            }
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), terminologyRequest.getType(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), terminologyRequest.getListSyscodes(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            List<SyscodeBo> syscodeBoList = baseSysCodeDetailDao.loadBaseSyscodes(terminologyRequest.getType(), terminologyRequest.getListSyscodes(), lang);
            ;
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), syscodeBoList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_SYSCODE_IS_NOT_FOUND_OBJECT);

            //数据转换
            List<SyscodeResponse> syscodeResponse = (List<SyscodeResponse>) this.converterList(syscodeBoList, new TypeToken<List<SyscodeResponse>>() {
            }.getType());

            resultObject.setData(syscodeResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject<List<SyscodeResponse>> internationalTextGet(Users users, String type,String language) {

        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<List<SyscodeResponse>>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(language)){
                lang=language;
            }
            List<SyscodeBo> syscodeBoList = baseSysCodeDetailDao.internationalTextGet(type, lang);
            //数据转换
            List<SyscodeResponse> syscodeResponseList = (List<SyscodeResponse>) this.converterList(syscodeBoList, new TypeToken<List<SyscodeResponse>>() {
            }.getType());

            resultObject.setData(syscodeResponseList);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }


    @Override
    public ResultObject<SyscodeResponse> internationalTextGetOne(Users users, String key, String type,String language) {

        ResultObject<SyscodeResponse> resultObject = new ResultObject<SyscodeResponse>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(language)){
                lang=language;
            }
            SyscodeBo syscodeBo = baseSysCodeDetailDao.internationalTextGetOne(key, type, lang);
            //数据封装
            SyscodeResponse syscodeResponse = (SyscodeResponse) this.converterObject(syscodeBo, SyscodeResponse.class);

            resultObject.setData(syscodeResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeResponse>> loadInternationalListByCodes(Users users, TerminologyRequest terminologyRequest) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<List<SyscodeResponse>>();
        try {
            String lang = PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.name();
            if(AssertUtils.isNotEmpty(users.getLanguage())){
                lang=users.getLanguage();
            }
            if(AssertUtils.isNotEmpty(terminologyRequest.getLanguage())){
                lang=terminologyRequest.getLanguage();
            }
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), terminologyRequest.getType(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), terminologyRequest.getListSyscodes(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            List<SyscodeBo> syscodeBoList = baseSysCodeDetailDao.loadInternationalListByCodes(terminologyRequest.getListSyscodes(), terminologyRequest.getType(),lang);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), syscodeBoList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_SYSCODE_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<SyscodeResponse> syscodeResponse = (List<SyscodeResponse>) this.converterList(syscodeBoList, new TypeToken<List<SyscodeResponse>>() {
            }.getType());

            resultObject.setData(syscodeResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INTERNATIONAL_LANGUAGE_ERROR);
            }
        }
        return resultObject;
    }
}