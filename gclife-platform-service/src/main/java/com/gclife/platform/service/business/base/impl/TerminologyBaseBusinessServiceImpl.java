package com.gclife.platform.service.business.base.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.feign.SyscodeReqFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.bo.StatusClassDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.base.service.TerminologyBaseService;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.vo.StatusClassResponse;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class TerminologyBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements TerminologyBaseBusinessService {

    @Autowired
    private TerminologyBaseService terminologyBaseService;

    @Autowired
    private InternationalBaseService internationalBaseService;

    /**
     * 根据类型查询术语集合
     * @param codeType　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    @Override
    public ResultObject<List<SyscodeResponse>> querySyscode(String codeType) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            List<InternationalDo> internationalDos = terminologyBaseService.querySyscode(codeType);
            if(AssertUtils.isNotEmpty(internationalDos)){
                List<SyscodeResponse> branchResponse = (List<SyscodeResponse>)this.converterList(internationalDos,new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                //国际化
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据code查询术语对象
     * @param codeKey 术语键
     * @param codeType　类型
     * @return  ResultObject<SyscodeResponse>
     */
    @Override
    public ResultObject<SyscodeResponse> queryOneSyscode(String codeType, String codeKey) {
        ResultObject<SyscodeResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),codeKey, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            InternationalDo internationalDo = terminologyBaseService.queryOneSyscode(codeType,codeKey);
            if(AssertUtils.isNotNull(internationalDo)){
                SyscodeResponse syscodeResponse = (SyscodeResponse)this.converterObject(internationalDo,SyscodeResponse.class);
                resultObject.setData(syscodeResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeResponse>> queryInternationSyscode(String codeType,String lang) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            List<InternationalDo> internationalDos = terminologyBaseService.querySyscode(codeType);
            if(AssertUtils.isNotEmpty(internationalDos)){
                List<SyscodeResponse> syscodeResponses = (List<SyscodeResponse>)this.converterList(internationalDos,new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                //国际化
                List<InternationalDo> internationalDoList = internationalBaseService.queryInternational(codeType,lang);
                if(AssertUtils.isNotEmpty(internationalDoList)){
                    syscodeResponses.forEach(syscodeResponse -> {
                        internationalDoList.stream().filter(internationalDo -> internationalDo.getCodeKey().equals(syscodeResponse.getCodeKey())).findFirst().ifPresent(internationalDo -> {
                            syscodeResponse.setCodeName(internationalDo.getCodeName());
                        });
                    });
                }
                resultObject.setData(syscodeResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<SyscodeResponse> queryOneInternationSyscode(String codeType, String codeKey,String lang) {
        ResultObject<SyscodeResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),codeKey, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            InternationalDo syscode = terminologyBaseService.queryOneSyscode(codeType,codeKey);
            if(AssertUtils.isNotNull(syscode)){
                SyscodeResponse syscodeResponse = (SyscodeResponse)this.converterObject(syscode,SyscodeResponse.class);
                //国际化
                InternationalDo internationalDo = internationalBaseService.queryOneInternational(codeType,codeKey,lang);
                if(AssertUtils.isNotNull(internationalDo)){
                    syscodeResponse.setCodeName(internationalDo.getCodeName());
                }
                resultObject.setData(syscodeResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据类型,codes值查询术语集合
     * @param codeKeys　术语key集合
     * @param codeType　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    @Override
    public ResultObject<List<SyscodeResponse>> querySyscodeByCodeKeys(String codeType, List<String> codeKeys) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),codeKeys, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            List<InternationalDo> internationalDos = terminologyBaseService.querySyscodeByCodeKeys(codeType,codeKeys);
            if(AssertUtils.isNotEmpty(internationalDos)){
                List<SyscodeResponse> syscodeResponses = (List<SyscodeResponse>)this.converterList(internationalDos,new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                resultObject.setData(syscodeResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeResponse>> queryInternationSyscodeByCodeKeys(SyscodeForm syscodeReqFc) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),syscodeReqFc.getCodeType(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),syscodeReqFc.getCodeKeys(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            List<InternationalDo> internationalDos = terminologyBaseService.querySyscodeByCodeKeys(syscodeReqFc.getCodeType(),syscodeReqFc.getCodeKeys());
            if(AssertUtils.isNotEmpty(internationalDos)){
                List<SyscodeResponse> syscodeResponses = (List<SyscodeResponse>)this.converterList(internationalDos,new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                //国际化
                List<InternationalDo> internationalDoList = internationalBaseService.queryInternational(syscodeReqFc.getCodeType(),syscodeReqFc.getLang());
                if(AssertUtils.isNotEmpty(internationalDoList)){
                    syscodeResponses.forEach(syscodeResponse -> {
                        internationalDoList.stream().filter(internationalDo -> internationalDo.getCodeKey().equals(syscodeResponse.getCodeKey())).findFirst().ifPresent(internationalDo -> {
                            syscodeResponse.setCodeName(internationalDo.getCodeName());
                        });
                    });
                }
                resultObject.setData(syscodeResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<StatusClassResponse>> queryStatusClassTerminologys(Users users, String type) {
        ResultObject<List<StatusClassResponse>> resultObject = new ResultObject<>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), type, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);

            List<StatusClassDo> syscodeBoList = terminologyBaseService.queryStatusClassByCodeType(type);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), syscodeBoList, PlatformErrorConfigEnum.PLATFORM_BUSINESS_SYSCODE_IS_NOT_FOUND_OBJECT);
            //数据转换
            List<StatusClassResponse> statusClassResponse = (List<StatusClassResponse>) this.converterList(syscodeBoList, new TypeToken<List<StatusClassResponse>>() {
            }.getType());

            resultObject.setData(statusClassResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeResponse>> querySyscodeTerminologys(Users users, String type, String statusClassCode) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),type, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),statusClassCode, PlatformErrorConfigEnum.PLATFORM_PARAMETER_TERMINOLOGY_STATUS_CLASS_CODE_IS_NOT_NULL);
            List<InternationalDo> internationalDos = terminologyBaseService.querySyscodeByCodeTypeAndStatusClassCode(type,statusClassCode);
            if(AssertUtils.isNotEmpty(internationalDos)){
                List<SyscodeResponse> syscodeResponses = (List<SyscodeResponse>)this.converterList(internationalDos,new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                resultObject.setData(syscodeResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

}