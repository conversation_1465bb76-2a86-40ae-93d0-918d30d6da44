package com.gclife.platform.service.business;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.platform.model.request.CustomerMessagesRequest;
import com.gclife.platform.model.request.UserCustomerBusinessRequest;
import com.gclife.platform.model.request.UserCustomerRequest;
import com.gclife.platform.model.response.CustomerMessageResponse;
import com.gclife.platform.model.response.CustomerMessagesResponse;
import com.gclife.platform.model.response.UserCustomerResponse;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public interface CustomerManageService {

    /**
     * 根据用户  获取他的客户列表
     *
     * @param users
     * @return
     */
    ResultObject<BasePageResponse<CustomerMessagesResponse>> getCustomerMessages(Users users, CustomerMessagesRequest customerMessagesRequest);


    /**
     * 添加 客户信息
     *
     * @param customerRequest
     * @return
     */
    ResultObject saveCustomerMessage(UserCustomerRequest customerRequest, Users users);


    /**
     * 获取 我的客户信息  根据客户ID
     *
     * @return
     */
    ResultObject<CustomerMessageResponse> getCustomerMessage(String customerId, Users users);



    /**
     * 添加 用户客户所有数据
     *
     * @param userCustomerBusiness
     * @param users
     * @return
     */
    ResultObject<UserCustomerResponse> saveCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness, Users users);


}