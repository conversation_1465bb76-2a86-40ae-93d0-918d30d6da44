package com.gclife.platform.service.data.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.*;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.core.jooq.Tables;
import com.gclife.platform.core.jooq.tables.daos.*;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.model.bo.UserLoginLogBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.service.data.UsersService;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午10:45
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Service
public class UsersServiceImpl extends BaseServiceImpl implements UsersService {


    @Autowired
    private UsersDao usersDao;

    @Autowired
    private RolesDao rolesDao;

    @Autowired
    private Users2rolesDao users2rolesDao;

    @Autowired
    private PermissionsDao permissionsDao;

    @Autowired
    private Permissions2rolesDao permissions2rolesDao;

    @Autowired
    private ResourcesDao resourcesDao;

    @Autowired
    private Resources2permissionsDao resources2permissionsDao;

    @Autowired
    private UserLoginLogDao userLoginLogDao;
    @Autowired
    private UserAppDeviceDao userAppDeviceDao;

    @Autowired
    private UserWeixinRelationDao userWeixinRelationDao;

    @Autowired
    private UserWeixinDao userWeixinDao;

    @Autowired
    private UserBranchDao userBranchDao;

    /**
     * 保存用户信息
     *
     * @param usersPo
     * @throws Exception
     */
    @Override
    public void saveUsersPo(UsersPo usersPo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(usersPo.getUserId())) {
                //执行新增
                usersPo.setUserId(UUIDUtils.getUUIDShort());
                usersPo.setCreatedDate(System.currentTimeMillis());
                usersDao.insert(usersPo);
            } else {
                //执行修改
                usersPo.setUpdatedDate(System.currentTimeMillis());
                usersDao.update(usersPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_USER_ERROR.getValue()+ ExceptionUtils.getFullStackTrace(e));
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_USER_ERROR);
        }
    }


    /**
     * 保存角色信息
     *
     * @param rolesPo
     * @throws Exception
     */
    public void saveRolesPo(RolesPo rolesPo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(rolesPo.getRoleId())) {
                //执行新增
                rolesPo.setRoleId(UUIDUtils.getUUIDShort());
                rolesDao.insert(rolesPo);
            } else {
                //执行修改
                rolesDao.update(rolesPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_ROLE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_ROLE_ERROR);
        }
    }



    /**
     * 保存权限用户关联信息
     *
     * @param permissionsPo
     * @throws Exception
     */
    public void savePermissionsPo(PermissionsPo permissionsPo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(permissionsPo.getPermissionId())) {
                //执行新增
                permissionsPo.setPermissionId(UUIDUtils.getUUIDShort());
                permissionsDao.insert(permissionsPo);
            } else {
                //执行修改
                permissionsDao.update(permissionsPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_PERMISSIONS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_PERMISSIONS_ERROR);
        }
    }


    /**
     * 保存权限角色关联信息
     *
     * @param permissions2rolesPo
     * @throws Exception
     */
    public void savePermissions2rolesPo(Permissions2rolesPo permissions2rolesPo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(permissions2rolesPo.getRolePermissionsId())) {
                //执行新增
                permissions2rolesPo.setRolePermissionsId(UUIDUtils.getUUIDShort());
                permissions2rolesDao.insert(permissions2rolesPo);
            } else {
                //执行修改
                permissions2rolesDao.update(permissions2rolesPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_PERMISSIONS2ROLES_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_PERMISSIONS2ROLES_ERROR);
        }
    }


    /**
     * 保存资源信息
     *
     * @param resourcesPo
     * @throws Exception
     */
    public void saveResourcesPo(ResourcesPo resourcesPo) throws RequestException {
        try {
            if (StringUtil.isNullString(resourcesPo.getResourceId())) {
                //执行新增
                resourcesPo.setResourceId(UUIDUtils.getUUIDShort());
                resourcesDao.insert(resourcesPo);
            } else {
                //执行修改
                resourcesDao.update(resourcesPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_RESOURCES_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_RESOURCES_ERROR);
        }
    }


    /**
     * 保存资源权限关联信息
     *
     * @param resources2permissionsPo
     * @throws Exception
     */

    public void saveResourcesPo(Resources2permissionsPo resources2permissionsPo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(resources2permissionsPo.getPermissionsResourcesId())) {
                //执行新增
                resources2permissionsPo.setResourceId(UUIDUtils.getUUIDShort());
                resources2permissionsDao.insert(resources2permissionsPo);
            } else {
                //执行修改
                resources2permissionsDao.update(resources2permissionsPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_RESOURCES2PERMISSIONS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_RESOURCES2PERMISSIONS_ERROR);
        }
    }


    @Override
    public void saveUserLoginLogBo(UserLoginLogBo userLoginLogBo) {
        try {
            if (!AssertUtils.isNotEmpty(userLoginLogBo.getUserDeviceId())) {
                //新增
                userLoginLogBo.setUserDeviceId(UUIDUtils.getUUIDShort());
                userLoginLogBo.setCreatedDate(System.currentTimeMillis());
                userLoginLogDao.insert(userLoginLogBo);
            } else {
                //修改
                userLoginLogBo.setUpdatedDate(System.currentTimeMillis());
                userLoginLogDao.update(userLoginLogBo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_USER_LOGIN_LOG_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_USER_LOGIN_LOG_ERROR);
        }
    }

    @Override
    public void saveUserBranchPo(UserBranchPo userBranchPo) {
        try {
            if (!AssertUtils.isNotEmpty(userBranchPo.getUserBranchId())) {
                //新增
                userBranchPo.setUserBranchId(UUIDUtils.getUUIDShort());
                userBranchPo.setCreatedDate(System.currentTimeMillis());
                userBranchDao.insert(userBranchPo);
            } else {
                //修改
                userBranchPo.setUpdatedDate(System.currentTimeMillis());
                userBranchDao.update(userBranchPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_USER_BRANCH_ERROR);
        }
    }

    @Override
    public void saveUserWeixinPo(UserWeixinPo userWeixinPo) {
        try {
            if (!AssertUtils.isNotEmpty(userWeixinPo.getUserWeixinId())) {
                //新增
                userWeixinPo.setUserWeixinId(UUIDUtils.getUUIDShort());
                userWeixinPo.setCreatedDate(DateUtils.getCurrentTime());
                userWeixinDao.insert(userWeixinPo);
            } else {
                //修改
                userWeixinPo.setUpdatedDate(DateUtils.getCurrentTime());
                userWeixinDao.update(userWeixinPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_USER_WEIXIN_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_USER_WEIXIN_ERROR);
        }
    }

    @Override
    public void saveUserWeixinRelationPo(UserWeixinRelationPo userWeixinRelationPo) {
        try {
            if (!AssertUtils.isNotEmpty(userWeixinRelationPo.getUserWeixinRelationId())) {
                //新增
                userWeixinRelationPo.setUserWeixinRelationId(UUIDUtils.getUUIDShort());
                userWeixinRelationPo.setCreatedDate(DateUtils.getCurrentTime());
                userWeixinRelationDao.insert(userWeixinRelationPo);
            } else {
                //修改
                userWeixinRelationPo.setUpdatedDate(DateUtils.getCurrentTime());
                userWeixinRelationDao.update(userWeixinRelationPo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_SAVE_USER_WEIXIN_ERROR.getValue()+ExceptionUtils.getFullStackTrace(e));
            throw new RequestException(PlatformErrorConfigEnum.USERS_SAVE_USER_WEIXIN_ERROR);
        }
    }

    /**
     * 保存用户设备token信息(批量)
     * @param userAppDevicePos 用户设备token信息集
     */
    @Override
    public void saveUserAppDevice(List<UserAppDevicePo> userAppDevicePos) {
        try {
            userAppDevicePos.forEach(userAppDevicePo -> {
                if (!AssertUtils.isNotEmpty(userAppDevicePo.getUserAppDeviceId())) {
                    //新增
                    userAppDevicePo.setUserAppDeviceId(UUIDUtils.getUUIDShort());
                    userAppDeviceDao.insert(userAppDevicePo);
                } else {
                    //修改
                    userAppDeviceDao.update(userAppDevicePo);
                }
            });
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_USER_APP_DEVICE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_USER_APP_DEVICE_ERROR);
        }
    }

}