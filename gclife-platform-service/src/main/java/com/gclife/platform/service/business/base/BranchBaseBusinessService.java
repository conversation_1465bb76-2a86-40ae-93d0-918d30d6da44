package com.gclife.platform.service.business.base;


import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.BranchPagingRequest;
import com.gclife.platform.form.branch.UserBranchRequest;
import com.gclife.platform.model.response.*;
import com.gclife.platform.vo.branch.BranchParentResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.branch.BranchSimpleResponse;
import com.gclife.platform.vo.branch.BranchTreeResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BranchBaseBusinessService extends BaseBusinessService {

    /**
     * 根据机构ID信息
     *
     * @param branchId 机构ID
     * @return ResultObject<BranchResponse>
     */
    ResultObject<BranchResponse> queryOneBranchById(String branchId,String lang);

    /**
     * 根据机构ID信息
     *
     * @param branchId 机构ID
     * @return ResultObject<BranchResponse>
     */
    ResultObject<BranchResponse> queryOneBranchAndManagerById(String branchId,String lang);

    /**
     * 根据机构ID信息
     *
     * @param branchId 机构ID
     * @return ResultObject<BranchResponse>
     */
    ResultObject<BranchResponse> queryOneTopBranchById(String branchId);

    /**
     * 根据机构ID信息
     *
     * @param branchIds 机构IDs
     * @return ResultObject<BranchResponse>
     */
    ResultObject<List<BranchResponse>> queryBranchByIds(List<String> branchIds);

    /********************************************************************************
     *
     *                                  查询机构列表
     *
     ***************************************************************************** */

    /**
     * 根据机构ID查询机构树上的　机构集合
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryBranchTreeBranchListById(String branchId);


    /**
     * 根据机构ID查询机构树上的叶子机构　机构集合
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryBranchTreeLeafListById(String branchId);


    /**
     * 根据机构ID查询父机构集合 (按照从下往上排序)
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryBranchParentListById(String branchId);


    //根据ids查询

    /**
     * 根据机构ID查询机构树上的　机构集合
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryBranchTreeBranchListByIds(List<String> branchIds);


    /**
     * 根据机构ID查询机构树上的叶子机构　机构集合
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryBranchTreeLeafListByIds(List<String> branchIds);


    /**
     * 根据机构ID查询父机构集合 (按照从下往上排序)
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchParentResponse>> queryBranchParentListByIds(List<String> branchIds);


    /********************************************************************************
     *
     *                                  查询机构树
     *
     ***************************************************************************** */

    /**
     * 根据机构ID查询机构树
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> queryBranchTreeById(String branchId);

    /**
     * 根据机构ID查询机构树
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> querySalesBranchTreeById(String branchId);

    /**
     * 根据机构ID查询机构树
     *
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> querySalesAllBranchTree();

    //根据ids查询

    /**
     * 根据机构ID查询机构树
     *
     * @param branchIds 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> queryBranchTreeByIds(List<String> branchIds);


    /********************************************************************************
     *
     *                                  查询用户的机构信息
     *
     ***************************************************************************** */
    /**
     * 查询用户机构信息
     *
     * @param userId 机构ID
     * @return ResultObject<BranchResponse>
     */
    ResultObject<BranchResponse> queryOneUserBranch(String userId);

    /**
     * 查询用户所属机构树
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> queryUserBranchTree(String userId);

    /**
     * 查询用户所属机构树
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchResponse>> queryUserBranchTreeList(String userId);


    /**
     * 查询用户管理的机构树列表
     * @param userId　用户ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryUserOptionBranchTreeList(String userId);

    /**
     * 查询用户管理的机构树列表
     * @param channelTypeCode　渠道编码
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryUserOptionBranchTreeListByCode(String channelTypeCode);

    /**
     * 查询用户管理的机构叶子列表
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> queryUserOptionBranchTreeLeaf(String userId);


    /**
     * 查询用户管理的机构第一层级的机构集合
     *
     * @return ResultObject<List<BranchResponse>>
     */
    ResultObject<List<BranchResponse>> loadUserOptionFirstLevelBranch(String userId);

    /**
     * 查询用户管理的机构树
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTree(String userId);


    /**
     * 查询用户管理的机构树
     *
     * @param userId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchTreeResponse>> queryUserOptionBranchTreeFiterNoLeafBranch(String userId);


    /**
     * 保存用户管理机构
     *
     * @param userBranchRequest 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<BaseResponse> saveUserOptionBranch(UserBranchRequest userBranchRequest);


    /********************************************************************************
     *
     *                                  根据机构层级查询机构信息
     *
     ***************************************************************************** */

    /**
     * 根据机构层级查询机构列表
     *
     * @param levelNum 层级
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<List<BranchSimpleResponse>> queryLevelBranch(String userId, Long levelNum);

    /**
     * 批量保存用户操作机构
     *
     * @param userBranchRequests 机构请求参数
     * @return ResultObject
     */
    ResultObject saveUserBranchByAgentIds(List<UserBranchRequest> userBranchRequests);


    /**
     * 查询机构所属层级
     *
     * @param branchId 机构ID
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<Long> queryBranchBelongLevel(String branchId);


    /**
     * 查询默认渠道机构
     *
     * @return ResultObject<List<BranchTreeResponse>>
     */
    ResultObject<BranchResponse> queryBranchDefaultChannel(String branchId);






    /********************************************************************************
     *
     *                                  机构模糊查询
     *
     ***************************************************************************** */

    /**
     *  机构模糊查询
     * @param branchPagingRequest 分页请求对象
     * @return  ResultObject
     */
    ResultObject<List<BranchFuzzyResponse>> queryBranchFuzzy(BranchPagingRequest branchPagingRequest,String branchType);


}
