package com.gclife.platform.service.data.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.core.jooq.Tables;
import com.gclife.platform.core.jooq.tables.daos.AccountAuditDao;
import com.gclife.platform.core.jooq.tables.daos.AccountDao;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.AccountQueryRequest;
import com.gclife.platform.service.data.AccountBoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:
 */
@Service
public class AccountBoServiceImpl extends BaseServiceImpl implements AccountBoService {


    @Autowired
    private AccountDao accountDao;

    @Autowired
    private AccountAuditDao accountAuditDao;

    /**
     * 保存用户信息
     *
     * @param accountBo
     * @throws Exception
     */
    @Override
    public void saveAccountPo(AccountBo accountBo) throws RequestException {
        try {
            if (!AssertUtils.isNotEmpty(accountBo.getAccountId())) {
                //执行新增
                accountBo.setAccountId(UUIDUtils.getUUIDShort());
                accountBo.setCreatedDate(System.currentTimeMillis());
                accountDao.insert(accountBo);
            } else {
                //执行修改
                accountBo.setUpdatedDate(System.currentTimeMillis());
                accountDao.update(accountBo);
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_ACCOUNT_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_ACCOUNT_ERROR);
        }
    }

    @Override
    public void saveAccountAuditPo(AccountAuditPo accountAuditPo) {
        if (!AssertUtils.isNotEmpty(accountAuditPo.getAccountAuditId())) {
            //执行新增
            accountAuditPo.setAccountAuditId(UUIDUtils.getUUIDShort());
            accountAuditPo.setCreatedDate(System.currentTimeMillis());
            accountAuditDao.insert(accountAuditPo);
        } else {
            //执行修改
            accountAuditPo.setUpdatedDate(System.currentTimeMillis());
            accountAuditDao.update(accountAuditPo);
        }
    }

}