package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.api.AgentSignAuditApi;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.platform.base.dao.AccountBaseDao;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.core.jooq.tables.daos.AccountDao;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.core.jooq.tables.pojos.AccountPo;
import com.gclife.platform.dao.AccountExtDao;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.AccountAuditRequest;
import com.gclife.platform.model.request.AccountQueryRequest;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AccountIdResponse;
import com.gclife.platform.model.response.AccountQueryResponse;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.service.business.*;
import com.gclife.platform.service.data.AccountBoService;
import com.gclife.platform.validate.parameter.AccountParameterValidate;
import com.gclife.platform.validate.parameter.transform.AccountTransData;
import com.gclife.platform.vo.branch.BranchResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息接口实现
 */
@Service
@Lazy
public class AccountBusinessServiceImpl extends BaseBusinessServiceImpl implements AccountBusinessService {

    @Autowired
    private AccountExtDao accountExtDao;

    @Autowired
    private AccountParameterValidate accountParameterValidate;

    @Autowired
    private AccountBoService accountBoService;

    @Autowired
    private AccountTransData accountTransData;

    @Autowired
    private AccountDao accountDao;
    @Autowired
    private AccountBaseDao accountBaseDao;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private BankBusinessService bankBusinessService;
    @Autowired
    private InternationalBaseService internationalBaseService;
    @Autowired
    private BranchBusinessService branchBusinessService;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private UsersBusinessService usersBusinessService;
    @Autowired
    private AgentSignAuditApi agentSignAuditApi;

    /**
     * 根据账户ID获取账户信息
     *
     * @param accountId
     * @return
     */
    @Override
    public ResultObject<AccountResponse> getAccountById(String accountId) {
        ResultObject<AccountResponse> resultObject = new ResultObject<>();
        AccountResponse accountResponse;
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), accountId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_ID_ERROR);
            //查询账户信息
            AccountBo accountBo = accountExtDao.loadAccountById(accountId);
            //账户验证
            AssertUtils.isNotNull(this.getLogger(), accountBo, PlatformErrorConfigEnum.PLATFORM_ACCOUNT_IS_NOT_FOUND_OBJECT);
            //数据转换
            accountResponse = (AccountResponse) this.converterObject(accountBo, AccountResponse.class);
            //设置审核信息
            accountTransData.transferAccountAudit(accountBo, accountResponse);
            //设置返回值
            resultObject.setData(accountResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }


        return resultObject;
    }

    /**
     * 账户信息保存
     *
     * @param accountRequest 请求类
     * @return AccountIdResponse
     * @throws RequestException
     */
    @Override
    @Transactional
    public ResultObject<AccountIdResponse> saveAccount(AccountRequest accountRequest) {
        ResultObject<AccountIdResponse> resultObject = new ResultObject<>();
        AccountIdResponse accountIdResponse = new AccountIdResponse();
        try {
            //参数验证
            accountParameterValidate.validParameterAddAccount(accountRequest);
            this.getLogger().info("查询到的accountRequest是" + JackSonUtils.objectToJsonStr(accountRequest));

            //查询数据库是否有该条记录
            AccountBo accountBo = accountExtDao.loadAccountExist(accountRequest.getAccountNo(), accountRequest.getAccountId(),accountRequest.getUseType());
            this.getLogger().info("查询到的accountBo是" + JackSonUtils.objectToJsonStr(accountBo));
            if (null == accountBo) {
                accountBo = new AccountBo();
            }
            //转换数据
            accountBo = accountTransData.transAccountBo(accountBo, accountRequest);

            //数据保存
            accountBoService.saveAccountPo(accountBo);

            //判断是否审核
            if (AssertUtils.isNotEmpty(accountBo.getUserId())) {
                AccountAuditPo accountAuditPo = accountExtDao.queryAccountAuditPoByAccountId(accountBo.getAccountId());
                // 未审核或审核通过就需要审核
                if (!AssertUtils.isNotNull(accountAuditPo) || !PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_PASS.name().equals(accountAuditPo.getAuditStatus())) {
                    accountBoService.saveAccountAuditPo(accountTransData.transferAccountAudit(accountAuditPo, accountBo));
                    //发送银行卡审核消息
                    messageBusinessService.sendBankCardReviewBatchMessage(PlatformTermEnum.MSG_BUSINESS_TYPE.AGENT_BANK_CARD_REVIEW_REMINDER.name(), accountBo.getUserId());
                }
            }

            //返回账户ID
            accountIdResponse.setAccountId(accountBo.getAccountId());

            resultObject.setData(accountIdResponse);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SAVE_ACCOUNT_ERROR);
            }
        }
        return resultObject;

    }

    /**
     * 账户信息保存
     *
     * @param accountRequest 请求类
     * @return AccountIdResponse
     * @throws RequestException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<AccountIdResponse> saveAccountInfo(AccountRequest accountRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), accountRequest.getAccountNo(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_NO_IS_NOT_NULL);

        // 查询数据库是否有该条记录
        AccountBo accountBo = accountExtDao.loadAccountExist(accountRequest.getAccountNo(), accountRequest.getAccountId(),accountRequest.getUseType());
        if (null == accountBo) {
            accountBo = new AccountBo();
        }
        ClazzUtils.copyPropertiesIgnoreNull(accountRequest, accountBo);
        accountBo.setIdTypeCode(accountRequest.getIdType());
        accountBo.setAccountTypeCode(accountRequest.getAccountType());
        accountBoService.saveAccountPo(accountBo);
        // 账户审核数据
        AccountAuditPo accountAuditPo = accountExtDao.queryAccountAuditPoByAccountId(accountBo.getAccountId());
        if (!AssertUtils.isNotNull(accountAuditPo)) {
            accountAuditPo = new AccountAuditPo();
            accountAuditPo.setAccountId(accountBo.getAccountId());
            accountAuditPo.setAuditStatus(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_PASS.name());
            accountBoService.saveAccountAuditPo(accountAuditPo);
        }

        // 返回账户ID
        AccountIdResponse accountIdResponse = new AccountIdResponse();
        accountIdResponse.setAccountId(accountBo.getAccountId());
        ResultObject<AccountIdResponse> resultObject = ResultObject.success();
        resultObject.setData(accountIdResponse);
        return resultObject;

    }

    /**
     * 账户信息批量保存
     * @param accountRequests
     * @return
     */
    @Override
    public ResultObject saveAccounts(List<AccountRequest> accountRequests) {
        // 参数验证
        accountRequests.forEach(accountRequest -> {
            AssertUtils.isNotEmpty(this.getLogger(), accountRequest.getAccountNo(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_NO_IS_NOT_NULL);
        });
        // 判断是否有主卡
        boolean anyMatch = accountRequests.stream()
                .anyMatch(accountRequest -> PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name().equals(accountRequest.getPrimaryFlag()));
        if (!anyMatch) {
            accountRequests.get(0).setPrimaryFlag(PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name());
        }
        //新增数据
        List<AccountPo> addAccountPos = new ArrayList<>();
        //修改
        List<AccountRequest> updateAccountRequests = new ArrayList<>();
        accountRequests.forEach(accountRequest -> {
            if (!AssertUtils.isNotEmpty(accountRequest.getAccountId())){
                AccountPo accountPo = (AccountPo) this.converterObject(accountRequest, AccountPo.class);
                if (!AssertUtils.isNotEmpty(accountPo.getPrimaryFlag())) {
                    accountPo.setPrimaryFlag(PlatformTermEnum.ACCOUNT_MAIN_FLAG.ADDITIONAL.name());
                }
                addAccountPos.add(accountPo);
            }else {
                updateAccountRequests.add(accountRequest);
            }
        });
        // 查询所有账户信息
        String[] userIds = accountRequests.stream().map(AccountRequest::getUserId).distinct().toArray(String[]::new);
        List<AccountPo> accountPos = accountDao.fetchByUserId(userIds);
        // 待修改
        List<AccountPo> updateAccountPos = new ArrayList<>();
        // 待删除数据
        List<AccountPo> deleteAccountPos = new ArrayList<>();
        // 删除不同数据
        accountPos.forEach(accountPo -> {
            Optional<AccountRequest> accountRequestOptional = updateAccountRequests.stream()
                    .filter(accountRequest -> accountPo.getAccountId().equals(accountRequest.getAccountId()))
                    .findFirst();
            if (accountRequestOptional.isPresent()) {
                // 待修改
                ClazzUtils.copyPropertiesIgnoreNull(accountRequestOptional.get(), accountPo);
                accountPo.setUpdatedDate(DateUtils.getCurrentTime());
                updateAccountPos.add(accountPo);
            } else {
                // 待删除
                deleteAccountPos.add(accountPo);
            }
        });
        // 删除原有账户信息
        if (AssertUtils.isNotEmpty(deleteAccountPos)) {
            accountDao.delete(deleteAccountPos);
        }
        if (AssertUtils.isNotEmpty(updateAccountPos)) {
            accountDao.update(updateAccountPos);
        }
        if (AssertUtils.isNotEmpty(addAccountPos)){
            // 新增银行卡
            accountBaseDao.addAccounts(addAccountPos);
            addAccountPos.forEach(accountPo -> {
                AccountAuditPo accountAuditPo = new AccountAuditPo();
                accountAuditPo.setAuditDate(DateUtils.getCurrentTime());
                accountAuditPo.setAuditStatus("AUDIT_PASS");
                accountAuditPo.setAccountId(accountPo.getAccountId());
                accountBoService.saveAccountAuditPo(accountAuditPo);
            });
        }
        return ResultObject.success();
    }

    /**
     * 批量保存账户信息
     *
     * @param accounts 账户信息集
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveAccount(List<AccountRequest> accounts) {
        ResultObject resultObject = new ResultObject();
        try {
            //参数验证
            accounts.forEach(accountRequest -> {
                accountParameterValidate.validParameterAddAccount(accountRequest);
            });
            //新增数据
            List<AccountPo> addAccountPos = new ArrayList<>();
            //修改
            List<AccountRequest> updateAccountRequests = new ArrayList<>();
            accounts.forEach(accountRequest -> {
                if (!AssertUtils.isNotEmpty(accountRequest.getAccountId())){
                    AccountPo accountPo = (AccountPo) this.converterObject(accountRequest, AccountPo.class);
                    if (!AssertUtils.isNotEmpty(accountPo.getPrimaryFlag())) {
                        accountPo.setPrimaryFlag(PlatformTermEnum.ACCOUNT_MAIN_FLAG.ADDITIONAL.name());
                    }
                    addAccountPos.add(accountPo);
                }else {
                    updateAccountRequests.add(accountRequest);
                }
            });
            // 查询所有账户信息
            String[] userIds = accounts.stream().map(AccountRequest::getUserId).distinct().toArray(String[]::new);
            List<AccountPo> accountPos = accountDao.fetchByUserId(userIds);
            // 待修改
            List<AccountPo> updateAccountPos = new ArrayList<>();
            // 待删除数据
            List<AccountPo> deleteAccountPos = new ArrayList<>();
            // 删除不同数据
            accountPos.forEach(accountPo -> {
                Optional<AccountRequest> accountRequestOptional = updateAccountRequests.stream()
                        .filter(accountRequest -> accountPo.getAccountId().equals(accountRequest.getAccountId()))
                        .findFirst();
                if (accountRequestOptional.isPresent()) {
                    // 待修改
                    ClazzUtils.copyPropertiesIgnoreNull(accountRequestOptional.get(), accountPo);
                    accountPo.setUpdatedDate(DateUtils.getCurrentTime());
                    updateAccountPos.add(accountPo);
                } else {
                    // 待删除
                    deleteAccountPos.add(accountPo);
                }
            });
            // 删除原有账户信息
            if (AssertUtils.isNotEmpty(deleteAccountPos)) {
                accountDao.delete(deleteAccountPos);
            }
            if (AssertUtils.isNotEmpty(updateAccountPos)) {
                accountDao.update(updateAccountPos);
            }
            if (AssertUtils.isNotEmpty(addAccountPos)){
                // 新增银行卡
                accountBaseDao.addAccounts(addAccountPos);
                addAccountPos.forEach(accountPo -> {
                    AccountAuditPo accountAuditPo = new AccountAuditPo();
                    accountAuditPo.setAuditDate(DateUtils.getCurrentTime());
                    accountAuditPo.setAuditStatus("AUDIT_PASS");
                    accountAuditPo.setAccountId(accountPo.getAccountId());
                    accountBoService.saveAccountAuditPo(accountAuditPo);
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_ACCOUNT_ERROR);
        }
        return resultObject;
    }

    /**
     * 删除账户信息
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    @Transactional
    public ResultObject deleteAccount(String userId) {
        ResultObject resultObject = new ResultObject();
        try {
            List<AccountPo> accountPos = accountDao.fetchByUserId(userId);
            if (AssertUtils.isNotEmpty(accountPos)) {
                accountDao.delete(accountPos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_SAVE_ACCOUNT_ERROR);
        }
        return resultObject;
    }


    /**
     * 根据用户ID获取账户信息
     *
     * @param userId
     * @return
     */
    @Override
    public ResultObject<List<AccountResponse>> getAccountByUserId(String userId) {
        ResultObject<List<AccountResponse>> resultObject = new ResultObject<>();
        List<AccountResponse> accountResponses;
        try {
            AssertUtils.isNotEmpty(getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询账户信息
            List<AccountBo> accountBos = accountExtDao.loadAccountByUserId(userId);
            if (!AssertUtils.isNotNull(accountBos)) {
                return resultObject;
            }
            accountResponses = new ArrayList<>();
            accountBos.forEach(accountBo -> {
                AccountResponse accountResponse = (AccountResponse) converterObject(accountBo, AccountResponse.class);
                if (AssertUtils.isNotNull(accountBo.getAccountAuditPo())) {
                    accountResponse.setAuditStatus(accountBo.getAccountAuditPo().getAuditStatus());
                }
                accountResponses.add(accountResponse);
            });
            //设置返回值
            resultObject.setData(accountResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject accountRollback(String accountId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(getLogger(), accountId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_ID_ERROR);
            //删除该账户
            accountDao.deleteById(accountId);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_ROLLBACK_ACCOUNT_ERROR);
            }
        }
        return resultObject;

    }

    @Override
    public ResultObject<List<AccountResponse>> getAccountList(List<String> accountIdList) {
        ResultObject<List<AccountResponse>> resultObject = new ResultObject<>();
        //参数验证
        AssertUtils.isNotEmpty(this.getLogger(), accountIdList, PlatformErrorConfigEnum.PLATFORM_PARAMETER_ACCOUNT_ID_ERROR);
        //查询账户信息
        List<AccountBo> accountBoList = accountExtDao.loadAccountById(accountIdList);
        //账户验证
        AssertUtils.isNotNull(this.getLogger(), accountBoList, PlatformErrorConfigEnum.PLATFORM_ACCOUNT_IS_NOT_FOUND_OBJECT);
        //数据转换
        List<AccountResponse> accountResponseList = (List<AccountResponse>) this.converterList(accountBoList, new TypeToken<List<AccountResponse>>() {
        }.getType());
        //设置返回值
        resultObject.setData(accountResponseList);
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<AccountQueryResponse>> queryAccountAuditList(Users users, AccountQueryRequest accountQueryRequest) {
        ResultObject<BasePageResponse<AccountQueryResponse>> resultObject = new ResultObject<>();
        List<AccountQueryResponse> accountQueryResponses = new ArrayList<>();
        List<String> userIds = null;
        List<String> keywordUserIds = null;
        if (AssertUtils.isNotEmpty(accountQueryRequest.getBranchId())) {
            List<String> branchIds = new ArrayList<>();
            branchIds.add(accountQueryRequest.getBranchId());
            ResultObject<List<AgentSimpleBaseResponse>> listAgentResultObject = agentBaseAgentApi.queryAgentsByBranchs(branchIds);
            if (!AssertUtils.isResultObjectListDataNull(listAgentResultObject)) {
                userIds = listAgentResultObject.getData().stream().map(AgentSimpleBaseResponse::getUserId).distinct().collect(Collectors.toList());
            }
        }
        if (AssertUtils.isNotEmpty(accountQueryRequest.getKeyword())) {
            ResultObject<List<AgentKeyWordResponse>> listResultObject = agentApi.agentsVagueGet(accountQueryRequest.getKeyword());
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                keywordUserIds = listResultObject.getData().stream().map(AgentKeyWordResponse::getUserId).distinct().collect(Collectors.toList());
            }
        }
        List<AccountBo> accountBos = accountExtDao.queryAccountAudits(accountQueryRequest, userIds, keywordUserIds);
        if (AssertUtils.isNotEmpty(accountBos)) {
            accountBos.forEach(accountBo -> {
                ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByUserIdGet(accountBo.getUserId());
                AccountQueryResponse accountQueryResponse = (AccountQueryResponse) converterObject(accountBo, AccountQueryResponse.class);
                accountQueryResponse.setAuditDate(DateUtils.timeStrToString(accountBo.getAccountAuditPo().getCreatedDate(), DateUtils.FORMATE6));
                if (AssertUtils.isNotEmpty(accountBo.getBankCode())) {
                    ResultObject<BankResponse> bankRespFcResultObject = bankBusinessService.getBankById(null,accountBo.getBankCode(), users.getLanguage());
                    if (!AssertUtils.isResultObjectDataNull(bankRespFcResultObject)) {
                        accountQueryResponse.setBankName(bankRespFcResultObject.getData().getBankName());
                    }
                }
                if (!AssertUtils.isNotEmpty(accountQueryResponse.getPrimaryFlag()) || PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name().equals(accountQueryResponse.getPrimaryFlag())) {
                    accountQueryResponse.setPrimaryFlag("是");
                } else {
                    accountQueryResponse.setPrimaryFlag("否");
                }
                if (!AssertUtils.isResultObjectDataNull(agentResponseResultObject)) {
                    accountQueryResponse.setBranchId(agentResponseResultObject.getData().getBranchId());
                    accountQueryResponse.setAgentMobile(agentResponseResultObject.getData().getMobile());
                    accountQueryResponse.setAgentName(agentResponseResultObject.getData().getAgentName());
                    ResultObject<BranchResponse> resultObject1 = branchBusinessService.loadBranchInfo(users, agentResponseResultObject.getData().getBranchId());
                    if (!AssertUtils.isResultObjectDataNull(resultObject1)) {
                        accountQueryResponse.setBranchName(resultObject1.getData().getBranchName() + "/" + resultObject1.getData().getChannelTypeName());
                    }
                }
                accountQueryResponses.add(accountQueryResponse);
            });
        } else {
            return resultObject;
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(accountBos) ? accountBos.get(0).getTotalLine() : null;

        BasePageResponse<AccountQueryResponse> basePageResponse = BasePageResponse.getData(accountQueryRequest.getCurrentPage(), accountQueryRequest.getPageSize(), totalLine, accountQueryResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }


    @Override
    public ResultObject<List<AccountResponse>> queryAccountAuditBanksList(String userId) {
        ResultObject<List<AccountResponse>> resultObject = new ResultObject<>();
        List<AccountResponse> accountResponses;
        try {
            AssertUtils.isNotEmpty(getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询账户信息
            List<AccountBo> accountBos = accountExtDao.queryAccountAuditBanks(userId);
            if (!AssertUtils.isNotNull(accountBos)) {
                return resultObject;
            }
            accountResponses = new ArrayList<>();
            accountBos.forEach(accountBo -> {
                AccountResponse accountResponse = (AccountResponse) converterObject(accountBo, AccountResponse.class);
                if (AssertUtils.isNotNull(accountBo.getAccountAuditPo())) {
                    accountResponse.setAuditStatus(accountBo.getAccountAuditPo().getAuditStatus());
                }
                accountResponses.add(accountResponse);
            });
            //设置返回值
            resultObject.setData(accountResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AccountResponse>> queryAccountAuditBanksListNew(String userId) {
        ResultObject<List<AccountResponse>> resultObject = new ResultObject<>();
        List<AccountResponse> accountResponses;
        try {
            AssertUtils.isNotEmpty(getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询账户信息
            List<AccountBo> accountBos = accountExtDao.queryAccountAuditBanksNew(userId);
            if (!AssertUtils.isNotNull(accountBos)) {
                return resultObject;
            }
            accountResponses = new ArrayList<>();
            accountBos.forEach(accountBo -> {
                AccountResponse accountResponse = (AccountResponse) converterObject(accountBo, AccountResponse.class);
                if (AssertUtils.isNotNull(accountBo.getAccountAuditPo())) {
                    accountResponse.setAuditStatus(accountBo.getAccountAuditPo().getAuditStatus());
                }
                accountResponses.add(accountResponse);
            });
            //设置返回值
            resultObject.setData(accountResponses);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<InternationalDo>> queryAuditExceptions(Users users) {
        ResultObject<List<InternationalDo>> resultObject = new ResultObject<>();
        List<InternationalDo> internationalDos = internationalBaseService.queryInternational(InternationalTypeEnum.ACCOUNT_AUDIT_EXCEPTION_QUESTION.name(), users.getLanguage());
        resultObject.setData(internationalDos);
        return resultObject;
    }

    @Override
    public ResultObject accountAudit(AccountAuditRequest accountAuditRequest) {
        ResultObject resultObject = new ResultObject();
        //校验参数
        accountParameterValidate.validParameterAccountAudit(accountAuditRequest);
        AccountBo accountBo = accountExtDao.loadAccountById(accountAuditRequest.getAccountId());
        AssertUtils.isNotNull(getLogger(), accountBo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        AccountAuditPo accountAuditPo = accountExtDao.queryAccountAuditPoByAccountIdAndStatus(accountAuditRequest.getAccountId(), PlatformTermEnum.ACCOUNT_AUDIT_STATUS.UNDER_REVIEW.name());
        AssertUtils.isNotNull(getLogger(), accountAuditPo, PlatformErrorConfigEnum.PLATFORM_ACCOUNT_AUDIT_IS_NOT_FOUND_OBJECT);
        //审核成功后，修改其他卡的主副卡属性
        String userId = accountBo.getUserId();
        List<AccountBo> accountBos = accountExtDao.loadAccountByUserId(userId);

        boolean auditPass = PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_PASS.name().equals(accountAuditRequest.getAuditStatus());
        boolean auditFailed = PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_FAILED.name().equals(accountAuditRequest.getAuditStatus());

        if (accountBo.getPrimaryFlag().equals(PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name()) && auditPass) {
            accountBos.forEach(accountBo1 -> {
                if (!accountBo1.getAccountId().equals(accountBo.getAccountId()) && accountBo1.getPrimaryFlag().equals(PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name())) {
                    accountBo1.setPrimaryFlag(PlatformTermEnum.ACCOUNT_MAIN_FLAG.ADDITIONAL.name());
                    accountBoService.saveAccountPo(accountBo1);
                }
            });
        }
        accountBoService.saveAccountAuditPo(accountTransData.transferAccountAudit(accountAuditPo, accountAuditRequest));

        //银行卡审核完成时修改业务员审核表状态
        /*ResultObject resultObject1 = agentSignAuditApi.signAuditUpdate(userId);
        this.getLogger().info("修改业务员审核状态结果：===============" + JackSonUtils.objectToJsonStr(resultObject1));*/

        //发送消息
        pushMessage(accountAuditRequest, accountBo, auditPass, auditFailed);

        return resultObject;
    }

    @Override
    public ResultObject<List<AccountResponse>> getAccountByUserIds(List<String> userIds) {
        ResultObject<List<AccountResponse>> resultObject = new ResultObject<>();
        List<AccountResponse> accountResponses;
        try {
            AssertUtils.isNotEmpty(getLogger(), userIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询账户信息
            List<AccountBo> accountBos = accountExtDao.loadAccountByUserIds(userIds);
            if (!AssertUtils.isNotNull(accountBos)) {
                return resultObject;
            }
            accountResponses = new ArrayList<>();
            accountBos.forEach(accountBo -> {
                AccountResponse accountResponse = (AccountResponse) converterObject(accountBo, AccountResponse.class);
                if (AssertUtils.isNotNull(accountBo.getAccountAuditPo())) {
                    accountResponse.setAuditStatus(accountBo.getAccountAuditPo().getAuditStatus());
                }
                accountResponses.add(accountResponse);
            });
            //设置返回值
            resultObject.setData(accountResponses);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
            }
        }
        return resultObject;
    }

    private void pushMessage(AccountAuditRequest accountAuditRequest, AccountBo accountBo, boolean auditPass, boolean auditFailed) {
        try {
            Map<String, String> messageParamMap = new HashMap<>();

            ResultObject<UsersBo> userById = usersBusinessService.getUserById(accountBo.getUserId());
            String language = AssertUtils.isResultObjectDataNull(userById) ? TerminologyConfigEnum.LANGUAGE.ZH_CN.name() : userById.getData().getLanguage();

            String bankName = "";
            if (AssertUtils.isNotEmpty(accountBo.getBankCode())) {
                ResultObject<BankResponse> bankRespFcResultObject = bankBusinessService.getBankById(null, accountBo.getBankCode(), language);
                if (!AssertUtils.isResultObjectDataNull(bankRespFcResultObject)) {
                    bankName = bankRespFcResultObject.getData().getBankName();
                }
            }

            messageParamMap.put("bankName", bankName);
            messageParamMap.put("reason", accountAuditRequest.getFailureReason());

            String jsonString = JSON.toJSONString(messageParamMap);

            String businessCode = "";
            if (auditFailed) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.AGENT_BANK_CARD_REVIEW_NOT_PASSING_REMINDER.name();
            }
            if (auditPass) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.AGENT_BANK_CARD_REVIEW_PASS_REMINDER.name();
            }

            messageBusinessService.pushBusinessMessage(businessCode, Collections.singletonList(accountBo.getUserId()), jsonString);
        } catch (Exception e) {
            this.getLogger().info("发送消息失败,错误原因:{}", e.getMessage());
        }
    }
}