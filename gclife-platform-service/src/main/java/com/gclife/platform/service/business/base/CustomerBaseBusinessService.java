package com.gclife.platform.service.business.base;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.CustomerMessagesRequest;
import com.gclife.platform.model.request.EndorseCustomerRequest;
import com.gclife.platform.model.response.CustomerAgentResponse;
import com.gclife.platform.model.response.CustomerHistoryResponse;
import com.gclife.platform.model.response.CustomerMessageResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-9-7
 * description:
 */
public interface CustomerBaseBusinessService extends BaseBusinessService{

    /**
     * 根据客户ID获取客户信息
     *
     * @return
     */
    ResultObject<CustomerMessageResponse> getBaseCustomer(String customerId,String versionNo);

    /**
     * 查询历史客户
     * @param customerId 客户id
     * @param versionNo 版本号
     * @return
     */
    ResultObject<CustomerHistoryResponse> getBaseCustomerHistory(String customerId,String versionNo);


    /**
     * 模糊搜索客户
     * @param appRequestHandler
     * @param customerMessagesRequest
     * @return
     */
    ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomerList(AppRequestHeads appRequestHandler, CustomerMessagesRequest customerMessagesRequest);

    /**
     * 同步客户信息
     * @param endorseCustomerRequest 客户信息
     * @return ResultObject
     */
    ResultObject synchronizeBaseCustomer(EndorseCustomerRequest endorseCustomerRequest,Users users);

    /**
     * 查询客户关联的代理人客户id集合
     * @param customerId 客户ID
     * @return
     */
    ResultObject<List<CustomerAgentResponse>> getAgentCustomerRelation(String customerId);

    /**
     * 回滚客户信息
     * @param customerId 客户ID
     * @param oldVersionNo 旧版本号
     * @return
     */
    ResultObject rollbackCustomer(String customerId, String oldVersionNo);
}
