package com.gclife.platform.service.business.impl;//package com.gclife.platform.service.business.impl;


import com.alibaba.fastjson.JSONObject;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.dao.EmployeeExtDao;
import com.gclife.platform.model.bo.ChannelsBo;
import com.gclife.platform.model.bo.EmployeeBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.EmployeeQueryRequest;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.EmployeeBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 职员业务处理
 * <AUTHOR>
 */
@Service
public class EmployeeBusinessServiceImpl extends BaseBusinessServiceImpl implements EmployeeBusinessService {


    @Autowired
    private EmployeeExtDao employeeExtDao;

    @Autowired
    private BaseSysCodeExtDao baseSysCodeExtDao;

    /**
     * 加载用户管理渠道列表
     * @param userId
     * @return
     */
    @Override
    public ResultObject loadUsersManagerChannels(Users users,String userId) {

        ResultObject<List<ChannelsResponse>> resultObject=new ResultObject<List<ChannelsResponse>>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(),userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询数据
            List<ChannelsBo> channelsBoList =employeeExtDao.loadUsersManagerChannels(userId,users.getLanguage());
            //国际化
            if(AssertUtils.isNotEmpty(channelsBoList)){
                List<SyscodeBo> syscodeBos = baseSysCodeExtDao.loadBaseSyscodes(TerminologyTypeEnum.CHANNEL_TYPE.name(), users.getLanguage());
                channelsBoList.forEach(channelsResponse -> {
                    syscodeBos.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(channelsResponse.getChannelTypeCode())).findFirst().ifPresent(syscodeBo -> {
                        channelsResponse.setChannelTypeName(syscodeBo.getCodeName());
                    });
                });
            }
            //数据转换
            List<ChannelsResponse>  channelsResponses =(List<ChannelsResponse>)this.converterList(channelsBoList,new TypeToken<List<ChannelsBo>>(){}.getType());

            //设置返回数据
            resultObject.setData(channelsResponses);

        }catch (Exception e){
            if(e instanceof RequestException){
                RequestException error  =(RequestException)e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            }else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_EMPLOYEE_ERROR);
            }
        }
        return  resultObject;
    }


    /**
     * 加载职员信息
     * @param userId 用户ID
     * @param users 当前用户
     * @return
     */
    @Override
    public ResultObject loadEmployeByUserId(Users users,String userId) {

        ResultObject<EmployeResponse> resultObject=new ResultObject<EmployeResponse>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(),userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            //查询数据
            EmployeeBo employeeBo =employeeExtDao.loadEmployeeByUserId(userId,users.getLanguage());
            //验证
            AssertUtils.isNotNull(this.getLogger(), employeeBo,PlatformErrorConfigEnum.PLATFORM_BUSINESS_EMPLOYEE_IS_NOT_FOUND_OBJECT);

            EmployeResponse employeResponse = (EmployeResponse)this.converterObject(employeeBo,EmployeResponse.class);
            //设置返回数据
            resultObject.setData(employeResponse);
        }catch (Exception e){
            if(e instanceof RequestException){
                RequestException error  =(RequestException)e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            }else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_EMPLOYEE_ERROR);
            }
        }
        return  resultObject;
    }


    /**
     * 加载职员信息
     * @param employeeQueryRequest  职员请求
     * @return
     */
    @Override
    public ResultObject loadEmployeByUserIds(Users users,EmployeeQueryRequest employeeQueryRequest) {
        ResultObject<List<EmployeResponse>> resultObject=new ResultObject<List<EmployeResponse>>();
        try {
            List<String> userIds = null;
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(),employeeQueryRequest.getListUserId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            try {
                userIds = JSONObject.parseArray(employeeQueryRequest.getListUserId(),String.class);
            }catch (Exception e){
                throw new RequestException(PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            }

            //查询数据
            List<EmployeeBo> employeeBos =employeeExtDao.loadEmployeeByUserIds(userIds.stream().distinct().collect(Collectors.toList()), null);
            //验证
            AssertUtils.isNotNull(this.getLogger(), employeeBos,PlatformErrorConfigEnum.PLATFORM_BUSINESS_EMPLOYEE_IS_NOT_FOUND_OBJECT);

            List<EmployeResponse> listEmployeResponse = (List<EmployeResponse>)this.converterList(employeeBos,new TypeToken<List<EmployeResponse>>(){}.getType());
            //设置返回数据
            resultObject.setData(listEmployeResponse);
        }catch (Exception e){
            if(e instanceof RequestException){
                RequestException error  =(RequestException)e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            }else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_EMPLOYEE_ERROR);
            }
        }
        return  resultObject;
    }
    

}