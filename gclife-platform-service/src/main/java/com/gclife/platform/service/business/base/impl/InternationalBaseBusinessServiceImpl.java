package com.gclife.platform.service.business.base.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.base.service.TerminologyBaseService;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.vo.SyscodeResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class InternationalBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements InternationalBaseBusinessService {

    @Autowired
    private InternationalBaseService internationalBaseService;

    @Autowired
    private TerminologyBaseService terminologyBaseService;

    /**
     * 根据类型和语言查询国际化集合
     *
     * @param codeType 　国际化类型
     * @return List<InternationalDo>
     */
    @Override
    public ResultObject<List<SyscodeResponse>> queryInternational(String codeType, String lang) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            List<InternationalDo> internationalDos = internationalBaseService.queryInternational(codeType, lang);
            if (AssertUtils.isNotEmpty(internationalDos)) {
                List<SyscodeResponse> branchResponse = (List<SyscodeResponse>) this.converterList(internationalDos, new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                resultObject.setData(branchResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据类型,key值和语言查询国际化对象
     *
     * @param codeKey  国际化键
     * @param codeType 　国家化类型
     * @return InternationalDo
     */
    @Override
    public ResultObject<SyscodeResponse> queryOneInternational(String codeType, String codeKey, String lang) {
        ResultObject<SyscodeResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), codeType, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), codeKey, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            InternationalDo internationalDo = internationalBaseService.queryOneInternational(codeType, codeKey, lang);
            if (AssertUtils.isNotNull(internationalDo)) {
                SyscodeResponse syscodeResponse = (SyscodeResponse) this.converterObject(internationalDo, SyscodeResponse.class);
                resultObject.setData(syscodeResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据类型,keys值和语言查询国际化集合
     *
     * @param syscodeReqFc 　国际化key集合
     * @return List<InternationalDo>
     */
    @Override
    public ResultObject<List<SyscodeResponse>> queryInternationalByCodeKeys(SyscodeForm syscodeReqFc) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), syscodeReqFc.getCodeType(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_TYPE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), syscodeReqFc.getCodeKeys(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            List<InternationalDo> internationalDos = internationalBaseService.queryInternationalByCodeKeys(syscodeReqFc.getCodeType(), syscodeReqFc.getCodeKeys(), syscodeReqFc.getLang());
            if (AssertUtils.isNotEmpty(internationalDos)) {
                List<SyscodeResponse> syscodeResponses = (List<SyscodeResponse>) this.converterList(internationalDos, new TypeToken<List<SyscodeResponse>>() {
                }.getType());
                resultObject.setData(syscodeResponses);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据id查询国际互对象id
     *
     * @param textId 　国际化id
     * @return InternationalDo
     */
    @Override
    public ResultObject<SyscodeResponse> queryOneInternationalById(String textId) {
        ResultObject<SyscodeResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), textId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_INTERNATIONAL_KEY_IS_NOT_NULL);
            InternationalDo internationalDo = internationalBaseService.queryOneInternationalById(textId);
            if (AssertUtils.isNotNull(internationalDo)) {
                SyscodeResponse syscodeResponse = (SyscodeResponse) this.converterObject(internationalDo, SyscodeResponse.class);
                resultObject.setData(syscodeResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_INFORMATION_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 批量查询国际化对象
     *
     * @param language  语言
     * @param codeTypes 类型集合
     * @return Map<String, List < SyscodeResponse>>
     */
    @Override
    public ResultObject<Map<String, List<SyscodeResponse>>> queryBatchInternationalByCodeKeys(String language, List<String> codeTypes) {
        ResultObject<Map<String, List<SyscodeResponse>>> resultObject = new ResultObject<>();

        if (!AssertUtils.isNotEmpty(codeTypes)) {
            return resultObject;
        }
        Map<String, List<SyscodeResponse>> map = new HashMap<>();
        codeTypes.stream().distinct().collect(Collectors.toList()).forEach(codeType -> {
            ResultObject<List<SyscodeResponse>> listResultObject = this.queryInternational(codeType, language);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                map.put(codeType, listResultObject.getData());
            }
        });
        resultObject.setData(map);
        return resultObject;
    }
}