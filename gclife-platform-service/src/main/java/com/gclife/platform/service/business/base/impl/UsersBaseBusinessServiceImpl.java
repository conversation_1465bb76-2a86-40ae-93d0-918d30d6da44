package com.gclife.platform.service.business.base.impl;


import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.UsersDo;
import com.gclife.platform.base.model.bo.UsersInfoDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.base.service.Users2RolesBaseService;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.base.service.login.UsersLoginBaseService;
import com.gclife.platform.core.jooq.tables.pojos.*;
import com.gclife.platform.model.response.UserDingRelationResponse;
import com.gclife.platform.model.response.UserThirdPartyResponse;
import com.gclife.platform.vo.branch.BranchResponse;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.platform.model.response.UserWeixinRelationResponse;
import com.gclife.platform.service.business.InternationalBusinessService;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.service.business.base.UsersBaseBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午12:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class UsersBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements UsersBaseBusinessService {

    @Autowired
    private UsersBaseService usersBaseService;
    @Autowired
    private InternationalBaseService internationalBaseService;
    @Autowired
    private InternationalBusinessService internationalBusinessService;
    @Autowired
    private BranchBaseBusinessService branchBaseBusinessService;
    @Autowired
    private Users2RolesBaseService users2RolesBaseService;
    @Autowired
    private UsersLoginBaseService usersLoginBaseService;

    /**
     * 根据用户ID,查询用户信息
     *
     * @param userId 用户ID
     * @return ResultObject<UserResponse>
     */
    @Override
    public ResultObject<UserResponse> queryOneUsersPoById(String userId) {
        ResultObject<UserResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            UsersPo usersPo = usersBaseService.queryOneUsersPoById(userId);
            AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);
            UserResponse userResponse = (UserResponse) this.converterObject(usersPo, UserResponse.class);
            UserLoginPo userLoginPo = usersLoginBaseService.queryOneUserLoginPoByUserId(userId);
            if (AssertUtils.isNotNull(userLoginPo)) {
                userResponse.setLoginCount(userLoginPo.getLoginCount());
                userResponse.setLoginLast(userLoginPo.getLoginLast());
            }
            // 查询第三方用户
            List<UserThirdPartyPo> userThirdPartyPos = usersBaseService.listUserThirdParty(userId);
            if (AssertUtils.isNotEmpty(userThirdPartyPos)) {
                List<UserThirdPartyResponse> userThirdPartyResponses = (List<UserThirdPartyResponse>) this.converterList(
                        userThirdPartyPos, new TypeToken<List<UserThirdPartyResponse>>() {}.getType()
                );
                userResponse.setListThirdParty(userThirdPartyResponses);
            }
            resultObject.setData(userResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据机构ID信息
     *
     * @param userIds 用户IDs
     * @return ResultObject<List                                                               <                                                               UserResponse>>
     */
    @Override
    public ResultObject<List<UserResponse>> queryUsersPoByIds(List<String> userIds) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), userIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
            List<UsersDo> usersPos = usersBaseService.queryUsersPoByIds(userIds);
            if (AssertUtils.isNotEmpty(usersPos)) {
                List<UserResponse> userResponses = (List<UserResponse>) this.converterList(usersPos, new TypeToken<List<UserResponse>>() {
                }.getType());
                resultObject.setData(userResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<UserResponse>> queryUsersPoByMobiles(String... mobiles) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            List<UsersPo> usersPos = usersBaseService.queryUsersPoByMobiles(mobiles);
            if (!AssertUtils.isNotEmpty(usersPos)) {
                return resultObject;
            }
            List<UserResponse> userResponses = (List<UserResponse>) this.converterList(usersPos, new TypeToken<List<UserResponse>>() {
            }.getType());

            resultObject.setData(userResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<UserResponse>> queryUsersPoByName(String name, Users users) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            List<UsersInfoDo> usersInfoDos = usersBaseService.queryUsersPoByName(name);
            if (!AssertUtils.isNotEmpty(usersInfoDos)) {
                return resultObject;
            }
            List<UserResponse> userResponses = (List<UserResponse>) this.converterList(usersInfoDos, new TypeToken<List<UserResponse>>() {
            }.getType());
            //查询机构
            List<String> branchIds = userResponses.stream().map(UserResponse::getBranchId).distinct().collect(Collectors.toList());

            ResultObject<List<BranchResponse>> resultObject1 = branchBaseBusinessService.queryBranchByIds(branchIds);
            AssertUtils.isResultObjectListDataNull(this.getLogger(), resultObject1, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            List<BranchResponse> branchResponses = resultObject1.getData();
            userResponses.forEach(userResponse -> {
                branchResponses.stream()
                        .filter(branchResponse -> branchResponse.getBranchId().equals(userResponse.getBranchId()))
                        .findFirst().ifPresent(branchResponse -> userResponse.setBranchName(branchResponse.getBranchName()));
            });
            //国际化
            //保单状态国际化
            List<SyscodeResponse> syscodeResponses = internationalBusinessService.internationalTextGet(users, TerminologyTypeEnum.ENABLED.name(), users.getLanguage()).getData();
            if (AssertUtils.isNotEmpty(syscodeResponses)) {
                userResponses.forEach(userResponse -> {
                    syscodeResponses.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(userResponse.getEnabled())).findFirst()
                            .ifPresent(syscodeResponse -> userResponse.setEnabledName(syscodeResponse.getCodeName()));
                });
            }
            resultObject.setData(userResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据openId查询用户信息
     *
     * @param openId 微信
     * @return UserWeixinRelationResponses
     */
    @Override
    public ResultObject<UserWeixinRelationResponse> queryOneUsersWechatById(String openId) {
        ResultObject<UserWeixinRelationResponse> resultObject = new ResultObject<>();
        UserWeixinRelationPo userWeixinRelationPo = usersBaseService.queryOneUsersWechatById(openId);
        if (AssertUtils.isNotNull(userWeixinRelationPo)) {
            UserWeixinRelationResponse userWeixinRelationResponse = (UserWeixinRelationResponse) this.converterObject(userWeixinRelationPo, UserWeixinRelationResponse.class);
            resultObject.setData(userWeixinRelationResponse);
        }
        return resultObject;
    }

    /**
     * 根据userId查询用户信息
     *
     * @param userId 微信
     * @return UserDingRelationResponse
     */
    @Override
    public ResultObject<UserDingRelationResponse> queryOneUsersDingById(String userId) {
        ResultObject<UserDingRelationResponse> resultObject = new ResultObject<>();
        UserDingRelationPo userDingRelationPo = usersBaseService.queryOneUsersDingById(userId);
        if (AssertUtils.isNotNull(userDingRelationPo)) {
            UserDingRelationResponse userDingRelationResponse = (UserDingRelationResponse) this.converterObject(userDingRelationPo, UserDingRelationResponse.class);
            resultObject.setData(userDingRelationResponse);
        }
        return resultObject;
    }

    /**
     * 根据账户或姓名模糊查询用户
     *
     * @param keyword
     * @param users
     * @return
     */
    @Override
    public ResultObject<List<UserResponse>> queryUsersPoByKeyword(String keyword, Users users) {
        ResultObject<List<UserResponse>> resultObject = new ResultObject<>();
        try {
            List<UsersInfoDo> usersInfoDos = usersBaseService.queryUsersPoByKeyword(keyword);
            if (!AssertUtils.isNotEmpty(usersInfoDos)) {
                return resultObject;
            }
            List<UserResponse> userResponses = (List<UserResponse>) this.converterList(usersInfoDos, new TypeToken<List<UserResponse>>() {
            }.getType());
            //查询机构
            List<String> branchIds = userResponses.stream().map(UserResponse::getBranchId).distinct().collect(Collectors.toList());

            ResultObject<List<BranchResponse>> resultObject1 = branchBaseBusinessService.queryBranchByIds(branchIds);
            AssertUtils.isResultObjectListDataNull(this.getLogger(), resultObject1, PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
            List<BranchResponse> branchResponses = resultObject1.getData();
            userResponses.forEach(userResponse -> {
                branchResponses.stream()
                        .filter(branchResponse -> branchResponse.getBranchId().equals(userResponse.getBranchId()))
                        .findFirst().ifPresent(branchResponse -> userResponse.setBranchName(branchResponse.getBranchName()));
            });
            //国际化
            //保单状态国际化
            List<SyscodeResponse> syscodeResponses = internationalBusinessService.internationalTextGet(users, TerminologyTypeEnum.ENABLED.name(), users.getLanguage()).getData();
            if (AssertUtils.isNotEmpty(syscodeResponses)) {
                userResponses.forEach(userResponse -> {
                    syscodeResponses.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(userResponse.getEnabled())).findFirst()
                            .ifPresent(syscodeResponse -> userResponse.setEnabledName(syscodeResponse.getCodeName()));
                });
            }
            resultObject.setData(userResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_USERS_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 赋予用户团险权限
     *
     * @param userId 用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject grantedGroupPermission(String userId) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(this.getLogger(), userId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
        UsersPo usersPo = usersBaseService.queryOneUsersPoById(userId);
        AssertUtils.isNotNull(this.getLogger(), usersPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_INFO_IS_NOT_FOUND_OBJECT);

        String roleId = "ROLE100008";

        Users2rolesPo users2rolesPo = users2RolesBaseService.queryUsers2Roles(roleId, userId);
        if(!AssertUtils.isNotNull(users2rolesPo)){
            users2rolesPo = new Users2rolesPo();
            users2rolesPo.setRoleId(roleId);
            users2rolesPo.setUserId(userId);
            users2RolesBaseService.saveUsers2rolesPo(users2rolesPo);
        }
        return resultObject;
    }
}