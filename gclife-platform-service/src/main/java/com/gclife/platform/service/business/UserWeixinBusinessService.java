package com.gclife.platform.service.business;

import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.UserWeixinMiniRequest;
import com.gclife.platform.model.request.UserWeixinRequest;
import com.gclife.platform.model.response.UserOpenPlatformRespone;
import com.gclife.platform.model.response.UserWeixinRelationResponse;

/**
 * <AUTHOR>
 * create 17-12-13
 * description:
 */
public interface UserWeixinBusinessService extends BaseBusinessService {
    /**
     * 保存用户登录日志
     * @param userWeixinRequest 微信请求对象
     * @return  ResultObject<BaseResponse>
     */
    ResultObject<BaseResponse> saveUsersWechatInfo(UserWeixinRequest userWeixinRequest);


    /**
     * 查询微信用户信息
     * @param userId 微信请求对象
     * @return  ResultObject<BaseResponse>
     */
    ResultObject<UserWeixinRelationResponse> queryUserWeixinRelationInfo(String userId);

    /**
     * 查询第三方开发平台是否已经注册了用户信息
     * @param userWeixinMiniRequest
     * @return
     * <AUTHOR>
     * @date 2019-02-28
     */
    ResultObject<UserOpenPlatformRespone> findUserWeixinRelationInfo(UserWeixinMiniRequest userWeixinMiniRequest);

    /**
     * 注册第三方开放平台用户信息
     * @param userWeixinRequest
     * @return
     */
    ResultObject<UserOpenPlatformRespone> registerUsersWechatInfo(UserWeixinRequest userWeixinRequest);
}
