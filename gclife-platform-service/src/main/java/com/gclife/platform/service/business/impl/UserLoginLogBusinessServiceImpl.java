package com.gclife.platform.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.base.model.bo.UsersDo;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.base.service.login.UsersLoginBaseService;
import com.gclife.platform.core.jooq.tables.daos.OauthAccessTokenDao;
import com.gclife.platform.core.jooq.tables.daos.UserAppDeviceDao;
import com.gclife.platform.core.jooq.tables.daos.UserLoginDao;
import com.gclife.platform.core.jooq.tables.daos.UsersDao;
import com.gclife.platform.core.jooq.tables.pojos.OauthAccessTokenPo;
import com.gclife.platform.core.jooq.tables.pojos.UserAppDevicePo;
import com.gclife.platform.core.jooq.tables.pojos.UserLoginPo;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.UserAppDeviceExtDao;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.dao.UsersLoginLogExtDao;
import com.gclife.platform.model.bo.UserLoginLogBo;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.response.UserLoginCheckResponse;
import com.gclife.platform.model.response.UserLoginLogResponse;
import com.gclife.platform.service.business.UserLoginLogBusinessService;
import com.gclife.platform.service.data.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.Date;
import java.util.List;

import static com.gclife.platform.base.model.config.PlatformErrorConfigEnum.CURRENT_USER_HAS_EXPIRED;


/**
 * <AUTHOR>
 * create 17-12-13
 * description:
 */
@Service
public class UserLoginLogBusinessServiceImpl extends BaseBusinessServiceImpl implements UserLoginLogBusinessService {

    @Autowired
    private UsersService usersService;

    @Autowired
    private UsersLoginLogExtDao usersLoginLogExtDao;

    @Autowired
    private UsersExtDao usersExtDao;
    @Autowired
    private UsersDao usersDao;

    @Autowired
    private UsersLoginBaseService usersLoginBaseService;

    @Autowired
    private UserLoginDao userLoginDao;

    // 最大重试次数
    private static final int MAX_LOGIN_RETRY = 5;
    @Autowired
    private UserAppDeviceExtDao userAppDeviceExtDao;

    @Autowired
    private UserAppDeviceDao userAppDeviceDao;
    @Autowired
    private UsersBaseService usersBaseService;
    @Autowired
    private OauthAccessTokenDao oauthAccessTokenDao;

    @Override
    @Transactional
    public ResultObject postUserLoginLog(AppRequestHeads appRequestHeads, String userId, String errorCode) {
        ResultObject resultObject = new ResultObject<>();
        try {
            this.getLogger().error("appRequestHeads:"+JSON.toJSONString(appRequestHeads));
            UserLoginLogBo userLoginLogBo = (UserLoginLogBo) this.converterObject(appRequestHeads, UserLoginLogBo.class);
            userLoginLogBo.setDeviceTokens(AssertUtils.isNotEmpty(appRequestHeads.getDeviceId())?appRequestHeads.getDeviceId():userId);
            userLoginLogBo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
            userLoginLogBo.setUserId(userId);
            userLoginLogBo.setCreatedUserId(userId);
            userLoginLogBo.setLoginDate(System.currentTimeMillis());
            userLoginLogBo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
            userLoginLogBo.setError(errorCode);
            this.getLogger().error("userLoginLogBo:"+JSON.toJSONString(userLoginLogBo));
            usersService.saveUserLoginLogBo(userLoginLogBo);

            if(BaseErrorConfigEnum.SUCCESS.name().equals(errorCode)){

                UsersPo usersPo = usersExtDao.loadUserPo(userId);

                if (AssertUtils.isNotNull(usersPo)) {

                    //设置第一次登录时间
                    if (!AssertUtils.isNotNull(usersPo.getFirstLogin())) {
                        usersPo.setFirstLogin(DateUtils.getCurrentTime());
                        usersDao.update(usersPo);
                    }

                    //设置用户语言
                    if(AssertUtils.isNotNull(appRequestHeads.getLanguage())){
                        usersPo.setLanguage(appRequestHeads.getLanguage());
                        usersDao.update(usersPo);
                    }
                }

                UserLoginPo userLoginPo = usersLoginBaseService.queryOneUserLoginPoByUserId(userId);
                if(AssertUtils.isNotNull(userLoginPo)){
                    if(!AssertUtils.isNotNull(userLoginPo.getLoginCount())){
                        userLoginPo.setLoginCount(0L);
                    }
                }else{
                    userLoginPo= new UserLoginPo();
                }

                if(!AssertUtils.isNotNull(userLoginPo.getLoginCount())){
                    userLoginPo.setLoginCount(0L);
                }
                userLoginPo.setLoginCount(userLoginPo.getLoginCount()+1);
                userLoginPo.setUpdatedDate(DateUtils.getCurrentTime());
                userLoginPo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
                userLoginPo.setLoginLast(DateUtils.getCurrentTime());
                userLoginPo.setUserId(userId);
                userLoginPo.setError(errorCode);
                userLoginPo.setIpAddress(appRequestHeads.getIpAddress());
                if(AssertUtils.isNotNull(userLoginPo.getCreatedDate())){
                    userLoginPo.setCreatedDate(DateUtils.getCurrentTime());
                }
                if(AssertUtils.isNotEmpty(userLoginPo.getUserLoginId())){
                    userLoginDao.update(userLoginPo);
                }else {
                    userLoginPo.setUserLoginId(UUIDUtils.getUUIDShort());
                    userLoginDao.insert(userLoginPo);
                }
            }

            // 修改设备token表记录
            if (AssertUtils.isNotEmpty(userId)
                    && AssertUtils.isNotEmpty(appRequestHeads.getDeviceId())
                    && AssertUtils.isNotEmpty(appRequestHeads.getOsType())
                    && AssertUtils.isNotEmpty(appRequestHeads.getDeviceChannel())) {
                UserAppDevicePo userAppDevicePo = new UserAppDevicePo();
                userAppDevicePo.setUserId(userId);
                userAppDevicePo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
                // 1.查询
                List<UserAppDevicePo> userAppDevicePos = userAppDeviceExtDao.queryUserAppDeviceList(userAppDevicePo);
                this.getLogger().error("userAppDevicePos1:"+JSON.toJSONString(userAppDevicePos));
                if (AssertUtils.isNotEmpty(userAppDevicePos)) {
                    boolean existFlag = false;
                    for (UserAppDevicePo devicePo : userAppDevicePos) {
                        if (devicePo.getDeviceTypeCode().equalsIgnoreCase(appRequestHeads.getOsType())) {
                            // 当前登录设备类型
                            existFlag = true;
                            devicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                            devicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                        } else {
                            // 非当前登录设备类型
                            devicePo.setIsOnline(PlatformTermEnum.YES_NO.NO.name());
                        }
                    }
                    if (!existFlag) {
                        userAppDevicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                        userAppDevicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                        userAppDevicePo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
                        // 新增
                        userAppDevicePos.add(userAppDevicePo);
                    }
                } else {
                    userAppDevicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                    userAppDevicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                    userAppDevicePo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
                    // 新增
                    userAppDevicePos.add(userAppDevicePo);
                }
                // 修改
                this.getLogger().error("userAppDevicePos2:"+JSON.toJSONString(userAppDevicePos));
                usersService.saveUserAppDevice(userAppDevicePos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_SAVE_USER_LOGIN_LOG_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    @Transactional
    public ResultObject postUserLoginLog(AppRequestHeads appRequestHeads, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {

            this.getLogger().error("appRequestHeads:"+JSON.toJSONString(appRequestHeads));
            UserLoginLogBo userLoginLogBo = (UserLoginLogBo) this.converterObject(appRequestHeads, UserLoginLogBo.class);
            userLoginLogBo.setDeviceTokens(AssertUtils.isNotEmpty(appRequestHeads.getDeviceId())?appRequestHeads.getDeviceId():users.getUserId());
            userLoginLogBo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
            userLoginLogBo.setUserId(users.getUserId());
            userLoginLogBo.setCreatedUserId(users.getUserId());
            userLoginLogBo.setLoginDate(System.currentTimeMillis());
            userLoginLogBo.setDeviceTypeCode(AssertUtils.isNotEmpty(appRequestHeads.getOsType())?appRequestHeads.getOsType().toUpperCase():"PC");
            userLoginLogBo.setError(BaseErrorConfigEnum.SUCCESS.name());
            this.getLogger().error("userLoginLogBo:"+JSON.toJSONString(userLoginLogBo));
            usersService.saveUserLoginLogBo(userLoginLogBo);

            UserLoginPo userLoginPo = usersLoginBaseService.queryOneUserLoginPoByUserId(users.getUserId());
            if(AssertUtils.isNotNull(userLoginPo)){
                if(!AssertUtils.isNotNull(userLoginPo.getLoginCount())){
                    userLoginPo.setLoginCount(0L);
                }
            }else{
                userLoginPo= new UserLoginPo();
            }

            if(!AssertUtils.isNotNull(userLoginPo.getLoginCount())){
                userLoginPo.setLoginCount(0L);
            }
            userLoginPo.setLoginCount(userLoginPo.getLoginCount()+1);
            userLoginPo.setUpdatedDate(DateUtils.getCurrentTime());
            userLoginPo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
            userLoginPo.setLoginLast(DateUtils.getCurrentTime());
            userLoginPo.setUserId(users.getUserId());
            userLoginPo.setIpAddress(appRequestHeads.getIpAddress());
            if(AssertUtils.isNotNull(userLoginPo.getCreatedDate())){
                userLoginPo.setCreatedDate(DateUtils.getCurrentTime());
            }
            if(AssertUtils.isNotEmpty(userLoginPo.getUserLoginId())){
                userLoginDao.update(userLoginPo);
            }else {
                userLoginPo.setUserLoginId(UUIDUtils.getUUIDShort());
                userLoginDao.insert(userLoginPo);
            }

            // 修改设备token表记录
            if (AssertUtils.isNotEmpty(users.getUserId())
                    && AssertUtils.isNotEmpty(appRequestHeads.getDeviceId())
                    && AssertUtils.isNotEmpty(appRequestHeads.getOsType())
                    && AssertUtils.isNotEmpty(appRequestHeads.getDeviceChannel())) {
                UserAppDevicePo userAppDevicePo = new UserAppDevicePo();
                userAppDevicePo.setUserId(users.getUserId());
                userAppDevicePo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
                // 1.查询
                List<UserAppDevicePo> userAppDevicePos = userAppDeviceExtDao.queryUserAppDeviceList(userAppDevicePo);
                this.getLogger().error("userAppDevicePos1:"+JSON.toJSONString(userAppDevicePos));
                if (AssertUtils.isNotEmpty(userAppDevicePos)) {
                    boolean existFlag = false;
                    for (UserAppDevicePo devicePo : userAppDevicePos) {
                        if (devicePo.getDeviceTypeCode().equalsIgnoreCase(appRequestHeads.getOsType())) {
                            // 当前登录设备类型
                            existFlag = true;
                            devicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                            devicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                        } else {
                            // 非当前登录设备类型
                            devicePo.setIsOnline(PlatformTermEnum.YES_NO.NO.name());
                        }
                    }
                    if (!existFlag) {
                        userAppDevicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                        userAppDevicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                        userAppDevicePo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
                        // 新增
                        userAppDevicePos.add(userAppDevicePo);
                    }
                } else {
                    userAppDevicePo.setIsOnline(PlatformTermEnum.YES_NO.YES.name());
                    userAppDevicePo.setDeviceTokens(appRequestHeads.getDeviceId());
                    userAppDevicePo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
                    // 新增
                    userAppDevicePos.add(userAppDevicePo);
                }
                // 修改
                this.getLogger().error("userAppDevicePos2:"+JSON.toJSONString(userAppDevicePos));
                usersService.saveUserAppDevice(userAppDevicePos);

                this.getLogger().error("userAppDevicePos2:"+JSON.toJSONString(userAppDevicePos));
            }

            // 修改用户登录语言
            if (AssertUtils.isNotNullString(appRequestHeads.getLanguage())
                    && AssertUtils.isNotNullString(users.getUserId())
                    && AssertUtils.isNotNullString(appRequestHeads.getDeviceChannel())
                    && appRequestHeads.getDeviceChannel().equals("gclife_bmp_pc")) {
                UsersPo usersPo = usersDao.fetchOneByUserId(users.getUserId());
                usersPo.setLanguage(appRequestHeads.getLanguage());
                usersDao.update(usersPo);
            }

        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_SAVE_USER_LOGIN_LOG_ERROR);
            }
        }
        return resultObject;
    }

    @Transactional
    @Override
    public ResultObject postUserLogout(AppRequestHeads appRequestHeads, Users users) {
        ResultObject resultObject =new ResultObject();
        this.getLogger().info("时间:[{}],app退出登录用户ID:[{}]",DateUtils.dateToString(new Date(),DateUtils.FORMATE6),users.getUserId());
        try {
            if (AssertUtils.isNotEmpty(users.getUserId())&& AssertUtils.isNotEmpty(appRequestHeads.getOsType())
                    && AssertUtils.isNotEmpty(appRequestHeads.getDeviceChannel())) {
                UserAppDevicePo userAppDevicePo = new UserAppDevicePo();
                userAppDevicePo.setUserId(users.getUserId());
                userAppDevicePo.setDeviceChannelId(appRequestHeads.getDeviceChannel());
                userAppDevicePo.setDeviceTypeCode(appRequestHeads.getOsType().toUpperCase());
                // 1.查询
                List<UserAppDevicePo> userAppDevicePos = userAppDeviceExtDao.queryUserAppDeviceList(userAppDevicePo);
                if(AssertUtils.isNotNull(userAppDevicePos)){
                    userAppDevicePos.forEach(userAppDevicePo1 -> {
                        userAppDevicePo1.setIsOnline(PlatformTermEnum.YES_NO.NO.name());
                    });
                    usersService.saveUserAppDevice(userAppDevicePos);
                }
            }
        }catch (Exception e){
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,PlatformErrorConfigEnum.USERS_SAVE_USER_LOGOUT_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject postUserAgentLogout(AppRequestHeads appRequestHeads, Users users, String userId) {
        ResultObject resultObject =new ResultObject();
        this.getLogger().info("时间:[{}],app退出登录用户ID:[{}]",DateUtils.dateToString(new Date(),DateUtils.FORMATE6),users.getUserId());
        try {
            if (AssertUtils.isNotEmpty(userId)) {
                UserAppDevicePo userAppDevicePo = new UserAppDevicePo();
                userAppDevicePo.setUserId(userId);
                // 1.查询
                List<UserAppDevicePo> userAppDevicePos = userAppDeviceExtDao.queryUserAppDeviceList(userAppDevicePo);
                if(AssertUtils.isNotNull(userAppDevicePos)){
                    userAppDevicePos.forEach(userAppDevicePo1 -> {
                        userAppDevicePo1.setIsOnline(PlatformTermEnum.YES_NO.NO.name());
                    });
                    usersService.saveUserAppDevice(userAppDevicePos);
                }

                UsersDo usersDo = usersBaseService.queryOneUsersPoById(userId);

                if (AssertUtils.isNotNull(usersDo)) {
                    if (AssertUtils.isNotNull(usersDo.getUsername())) {
                        List<OauthAccessTokenPo> oauthAccessTokenPos = oauthAccessTokenDao.fetchByUserName(usersDo.getUsername());
                        oauthAccessTokenDao.delete(oauthAccessTokenPos);
                    }
                }
            }
        }catch (Exception e){
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,PlatformErrorConfigEnum.USERS_SAVE_USER_LOGOUT_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询用户APP最新登录日志
     * @param userId 用户ID
     * @return  ResultObject<UserLoginLogResponse>
     */
    @Override
    public ResultObject<UserLoginLogResponse> loadUserAppNewestLoginLog(String userId) {
        ResultObject<UserLoginLogResponse> resultObject = new ResultObject<UserLoginLogResponse>();
        try {
            UserLoginLogBo userLoginLogBo = usersLoginLogExtDao.loadUserAppNewestLoginLog(userId);
            UserLoginLogResponse userLoginLogResponse = (UserLoginLogResponse) this.converterObject(userLoginLogBo, UserLoginLogResponse.class);
            resultObject.setData(userLoginLogResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_SAVE_USER_LOGIN_LOG_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 查询用户最新的登录统计信息
     * @param countryCode 地区区号
     * @param mobile 手机号码
     * @param deviceChannel 设备渠道类型
     * @return
     */
    @Override
    public ResultObject<UserLoginCheckResponse> appUserLoginCheck(String countryCode, String mobile, String deviceChannel) {
        ResultObject<UserLoginCheckResponse> resultObject = new ResultObject<UserLoginCheckResponse>();
        UserLoginCheckResponse userLoginCheckResponse = new UserLoginCheckResponse();

        // 先获取用户信息
        UsersBo usersBo = usersExtDao.loadMobileVerify(countryCode, mobile, deviceChannel);
        if (usersBo == null) {
            userLoginCheckResponse.setAllowLogin(false);
            resultObject.setIenum(PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_REGISTERED);
        } else {
            userLoginCheckResponse.setUserId(usersBo.getUserId());
            try {
                // 获取用户30分钟内登录失败的次数
                int failedCount = usersLoginLogExtDao.getAppUserFailCount(usersBo.getUserId());
                userLoginCheckResponse.setFailedCount(failedCount);
                userLoginCheckResponse.setAllowLogin(MAX_LOGIN_RETRY > failedCount);
                resultObject.setData(userLoginCheckResponse);
                if(MAX_LOGIN_RETRY <= failedCount) {
                    resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_LOGIN_FAILED_EXCEED);
                }
            } catch (Exception e) {
                if (e instanceof RequestException) {
                    RequestException error = (RequestException) e;
                    //错误设置
                    resultObject.setIenum(error.getiEnum());
                } else {
                    resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_LOGIN_CHECK_ERROR);
                }
            }
        }

        return resultObject;
    }

    @Override
    public ResultObject userValidCheck(String userName) {
        ResultObject resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(userName)) {
            return resultObject;
        }
        UsersPo usersPo = usersExtDao.loadUserByUsersName(userName);

        //短暂用户如果过了用户失效时间就不允许在登录核心系统
        if (AssertUtils.isNotNull(usersPo)) {
            Long userInvalidDate = usersPo.getUserInvalidDate();
            if ("TEMPORARY".equals(usersPo.getUserType()) && AssertUtils.isNotNull(userInvalidDate) && userInvalidDate <= DateUtils.getDayBegin().getTime()) {
                throwsException(this.getLogger(),CURRENT_USER_HAS_EXPIRED);
            }
        }
        return resultObject;
    }
}
