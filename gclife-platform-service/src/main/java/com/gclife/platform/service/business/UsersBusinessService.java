package com.gclife.platform.service.business;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.request.*;
import com.gclife.platform.model.response.SaveUserResponse;
import com.gclife.platform.model.response.UserBaseResponse;
import com.gclife.platform.model.response.UserResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UsersBusinessService extends BaseBusinessService {
    /**
     * 加载用户列表
     *
     * @param roleId 角色id
     * @return
     */
    ResultObject loadUserRoles(String roleId);


    /**
     * 加载用户列表
     *
     * @param userQueryRequest 查询用户请求对象
     * @return
     */
    ResultObject loadUserList(UserQueryRequest userQueryRequest);


    /**
     * 加载授权用户
     *
     * @param userId
     * @return
     */
    ResultObject loadUserDetailById(String userId);


    /**
     * 新增用户
     *
     * @param userRequest
     * @return
     */
    ResultObject addUser(UserRequest userRequest);

    /**
     * 修改用户
     *
     * @param userRequest 修改数据
     * @param userId      修改用户ID
     * @return
     */
    ResultObject updateUser(UserRequest userRequest, String userId);


    /**
     * 根据用户账户名查询用户信息
     *
     * @param users    当前用户
     * @param username
     * @return
     */
    ResultObject loadUserByUserName(Users users, String username);

    /**
     * 根据当前登录用户ID获取用户详情
     *
     * @param userId
     * @return
     */
    ResultObject<UsersBo> getUserById(String userId);

    /**
     * 根据手机号码获取用户详情
     *
     * @param mobile
     * @param deviceChannel
     * @return
     */
    ResultObject<UsersBo> getUserByMobile(String mobile, String deviceChannel);

    /**
     * 修改用户信息
     *
     * @param userId
     * @param userInfoRequest
     * @param appRequestHeads
     * @return
     */
    ResultObject putUserInfo(String userId, UserInfoRequest userInfoRequest, AppRequestHeads appRequestHeads);

    /**
     * 修改用户密码
     *
     * @param userLoginPasswordResetRequest
     * @return
     */
    ResultObject changeUserPwd(UserLoginPasswordResetRequest userLoginPasswordResetRequest);

    /**
     * 判断用户是否首次登录
     *
     * @param username
     * @return
     */
    ResultObject checkUserPwd(String username);

    /**
     * 根据代理人ids查询代理人用户信息
     *
     * @param userIds
     * @return
     */
    ResultObject<List<UserResponse>> postAgents(List<String> userIds);


    /**
     * 加载用户业务信息
     *
     * @param userId
     * @return
     */
    ResultObject<UserResponse> loadBusinessUserDetailById(String userId);

    /**
     * 加载用户业务信息
     *
     * @param userId
     * @return
     */
    ResultObject<List<UserResponse>> userInfoDetailGet(List<String> userIds);


    /**
     * 加载用户业务信息
     *
     * @param resourceCode 资源编码
     * @param branchId     机构ID
     * @return ResultObject<List                                                                                                                                                                                                                                                               <                                                                                                                                                                                                                                                               String>>
     */
    ResultObject<List<String>> loadResourceUserRecentList(String resourceCode, String branchId);


    /**
     * 加载用户业务信息
     *
     * @param resourceCode 资源编码
     * @param branchId     机构ID
     * @return ResultObject<List                                                                                                                                                                                                                                                               <                                                                                                                                                                                                                                                               String>>
     */
    ResultObject<List<String>> loadResourceUserList(String resourceCode, String branchId);

    /**
     * 注册用户回滚
     *
     * @param userId 用户id
     * @return ResultObject
     */
    ResultObject rollbackRegister(String userId);

    /**
     * 代理人添加用户
     *
     * @param userRequestList
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<SaveUserResponse>> agentSaveUsers(List<SaveUserRequest> userRequestList, Users currentLoginUsers);

    /**
     * 代理人添加用户
     *
     * @param userRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<SaveUserResponse> agentSaveUsers(SaveUserRequest userRequest, Users currentLoginUsers);

    /**
     * 获取有该业务配置的角色对应用户
     * @param businessCode 业务编码
     * @param branchId 机构ID
     * @return
     */
    ResultObject<List<String>> loadBusinessUserRecentList(String businessCode, String branchId,String branchMode);

    /**
     * 获取有该业务配置的角色对应用户
     * @param businessCode 业务编码
     * @param branchId 机构ID
     * @return
     */
    ResultObject<List<String>> loadBusinessUserRecentListNew(String businessCode);

    /**
     * 检验用户信息是否存在
     * @param userName
     * @param appRequestHeads
     * @return
     */
    ResultObject checkUserWhetherExist(String userName, AppRequestHeads appRequestHeads);

    /**
     * 检验验证码有效性
     * @param smsVerifyCodeCheckRequest
     * @return
     */
    ResultObject getVerifyCodeCheck(SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest);


    /**
     * 获取登录（或找回密码）验证码
     * @param userName
     * @param appRequestHeads
     * @return
     */
    ResultObject getFindPasswordVerifyCodeGenerator(String userName, AppRequestHeads appRequestHeads);

    /**
     * 重置密码
     * @param request
     * @return
     */
    ResultObject resetPassword(UserLoginPasswordResetRequest request);


    /**
     * 根据手机号码和设备渠道获取用户信息
     *
     * @param mobile
     * @param deviceChannel
     * @return
     */
    ResultObject<UserResponse> getUserByMobileAndDevice(String mobile, String deviceChannel);

    /**
     * 绑定手机号
     *   1.查询该手机号是否注册用户
     *      (1).已注册用户：删除当前用户，更新该手机号对应用户并返回
     *      (2).未注册用户：更新当前用户并返回
     * @param mobile 手机号
     * @param users 当前用户
     * @param appRequestHeads
     * @return
     */
    ResultObject<UserBaseResponse> bindMobile(String mobile, Users users, AppRequestHeads appRequestHeads);

    /**
     * 绑定邮箱
     *   1.查询该邮箱是否绑定用户
     *      (1).已绑定用户：
     *          a.绑定用户无手机号，删除该用户并更新当前用户
     *          b.绑定用户有手机号，抛出错误提示信息
     *      (2).未绑定用户：更新当前用户
     * @param email 邮箱
     * @param users 当前用户
     * @param appRequestHeads
     * @return
     */
    ResultObject bindEmail(String email, Users users, AppRequestHeads appRequestHeads);

    /**
     * 绑定手机号
     *   1.查询该手机号是否注册用户
     *      (1).已注册用户：删除当前用户，更新该手机号对应用户并返回
     *      (2).未注册用户：更新当前用户并返回
     * @param mobile
     * @param userId
     * @param deviceChannel
     * @return
     */
    ResultObject<UserBaseResponse> baseBindMobile(String mobile, String userId, String deviceChannel);

    /**
     * 无需登录 绑定邮箱
     *
     * @param email
     * @param userId
     * @param deviceChannel
     * @return
     */
    ResultObject baseBindEmail(String email, String userId, String deviceChannel);

    /**
     * 无需登录 修改用户信息
     *
     * @param userInfoRequest
     * @param userId
     * @param deviceChannel
     * @return
     */
    ResultObject baseUserInfoPut(UserInfoRequest userInfoRequest, String userId, String deviceChannel);

    /**
     * 处理暂时用户到期状态为禁用
     * @return
     */
    ResultObject temporarilyUserUpdate();
}
