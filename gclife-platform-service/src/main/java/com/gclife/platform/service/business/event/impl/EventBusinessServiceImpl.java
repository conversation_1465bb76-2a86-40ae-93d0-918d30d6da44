package com.gclife.platform.service.business.event.impl;

import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.EventBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.EventBaseService;
import com.gclife.platform.core.jooq.tables.pojos.EventPo;
import com.gclife.platform.model.request.EventRequest;
import com.gclife.platform.model.response.EventResponse;
import com.gclife.platform.service.business.MessageBusinessService;
import com.gclife.platform.service.business.event.EventBusinessService;
import com.gclife.platform.validate.parameter.EventParameterValidate;
import com.gclife.platform.validate.transfer.EventTransfer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-2-26
 * description:
 */
@Service
public class EventBusinessServiceImpl extends BaseBusinessServiceImpl implements EventBusinessService {

    @Autowired
    EventBaseService eventBaseService;

    @Autowired
    EventTransfer eventTransfer;

    @Autowired
    private EventParameterValidate eventParameterValidate;

    @Autowired
    private MessageBusinessService messageBusinessService;

    @Override
    public ResultObject<BasePageResponse<EventResponse>> getEventInfo(BasePageRequest basePageRequest) {
        ResultObject<BasePageResponse<EventResponse>> resultObject = new ResultObject<>();
        List<EventBo> eventBos = eventBaseService.queryEventBo(basePageRequest);
        if (AssertUtils.isNotEmpty(eventBos)) {
            BasePageResponse<EventResponse> eventResponseBasePageResponse = BasePageResponse.getData(basePageRequest.getCurrentPage(), basePageRequest.getPageSize()
                    , eventBos.get(0).getTotalLine(), eventTransfer.transferEventData(eventBos));
            resultObject.setData(eventResponseBasePageResponse);
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject likeOrForward(Users users, String dynamicType, String eventId) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(getLogger(), dynamicType, PlatformErrorConfigEnum.PLATFORM_EVENT_DYNAMIC_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), eventId, PlatformErrorConfigEnum.PLATFORM_EVENT_EVENT_ID_IS_NOT_NULL);
        EventPo eventPo = eventBaseService.queryOneEventPo(eventId);
        AssertUtils.isNotNull(getLogger(), eventPo, PlatformErrorConfigEnum.PLATFORM_BUSINESS_EVENT_IS_NOT_FOUND_OBJECT);
        //统计数据
        eventBaseService.saveEventPo(eventTransfer.transferEvent(eventPo, dynamicType));
        //保存动态
        eventBaseService.saveEventDynamicPo(eventTransfer.transferEventDynamic(eventPo, dynamicType, users));
        try {
            //发送消息
            EventBo eventBo = eventBaseService.queryOneEventBo(eventId);
            String businessCode = null;
            if (PlatformTermEnum.EVENT_BUSINESS_TYPE.PLAN.name().equals(eventBo.getBusinessType())) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.APPLY_PLAN_DIANZAN_REMIND_FOR_AGENT.name();
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.HAPPY_NEWS.name().equals(eventBo.getBusinessType())) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.APPLY_UNDERWRITE_DIANZAN_REMIND_FOR_AGENT.name();
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INSURE.name().equals(eventBo.getBusinessType())) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.APPLY_ACCEPT_DIANZAN_REMIND_FOR_AGENT.name();
            } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INCREASE.name().equals(eventBo.getBusinessType())) {
                businessCode = PlatformTermEnum.MSG_BUSINESS_TYPE.APPLY_ADD_MEMBER_DIANZAN_REMIND_FOR_AGENT.name();
            }
            if (AssertUtils.isNotEmpty(businessCode)) {
                //发送点赞消息
                messageBusinessService.pushEventMessageSingle(businessCode, eventBo, eventBo.getUserId());
            }
        } catch (Exception e) {

        }
        return resultObject;
    }

    @Override
    public ResultObject<EventResponse> queryOneEventInfo(String eventId) {
        ResultObject<EventResponse> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), eventId, PlatformErrorConfigEnum.PLATFORM_EVENT_EVENT_ID_IS_NOT_NULL);
        EventBo eventBo = eventBaseService.queryOneEventBo(eventId);
        resultObject.setData(eventTransfer.transferEventBo(eventBo));
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<EventResponse> saveEventInfo(EventRequest eventRequest) {
        ResultObject<EventResponse> resultObject = new ResultObject<>();
        eventParameterValidate.validateEventParameter(eventRequest);
        EventPo eventPo = eventTransfer.transferEventPo(eventRequest);
        eventBaseService.saveEventPo(eventPo);
        if (PlatformTermEnum.EVENT_BUSINESS_TYPE.HAPPY_NEWS.name().equals(eventRequest.getBusinessType())) {
            //喜报
            eventBaseService.saveEventHappyNews(eventTransfer.transferEventHappyNews(eventRequest, eventPo));
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INCREASE.name().equals(eventRequest.getBusinessType())) {
            //增员
            eventBaseService.saveEventIncrease(eventTransfer.transferEventIncrease(eventRequest, eventPo));
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.PLAN.name().equals(eventRequest.getBusinessType())) {
            //计划书
            eventBaseService.saveEventPlan(eventTransfer.transferEventPlan(eventRequest, eventPo));
        } else if (PlatformTermEnum.EVENT_BUSINESS_TYPE.INSURE.name().equals(eventRequest.getBusinessType())) {
            //交单
            eventBaseService.saveEventInsure(eventTransfer.transferEventInsure(eventRequest, eventPo));
        }
        resultObject.setData((EventResponse) converterObject(eventPo, EventResponse.class));
        return resultObject;
    }
}





