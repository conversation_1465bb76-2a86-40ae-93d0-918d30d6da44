package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.base.model.bo.CareerDo;
import com.gclife.platform.base.model.bo.CareerTreeDo;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.CareerBaseService;
import com.gclife.platform.base.service.InternationalBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BaseCareerPo;
import com.gclife.platform.model.response.*;
import com.gclife.platform.service.business.CareerBusinessService;
import org.codehaus.janino.IClass;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码接口实现
 */
@Service
public class CareerBusinessServiceImpl extends BaseBusinessServiceImpl implements CareerBusinessService {
    @Autowired
    private InternationalBaseService internationalBaseService;
    @Autowired
    private CareerBaseService careerBaseService;

    @Override
    public ResultObject<List<CareerResponse>> getCareerById(String careerId, String providerId, Users users, String transformLanguage) {
        ResultObject<List<CareerResponse>> resultObject = new ResultObject<>();
        try {
            if (StringUtil.isNullString(careerId)) {
                //设置根节点编码ID
                careerId = "0000";
            }
            List<CareerDo> careerDos = careerBaseService.queryCareerChilds(careerId, providerId);
            if (!AssertUtils.isNotEmpty(careerDos)) {
                return resultObject;
            }
            //List<InternationalDo> syscodeBos = internationalBaseService.queryInternational(PlatformTermEnum.CAREER.CAREER.name(), users.getLanguage());
            List<InternationalDo> syscodeBos = internationalBaseService.queryInternational(PlatformTermEnum.CAREER.CAREER.name(), AssertUtils.isNotEmpty(transformLanguage) ? transformLanguage : users.getLanguage());

            if (AssertUtils.isNotEmpty(syscodeBos)) {
                careerDos.forEach(careerResponse -> {
                    syscodeBos.stream().filter(syscodeBo -> syscodeBo.getCodeKey().equals(careerResponse.getCareerId())).findFirst().ifPresent(syscodeBo -> {
                        careerResponse.setCareerName(syscodeBo.getCodeName());
                    });
                });
            }
            //转换
            List<CareerResponse> careerResponses = (List<CareerResponse>) this.converterList(careerDos, new TypeToken<List<CareerResponse>>() {
            }.getType());
            if (AssertUtils.isNotNull(careerResponses)) {
                for (CareerResponse careerResponse : careerResponses) {
                    if (AssertUtils.isNotNull(careerResponse)) {
                        BaseCareerPo careerPo = careerBaseService.queryOneCareerPo(careerResponse.getCareerId());
                        String name = careerResponse.getCareerName();
                        if ("PRO8888888888888".equals(careerPo.getProviderId())) {
                            name = careerResponse.getCareerId() + " " + careerResponse.getCareerName();
                        }
                        careerResponse.setCareerIdName(name);
                    }
                }
            }
            resultObject.setData(careerResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CareerNewResponse> getCareerInfoById(String careerId, String language) {
        ResultObject<CareerNewResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            CareerNewResponse careerResponse = (CareerNewResponse) this.converterObject(careerBaseService.queryOneCareerPo(careerId), CareerNewResponse.class);
            if (AssertUtils.isNotNull(careerResponse)) {
                InternationalDo syscodeBo = internationalBaseService.queryOneInternational(TerminologyTypeEnum.CAREER.name(), careerResponse.getCareerId(), language);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    careerResponse.setCareerName(syscodeBo.getCodeName());
                }
                String name = careerResponse.getCareerName();
                if ("PRO8888888888888".equals(careerResponse.getProviderId())) {
                    name = careerResponse.getCareerId() + " " + careerResponse.getCareerName();
                }
                careerResponse.setCareerIdName(name);
            }
            resultObject.setData(careerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CareerResponse> getCareerInfoByIdTest(String careerId, String language) {
        ResultObject<CareerResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            CareerResponse careerResponse = (CareerResponse) this.converterObject(careerBaseService.queryOneCareerPo(careerId), CareerResponse.class);
            /*if (AssertUtils.isNotNull(careerResponse)) {
                InternationalDo syscodeBo = internationalBaseService.queryOneInternational(TerminologyTypeEnum.CAREER.name(), careerResponse.getCareerId(), language);
                if (AssertUtils.isNotNull(syscodeBo)) {
                    careerResponse.setCareerName(syscodeBo.getCodeName());
                }
            }*/
            resultObject.setData(careerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CareerResponse>> postCareerInfoByIds(List<String> careerIds, String language) {
        ResultObject<List<CareerResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), careerIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            List<BaseCareerPo> careerBos = careerBaseService.queryCareerPoByIdsOrCodes(careerIds);
            List<CareerResponse> careerResponses = (List<CareerResponse>) this.converterList(careerBos, new TypeToken<List<CareerResponse>>() {
            }.getType());
            if (AssertUtils.isNotEmpty(careerResponses)) {
                List<InternationalDo> internationalDos = internationalBaseService.queryInternational(TerminologyTypeEnum.CAREER.name(), language);
                if (AssertUtils.isNotEmpty(internationalDos)) {
                    careerResponses.forEach(careerResponse -> {
                        internationalDos.stream().filter(internationalDo -> internationalDo.getCodeKey().equals(careerResponse.getCareerId())).findFirst().ifPresent(international -> {
                            careerResponse.setCareerName(international.getCodeName());
                            String name = careerResponse.getCareerName();
                            if ("PRO8888888888888".equals(careerResponse.getProviderId())) {
                                name = careerResponse.getCareerId() + " " + careerResponse.getCareerName();
                            }
                            careerResponse.setCareerIdName(name);
                        });
                    });
                }
            }
            resultObject.setData(careerResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CareerNameResponse> getCareerNameById(String careerId, Users users) {
        ResultObject<CareerNameResponse> resultObject = new ResultObject<>();
        CareerNameResponse careerNameResponse = new CareerNameResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);

            //查询地址树
            List<CareerDo> careerTreeResponses = careerBaseService.queryCareerParentTreeList(careerId);

            AssertUtils.isNotEmpty(this.getLogger(), careerTreeResponses, PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);

            //从国际化文本中取name
            careerTreeResponses.forEach(careerResponse -> {
                InternationalDo syscodeBo = internationalBaseService.queryOneInternational(PlatformTermEnum.CAREER.CAREER.name(), careerResponse.getCareerId(), users.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    careerResponse.setCareerName(syscodeBo.getCodeName());
                }
            });

            List<String> careerNames = new ArrayList<>();
            List<String> careerIdNames = new ArrayList<>();
            careerTreeResponses.forEach(careerResponse -> careerNames.add(careerResponse.getCareerName()));
            String careerName = StringUtils.join(careerNames.toArray(), "/");
            careerNameResponse.setCareerName(careerName);
            for (CareerDo careerTreeResponse : careerTreeResponses) {
                careerNameResponse.setCareerId(careerTreeResponse.getCareerId());
                if (AssertUtils.isNotNull(careerTreeResponse)) {
                    BaseCareerPo careerPo = careerBaseService.queryOneCareerPo(careerTreeResponse.getCareerId());
                    String name = careerTreeResponse.getCareerName();
                    if ("PRO8888888888888".equals(careerPo.getProviderId())) {
                        name = careerTreeResponse.getCareerId() + " " + careerTreeResponse.getCareerName();
                    }
                    careerIdNames.add(name);
                }
            }
            String careerIdName = StringUtils.join(careerIdNames.toArray(), "/");
            careerNameResponse.setCareerIdName(careerIdName);

            //返回数据
            resultObject.setData(careerNameResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据Id获取职业父代码列表
     *
     * @param careerId 职业ID
     * @return CareerResponse
     */
    @Override
    public ResultObject<List<CareerTreeResponse>> getCareerParentsById(String careerId, Users users) {
        ResultObject<List<CareerTreeResponse>> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), careerId, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            //查询地址树
            List<CareerDo> careerDos = careerBaseService.queryCareerParentTreeList(careerId);
            AssertUtils.isNotEmpty(this.getLogger(), careerDos, PlatformErrorConfigEnum.PLATFORM_CAREER_IS_NOT_FOUND_OBJECT);
            //从国际化文本中取name
            careerDos.forEach(careerResponse -> {
                InternationalDo syscodeBo = internationalBaseService.queryOneInternational(PlatformTermEnum.CAREER.CAREER.name(), careerResponse.getCareerId(), users.getLanguage());
                if (AssertUtils.isNotNull(syscodeBo)) {
                    careerResponse.setCareerName(syscodeBo.getCodeName());
                }
            });
            //转换
            List<CareerTreeResponse> careerResponses = (List<CareerTreeResponse>) this.converterList(careerDos, new TypeToken<List<CareerTreeResponse>>() {
            }.getType());
            if (AssertUtils.isNotNull(careerResponses)) {
                for (CareerTreeResponse careerTreeResponse : careerResponses) {
                    if (AssertUtils.isNotNull(careerTreeResponse)) {
                        BaseCareerPo careerPo = careerBaseService.queryOneCareerPo(careerTreeResponse.getCareerId());
                        String name = careerTreeResponse.getCareerName();
                        if ("PRO8888888888888".equals(careerPo.getProviderId())) {
                            name = careerTreeResponse.getCareerId() + " " + careerTreeResponse.getCareerName();
                        }
                        careerTreeResponse.setCareerIdName(name);
                    }
                }
            }
            resultObject.setData(careerResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(String providerId) {
        ResultObject<List<CareerBaseTreeResponse>> resultObject = new ResultObject<>();
        try {
            List<CareerTreeDo> careerTreeDos = careerBaseService.queryCareerAllTree(providerId);
            if (AssertUtils.isNotEmpty(careerTreeDos)) {
                List<CareerBaseTreeResponse> careerResponses = (List<CareerBaseTreeResponse>) this.converterList(careerTreeDos, new TypeToken<List<CareerBaseTreeResponse>>() {
                }.getType());
                for (CareerBaseTreeResponse careerResponse : careerResponses) {
                    if (AssertUtils.isNotNull(careerResponse) && "PRO8888888888888".equals(careerResponse.getProviderId())) {
                        careerResponse.setCareerIdName(careerResponse.getCareerId() + " " + careerResponse.getCareerName());
                        List<CareerBaseTreeResponse> childs = careerResponse.getChilds();
                        if (AssertUtils.isNotNull(childs)) {
                            for (CareerBaseTreeResponse child : childs) {
                                if (AssertUtils.isNotNull(child)) {
                                    child.setCareerIdName(child.getCareerId() + " " + child.getCareerName());
                                    List<CareerBaseTreeResponse> childChilds = child.getChilds();
                                    if (AssertUtils.isNotNull(childChilds)) {
                                        for (CareerBaseTreeResponse childChild : childChilds) {
                                            childChild.setCareerIdName(childChild.getCareerId() + " " + childChild.getCareerName());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                resultObject.setData(careerResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return resultObject;
    }

    /**
     * 根据叶子节点ID获取父类职业名称
     *
     * @param careerIds
     * @param users
     * @return
     */
    @Override
    public ResultObject<List<CareerNameResponse>> postCareerName(List<String> careerIds, Users users) {
        ResultObject<List<CareerNameResponse>> resultObject = new ResultObject<>();
        List<CareerNameResponse> careerNameListResponses = new ArrayList<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), careerIds, PlatformErrorConfigEnum.PLATFORM_PARAMETER_CAREER_ID_IS_NOT_NULL);
            List<String> newCareerIds = careerIds.stream().distinct().collect(Collectors.toList());
            newCareerIds.forEach(careerId -> {
                CareerNameResponse careerNameResponse = new CareerNameResponse();
                //查询地址树
                List<CareerDo> careerTreeResponses = careerBaseService.queryCareerParentTreeList(careerId);
                if (AssertUtils.isNotEmpty(careerTreeResponses)) {
                    //从国际化文本中取name
                    careerTreeResponses.forEach(careerResponse -> {
                        InternationalDo syscodeBo = internationalBaseService.queryOneInternational(PlatformTermEnum.CAREER.CAREER.name(), careerResponse.getCareerId(), users.getLanguage());
                        if (AssertUtils.isNotNull(syscodeBo)) {
                            careerResponse.setCareerName(syscodeBo.getCodeName());
                        }
                    });
                    List<String> careerNames = new ArrayList<>();
                    List<Object> careerIdNames = new ArrayList<>();
                    careerTreeResponses.forEach(careerResponse -> careerNames.add(careerResponse.getCareerName()));
                    String careerName = StringUtils.join(careerNames.toArray(), "/");
                    careerNameResponse.setCareerName(careerName);
                    careerNameResponse.setCareerId(careerId);
                    if (AssertUtils.isNotNull(careerTreeResponses)) {
                        for (CareerDo careerTreeResponse : careerTreeResponses) {
                            if (AssertUtils.isNotNull(careerTreeResponse)) {
                                BaseCareerPo careerPo = careerBaseService.queryOneCareerPo(careerTreeResponse.getCareerId());
                                String name = careerTreeResponse.getCareerName();
                                if ("PRO8888888888888".equals(careerPo.getProviderId())) {
                                    name = careerTreeResponse.getCareerId() + " " + careerTreeResponse.getCareerName();
                                }
                                careerIdNames.add(name);
                            }
                        }
                    }
                    String careerIdName = StringUtils.join(careerIdNames.toArray(), "/");
                    careerNameResponse.setCareerIdName(careerIdName);

                    careerNameListResponses.add(careerNameResponse);
                }
            });
            resultObject.setData(careerNameListResponses);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return resultObject;
    }

}