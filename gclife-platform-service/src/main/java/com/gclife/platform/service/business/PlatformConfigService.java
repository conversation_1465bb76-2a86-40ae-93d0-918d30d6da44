package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.BaseFactorConfigResponse;
import com.gclife.platform.model.response.BranchConfigResponse;
import com.gclife.platform.model.response.NotifyConfigResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-1-23
 * description:
 */
public interface PlatformConfigService extends BaseBusinessService {

    /**
     * 查询顶级机构配置
     *
     * @param branchId   机构id
     * @param configType 配置类型
     * @return BranchConfigResponse
     */
    ResultObject<BranchConfigResponse> getBranchConfig(String branchId, String configType);

    /**
     * 查询支付配置回调url
     *
     * @param businessType 业务类型
     * @return NotifyConfigResponse
     */
    ResultObject<NotifyConfigResponse> getNotifyConfig(String businessType);

    /**
     * 查询基础要素配置
     *
     * @param configCode 配置编码
     * @return BaseFactorConfigResponse
     */
    ResultObject<BaseFactorConfigResponse> getBaseFactorConfig(String configCode);

    /**
     * 内勤登录微信验证开关
     *
     * @return ResultObject
     */
    ResultObject editWeChatSwitch();
}
