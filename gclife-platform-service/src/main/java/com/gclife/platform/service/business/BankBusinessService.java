package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.BankResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
public interface BankBusinessService extends BaseBusinessService {
    /**
     * 返回银行表数据
     *
     * @param bankType
     * @param bankCode
     * @param language
     * @return
     */
    ResultObject<BankResponse> getBankById(String bankType, String bankCode, String language);

    /**
     * 银行列表
     *
     * @return
     */
    ResultObject<List<BankResponse>> getBanks(String language);

    /**
     * 扣款银行列表
     * @param language 语言
     * @return
     */
    ResultObject<List<BankResponse>> getDeductionBanks(String language);

    /**
     * 根据银行类型查询银行列表
     * @param bankType 银行类型
     * @param validFlag 有效标识
     * @return
     */
    ResultObject<List<BankResponse>> listBank(String bankType, String validFlag);
}