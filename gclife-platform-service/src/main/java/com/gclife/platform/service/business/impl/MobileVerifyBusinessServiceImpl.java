package com.gclife.platform.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.service.business.MobileVerifyBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
@Service
public class MobileVerifyBusinessServiceImpl extends BaseBusinessServiceImpl implements MobileVerifyBusinessService {

    @Autowired
    private UsersExtDao usersExtDao;

    /**
     * 手机号验证
     *
     * @param countryCode
     * @param mobile
     * @param deviceChannel
     * @return
     */
    @Override
    public ResultObject<UsersPo> getMobileVerify(String countryCode, String mobile, String deviceChannel) {
        ResultObject<UsersPo> resultObject = new ResultObject<>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), countryCode, PlatformErrorConfigEnum.USERS_PARAMETER_AREA_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), mobile, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), deviceChannel, PlatformErrorConfigEnum.USERS_PARAMETER_DEVICE_CHANNEL_IS_NOT_NULL);

            //查询数据
            UsersBo usersBo = usersExtDao.loadMobileVerify(countryCode, mobile, deviceChannel);

            resultObject.setData(usersBo);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_QUERY_MOBILE_ERROR);
            }
        }

        return resultObject;
    }

    @Override
    public ResultObject getRecommendMobile(String mobile, String deviceChannel) {
        ResultObject<UsersPo> resultObject = new ResultObject<>();
        try {
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), mobile, PlatformErrorConfigEnum.USERS_PARAMETER_MOBILE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), deviceChannel, PlatformErrorConfigEnum.USERS_PARAMETER_DEVICE_CHANNEL_IS_NOT_NULL);

            UsersBo usersBo = usersExtDao.loadMobileVerify(null, mobile, deviceChannel);
            if (null == usersBo) {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_BUSINESS_USER_IS_NOT_FOUND_OBJECT);
                return resultObject;
            }
            resultObject.setData(usersBo);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.USERS_QUERY_MOBILE_ERROR);
            }
        }

        return resultObject;
    }
}