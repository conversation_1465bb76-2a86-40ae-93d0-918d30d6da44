package com.gclife.platform.service.business.base;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-29
 * description:
 */
public interface AreaBaseBusinessService extends BaseBusinessService {
    /**
     * 查询地址孩子集合
     *
     * @param areaId 　父地址ID
     * @return List<AreaDo>  ResultObject<List<AreaResponse>>
     */
    ResultObject<List<AreaResponse>> queryAreaChilds(String areaId);

    /**
     * 查询地址信息
     *
     * @param areaId 地址ID
     * @return AreaBo ResultObject<AreaResponse>
     */
    ResultObject<AreaResponse> queryOneAreaPo(String areaId);

    /**
     * 根据地址ids或者地址codes获取地址详情
     *
     * @param areaIds 地址ID
     * @return ResultObject<List<AreaResponse>>
     */
    ResultObject<List<AreaResponse>> queryAreaPoByIdsOrCodes(List<String> areaIds);

    /**
     * 地址树集合
     *
     * @param areaId 　地址ID或地址编码
     * @return ResultObject<List<AreaResponse>>
     */
    ResultObject<List<AreaTreeResponse>> queryAreaParentTreeList(String areaId);
}
