package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.UserLoginLogResponse;
import com.gclife.platform.model.response.UserLoginCheckResponse;

/**
 * <AUTHOR>
 * create 17-12-13
 * description:
 */
public interface UserLoginLogBusinessService extends BaseBusinessService {
    /**
     * 保存用户登录日志
     * @param appRequestHeads
     * @param userId
     * @param error
     * @return
     */
    ResultObject postUserLoginLog(AppRequestHeads appRequestHeads,String userId, String error);

    /**
     * 保存用户登录日志
     * @param appRequestHeads
     * @param users
     * @return
     */
    ResultObject postUserLoginLog(AppRequestHeads appRequestHeads, Users users);

    /**
     * 用户退出登录
     * @param appRequestHeads
     * @param users
     * @return
     */
    ResultObject postUserLogout(AppRequestHeads appRequestHeads, Users users);

    /**
     * 用户退出登录
     * @param appRequestHeads
     * @param users
     * @return
     */
    ResultObject postUserAgentLogout(AppRequestHeads appRequestHeads, Users users,String userId);

    /**
     * 查询用户最新的登录日志
     * @param userId 当前用户
     * @return
     */
    ResultObject<UserLoginLogResponse> loadUserAppNewestLoginLog(String userId);

    /**
     * 查询用户最新的登录统计信息
     * @param countryCode 地区区号
     * @param mobile 手机号码
     * @param deviceChannel 设备渠道类型
     * @return
     */
    ResultObject<UserLoginCheckResponse> appUserLoginCheck(String countryCode, String mobile, String deviceChannel);

    /**
     * 短暂业务员用户登录校验
     * @param userName
     * @return
     */
    ResultObject userValidCheck(String userName);
}
