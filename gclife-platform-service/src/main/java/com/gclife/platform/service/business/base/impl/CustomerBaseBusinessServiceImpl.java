package com.gclife.platform.service.business.base.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.vo.CustomerQueryVo;
import com.gclife.platform.base.service.CustomerBaseService;
import com.gclife.platform.base.service.CustomerHistoryBaseService;
import com.gclife.platform.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerHistoryPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerPo;
import com.gclife.platform.model.request.CustomerMessagesRequest;
import com.gclife.platform.model.request.EndorseCustomerRequest;
import com.gclife.platform.model.response.CustomerAgentResponse;
import com.gclife.platform.model.response.CustomerHistoryResponse;
import com.gclife.platform.model.response.CustomerMessageResponse;
import com.gclife.platform.model.response.CustomerSynchResponse;
import com.gclife.platform.service.business.base.CustomerBaseBusinessService;
import com.gclife.platform.validate.parameter.CustomerParameterValidate;
import com.gclife.platform.validate.transfer.CustomerBaseTransfer;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.gclife.platform.base.model.config.PlatformErrorConfigEnum.PLATFORM_CUSTOMER_ID_IS_NOT_NULL;
import static com.gclife.platform.base.model.config.PlatformErrorConfigEnum.PLATFORM_CUSTOMER_VERSION_NO_IS_NOT_NULL;

/**
 * <AUTHOR>
 * create 18-9-7
 * description:
 */
@Service
public class CustomerBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements CustomerBaseBusinessService {

    @Autowired
    private CustomerBaseService customerBaseService;

    @Autowired
    private CustomerHistoryBaseService customerHistoryBaseService;

    @Autowired
    private CustomerParameterValidate customerParameterValidate;

    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;

    /**
     * 根据客户ID获取客户信息
     *
     * @return
     */
    @Override
    public ResultObject<CustomerMessageResponse> getBaseCustomer(String customerId, String versionNo) {
        ResultObject<CustomerMessageResponse> resultObject = new ResultObject<>();
        try {
            CustomerMessageResponse customerMessageResponse = null;
            AssertUtils.isNotEmpty(getLogger(), customerId, PLATFORM_CUSTOMER_ID_IS_NOT_NULL);
            CustomerPo customerPo = customerBaseService.queryOneCustomer(customerId, versionNo);
            if(AssertUtils.isNotNull(customerPo)) {
                customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerPo, CustomerMessageResponse.class);
            }else{
                CustomerHistoryPo customerHistoryPo=customerHistoryBaseService.queryOneCustomerHistory(customerId, versionNo);
                if(AssertUtils.isNotNull(customerHistoryPo)){
                    customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerHistoryPo, CustomerMessageResponse.class);
                }
            }
            resultObject.setData(customerMessageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<CustomerHistoryResponse> getBaseCustomerHistory(String customerId, String versionNo) {
        ResultObject<CustomerHistoryResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), customerId, PLATFORM_CUSTOMER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), versionNo, PLATFORM_CUSTOMER_VERSION_NO_IS_NOT_NULL);
            CustomerHistoryPo customerHistoryPo = customerHistoryBaseService.queryOneCustomerHistory(customerId, versionNo);
            CustomerHistoryResponse customerHistoryResponse = (CustomerHistoryResponse) this.converterObject(customerHistoryPo, CustomerHistoryResponse.class);
            resultObject.setData(customerHistoryResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_HISTORY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    @Override
    public ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomerList(AppRequestHeads appRequestHandler, CustomerMessagesRequest customerMessagesRequest) {
        ResultObject<BasePageResponse<CustomerMessageResponse>> resultObject = new ResultObject<>();
        try {
            List<CustomerPo> customerPoList = customerBaseService.queryCustomerByKeyword((CustomerQueryVo) this.converterObject(customerMessagesRequest, CustomerQueryVo.class));
            List<CustomerMessageResponse> customerMessageResponseList = (List<CustomerMessageResponse>) this.converterList(customerPoList, new TypeToken<List<CustomerMessageResponse>>() {
            }.getType());

            //获取总页数
            int totalLine = AssertUtils.isNotEmpty(customerMessageResponseList) ? customerMessageResponseList.size() : 0;
            BasePageResponse basePageResponse = BasePageResponse.getData(customerMessagesRequest.getCurrentPage(), customerMessagesRequest.getPageSize(), totalLine, customerMessageResponseList);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject synchronizeBaseCustomer(EndorseCustomerRequest endorseCustomerRequest, Users users) {
        ResultObject<CustomerSynchResponse> resultObject = new ResultObject<>();
        try {
            customerParameterValidate.validCustomerData(endorseCustomerRequest, users);
            CustomerPo customerPo = customerBaseService.queryOneCustomer(endorseCustomerRequest.getCustomerId(),null);
            AssertUtils.isNotNull(getLogger(), customerPo, PlatformErrorConfigEnum.PLATFORM_UPDATE_CUSTOMER_IS_NULL);
            //备份客户历史数据
            CustomerHistoryPo customerHistoryPo = (CustomerHistoryPo) converterObject(customerPo, CustomerHistoryPo.class);
            customerHistoryBaseService.saveCustomerHistoryPo(customerHistoryPo);

            // 旧版本号
            String oldVersion = customerPo.getVersionNo();
            //修改当前最新历史信息，版本更新
            customerBaseTransfer.transformCustomerBase(customerPo, endorseCustomerRequest);
            // 新版本号
            String newVersion = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
            customerPo.setVersionNo(newVersion);
            customerBaseService.saveCustomerPo(customerPo);

            resultObject.setData(new CustomerSynchResponse(customerPo.getCustomerId(), oldVersion, newVersion));
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_CUSTOMER_SYNCHRONIZE_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerAgentResponse>> getAgentCustomerRelation(String customerId) {
        ResultObject<List<CustomerAgentResponse>> resultObject = new ResultObject<>();
        try {
            List<CustomerAgentPo> customerAgentPos = customerBaseService.queryCustomerAgentByCustomerId(customerId);
            List<CustomerAgentResponse> customerAgentResponseList = (List<CustomerAgentResponse>) this.converterList(customerAgentPos, new TypeToken<List<CustomerAgentResponse>>() {
            }.getType());
            resultObject.setData(customerAgentResponseList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    /**
     * 回滚客户信息
     * @param customerId 客户ID
     * @param oldVersionNo 旧版本号
     * @return
     */
    @Override
    @Transactional
    public ResultObject rollbackCustomer(String customerId, String oldVersionNo) {
        ResultObject resultObject = new ResultObject();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), customerId, PlatformErrorConfigEnum.PLATFORM_CUSTOMER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), oldVersionNo, PlatformErrorConfigEnum.PLATFORM_CUSTOMER_VERSION_NO_IS_NOT_NULL);

            // 回滚客户数据
            customerHistoryBaseService.rollbackCustomerHistory(customerId, oldVersionNo);
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PlatformErrorConfigEnum.PLATFORM_ROLLBACK_CUSTOMER_ERROR);
        }
        return resultObject;
    }
}
