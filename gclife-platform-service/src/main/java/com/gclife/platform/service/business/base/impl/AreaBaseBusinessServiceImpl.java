package com.gclife.platform.service.business.base.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.AreaDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.service.AreaBaseService;
import com.gclife.platform.core.jooq.tables.pojos.BaseAreaPo;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.platform.service.business.base.AreaBaseBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-29
 * description:
 */
@Service
public class AreaBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements AreaBaseBusinessService {
    @Autowired
    private AreaBaseService areaBaseService;

    @Override
    public ResultObject<List<AreaResponse>> queryAreaChilds(String areaId) {
        ResultObject<List<AreaResponse>> resultObject = new ResultObject<>();
        try {
            List<AreaDo> areaDos = areaBaseService.queryAreaChildren(AssertUtils.isNotEmpty(areaId) ? areaId : "ROOT");
            //转换
            if (AssertUtils.isNotEmpty(areaDos)) {
                List<AreaResponse> areaResponses = (List<AreaResponse>) this.converterList(areaDos, new TypeToken<List<AreaResponse>>() {
                }.getType());
                resultObject.setData(areaResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AreaResponse> queryOneAreaPo(String areaId) {
        ResultObject<AreaResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);
            BaseAreaPo areaPo = areaBaseService.queryOneAreaPo(areaId);
            AssertUtils.isNotNull(this.getLogger(), areaPo, PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);
            AreaResponse areaResponse = (AreaResponse) this.converterObject(areaPo, AreaResponse.class);
            resultObject.setData(areaResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AreaResponse>> queryAreaPoByIdsOrCodes(List<String> areaIds) {
        ResultObject<List<AreaResponse>> resultObject = new ResultObject<>();
        try {
            List<BaseAreaPo> areaDos = areaBaseService.queryAreaPoByIdsOrCodes(areaIds);
            //转换
            if (AssertUtils.isNotEmpty(areaDos)) {
                List<AreaResponse> areaResponses = (List<AreaResponse>) this.converterList(areaDos, new TypeToken<List<AreaResponse>>() {
                }.getType());
                resultObject.setData(areaResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AreaTreeResponse>> queryAreaParentTreeList(String areaId) {
        ResultObject<List<AreaTreeResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), areaId, PlatformErrorConfigEnum.PLATFORM_AREA_ID_IS_NOT_NULL);
            List<AreaDo> areaDos = areaBaseService.queryAreaParentTreeList(areaId);
            //转换
            if (AssertUtils.isNotEmpty(areaDos)) {
                List<AreaTreeResponse> areaResponses = (List<AreaTreeResponse>) this.converterList(areaDos, new TypeToken<List<AreaTreeResponse>>() {
                }.getType());
                resultObject.setData(areaResponses);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_AREA_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }
}
