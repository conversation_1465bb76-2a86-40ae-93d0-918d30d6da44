package com.gclife.platform.service.data.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.core.jooq.tables.daos.TccDetailDao;
import com.gclife.platform.core.jooq.tables.daos.TccEntryDao;
import com.gclife.platform.core.jooq.tables.pojos.TccDetailPo;
import com.gclife.platform.core.jooq.tables.pojos.TccEntryPo;
import com.gclife.platform.service.data.TccDetailService;
import com.gclife.platform.service.data.TccEntryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午10:45
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Service
public class TccDetailServiceImpl extends BaseServiceImpl implements TccDetailService {

    @Autowired
    TccDetailDao tccDetailDao;

    @Override
    public void saveTccDetailPo(TccDetailPo tccDetailPo) {
        try {
            tccDetailDao.insert(tccDetailPo);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_TCC_DETAIL_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_TCC_DETAIL_ERROR);
        }
    }
}