package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;

/**
 * <AUTHOR>
 *         create 17-11-4
 *         description:
 */
public interface MobileVerifyBusinessService extends BaseBusinessService {
    /**
     * 手机号验证
     *
     * @param countryCode
     * @param mobile
     * @param deviceChannel
     * @return
     */
    ResultObject getMobileVerify(String countryCode, String mobile,String deviceChannel);

    /**
     * 推荐人手机验证
     *
     * @param mobile
     * @param deviceChannel
     * @return
     */
    ResultObject getRecommendMobile(String mobile,String deviceChannel);
}