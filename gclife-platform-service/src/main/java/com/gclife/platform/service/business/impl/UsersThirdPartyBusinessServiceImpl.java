package com.gclife.platform.service.business.impl;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.base.dao.UsersBaseDao;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.base.service.UsersBaseService;
import com.gclife.platform.core.jooq.tables.pojos.UserThirdPartyPo;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.model.request.UserThirdPartyRequest;
import com.gclife.platform.model.response.UserBaseResponse;
import com.gclife.platform.model.response.UserThirdPartyResponse;
import com.gclife.platform.service.business.UsersThirdPartyBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
@Service
public class UsersThirdPartyBusinessServiceImpl extends BaseBusinessServiceImpl implements UsersThirdPartyBusinessService {

    @Autowired
    private UsersBaseService usersBaseService;
    @Autowired
    private UsersBaseDao usersBaseDao;

    /**
     * 查询第三方用户信息
     * @param openId 开放标识
     * @return
     */
    @Override
    public ResultObject<UserBaseResponse> queryUserInfo(String openId) {
        ResultObject<UserBaseResponse> resultObject = ResultObject.success();
        // 查询第三方用户
        UserThirdPartyPo userThirdPartyPo = usersBaseService.queryUserThirdPartyByOpenId(openId);
        if (AssertUtils.isNotNull(userThirdPartyPo)) {
            UsersPo usersPo = usersBaseService.queryById(userThirdPartyPo.getUserId());
            if (AssertUtils.isNotNull(usersPo)) {
                UserBaseResponse userBaseResponse = (UserBaseResponse) this.converterObject(usersPo, UserBaseResponse.class);
                resultObject.setData(userBaseResponse);
            }
        }
        return resultObject;
    }

    /**
     * 保存第三方用户信息
     * @param userThirdPartyRequest
     * @param appRequestHeads
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<UserBaseResponse> saveInfo(UserThirdPartyRequest userThirdPartyRequest, AppRequestHeads appRequestHeads) {
        // 参数校验
        AssertUtils.isNotEmpty(getLogger(), userThirdPartyRequest.getOpenId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_THIRD_PARTY_OPENID_IS_NOT_NULL);
        // 查询第三方用户
        UserThirdPartyPo userThirdPartyPo = usersBaseService.queryUserThirdPartyByOpenId(userThirdPartyRequest.getOpenId());
        UsersPo usersPo = null;
        if (AssertUtils.isNotNull(userThirdPartyPo)) {
            usersPo = usersBaseService.queryById(userThirdPartyPo.getUserId());
        } else {
            userThirdPartyPo = new UserThirdPartyPo();
        }
        String deviceChannel = appRequestHeads.getDeviceChannel();
        if (!AssertUtils.isNotNull(usersPo)) {
            // 以前使用第三方登录过客户APP 但解绑第三方账号之后再次用第三方登录
            String mobile = userThirdPartyPo.getMobile();
            if (AssertUtils.isNotEmpty(mobile)) {
                UsersPo oldMobileUsersPo = usersBaseDao.queryUsersPoByMobileAndDeviceChannel(mobile, deviceChannel);
                if (AssertUtils.isNotNull(oldMobileUsersPo)) {
                    throwsException(getLogger(), PlatformErrorConfigEnum.PLATFORM_ACCOUNT_MOBILE_IS_REGISTERED_ERROR);
                }
            }
            String email = userThirdPartyPo.getEmail();
            if (AssertUtils.isNotEmpty(email)) {
                UsersPo oldEmailUsersPo = usersBaseDao.getUsersPoByEmailAndDeviceChannel(email, deviceChannel);
                if (AssertUtils.isNotNull(oldEmailUsersPo)) {
                    throwsException(getLogger(), PlatformErrorConfigEnum.PLATFORM_ACCOUNT_EMAIL_IS_REGISTERED_ERROR);
                }
            }

            usersPo = new UsersPo();
            usersPo.setUsername(UUIDUtils.getUUIDShort());
            usersPo.setName(userThirdPartyRequest.getNickname());
            usersPo.setNickName(userThirdPartyRequest.getNickname());
            usersPo.setEnabled(TerminologyConfigEnum.ENABLED.ENABLED.name());
            usersPo.setEmail(userThirdPartyRequest.getEmail());
            if (AssertUtils.isNotEmpty(userThirdPartyRequest.getSex())) {
                usersPo.setGender(userThirdPartyRequest.getSex());
            }
            usersPo.setDeviceChannelId(deviceChannel);
        }
        // 保存信息
        usersBaseService.saveUserPo(usersPo);
        ClazzUtils.copyPropertiesIgnoreNull(userThirdPartyRequest, userThirdPartyPo);
        userThirdPartyPo.setUserId(usersPo.getUserId());
        usersBaseService.saveUserThirdParty(userThirdPartyPo);

        UserBaseResponse userBaseResponse = (UserBaseResponse) this.converterObject(usersPo, UserBaseResponse.class);
        ResultObject<UserBaseResponse> resultObject = ResultObject.success();
        resultObject.setData(userBaseResponse);

        return resultObject;
    }

    /**
     * 查询第三方信息
     * @param userId 用户ID
     * @return
     */
    @Override
    public ResultObject<List<UserThirdPartyResponse>> listThirdPartyInfo(String userId) {
        // 查询第三方用户
        List<UserThirdPartyPo> userThirdPartyPos = usersBaseService.listUserThirdParty(userId);
        List<UserThirdPartyResponse> userThirdPartyResponses = (List<UserThirdPartyResponse>) this.converterList(
                userThirdPartyPos, new TypeToken<List<UserThirdPartyResponse>>() {}.getType()
        );
        ResultObject<List<UserThirdPartyResponse>> resultObject = ResultObject.success();
        resultObject.setData(userThirdPartyResponses);
        return resultObject;
    }

    /**
     * 第三方信息绑定
     * @param userThirdPartyRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject bind(UserThirdPartyRequest userThirdPartyRequest) {
        // 参数校验
        AssertUtils.isNotEmpty(getLogger(), userThirdPartyRequest.getOpenId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_THIRD_PARTY_OPENID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), userThirdPartyRequest.getUserId(), PlatformErrorConfigEnum.PLATFORM_PARAMETER_USER_UID_IS_NOT_NULL);
        // 查询第三方用户
        UserThirdPartyPo userThirdPartyPo = usersBaseService.queryUserThirdPartyByOpenId(userThirdPartyRequest.getOpenId());
        if (!AssertUtils.isNotNull(userThirdPartyPo)) {
            userThirdPartyPo = new UserThirdPartyPo();
        }
        ClazzUtils.copyPropertiesIgnoreNull(userThirdPartyRequest, userThirdPartyPo);
        usersBaseService.saveUserThirdParty(userThirdPartyPo);

        return ResultObject.success();
    }

    /**
     * 第三方信息解绑
     * @param userId 用户ID
     * @param thirdPartyCode 第三方平台编码
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject unbind(String userId, String thirdPartyCode) {
        // 查询第三方用户
        UserThirdPartyPo userThirdPartyPo = usersBaseService.queryUserThirdParty(userId, thirdPartyCode);
        if (AssertUtils.isNotNull(userThirdPartyPo)) {
            userThirdPartyPo.setUserId(null);
            userThirdPartyPo.setBindStatus(PlatformTermEnum.BIND_STATUS.UNBIND.name());
            usersBaseService.saveUserThirdParty(userThirdPartyPo);
        }

        return ResultObject.success();
    }
}
