package com.gclife.platform.service.business;

import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.base.model.bo.EventBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 消息处理
 * @date 18-4-13
 */
public interface MessageBusinessService extends BaseBusinessService {
    /**
     * 喜报消息发送(单个用户)
     * @param businessCode 消息类型
     * @param eventBo 喜报单对象
     * @param userId 用户ID
     */
    void pushEventMessageSingle(String businessCode, EventBo eventBo, String userId);

    /**
     * 喜报消息发送(单个用户)
     * @param businessCode 消息类型
     * @param eventBo 喜报对象
     * @param userId 用户ID
     */
    void pushEventMessageSingle(String businessCode, EventBo eventBo, String userId, Map<String, String> map);

    /**
     * 发送业务员银行卡审核提醒 批量用户
     *
     * @param businessCode 业务类型
     * @param userId       用户ID
     */
    void sendBankCardReviewBatchMessage(String businessCode, String userId);

    /**
     * 消息发送
     *
     * @param businessCode 消息类型
     * @param userIds      用户IDS
     * @param messageParam 消息参数
     */
    void pushBusinessMessage(String businessCode, List<String> userIds, String messageParam);
}
