package com.gclife.platform.service.business.base;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.feign.SyscodeReqFc;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.vo.StatusClassResponse;
import com.gclife.platform.vo.SyscodeResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TerminologyBaseBusinessService extends BaseBusinessService {

    /**
     * 根据类型查询术语集合
     * @param codeType　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> querySyscode(String codeType);

    /**
     * 根据code查询术语对象
     * @param codeKey 术语键
     * @param codeType　类型
     * @return  ResultObject<SyscodeResponse>
     */
    ResultObject<SyscodeResponse> queryOneSyscode(String codeType, String codeKey);


    /**
     * 根据类型查询术语集合（国际化）
     * @param codeType　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> queryInternationSyscode(String codeType,String lang);

    /**
     * 根据code查询术语对象（国际化）
     * @param codeKey 术语键
     * @param codeType　类型
     * @return  ResultObject<SyscodeResponse>
     */
    ResultObject<SyscodeResponse> queryOneInternationSyscode(String codeType, String codeKey,String lang);

    /**
     * 根据类型,codes值查询术语集合
     * @param codeKeys　术语key集合
     * @param codeType　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> querySyscodeByCodeKeys(String codeType, List<String> codeKeys);


    /**
     * 根据类型查询术语集合（国际化）
     * @param syscodeReqFc　类型
     * @return  ResultObject<List<SyscodeResponse>>
     */
    public ResultObject<List<SyscodeResponse>> queryInternationSyscodeByCodeKeys(SyscodeForm syscodeReqFc);

    /**
     * 根据类型 key 查询数据
     * @param users 当前用户
     * @param type　国际化请求类
     * @return
     */
    ResultObject<List<StatusClassResponse>> queryStatusClassTerminologys(Users users, String type);

    /**
     * 根据类型 key和状态分类 查询数据
     * @param users 当前用户
     * @param type　国际化请求类
     * @param statusClassCode 状态分类编码
     * @return
     */
    ResultObject<List<SyscodeResponse>> querySyscodeTerminologys(Users users, String type,String statusClassCode);
}
