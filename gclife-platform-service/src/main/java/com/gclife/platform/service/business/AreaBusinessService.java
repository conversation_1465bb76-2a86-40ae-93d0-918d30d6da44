package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.AreaResponse;
import com.gclife.platform.model.response.AreaTreeResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:
 */
public interface AreaBusinessService extends BaseBusinessService {
    /**
     * 根据区域ID获取区域地址
     *
     * @param areaId 地址ID
     * @return
     */
    ResultObject<List<AreaResponse>> getAreaById(String areaId, Users users, AppRequestHeads appRequestHeads);

    /**
     * 根据叶子节点ID获取区域地址树
     *
     * @param areaId   地址ID
     * @param sortType
     * @return
     */
    ResultObject<List<AreaTreeResponse>> getAreaTreeById(String areaId, Users users, String sortType);

    /**
     * 根据叶子节点ID获取区域地址名称
     *
     * @param areaId   地址ID
     * @param sortType
     * @return
     */
    ResultObject<AreaNameResponse> getAreaNameById(String areaId, Users users, String language, String sortType);

    /**
     * 网销根据叶子节点ID获取区域地址名称
     *
     * @param areaId 地址ID
     * @return
     */
    ResultObject<AreaNameResponse> getAreaNameByIdOnline(String areaId,AppRequestHeads appRequestHeads);

    /**
     * 网销根据叶子节点ID获取区域地址名称
     *
     * @param areaId 地址ID
     * @return
     */
    ResultObject<AreaNameResponse> getAreaNameByIdOnlineNormal(String areaId,AppRequestHeads appRequestHeads);

    /**
     * 根据叶子节点ID获取区域地址名称(按照子节点到父节点排序)
     *
     * @param areaId 地址ID
     * @return
     */
    ResultObject<AreaNameResponse> getAreaNameByIdNew(String areaId, Users users,String language);

    /**
     * 根据ID获取区域地址信息
     *
     * @param areaId
     * @param users
     * @return
     */
    ResultObject<AreaResponse> getAreaInfo(String areaId, Users users);

    /**
     * 根据id集合查询
     * @param areaIds
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<AreaResponse>> getAreaList(List<String> areaIds, Users currentLoginUsers);

    /**
     * 根据叶子节点ID批量获取区域地址树
     * @param areaIds
     * @param users
     * @return
     */
    ResultObject<Map<String, List<AreaTreeResponse>>> getAreaTreeByIds(List<String> areaIds, Users users);

    /**
     * 根据叶子节点ID批量获取父类区域地址名称
     *
     * @param areaIds
     * @param users
     * @param language
     * @param sortType
     * @return
     */
    ResultObject<List<AreaNameResponse>> areaNameGetBatch(List<String> areaIds, Users users, String language, String sortType);
}