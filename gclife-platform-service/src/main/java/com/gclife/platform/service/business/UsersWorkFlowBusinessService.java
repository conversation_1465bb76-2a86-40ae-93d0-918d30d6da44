package com.gclife.platform.service.business;


import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.request.UserQueryRequest;
import com.gclife.platform.model.request.UserRequest;
import com.gclife.platform.model.response.workflow.UsersWfResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UsersWorkFlowBusinessService extends BaseBusinessService {

    /**
     * 加载workflow用户信息
     * @param userId
     * @return
     */
    ResultObject loadWorkflowUser(String userId);

    /**
     * 加载workflow用户角色列表
     * @param userId
     * @return
     */
    ResultObject loadWorkflowUserRoles(String userId);


    /**
     * 加载workflow角色列
     * @param roleId 角色信息
     * @return
     */
    ResultObject loadWorkflowRole(String roleId);

    /**
     * 根据节点ID和机构ID查看哪些用户拥有对应的操作权限
     * @param activityAuid
     * @param branchId
     * @return
     */
    ResultObject<UsersWfResponse> loadWorkflowUsers(String activityAuid, String branchId);


    /**
     * 根据节点ID和机构ID查看哪些用户拥有对应的操作权限
     * @param activityAuid 工作流节点
     * @param branchId　机构
     * @param branchMode　模式
     * @return
     */
    ResultObject<List<String>> queryWorkflowNodePermissionUsers(String activityAuid, String branchId,String branchMode);

}
