package com.gclife.platform.service.business;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.platform.model.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码接口
 */
public interface CareerBusinessService extends BaseBusinessService {
    /**
     * 根据Id获取职业代码
     *
     * @param careerId   职业ID
     * @param providerId 保险公司ID
     * @return CareerResponse
     */
    ResultObject<List<CareerResponse>> getCareerById(String careerId, String providerId, Users users, String transformLanguage);

    /**
     * 获取职业信息
     *
     * @param careerId 职业ID
     * @param language 语言
     * @return CareerResponse
     */
    ResultObject<CareerNewResponse> getCareerInfoById(String careerId, String language);

    /**
     * 根据职业ids或者职业codes获取职业详情
     *
     * @param careerIds
     * @param language
     * @return
     */
    ResultObject<List<CareerResponse>> postCareerInfoByIds(List<String> careerIds, String language);

    /**
     * 根据叶子节点ID获取父类职业名称
     *
     * @param careerId
     * @param users
     * @return
     */
    ResultObject<CareerNameResponse> getCareerNameById(String careerId, Users users);


    /**
     * 根据Id获取职业父代码列表
     *
     * @param careerId 职业ID
     * @return CareerResponse
     */
    ResultObject<List<CareerTreeResponse>> getCareerParentsById(String careerId, Users users);

    /**
     * 查询所有职业树
     *
     * @param providerId 供应商ID
     * @return CareerBaseTreeResponses
     */
    ResultObject<List<CareerBaseTreeResponse>> queryCareerTreeById(String providerId);

    /**
     * 根据叶子节点ID获取父类职业名称
     *
     * @param careerIds
     * @param users
     * @return
     */
    ResultObject<List<CareerNameResponse>> postCareerName(List<String> careerIds, Users users);

    ResultObject getCareerInfoByIdTest(String careerId, String language);
}