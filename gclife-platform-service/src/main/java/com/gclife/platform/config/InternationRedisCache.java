package com.gclife.platform.config;

import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.bo.InternationalDo;
import com.gclife.platform.base.service.InternationalBaseService;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: chenjinrong
 * @Date: 2019/12/10 10:18
 * @Description:redis缓存国际化字典
 */
@Component
public class InternationRedisCache {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private InternationalBaseService internationalBaseService;

    //    @Scheduled(initialDelay = 100*1000,fixedDelay = 1800*1000)
    @Scheduled(cron = "0 0/30 * * * ?")
    public void doInternationRedisCache() {
        int page = 1, pageSize = 1000;
        List<InternationalDo> internationalDos = new ArrayList<>();
        do {
            internationalDos = internationalBaseService.queryAllInternation(page, pageSize);
            System.out.println("国际化缓存 page:" + page);
            page++;
            RMap<String, String> rmap = redissonClient.getMap("internation");
            Map<String, String> internationMap = new HashMap<>(1000);
            if (AssertUtils.isNotEmpty(internationalDos)) {
                internationalDos.stream().forEach(internationalDo -> {
                    String key = internationalDo.getCodeType() + "_" + internationalDo.getCodeKey() + "_" + internationalDo.getLanguage();
                    internationMap.put(key, internationalDo.getCodeName());
                });
            }
            rmap.putAll(internationMap);
        } while (internationalDos.size() > 0);
    }
}
