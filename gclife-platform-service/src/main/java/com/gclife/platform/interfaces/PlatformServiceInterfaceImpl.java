package com.gclife.platform.interfaces;

import com.gclife.common.interfaces.BaseInternationService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.feign.BaseFactorConfigRespFc;
import com.gclife.common.model.feign.SyscodeReq;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.feign.SyscodeStatusRespFc;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.platform.model.response.BaseFactorConfigResponse;
import com.gclife.platform.vo.SyscodeResponse;
import com.gclife.platform.service.business.InternationalBusinessService;
import com.gclife.platform.service.business.PlatformConfigService;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *         create 17-10-20
 *         description:
 */
@Service
public class PlatformServiceInterfaceImpl implements BaseInternationService {


    @Autowired
    InternationalBusinessService internationalBusinessService;

    @Autowired
    InternationalBaseBusinessService internationalBaseBusinessService;

    @Autowired
    TerminologyBaseBusinessService terminologyBaseBusinessService;

    @Autowired
    PlatformConfigService platformConfigService;


    @Override
    public ResultObject<List<SyscodeRespFc>> queryTerminology(String s) {
        return null;
    }

    @Override
    public ResultObject<SyscodeRespFc> queryOneTerminology(String s, String s1) {
        ResultObject<SyscodeRespFc> respFcResultObject =new ResultObject<>();
        SyscodeResponse syscodeResponse = terminologyBaseBusinessService.queryOneSyscode(s,s1).getData();
        SyscodeRespFc syscodeRespFc =null;
        if(AssertUtils.isNotNull(syscodeResponse)){
            syscodeRespFc = new SyscodeRespFc();
            ClazzUtils.copyPropertiesIgnoreNull(syscodeResponse,syscodeRespFc);
        }
        respFcResultObject.setData(syscodeRespFc);;
        return respFcResultObject;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> queryInternationalTerminology(String s, String s1) {
        ResultObject<List<SyscodeRespFc>> respFcResultObject =new ResultObject<>();
        List<SyscodeResponse> syscodeResponse = terminologyBaseBusinessService.queryInternationSyscode(s,s1).getData();
        List<SyscodeRespFc> syscodeRespFcs =null;
        if(AssertUtils.isNotEmpty(syscodeResponse)){
            syscodeRespFcs=syscodeResponse.stream().map(syscodeResponse1 -> {
                SyscodeRespFc syscodeRespFc = new SyscodeRespFc();
                ClazzUtils.copyPropertiesIgnoreNull(syscodeResponse1,syscodeRespFc);
                return syscodeRespFc;
            }).collect(Collectors.toList());
        }
        respFcResultObject.setData(syscodeRespFcs);;
        return respFcResultObject;
    }

    @Override
    public ResultObject<SyscodeRespFc> queryOneInternationalTerminology(String s, String s1, String s2) {
        ResultObject<SyscodeRespFc> respFcResultObject =new ResultObject<>();
        SyscodeResponse syscodeResponse = terminologyBaseBusinessService.queryOneInternationSyscode(s,s1,s2).getData();
        SyscodeRespFc syscodeRespFc =null;
        if(AssertUtils.isNotNull(syscodeResponse)){
            syscodeRespFc = new SyscodeRespFc();
            ClazzUtils.copyPropertiesIgnoreNull(syscodeResponse,syscodeRespFc);
        }
        respFcResultObject.setData(syscodeRespFc);;
        return respFcResultObject;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> queryInternational(String s, String s1) {
        ResultObject<List<SyscodeRespFc>> respFcResultObject =new ResultObject<>();
        List<SyscodeResponse> syscodeResponse = internationalBaseBusinessService.queryInternational(s,s1).getData();
        List<SyscodeRespFc> syscodeRespFcs =null;
        if(AssertUtils.isNotEmpty(syscodeResponse)){
            syscodeRespFcs=syscodeResponse.stream().map(syscodeResponse1 -> {
                SyscodeRespFc syscodeRespFc = new SyscodeRespFc();
                ClazzUtils.copyPropertiesIgnoreNull(syscodeResponse1,syscodeRespFc);
                return syscodeRespFc;
            }).collect(Collectors.toList());
        }
        respFcResultObject.setData(syscodeRespFcs);;
        return respFcResultObject;
    }

    @Override
    public ResultObject<SyscodeRespFc> queryOneInternational(String s, String s1, String s2) {
        ResultObject<SyscodeRespFc> respFcResultObject =new ResultObject<>();
        SyscodeResponse syscodeResponse = internationalBaseBusinessService.queryOneInternational(s,s1,s2).getData();
        SyscodeRespFc syscodeRespFc =null;
        if(AssertUtils.isNotNull(syscodeResponse)){
            syscodeRespFc = new SyscodeRespFc();
            ClazzUtils.copyPropertiesIgnoreNull(syscodeResponse,syscodeRespFc);
        }
        respFcResultObject.setData(syscodeRespFc);;
        return respFcResultObject;
    }

    @Override
    public ResultObject<SyscodeRespFc> queryOneSpecialInternational(String s, String s1, String s2) {
        ResultObject<SyscodeResponse> responseResultObject  =  internationalBaseBusinessService.queryOneInternational(s,s1,s2);
        //设置值
        ResultObject<SyscodeRespFc> respFcResultObject = new ResultObject<>();
        respFcResultObject.setErrorInfo(responseResultObject);
        if(AssertUtils.isNotNull(respFcResultObject)){
            SyscodeRespFc syscodeRespFc = new SyscodeRespFc();
            syscodeRespFc.setCodeKey(responseResultObject.getData().getCodeKey());
            syscodeRespFc.setCodeName(responseResultObject.getData().getCodeName());
            syscodeRespFc.setDescribe(responseResultObject.getData().getDescribe());
            syscodeRespFc.setLanguage(responseResultObject.getData().getLanguage());
            respFcResultObject.setData(syscodeRespFc);
        }
        return respFcResultObject;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> queryInternational(SyscodeRespFc syscodeRespFc) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> queryInternationalTerminology(SyscodeRespFc syscodeRespFc) {
        return null;
    }

    @Override
    public ResultObject<SyscodeRespFc> getTerminology(String s, String s1) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getTerminologyList(String s) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getTerminologyList(String s, String s1) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeStatusRespFc>> getTerminologyStatusClassList(String s) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getTerminologyAssignLanguageList(String s, String s1) {
        return null;
    }

    @Override
    public ResultObject<SyscodeRespFc> getTerminologyAssignLanguage(String s, String s1, String s2) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getTerminologyList(SyscodeReq syscodeReq) {
        return null;
    }

    @Override
    public ResultObject<SyscodeRespFc> getInternational(String s, String s1) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getInternationalList(String s) {
        return null;
    }

    @Override
    public ResultObject<SyscodeRespFc> getInternationalAssignLanguage(String s, String s1, String s2) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getInternationalAssignLanguageList(String s, String s1) {
        return null;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getInternationalList(SyscodeReq syscodeReq) {
        return null;
    }

    /**
     * 国际化
     * @param users
     * @param s
     * @param s1
     * @return
     */
    @Override
    public ResultObject<SyscodeRespFc> getTerminologyLanguagePlatform(Users users, String s, String s1) {
        ResultObject<SyscodeResponse> responseResultObject  =  internationalBusinessService.loadTerminology(users,s,s1,null);
        //设置值
        ResultObject<SyscodeRespFc> respFcResultObject = new ResultObject<>();
        respFcResultObject.setErrorInfo(responseResultObject);
        if(AssertUtils.isNotNull(respFcResultObject)){
            SyscodeRespFc syscodeRespFc = new SyscodeRespFc();
            syscodeRespFc.setCodeKey(responseResultObject.getData().getCodeKey());
            syscodeRespFc.setCodeName(responseResultObject.getData().getCodeName());
            syscodeRespFc.setDescribe(responseResultObject.getData().getDescribe());
            syscodeRespFc.setLanguage(responseResultObject.getData().getLanguage());
            respFcResultObject.setData(syscodeRespFc);
        }
        return respFcResultObject;
    }


    @Override
    public ResultObject<BaseFactorConfigRespFc> queryOneBaseFactorConfig(String s) {
        ResultObject<BaseFactorConfigRespFc> resultObject = new ResultObject<BaseFactorConfigRespFc>();
        BaseFactorConfigRespFc baseFactorConfigRespFc =null;
        BaseFactorConfigResponse baseFactorConfigResponse  = platformConfigService.getBaseFactorConfig(s).getData();
        if(AssertUtils.isNotNull(baseFactorConfigResponse)){
            baseFactorConfigRespFc=new BaseFactorConfigRespFc();
            baseFactorConfigRespFc.setConfigCode(baseFactorConfigResponse.getConfigCode());
            baseFactorConfigRespFc.setConfigDescription(baseFactorConfigResponse.getConfigDescription());
            baseFactorConfigRespFc.setConfigId(baseFactorConfigResponse.getConfigId());
            baseFactorConfigRespFc.setConfigName(baseFactorConfigResponse.getConfigName());
            baseFactorConfigRespFc.setConfigValue(baseFactorConfigResponse.getConfigValue());
        }
        resultObject.setData(baseFactorConfigRespFc);
        return resultObject;
    }


}