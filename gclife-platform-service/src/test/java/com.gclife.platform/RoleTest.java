package com.gclife.platform;

import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.core.jooq.tables.Permissions;
import com.gclife.platform.core.jooq.tables.daos.*;
import com.gclife.platform.core.jooq.tables.pojos.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@SpringBootTest
public class RoleTest extends UsersTest {

    @Autowired
    UsersDao usersDao;
    @Autowired
    Users2rolesDao users2rolesDao;
    @Autowired
    RolesDao rolesDao;
    @Autowired
    Permissions2rolesDao permissions2rolesDao;
    @Autowired
    PermissionsDao permissionsDao;
    @Autowired
    Resources2permissionsDao resources2permissionsDao;
    @Autowired
    ResourcesDao resourcesDao;

    @Test
    public void test() {

//        List<Resources2permissionsPo> resources2permissionsPoList = new ArrayList<>();
//        for (int i = 2; i <= 7; i++) {
//
//            PermissionsPo permissionsPo = permissionsDao.fetchOneByName("PERMISSION_" + i);
//
//            List<ResourcesPo> resourcesDaoAll = resourcesDao.findAll();
//            if(AssertUtils.isNotEmpty(resourcesDaoAll)){
//                return;
//            }
//            resourcesDaoAll.forEach(resourcesPo -> {
//                Resources2permissionsPo resources2permissionsPo = new Resources2permissionsPo();
//                resources2permissionsPo.setPermissionsResourcesId("RESOURCES_PERMISSIONS_" + UUIDUtils.getUUIDStr());
//                resources2permissionsPo.setPermissionId(permissionsPo.getPermissionId());
//                resources2permissionsPo.setResourceId(resourcesPo.getResourceId());
//                resources2permissionsPoList.add(resources2permissionsPo);
//            });
//
//        }
//        resources2permissionsDao.insert(resources2permissionsPoList);
    }

}
