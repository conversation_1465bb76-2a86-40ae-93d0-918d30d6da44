package com.gclife.platform;


import com.gclife.common.util.AssertUtils;
import com.gclife.platform.model.request.UserCustomerBusinessRequest;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;


/**
 * <AUTHOR>
 * create 17-11-18
 * description:
 */
public class BaiTest {


    public static void main(String[] args) throws Exception {
        UserCustomerBusinessRequest userCustomerBusinessRequest = new UserCustomerBusinessRequest();
        userCustomerBusinessRequest.setAvatar("123456");
        userCustomerBusinessRequest.setAvoirdupois("123456");
        userCustomerBusinessRequest.setBankCode("22222222222222222222");

        UserCustomerBusinessRequest userCustomerBusinessRequest1 = new UserCustomerBusinessRequest();
        userCustomerBusinessRequest1.setBankCode("1111111111111111111111");

        BaiTest.copy(userCustomerBusinessRequest, userCustomerBusinessRequest1);
        System.out.println(userCustomerBusinessRequest1);
        System.out.println(userCustomerBusinessRequest);
    }


    /**
     * @param source 原数据
     * @param target 目标
     *               会将原 属性 值复制到 目标对象  null 除外
     *
     * @throws NoSuchMethodException
     * @throws SecurityException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    public static void copy(Object source, Object target) throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {

        Class sourceClass = source.getClass();//得到对象的Class
        Class targetClass = target.getClass();//得到对象的Class

        Field[] sourceFields = sourceClass.getDeclaredFields();//得到Class对象的所有属性
        Field[] targetFields = targetClass.getDeclaredFields();//得到Class对象的所有属性

        for (Field sourceField : sourceFields) {
            String name = sourceField.getName();//属性名
            Class type = sourceField.getType();//属性类型

            String methodName = name.substring(0, 1).toUpperCase() + name.substring(1);

            Method getMethod = sourceClass.getMethod("get" + methodName);//得到属性对应get方法

            Object value = getMethod.invoke(source);//执行源对象的get方法得到属性值

            if (!AssertUtils.isNotNull(value)) {
                continue;
            }
            for (Field targetField : targetFields) {
                String targetName = targetField.getName();//目标对象的属性名

                if (targetName.equals(name)) {
                    Method setMethod = targetClass.getMethod("set" + methodName, type);//属性对应的set方法

                    setMethod.invoke(target, value);//执行目标对象的set方法
                }
            }
        }
    }

}
