package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.base.dao.CareerBaseDao;
import com.gclife.platform.base.model.bo.CareerDo;
import com.gclife.platform.base.model.bo.CareerTreeDo;
import com.gclife.platform.base.service.CareerBaseService;
import com.gclife.platform.model.response.CareerBaseTreeResponse;
import com.gclife.platform.service.business.AreaBusinessService;
import com.gclife.platform.service.business.CareerBusinessService;
import com.gclife.platform.service.business.base.CareerBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import sun.util.resources.cldr.en.CalendarData_en_US;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 职业 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class CareerBusinessServiceTest extends UsersTest {
    @Autowired
    CareerBusinessService careerBusinessService;
    @Autowired
    AreaBusinessService areaBusinessService;

    @Autowired
    CareerBaseDao careerBaseDao;

    @Autowired
    private CareerBaseService careerBaseService;

    @Autowired
    private CareerBaseBusinessService careerBaseBusinessService;


    @Test
    public void queryCareerAllTree() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            ResultObject<List<CareerBaseTreeResponse>> listResultObject = careerBusinessService.queryCareerTreeById(null);
            System.out.println(JSON.toJSON(listResultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public void queryCareerChilds() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            List<CareerDo> careerDos= careerBaseDao.queryCareerChilds("0000", null);
            System.out.println(JSON.toJSON(careerDos));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }




    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public void getCareerById() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = careerBusinessService.getCareerById("0000", "PRO8888888888888",users,"EN_US");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public void getCareerInfoById() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
//            ResultObject resultObject = careerBusinessService.getCareerById(null,"PRO8888888888888",users);
            ResultObject resultObject = careerBusinessService.getCareerInfoById("GC-0901020", "ZH_CN");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public void postCareerInfoByIds() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            List<String> ids = Arrays.asList("GC-0103003", "GC-0303009", "GC-0501016", "GC-0602006", "GC-0608002", "GC-0702003", "GC-0903011");
            System.out.println(JSON.toJSONString(ids));
            ResultObject resultObject = careerBusinessService.postCareerInfoByIds(ids, "ZH_CN");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public void loadArea() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
//            ResultObject resultObject = careerBusinessService.getCareerById(null,"PRO8888888888888",users);
            ResultObject resultObject = areaBusinessService.getAreaNameById("817100", users, "EN_US");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public void getCareerParentsById() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = careerBusinessService.getCareerParentsById("GCL-000010000100001", users);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


}*/
