package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.request.UserWeixinRequest;
import com.gclife.platform.service.business.UserWeixinBusinessService;
import com.gclife.platform.service.business.UsersBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
@SpringBootTest
public class UsersWeixinServiceTest extends UsersTest{
    @Autowired
    UserWeixinBusinessService userWeixinBusinessService;

    /**
     * 测试查询用户授权信息
     */
    @Test
    public  void loadUserByUserNameGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
//            UserWeixinRequest userWeixinRequest = new UserWeixinRequest();
//            userWeixinRequest.setWechatAppId("wx96747b900c947918");
//            userWeixinRequest.setUserId("09a0acb835cb4f07b651e9e8026a8425");
//            userWeixinRequest.setCity("深圳");
//            userWeixinRequest.setCountry("中国");
//            userWeixinRequest.setGroupId(0);
//            userWeixinRequest.setHeadImgUrl("http://thirdwx.qlogo.cn/mmopen/MrQ1awLaEcpebWT9GS13vr0BhjdTnW05iaznQOrknCB7vVX07INeAvqUAk4WGfuiax8zpqRKQ89CicFCPzLz46AFHNXD3DQdUoX/132");
//            userWeixinRequest.setNickname("但盼风雨来\uD83C\uDF8F");
//            userWeixinRequest.setProvince("广东");
//            userWeixinRequest.setSex("MALE");
//            userWeixinRequest.setSubscribe("YES");
//            userWeixinRequest.setOpenId("ohNYZ0oprVy0bYN7mlXFG5XQEDGo");
//            ResultObject resultObject = userWeixinBusinessService.saveUsersWechatInfo(userWeixinRequest);
//            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


}