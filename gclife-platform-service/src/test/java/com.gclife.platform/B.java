//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.gclife.platform;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.service.BaseBusinessService;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import com.gclife.platform.model.response.CustomerMessagesResponse;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeToken;
import org.modelmapper.internal.util.Types;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class B<T> implements BaseBusinessService<T> {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    ModelMapper modelMapper;

    public B() {
    }

    public ModelMapper getModelMapper() {
        return this.modelMapper;
    }

    public void setModelMapper(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
    }

    public Logger getLogger() {
        return this.logger;
    }

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    public <D> D converterObject(Object source, Class<D> destinationType) {
        try {
            return Optional.ofNullable(source).isPresent() ? this.getModelMapper().map(source, destinationType) : null;
        } catch (Exception var4) {
            this.logger.error(BaseErrorConfigEnum.CONVERTER_FAIL.getValue());
            throw new RequestException(BaseErrorConfigEnum.CONVERTER_FAIL);
        }
    }

    public <D> D converterList(Object source, Type type) {
        try {
            if (Optional.ofNullable(source).isPresent() && source instanceof List) {
                List o = (List) source;
                if (Optional.ofNullable(o).filter((list) -> {
                    return list.size() > 0;
                }).isPresent()) {
                    return this.getModelMapper().map(source, type);
                }
            }

            return null;
        } catch (Exception var4) {
            this.logger.error(BaseErrorConfigEnum.CONVERTER_FAIL.getValue());
            throw new RequestException(BaseErrorConfigEnum.CONVERTER_FAIL);
        }
    }


        public static void main(String[] args) {
            List<String> stringList = new ArrayList<>();
            stringList.add("1");
            System.out.println(stringList.size());
        }
}
