package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.service.business.EmployeeBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmployeServiceTest extends UsersTest {
    @Autowired
    EmployeeBusinessService employeeBusinessService;

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadBranchInfoGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = employeeBusinessService.loadUsersManagerChannels(users,"1");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

}*/
