package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.model.response.UserLoginLogResponse;
import com.gclife.platform.service.business.AccountBusinessService;
import com.gclife.platform.service.business.UserLoginLogBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserLoginLogServiceTest extends UsersTest {
    @Autowired
    UserLoginLogBusinessService userLoginLogBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void loadAccountByIdGet() {
        try {
            System.out.println("=============================================测试获取账户信息 start================================================");
            String accountId = "1";
            ResultObject<UserLoginLogResponse> resultObject = userLoginLogBusinessService.loadUserAppNewestLoginLog("lb");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取账户信息 error================================================");
        }
        System.out.println("=============================================测试获取账户信息 end================================================");
    }

    */
/**
     * 用户登录校验测试
     *
     *
     *//*

    @Test
    public void appUserLoginCheckTest()
    {
        try {
            System.out.println(JSON.toJSONString(userLoginLogBusinessService.appUserLoginCheck("86", "***********", "gclife_agent_app")));
            System.out.println(JSON.toJSONString(userLoginLogBusinessService.appUserLoginCheck("86", "5***********", "gclife_agent_app")));
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void postUserLoginLogTest() {
        AppRequestHeads appRequestHeads = new AppRequestHeads();

        try {
            appRequestHeads.setDeviceChannel("gclife_agent_app");
            appRequestHeads.setOsType("ios");
            appRequestHeads.setDeviceId("xxxx");
            appRequestHeads.setAppVersion("v3.2.0");
            appRequestHeads.setDistrict("86");
            appRequestHeads.setLanguage("ZH_CN");
            appRequestHeads.setPhoneModel("");
            appRequestHeads.setOsVersion("Unix");
            appRequestHeads.setCoordiate("584");
            appRequestHeads.setNetworkType("INET");
            appRequestHeads.setIpAddress("************");
            ResultObject resultObject =userLoginLogBusinessService.postUserLoginLog(appRequestHeads, "GM_USER_TEST_103", "SUCCESS");
            System.out.println(resultObject);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void postUserLoginLog() {
        AppRequestHeads appRequestHeads = new AppRequestHeads();

        try {
            appRequestHeads.setDeviceChannel("gclife_agent_app");
            appRequestHeads.setOsType("ios");
            appRequestHeads.setDeviceId("xxxx");
            appRequestHeads.setAppVersion("v3.2.0");
            appRequestHeads.setDistrict("86");
            appRequestHeads.setLanguage("ZH_CN");
            appRequestHeads.setPhoneModel("");
            appRequestHeads.setOsVersion("Unix");
            appRequestHeads.setCoordiate("584");
            appRequestHeads.setNetworkType("INET");
            appRequestHeads.setIpAddress("************");
            ResultObject resultObject =userLoginLogBusinessService.postUserLoginLog(appRequestHeads, this.getCurrentLoginUsers());
            System.out.println(resultObject);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

}*/
