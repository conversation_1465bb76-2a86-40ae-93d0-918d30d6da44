package com.gclife.platform.base;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.UsersTest;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.request.BranchPagingRequest;
import com.gclife.platform.form.branch.UserBranchRequest;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class BranchBaseServiceTest extends UsersTest {
    @Autowired
    BranchBaseBusinessService baseBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryOneBranchById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryOneBranchById("GMA",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }
    @Test
    public void queryOneTopBranchById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryOneTopBranchById("GMA104008");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchTreeBranchListById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeBranchListById("GMA");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void querySalesAllBranchTree() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.querySalesAllBranchTree();
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }
    @Test
    public void saveUserOptionBranch() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            UserBranchRequest userBranchRequest = new UserBranchRequest();
            userBranchRequest.setBranchId("aa");
            userBranchRequest.setUserId("aa");
            ResultObject resultObject = baseBusinessService.saveUserOptionBranch(userBranchRequest);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchTreeLeafListById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeLeafListById("GMA");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryBranchTreeBranchListByIds() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GMA");
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeBranchListByIds(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryBranchTreeLeafListByIds() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GMA");
            ids.add("GMC");
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeLeafListByIds(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchParentListById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchParentListById("GMA101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchParentListByIds() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GMA101101");
            ids.add("GMA102101");
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchParentListByIds(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryBranchTreeById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeById("GMA");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void querySalesBranchTreeById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.querySalesBranchTreeById("GM");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchTreeByIds() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GMA");
            ids.add("GMA103");
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchTreeByIds(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }



    @Test
    public void queryOneUserBranch() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryOneUserBranch("GM_USER_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }



    @Test
    public void queryOneUserBranchTree() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserBranchTree("GM_USER_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryUserBranchTreeList() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserBranchTreeList("GM_USER_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryUserOptionBranchTree() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserOptionBranchTree("GM_USER_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }
    @Test
    public void queryUserOptionBranchTreeFiterNoLeafBranch() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserOptionBranchTreeFiterNoLeafBranch("GM_USER_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryUserOptionBranchTreeLeaf() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserOptionBranchTreeLeaf("GM_USER_TEST_106");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryUserOptionBranchTreeList() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryUserOptionBranchTreeList("GM_USER_TEST_106");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryLevelBranch() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryLevelBranch("GM_USER_101",3L);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void loadUserOptionFirstLevelBranch() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.loadUserOptionFirstLevelBranch("GM_USER_TEST_101");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryBranchBelongLevel() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = baseBusinessService.queryBranchBelongLevel("GMA");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchFuzzy() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            BranchPagingRequest branchPagingRequest = new BranchPagingRequest();
            branchPagingRequest.setKeyword("金边");
            ResultObject resultObject = baseBusinessService.queryBranchFuzzy(branchPagingRequest,null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryBranchFuzzy2() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            BranchPagingRequest branchPagingRequest = new BranchPagingRequest();
            branchPagingRequest.setKeyword("金边");
            ResultObject resultObject = baseBusinessService.queryBranchFuzzy(branchPagingRequest, PlatformTermEnum.CHANNEL_TYPE.MANAGER.name());
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }
}*/
