package com.gclife.platform.base;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.UsersTest;
import com.gclife.platform.service.business.UsersBusinessService;
import com.gclife.platform.service.business.base.UsersBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class UsersBaseServiceTest extends UsersTest {
    @Autowired
    UsersBaseBusinessService usersBaseBusinessService;

    @Autowired
    UsersBusinessService usersBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryOneUsersPoById() {
        try {
            System.out.println("=============================================查询用户信息 start================================================");
            ResultObject resultObject = usersBaseBusinessService.queryOneUsersPoById("GM_USER_TEST_102");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询用户信息 end================================================");
    }



    @Test
    public void queryUsersPoByIds() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GM_USER_TEST_102");
            System.out.println("=============================================查询用户信息 start================================================");
            ResultObject resultObject = usersBaseBusinessService.queryUsersPoByIds(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询用户信息 end================================================");
    }


    @Test
    public void loadBusinessUserRecentList() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GM_USER_TEST_102");
            System.out.println("=============================================查询用户信息 start================================================");
            ResultObject resultObject = usersBusinessService.loadBusinessUserRecentList("APPLY_PAY_APPLY_EMP","GMM","MANAGER");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询用户信息 end================================================");
    }





}*/
