package com.gclife.platform.base;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.UsersTest;
import com.gclife.platform.service.business.base.BranchLevelBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class BranchLevelBaseServiceTest extends UsersTest {
    @Autowired
    BranchLevelBaseBusinessService branchLevelBaseBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryOneBranchById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = branchLevelBaseBusinessService.queryBranchLevel("","GMA");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


}*/
