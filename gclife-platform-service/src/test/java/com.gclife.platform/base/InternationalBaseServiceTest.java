package com.gclife.platform.base;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.feign.SyscodeReqFc;
import com.gclife.platform.UsersTest;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.service.business.InternationalBusinessService;
import com.gclife.platform.service.business.base.BranchBaseBusinessService;
import com.gclife.platform.service.business.base.InternationalBaseBusinessService;
import com.gclife.platform.service.business.base.TerminologyBaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class InternationalBaseServiceTest extends UsersTest {
    @Autowired
    InternationalBaseBusinessService internationalBaseBusinessService;
    @Autowired
    TerminologyBaseBusinessService terminologyBaseBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryInternational() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = internationalBaseBusinessService.queryInternational("CURRENCY",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryOneInternational() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = internationalBaseBusinessService.queryOneInternational("CURRENCY","KRW",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void queryOneSyscode() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = terminologyBaseBusinessService.queryOneSyscode("CURRENCY","KRW");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void querySyscode() {
        try {
            List<String> ids = new ArrayList<>();
            ids.add("GMA");
            System.out.println("=============================================查询机构信息 start================================================");
            ResultObject resultObject = terminologyBaseBusinessService.querySyscode("CERTIFICATION_STATUS");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void querySyscodeByStatusClassCode() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");

            ResultObject resultObject = terminologyBaseBusinessService.querySyscodeTerminologys(getCurrentLoginUsers(),"POLICY_STATUS_GROUP","STATUS_CLASS_EFFECTIVE");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryInternationalByCodeKeys() {
        try {
            SyscodeForm syscodeReqFc = new SyscodeForm();
            syscodeReqFc.setLang("ZH_CN");
            syscodeReqFc.setCodeType("PRODUCT_FIELD");
            List<String> codeKeys = new ArrayList<>();
            codeKeys.add("COVERAGE_PERIOD");
            codeKeys.add("COVERAGE_PERIOD_UNIT");
            codeKeys.add("PENSION_RECEIVE_DATE");
            syscodeReqFc.setCodeKeys(codeKeys);
            System.out.println(JSON.toJSONString(internationalBaseBusinessService.queryInternationalByCodeKeys(syscodeReqFc)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}*/
