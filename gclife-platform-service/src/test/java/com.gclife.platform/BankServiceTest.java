package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.service.business.BankBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-18
 * description:银行信息接口测试
 */
@SpringBootTest
public class BankServiceTest extends UsersTest {
    @Autowired
    BankBusinessService bankBusinessService;
    private String language = "EN_US";

    @Test
    public void loadBankByIdGet() {
        /*try {
            System.out.println("=============================================测试获取银行信息 start================================================");
            String bankCode = "1";
            ResultObject<BankResponse> resultObject = bankBusinessService.getBankById(null, bankCode, language);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取银行信息 error================================================");
        }
        System.out.println("=============================================测试获取银行信息 end================================================");*/
    }

    @Test
    public void loadBanks() {
        try {
            System.out.println("=============================================测试获取银行列表信息 start================================================");
            ResultObject<List<BankResponse>> resultObject = bankBusinessService.getBanks(language);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取银行列表信息 error================================================");
        }
        System.out.println("=============================================测试获取银行列表信息 end================================================");
    }
}