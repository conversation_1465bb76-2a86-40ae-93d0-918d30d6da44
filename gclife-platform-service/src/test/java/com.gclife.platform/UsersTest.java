package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.base.Users;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-22
 * \* Time: 下午11:53
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public class UsersTest {

    protected Users getCurrentLoginUsers() {

        String jsonString = "{\"countryCode\":\"86\",\"createdDate\":1505404711512,\"email\":\"<EMAIL>\",\"enabled\":\"ENABLED\",\"gender\":\"FMALE\",\"language\":\"ZH_CN\",\"lastLogin\":1505404711512,\"mobile\":\"18898617354\",\"name\":\"曹清华\",\"permissionsPoList\":[{\"description\":\"新契约操作权限\",\"name\":\"新契约权限\",\"parentPermissionId\":\"ROOT\",\"permissionId\":\"NEWCONTRACT_PERMISSIONS\"},{\"description\":\"受理页面操作权限\",\"name\":\"受理权限\",\"parentPermissionId\":\"NEWCONTRACT_PERMISSIONS\",\"permissionId\":\"ACCEPTANCE_ENTER_PERMISSIONS\"},{\"description\":\"受理页面操作权限\",\"name\":\"受理权限\",\"parentPermissionId\":\"NEWCONTRACT_PERMISSIONS\",\"permissionId\":\"ACCEPTANCE_ENTER_PERMISSIONS\"},{\"description\":\"新契约工作流权限\",\"name\":\"新契约工作流权限\",\"parentPermissionId\":\"ROOT\",\"permissionId\":\"NEW_CONTRACT_WORKFLOW_PERMISSIONS\"},{\"description\":\"URL地址访问权限\",\"name\":\"URL地址访问权限\",\"parentPermissionId\":\"ROOT\",\"permissionId\":\"URL_OPERATION\"}],\"resourcesAccessType\":\"NORMAL_ACCESS\",\"resourcesPoList\":[{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"受理\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"RECEIVE_TASK_RESOURCE\",\"serviceName\":\"\",\"type\":\"TASK_NODE\",\"value\":\"RECEIVE_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"影像件扫描\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"IMAGE_SCANNING_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"IMAGE_SCANNING_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"新单录入\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"NEW_ENTRY_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"NEW_ENTRY_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"复核\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"REVIEW_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"REVIEW_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"人工核保\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"ARTIFICIAL_UNDERWRITING_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"ARTIFICIAL_UNDERWRITING_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"保单打印\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"POLICY_PRINT_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"POLICY_PRINT_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"客户签收\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"CUSTOMER_SIGN_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"CUSTOMER_SIGN_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"回执回销\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"RETURN_RECEIPT_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"RETURN_RECEIPT_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"description\":\"代理人确认\",\"effective\":\"EFFECTIVE\",\"parentResourceId\":\"root\",\"resourceId\":\"AGENT_CONFIRM_TASK_RESOURCE\",\"type\":\"TASK_NODE\",\"value\":\"AGENT_CONFIRM_TASK\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"code\":\"RESOURCE_APPLY_POLICYSELECTEDPRINT\",\"createdDate\":1509364121179,\"description\":\"保单打印查询\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"internationalText\":\"保单打印查询\",\"level\":3,\"parentResourceId\":\"b43719bc-e533-11e7-b54d-93d33bd37919\",\"resourceId\":\"02a9d4c0-4c8c-4ddf-beca-2dbeaeb9f918\",\"serviceName\":\"gclife-apply-front\",\"type\":\"FRONTEND_URL\",\"value\":\"/policySelectedPrint\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"code\":\"RESOURCE_DASHBOARD_LOGIN\",\"createdDate\":1509364121146,\"description\":\"登录\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"internationalText\":\"登录\",\"level\":0,\"parentResourceId\":\"root\",\"resourceId\":\"440157c8-e3b9-11e7-92a1-775171f1f033\",\"serviceName\":\"gclife-dashboard-front\",\"type\":\"FRONTEND_URL\",\"value\":\"/\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"createdDate\":1509364121179,\"description\":\"保单打印\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"level\":3,\"parentResourceId\":\"b43719bc-e533-11e7-b54d-93d33bd37919\",\"resourceId\":\"02a9d4c0-4c8c-4ddf-beca-2dbcaeb9f918\",\"serviceName\":\"gclife-apply-front\",\"type\":\"FRONTEND_URL\",\"value\":\"/policyPrint\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"createdDate\":1509364121179,\"description\":\"保单查询\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"level\":3,\"parentResourceId\":\"b43719bc-e533-11e7-b54d-93d33bd37919\",\"resourceId\":\"02a9d4c0-4c8c-4ddf-beca-2dbcaeb9f998\",\"serviceName\":\"gclife-apply-front\",\"type\":\"FRONTEND_URL\",\"value\":\"/policy\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NO_ACCESS_CONTROL\",\"code\":\"RESOURCE_DASHBOARD_DASHBOARD\",\"createdDate\":1509364121146,\"description\":\"首页\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"internationalText\":\"仪表板\",\"level\":0,\"parentResourceId\":\"root\",\"resourceId\":\"b43719bc-e533-11e7-b54d-93d33bd37919\",\"serviceName\":\"gclife-dashboard-front\",\"type\":\"FRONTEND_URL\",\"value\":\"/dashboard\",\"visible\":\"VISIBLE\"}],\"rolesPoList\":[{\"code\":\"ROLE_ADMIN\",\"description\":\"管理员\",\"name\":\"ADMIN\",\"parentRoleId\":\"ROOT\",\"roleId\":\"ADMIN\"},{\"code\":\"ROLE_INPUT\",\"description\":\"录单员\",\"name\":\"录单员\",\"parentRoleId\":\"ROOT\",\"roleId\":\"ENTER_ROLE\"},{\"code\":\"ROLE_APPLY_INPUT\",\"description\":\"受理岗位\",\"name\":\"受理录入员\",\"parentRoleId\":\"ROOT\",\"roleId\":\"ACCEPTANCE_ENTER_ROLE\"},{\"code\":\"ROLE_OSS_MANAGER\",\"description\":\"OSS管理员\",\"name\":\"OSS管理员\",\"parentRoleId\":\"ROOT\",\"roleId\":\"OSS_MANAGER_ROLE\"},{\"code\":\"ROLE_WORKFLOW\",\"description\":\"新契约工作流用户组\",\"name\":\"新契约工作流用户组\",\"parentRoleId\":\"ROOT\",\"roleId\":\"NEW_CONTRACT_WORKFLOW_ROLE\"}],\"updatedDate\":1513495503189,\"userId\":\"GM_USER_101\",\"username\":\"user1\"}";
        Users users = JSON.toJavaObject(JSON.parseObject(jsonString), Users.class);

//        Users users = new Users();
//        users.setUserId("wcl");
//        users.setUsername("wangchenglong");
//        users.setLanguage("ZH_CH");
//        users.setUpdatedDate(System.currentTimeMillis());



        return users;
    }
}