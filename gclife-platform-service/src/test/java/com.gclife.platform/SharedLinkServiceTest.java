package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.dao.SharedLinkConfigExtDao;
import com.gclife.platform.dao.SharedLinkExtDao;
import com.gclife.platform.model.bo.SharedLinkBo;
import com.gclife.platform.model.bo.SharedLinkConfigBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.SharedLinkSaveRequest;
import com.gclife.platform.model.response.SharedLinkResponse;
import com.gclife.platform.model.response.SharedLinkSaveResponse;
import com.gclife.platform.service.business.SharedLinkBusinessService;
import com.gclife.platform.service.data.SharedLinkService;
import com.gclife.platform.validate.parameter.transform.SharedLinkTransData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * create 17-12-16
 * description: SharedLinkService的测试类
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class SharedLinkServiceTest extends UsersTest {

    private static final String PSEUDO_LINK = "/shared";

    @Autowired
    SharedLinkExtDao sharedLinkExtDao;

    @Autowired
    SharedLinkConfigExtDao sharedLinkConfigExtDao;

    @Autowired
    SharedLinkTransData sharedLinkTransData;

    @Autowired
    SharedLinkBusinessService sharedLinkBusinessService;

    @Autowired
    SharedLinkService sharedLinkService;

    */
/**
     * retriveSharedLinkBySignature的测试方法
     *//*

    @Test
    public void retriveSharedLinkBySignatureTest()
    {
        ResultObject<SharedLinkResponse> resultObject = new ResultObject<>();
        String signature = "3317cb1a29ba1f0878d2d68acb56a47a";

        try {
            SharedLinkBo sharedLinkBo = sharedLinkExtDao.loadSharedLinkBySignature(signature);

            if (null != sharedLinkBo) {
                resultObject.setData(sharedLinkTransData.transSharedLinkResponse(sharedLinkBo));
            }

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_ERROR);
            }
        }

        System.out.println(JSON.toJSONString(resultObject));
    }

    */
/**
     * saveSharedLink的测试方法
     *//*

    @Test
    public void saveSharedLinkTest()
    {
        Users users = this.getCurrentLoginUsers();
        String userId = users.getUserId();
        ResultObject<SharedLinkSaveResponse> resultObject = new ResultObject<>();
        SharedLinkSaveRequest sharedLinkSaveRequest = new SharedLinkSaveRequest();
        sharedLinkSaveRequest.setLinkType("INSURANCE_PLAN");
        sharedLinkSaveRequest.setLinkParameters(JSON.toJSONString(users));

        try {
            // 根据链接类型获取链接配置信息
            SharedLinkConfigBo sharedLinkConfigBo = sharedLinkConfigExtDao.loadSharedLinkConfigByType(sharedLinkSaveRequest.getLinkType());

            SharedLinkBo sharedLinkBo = sharedLinkTransData.transSharedLinkBo(userId, sharedLinkConfigBo, sharedLinkSaveRequest);
            sharedLinkService.saveSharedLink(sharedLinkBo);

            SharedLinkSaveResponse sharedLinkSaveResponse = new SharedLinkSaveResponse();
            sharedLinkSaveResponse.setSignature(sharedLinkBo.getSignature());
            resultObject.setData(sharedLinkSaveResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PlatformErrorConfigEnum.PLATFORM_SAVE_SHARED_LINK_ERROR);
            }
        }

        System.out.println(JSON.toJSONString(resultObject));
    }
}
*/
