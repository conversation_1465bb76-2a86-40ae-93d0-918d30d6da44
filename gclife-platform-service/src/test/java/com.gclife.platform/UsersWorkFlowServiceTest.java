package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.service.business.UsersBusinessService;
import com.gclife.platform.service.business.UsersWorkFlowBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class UsersWorkFlowServiceTest extends UsersTest {
    @Autowired
    UsersWorkFlowBusinessService usersWorkFlowBusinessService;
    @Autowired
    UsersExtDao usersExtDao;

    @Autowired
    private UsersBusinessService usersBusinessService;

    @Test
    public void loadWorkflowUsers() {
        try {
            System.out.println("=============================================测试工作流筛选用户权限信息 start================================================");
            UsersWfResponse usersWfResponse = usersExtDao.loadWorkflowUser("RECEIVE_TASK", "GMA101101");
            ResultObject<UsersWfResponse> resultObject = new ResultObject<>();
            resultObject.setData(usersWfResponse);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试工作流筛选用户权限信息 error================================================");
        }
        System.out.println("=============================================测试工作流筛选用户权限信息 end================================================");
    }

    @Test
    public void queryWorkflowNodePermissionUsers() {
        try {
            System.out.println("=============================================测试工作流筛选用户权限信息 start================================================");
            List<String> ids  = usersExtDao.queryWorkflowNodePermissionUsers("ENDORSE_ACCEPT_TASK", "GMA101101",PlatformTermEnum.BRANCH_MODE.MANAGER.name());
            ResultObject<List<String>> resultObject = new ResultObject<>();
            resultObject.setData(ids);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试工作流筛选用户权限信息 error================================================");
        }
        System.out.println("=============================================测试工作流筛选用户权限信息 end================================================");
    }

    */
/**
     * 测试查询用户授权信息
     *//*

    @Test
    public void loadWorkflowUser() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = usersWorkFlowBusinessService.loadWorkflowUser("wcl");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 测试查询用户授权信息
     *//*

    @Test
    public void loadWorkflowUserRoles() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = usersWorkFlowBusinessService.loadWorkflowUserRoles("wcl");
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }



    */
/**
     * 测试查询用户授权信息
     *//*

    @Test
    public void loadResourceUserLastLevelList() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = usersBusinessService.loadResourceUserRecentList("RESOURCE_APPLY_IMAGEPARTSLIST","GMA102101");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e) {
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


}*/
