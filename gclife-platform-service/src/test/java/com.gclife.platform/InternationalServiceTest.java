package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.request.TerminologyRequest;
import com.gclife.platform.service.business.BranchBusinessService;
import com.gclife.platform.service.business.InternationalBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class InternationalServiceTest extends UsersTest {
    @Autowired
    InternationalBusinessService internationalBusinessService;

    */
/**
     * 测试查询用户授权信息
     *//*

    @Test
    public  void loadTerminologyLanguageGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = internationalBusinessService.loadTerminology(users,"CURRENCY","USD",null);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }



    @Test
    public  void loadTerminologyLanguagesGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = internationalBusinessService.loadTerminologys(users,"ERROR_QUESTION",null);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public  void loadTerminologyLanguagesPost() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            TerminologyRequest terminologyRequest = new TerminologyRequest();
            terminologyRequest.setType("ERROR_QUESTION");

            List<String> strings = new ArrayList<>();
            strings.add("ERROR_QUESTION_001");
            terminologyRequest.setListSyscodes(strings);
            ResultObject resultObject = internationalBusinessService.loadTerminologys(users,terminologyRequest);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

}*/
