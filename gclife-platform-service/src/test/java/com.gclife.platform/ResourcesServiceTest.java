package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Resources;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.AuthItemConfigEnum;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.MenuResourcesBo;
import com.gclife.platform.model.response.GrantedSubMenuResponse;
import com.gclife.platform.service.business.ResourcesBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * create 17-12-22
 * description:资源测试类
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class ResourcesServiceTest extends UsersTest{

    @Autowired
    ResourcesBusinessService resourcesBusinessService;

    @Autowired
    UsersExtDao usersExtDao;

    @Autowired
    RestTemplate restTemplate;

    @Test
    public void retriveGrantedSubmenuTest()
    {
        String parentMenuValue = "/";
        String httpMethod = "GET";
        String serviceName = "gclife-agent-web";
        String parentType = "FRONTEND_DOMAIN";
        String childType = "FRONTEND_URL";

       ResultObject<GrantedSubMenuResponse> resultObject = resourcesBusinessService.retriveGrantedSubmenu(getCurrentLoginUsers(), parentMenuValue, httpMethod, serviceName, parentType, childType, "EN_US");
       System.out.println(JSON.toJSONString(resultObject));
    }

    @Test
    public void retriveByCurrentUrlTest()
    {
        String jsonString = "{\n" +
                "    \"userId\": \"GM_USER_101\",\n" +
                "    \"username\": \"gm_admin\",\n" +
                "    \"password\": null,\n" +
                "    \"enabled\": \"ENABLED\",\n" +
                "    \"name\": \"系统管理用户\",\n" +
                "    \"gender\": \"MALE\",\n" +
                "    \"email\": \"<EMAIL>\",\n" +
                "    \"createdDate\": null,\n" +
                "    \"updatedDate\": null,\n" +
                "    \"lastLogin\": null,\n" +
                "    \"language\": \"ZH_CN\",\n" +
                "    \"mobile\": \"\",\n" +
                "    \"nickName\": \"系统管理用户\",\n" +
                "    \"countryCode\": \"855\",\n" +
                "    \"resourcesAccessType\": \"NORMAL_ACCESS\",\n" +
                "    \"deviceChannelId\": \"gclife_bmp_pc\",\n" +
                "    \"rolesPoList\": null,\n" +
                "    \"permissionsPoList\": null,\n" +
                "    \"resourcesPoList\": null,\n" +
                "    \"authorities\": [],\n" +
                "    \"accountNonExpired\": true,\n" +
                "    \"accountNonLocked\": true,\n" +
                "    \"credentialsNonExpired\": true\n" +
                "}";
        try {
            Users users = JSON.toJavaObject(JSON.parseObject(jsonString), Users.class);
            String url = "/abcde";
            String uri = url.replaceAll(":[^/]+", "");
            String currentUri = "/abcde/567";
            if (currentUri.contains(uri)) {
                int m = 0;
            }

            String jsonString2 = "[{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_CONTRACT_MANAGEMENT\",\"description\":\"合约管理\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":8,\"internationalText\":\"合约管理\",\"logo\":\"\",\"name\":\"\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1033\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_DOMAIN\",\"value\":\"\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_RATE_MANAGEMENT\",\"description\":\"费率管理\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":9,\"internationalText\":\"费率管理\",\"logo\":\"\",\"name\":\"\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1034\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_DOMAIN\",\"value\":\"\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_DASHBOARD\",\"description\":\"仪表板\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":0,\"internationalText\":\"仪表板\",\"logo\":\"\",\"name\":\"Dashboard\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1001\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_PRODUCTLIST\",\"description\":\"产品列表\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":1,\"internationalText\":\"产品列表\",\"logo\":\"\",\"name\":\"ProductList\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1003\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/productList\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_PRODUCTDEFINELIST\",\"description\":\"产品定义\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":2,\"internationalText\":\"产品定义\",\"logo\":\"\",\"name\":\"ProductDefineList\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1005\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/productDefineList\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_SUPPLIERMANAGELIST\",\"description\":\"供应商管理\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":3,\"internationalText\":\"供应商管理\",\"logo\":\"\",\"name\":\"SupplierManageList\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1008\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/supplierManageList\",\"visible\":\"VISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_QUERYCONTRACT\",\"description\":\"合约查询\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":7,\"internationalText\":\"合约查询\",\"logo\":\"\",\"name\":\"QueryContract\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1024\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/queryContract\",\"visible\":\"INVISIBLE\"},{\"accessControlType\":\"NORMAL_ACCESS_CONTROL\",\"code\":\"RESOURCE_PRODUCT_PRODUCTSEARCH\",\"description\":\"产品查询\",\"effective\":\"EFFECTIVE\",\"httpMethod\":\"GET\",\"index\":10,\"internationalText\":\"产品查询\",\"logo\":\"\",\"name\":\"ProductSearch\",\"parentResourceId\":\"PRODUCT1000\",\"resourceId\":\"PRODUCT1032\",\"serviceName\":\"https://dev-bmp-product.zjlh.lan\",\"type\":\"FRONTEND_URL\",\"value\":\"/productSearch\",\"visible\":\"VISIBLE\"}]";
            List<Resources> resList = JSONObject.parseArray(jsonString2, Resources.class);

            resList.sort(Comparator.comparing(Resources::getVisible, (o1, o2)-> o1.compareTo(o2)).reversed()
                        .thenComparing(Resources::getIndex, (o1, o2) -> (o1.intValue() - o2.intValue())));

//            resList.sort((a, b) -> a.getIndex().intValue() - b.getIndex().intValue());


            System.out.println(JSON.toJSONString(resList));

            System.out.println(JSON.toJSONString(resourcesBusinessService.retriveByCurrentUrl(users, "https://dev-bmp-apply.zjlh.lan/#/applyDetails/a1096d43c44e4183bd507b2741b67c5d?pathName=policyQuery", "GET", "EN_US")));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void retriveByParentResourceIdTest()
    {
        String jsonString = "{\n" +
                "    \"userId\": \"GM_USER_101\",\n" +
                "    \"username\": \"gm_admin\",\n" +
                "    \"password\": null,\n" +
                "    \"enabled\": \"ENABLED\",\n" +
                "    \"name\": \"系统管理用户\",\n" +
                "    \"gender\": \"MALE\",\n" +
                "    \"email\": \"<EMAIL>\",\n" +
                "    \"createdDate\": null,\n" +
                "    \"updatedDate\": null,\n" +
                "    \"lastLogin\": null,\n" +
                "    \"language\": \"ZH_CN\",\n" +
                "    \"mobile\": \"\",\n" +
                "    \"nickName\": \"系统管理用户\",\n" +
                "    \"countryCode\": \"855\",\n" +
                "    \"resourcesAccessType\": \"NORMAL_ACCESS\",\n" +
                "    \"deviceChannelId\": \"gclife_bmp_pc\",\n" +
                "    \"rolesPoList\": null,\n" +
                "    \"permissionsPoList\": null,\n" +
                "    \"resourcesPoList\": null,\n" +
                "    \"authorities\": [],\n" +
                "    \"accountNonExpired\": true,\n" +
                "    \"accountNonLocked\": true,\n" +
                "    \"credentialsNonExpired\": true\n" +
                "}";
        try {
            Users users = JSON.toJavaObject(JSON.parseObject(jsonString), Users.class);
            System.out.println(JSON.toJSONString(resourcesBusinessService.retriveByParentResourceId(users, "APPLY1000", "EN_US")));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void loadFrontendResourcesListTest() {
        try {
            System.out.println(JSON.toJSONString(usersExtDao.loadFrontendResourcesList("GM_USER_101", "ZH_CN")));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void accsResCheckTest() {

        try {
            Users users = new Users();
            users.setUserId("GM_USER_101");

            String requestUrl = "/";
            String httpMethod = "GET";
            String serviceName = "https://dev-bmp-apply.zjlh.lan";
            String type = AuthItemConfigEnum.FRONTEND_URL.getValue();
            ResultObject<String> resultObject = resourcesBusinessService.accsResCheck(users, requestUrl, httpMethod, serviceName, type);
            System.out.println(JSON.toJSONString(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void retriveRedirectUrlTest() {
        try {
            Users users = new Users();
            users.setUserId("GM_USER_101");

            ResultObject<MenuResourcesBo> resultObject = resourcesBusinessService.retriveRedirectUrl(users, "FINANCE1000", "EN_US");
            System.out.println(JSON.toJSONString(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void accsResCheckBackendTest() {
        try {
            Users users = new Users();
            users.setUserId("GM_USER_101");
            System.out.println(JSON.toJSONString(resourcesBusinessService.accsResCheckBackend(users, "/payment/v1/payment/list/sfdsfswecvew8794616", "GET")));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
*/
