package com.gclife.platform;


import com.alibaba.fastjson.JSON;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.service.business.UsersBusinessService;
import org.jooq.DSLContext;
import org.jooq.Record4;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class UsersServiceTest  extends UsersTest{
    @Autowired
    UsersBusinessService usersService;

    */
/**
     * 测试查询用户授权信息
     *//*

    @Test
    public  void loadUserByUserNameGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = usersService.loadUserByUserName(users,"wangchenglong");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public  void aaa() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            List<String> ids = new ArrayList<>();
            ids.add("39482f047c744c64930a42ef3706484f");
            ids.add("c906ecc6f2374b3983a5b9eb36e9d7e4");
            ids.add("ad57e7dc701044698773429c634f7898");
            ids.add("ef293c6e6dc34b4b86f782514bc1465c");
            ids.add("GM_USER_113");
            ResultObject resultObject = usersService.postAgents(ids);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }



    @Test
    public  void loadBusinessUserDetailById() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            ResultObject resultObject = usersService.loadBusinessUserDetailById("GMA101104_AGENT_001");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public  void loadResourceUserRecentList() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            ResultObject resultObject = usersService.loadResourceUserRecentList("RESOURCE_FINANCE_CASHTRANSFER","GMA101101");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    @Test
    public void userLoginTest() {
        for (int i = 0; i < 100; i++) {
            new Thread() {
                @Override
                public void run() {
                    String url = "http://localhost:13040/user/login?mobile=158177363&countryCode=855";

                    try {
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("deviceChannel", "gclife_agent_app");
                        headers.add("language", "EN_US");
                        HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
                        RestTemplate restTemplate = new RestTemplate();
                        System.out.println(restTemplate.exchange(url, HttpMethod.GET,
                                requestEntity, String.class));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

//                    String url = "http://localhost:13040/oauth/token?grant_type=refresh_token&client_id=client_test&client_secret=secret_test&refresh_token=c69d2ffa-b893-4542-bedc-0d6624900dfa";
//
//                    try {
//                        HttpHeaders headers = new HttpHeaders();
//                        headers.add("Content-Type", "application/json;charset=UTF-8");
////                        headers.add("deviceChannel", "gclife_agent_app");
////                        headers.add("language", "EN_US");
//                        HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
//                        RestTemplate restTemplate = new RestTemplate();
//                        System.out.println(restTemplate.exchange(url, HttpMethod.POST,
//                                requestEntity, String.class));
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }

                }
            }.start();
        }
    }

}*/
