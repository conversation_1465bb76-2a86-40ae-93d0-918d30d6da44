package com.gclife.platform;


import com.alibaba.fastjson.JSON;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.base.model.bo.BranchTreeDo;
import com.gclife.platform.base.service.BranchBaseService;
import com.gclife.platform.service.business.BranchBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class BranchServiceTest  extends UsersTest{
    @Autowired
    BranchBusinessService branchBusinessService;
    @Autowired
    BranchBaseService branchBaseService;

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void querySalesBranchLeafByUserId() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            List<BranchTreeDo> branchTreeDos =branchBaseService.querySalesBranchLeafByUserId("GM_USER_101");
            System.out.println(JSON.toJSON(branchTreeDos));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadBranchInfoGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadBranchInfo(users,"100100S2000000");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 查询机构父树
     *//*

    @Test
    public  void loadParentBranchs() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            List<String> branchIds = new ArrayList<>();
            branchIds.add("GMA");
            //branchIds.add("100100S1000000");
            ResultObject resultObject = branchBusinessService.loadParentBranchs(users,branchIds);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 查询机构叶子节点
     *//*

    @Test
    public  void loadUserBranchTreeLeaf() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadUserBranchTreeLeaf(users);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }
        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    */
/**
     * 查询机构叶子节点
     *//*

    @Test
    public  void loadBranchAllBranchLeaf() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadBranchAllBranchLeaf(users,"10010000000000");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }
        System.out.println("=============================================测试获取用户授权信息 end================================================");
    }

    */
/**
     * 查询机构叶子节点
     *//*

    @Test
    public  void loadUserManagerBranchs() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadUserManagerAllBranchs(users);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadBranchsByIdsGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {

            Users users = this.getCurrentLoginUsers();
            List<String> branchids = new ArrayList<>();
            branchids.add("100100S2000000");
            branchids.add("100100S1000000");
            ResultObject resultObject = branchBusinessService.loadBranchsByIds(users,branchids);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadUserBranchTreeGet() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadUserBranchTree(users);
            System.out.println(JSON.toJSON(resultObject));

        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadBranchTreesByBranchId() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadBranchTreesByBranchId(users,"10010000000000");
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }


    */
/**
     * 测试查询机构信息
     *//*

    @Test
    public  void loadBranchTreeByBranchIds() {
        System.out.println("=============================================测试获取用户授权信息 start================================================");
        try {
            List<String> branchIds = new ArrayList<>();
            branchIds.add("GA102");
            branchIds.add("GA101");
            Users users = this.getCurrentLoginUsers();
            ResultObject resultObject = branchBusinessService.loadBranchTreeByBranchIds(users,branchIds);
            System.out.println(JSON.toJSON(resultObject));
        }catch (Exception e){
            System.out.println("=============================================测试获取用户授权信息 error================================================");
        }

        System.out.println("=============================================测试获取用户授权信息 end================================================");

    }

}*/
