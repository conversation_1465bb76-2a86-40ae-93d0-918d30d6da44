package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.service.business.AccountBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 *         create 17-10-18
 *         description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccountServiceTest extends UsersTest {
    @Autowired
    AccountBusinessService accountBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void loadAccountByIdGet() {
        try {
            System.out.println("=============================================测试获取账户信息 start================================================");
            String accountId = "ACCOUNT_d5eb9c8c-54f7-4235-a53f-dcc275515934";
            ResultObject<AccountResponse> resultObject = accountBusinessService.getAccountById(accountId);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取账户信息 error================================================");
        }
        System.out.println("=============================================测试获取账户信息 end================================================");
    }

}*/
