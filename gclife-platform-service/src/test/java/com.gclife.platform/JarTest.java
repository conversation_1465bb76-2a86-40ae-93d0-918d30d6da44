package com.gclife.platform;

/**
 * @description:
 * @author: aurong
 * @create: 2022-05-19
 **/
public class JarTest {
    public static void main(String[] args) {
        String jars[] = new String[]{"aolj","collections","dvt-jclient","dvt-utils","i18nAPI_v3",
                "jewt-core-jewt4","jewt4","mail","ojdbc7","ojdl","orai18n","orai18n-api","orai18n-collation-api","orai18n-mapping"
                ,"orai18n-mapping-api","osdt_cert","osdt_cms","osdt_core","osdt_smime","share","xdocore","xdoparser"
                ,"xdoparser11g","xdoparser12c","xmlparserv2"};

        for(String artifactId:jars){
            System.out.println("mvn deploy:deploy-file -DgroupId=com.oracle -DartifactId="+artifactId+" -Dversion=1.0.0 -Dpackaging=jar -Dfile="+artifactId+"-1.0.0.jar -Durl=http://mvn-repo:8081/repository/gclife-mvn-releases/ -DrepositoryId=nexus");
        }
    }
}
