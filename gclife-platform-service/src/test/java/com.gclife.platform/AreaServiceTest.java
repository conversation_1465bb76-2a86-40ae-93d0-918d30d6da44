package com.gclife.platform;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.service.business.AreaBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * create 17-11-13
 * description:
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class AreaServiceTest {
    @Autowired
    private AreaBusinessService areaBusinessService;

    @Test
    public void loadAreaTreeByIdGet() {
        try {
            System.out.println("=============================================测试获取地址树 start================================================");
            ResultObject resultObject = areaBusinessService.getAreaTreeById("440303",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取地址树 error================================================");
        }
        System.out.println("=============================================测试获取地址树 end================================================");
    }

    @Test
    public void getAreaTreeById() {
        try {
            System.out.println("=============================================测试获取地址树 start================================================");
            ResultObject resultObject = areaBusinessService.getAreaTreeById("ROOT",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取地址树 error================================================");
        }
        System.out.println("=============================================测试获取地址树 end================================================");
    }

    @Test
    public void getAreaById() {
        try {
            System.out.println("=============================================测试获取地址树 start================================================");
            ResultObject resultObject = areaBusinessService.getAreaById("ROOT",null);
            System.out.println(JSON.toJSON(resultObject));
        } catch (Exception e) {
            System.out.println("=============================================测试获取地址树 error================================================");
        }
        System.out.println("=============================================测试获取地址树 end================================================");
    }

}*/
