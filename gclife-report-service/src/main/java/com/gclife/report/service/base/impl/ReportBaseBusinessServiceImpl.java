package com.gclife.report.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentReportApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentReportResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.AppApplyApi;
import com.gclife.apply.api.ApplyReportApi;
import com.gclife.apply.api.BaseApplyApi;
import com.gclife.apply.model.respone.ApplyRemarksResponse;
import com.gclife.apply.model.respone.ApplyReportResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.api.AttachmentBase64Api;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.claim.api.ClaimReportApi;
import com.gclife.claim.model.respone.*;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.endorse.api.EndorseReportApi;
import com.gclife.endorse.api.GroupEndorseApi;
import com.gclife.endorse.model.response.EndorseAcceptResponse;
import com.gclife.endorse.model.response.ReportEndorseListResponse;
import com.gclife.endorse.model.response.ReportEndorsePaymentResponse;
import com.gclife.endorse.model.response.ReportEndorseResponse;
import com.gclife.party.api.PartyReportApi;
import com.gclife.party.model.response.ReturnVisitReportResponse;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.api.PaymentReportApi;
import com.gclife.payment.api.ReceiptBaseApi;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.payment.model.response.PaymentReportResponse;
import com.gclife.payment.model.response.PaymentStatusResponse;
import com.gclife.payment.model.response.PolicyReportResponse;
import com.gclife.payment.model.response.ReceiptStatusResponse;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.api.PolicyReportApi;
import com.gclife.policy.model.response.*;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductRateApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.insurance.policy.PolicyPaymentRequest;
import com.gclife.product.model.response.ProductResponse;
import com.gclife.product.model.response.insurnce.policy.PolicyPaymentResponse;
import com.gclife.renewal.api.RenewalBaseApi;
import com.gclife.renewal.api.RenewalReportApi;
import com.gclife.renewal.model.response.ReportGroupRenewalBo;
import com.gclife.renewal.model.response.ReportRenewalCoverageResponse;
import com.gclife.renewal.model.response.ReportRenewalResponse;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import com.gclife.report.core.jooq.tables.daos.*;
import com.gclife.report.core.jooq.tables.pojos.*;
import com.gclife.report.dao.ReferralActivityBaseDao;
import com.gclife.report.dao.ReportActualPerformanceBaseDao;
import com.gclife.report.dao.ReportBaseDao;
import com.gclife.report.dao.ReportSaleApplyPolicyBaseDao;
import com.gclife.report.model.bo.ReportPaymentBo;
import com.gclife.report.model.bo.ReportQuarterlyReserveWithdrawalBo;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportUWSpecialTreatmentRequest;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;
import com.gclife.report.service.*;
import com.gclife.report.service.base.ReportBaseBusinessService;
import com.gclife.report.validate.transform.LanguageUtils;
import com.gclife.report.validate.transform.ReportListTransData;
import com.gclife.report.validate.transform.ReportMonthlyStatisticsTransData;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.EN_US;
import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.ZH_CN;
import static com.gclife.common.model.config.TerminologyTypeEnum.PAYMENT_METHODS;
import static com.gclife.report.model.config.ReportTermEnum.ANNUAL_EARNED_INCOME.ANNUAL_EARNED_INCOME;
import static com.gclife.report.model.config.ReportTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT;
import static com.gclife.report.model.config.ReportTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_BUSINESS_TYPE.*;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS;
import static com.gclife.report.model.config.ReportTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_TYPE.LONG;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_TYPE.SHORT;
import static com.gclife.report.model.config.ReportTermEnum.REPORT_TYPE;
import static com.gclife.report.model.config.ReportTermEnum.REPORT_TYPE.GROUP_INSTALLMENT;
import static com.gclife.report.model.config.ReportTermEnum.REPORT_TYPE.RENEWAL;

/**
 * <AUTHOR>
 * create 18-11-7
 * description:报表基础服务
 */
@Service
public class ReportBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements ReportBaseBusinessService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportBaseBusinessServiceImpl.class);
    @Autowired
    private ApplyReportApi applyServiceInterface;
    @Autowired
    private PaymentReportApi paymentReportApi;
    @Autowired
    private PolicyReportApi policyReportApi;
    @Autowired
    private AgentReportApi agentReportApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ReportServiceChargeBankChannelDao reportServiceChargeBankChannelDao;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private ReportBaseService reportBaseService;
    @Autowired
    private ReportClaimPolicyDao reportClaimPolicyDao;
    @Autowired
    private ReportClaimPolicyCoverageDao reportClaimPolicyCoverageDao;
    @Autowired
    private ReportClaimPolicyCoverageFeeDao reportClaimPolicyCoverageFeeDao;
    @Autowired
    private EndorseReportApi endorseReportApi;
    @Autowired
    private RenewalReportApi renewalReportApi;
    @Autowired
    private ApplyReportApi applyReportApi;
    @Autowired
    private ReportQueryBaseService reportQueryBaseService;
    @Autowired
    private ReportGroupRenewalBaseService reportGroupRenewalBaseService;
    @Autowired
    private ReportGroupRenewalDao reportGroupRenewalDao;
    @Autowired
    private ReportGroupRenewalCoverageDutyDao reportGroupRenewalCoverageDutyDao;
    @Autowired
    private ReportGroupRenewalCoverageLevelDao reportGroupRenewalCoverageLevelDao;
    @Autowired
    private ReportGroupRenewalCoverageDao reportGroupRenewalCoverageDao;
    @Autowired
    private ReportBaseDao reportBaseDao;
    @Autowired
    private ReportListTransData reportListTransData;
    @Autowired
    private ClaimReportApi claimReportApi;
    @Autowired
    private PartyReportApi partyReportApi;
    @Autowired
    private ReportMonthlyStatisticsTransData reportMonthlyStatisticsTransData;
    @Autowired
    private ReportMonthlyStatisticsDao reportMonthlyStatisticsDao;
    @Autowired
    private ReportActualPerformanceDao reportActualPerformanceDao;
    @Autowired
    private ReportQuarterlyReserveWithdrawalDao reportQuarterlyReserveWithdrawalDao;
    @Autowired
    private ReportActualPerformanceBaseDao reportActualPerformanceBaseDao;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private AttachmentApi attachmentServiceInterface;
    @Autowired
    private AttachmentBase64Api attachmentBase64Api;
    @Autowired
    private ReportAttachmentBaseService reportAttachmentBaseService;
    @Autowired
    private ReportAttachmentDao reportAttachmentDao;
    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private ReportServiceChargeBankChannelBaseService reportServiceChargeBankChannelBaseService;
    @Autowired
    private ReportQuarterlyReserveWithdrawalBaseService reportQuarterlyReserveWithdrawalBaseService;
    @Autowired
    private ReportSaleApplyPolicyBaseDao reportSaleApplyPolicyBaseDao;
    @Autowired
    private ReportSaleApplyPolicyDao reportSaleApplyPolicyDao;

    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Autowired
    private ReportSaleApplyPolicyBaseService reportSaleApplyPolicyBaseService;
    @Autowired
    private ReportSuspenseDao reportSuspenseDao;

    @Autowired
    private AgentApi agentApi;

    @Autowired
    private AppApplyApi appApplyApi;

    @Autowired
    private BaseApplyApi baseApplyApi;

    @Autowired
    private PaymentBaseApi paymentBaseApi;

    @Autowired
    private ReceiptBaseApi receiptBaseApi;

    @Autowired
    private ReferralActivityBaseDao referralActivityBaseDao;

    @Autowired
    private ReferralActivityDao referralActivityDao;

    @Autowired
    private ProductRateApi productRateApi;

    @Autowired
    private ReportGroupPolicySdfBaseService reportGroupPolicySdfBaseService;

    @Autowired
    private ReportGroupPolicySdfDao reportGroupPolicySdfDao;

    @Autowired
    private RenewalBaseApi renewalBaseApi;

    @Autowired
    private GroupEndorseApi groupEndorseApi;


    private List<SyscodeResponse> reportInsuredStatusSyscodeList;
    private List<SyscodeResponse> paymentWaySyscodeList;
    private List<SyscodeResponse> productLevelSyscodeList;
    private List<SyscodeResponse> productMainProductFlagSyscodeList;
    private List<SyscodeResponse> productPremiumPeriodUnitSyscodeList;
    private List<SyscodeResponse> productPremiumFrequencySyscodeList;
    private List<SyscodeResponse> productCoveragePeriodUnitSyscodeList;
    private List<SyscodeResponse> idTypeSyscodeList;
    private List<SyscodeResponse> companyIdTypeSyscodeList;
    private List<SyscodeResponse> policyStatusSyscodeList;
    private List<SyscodeResponse> applyStatusSyscodeList;
    private List<SyscodeResponse> dateTypeSyscodeList;
    private List<SyscodeResponse> bankSyscodeList;
    private List<SyscodeResponse> paymentMethodsSyscodeList;
    private List<SyscodeResponse> coverageStatusSyscodeList;
    private List<SyscodeResponse> gendeSyscodeList;
    private List<SyscodeResponse> insuredStatusSyscodeList;
    private List<SyscodeResponse> channelTypeSyscodeList;
    private List<SyscodeResponse> annualEarnedIncomeSyscodeList;
    private List<SyscodeResponse> insuredTypesSyscodeList;
    private List<SyscodeResponse> productSalesPackages;

    /**
     * 批量同步财务数据
     *
     * @param basePageRequest 分页参数
     * @return ResultObject
     */
    @Override
    public String syncReportPayment(BasePageRequest basePageRequest) {
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步财务数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        //获取事物状态
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        List<String> listApplyId = new ArrayList<>();
        List<String> listEndorseNo = new ArrayList<>();
        List<String> listPolicyNo = new ArrayList<>();
        List<ReportPaymentBo> reportPaymentPos = new ArrayList<>();
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.PAYMENT.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.PAYMENT.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            // 分页调用财务数据
            ResultObject<List<PaymentReportResponse>> paymentReportReqFcs = paymentReportApi.listPaymentRecord(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(paymentReportReqFcs)) {
                // //1.查数据   3.匹配原有数据，存入主键ID以便更新 4.删除指定时间段的数据,不删除已存在待更新的数据 5.保存数据

                List<PaymentReportResponse> reportReqFcsData = paymentReportReqFcs.getData();
                reportCollectPo.setTotal((long) reportReqFcsData.size());

                //将同步到的ID查原有续期数据
                List<String> paymentIds = new ArrayList<>();
                List<String> receiptIds = new ArrayList<>();
                List<String> allPaymentIds = new ArrayList<>();

                reportReqFcsData.forEach(paymentReportReqFc -> {
                    paymentIds.add(paymentReportReqFc.getPaymentId());
                    receiptIds.add(paymentReportReqFc.getReceiptId());

                    //分类判断保单，保全，投保单
                    String businessType = paymentReportReqFc.getBusinessType();
                    boolean matchFlag = false;
                    if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.APPLY.name().equals(businessType) || APPLY_GROUP.name().equals(businessType)) {
                        listApplyId.add(paymentReportReqFc.getBusinessId());
                        matchFlag = true;
                    }

                    if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_ADD_SUBTRACT_INSURED.name().equals(businessType) || POLICY_ENDORSE.name().equals(businessType)) {
                        listPolicyNo.add(paymentReportReqFc.getBusinessNo());
                        listEndorseNo.add(paymentReportReqFc.getBusinessNo());
                        matchFlag = true;
                    }
                    if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.POLICY_RENEWAL_PAYMENT.name().equals(businessType)
                            || ReportTermEnum.PAYMENT_BUSINESS_TYPE.POLICY_RENEWAL_INSURANCE.name().equals(businessType)
                            || ReportTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_ADD_ADDITIONAL.name().equals(businessType)
                    ) {
                        listPolicyNo.add(paymentReportReqFc.getBusinessNo());
                        matchFlag = true;
                    }

                    if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_ADD_INSURED.name().equals(businessType)
                            || ReportTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_SUBTRACT_INSURED.name().equals(businessType)
                    ) {
                        listEndorseNo.add(paymentReportReqFc.getBusinessNo());
                        matchFlag = true;
                    }

                    if (!matchFlag) {
                        listPolicyNo.add(paymentReportReqFc.getBusinessNo());
                    }
                });

                allPaymentIds.addAll(paymentIds);
                allPaymentIds.addAll(receiptIds);

                List<ReportPaymentPo> originPayments = reportQueryBaseService.queryReportPaymentByPaymentId(paymentIds);
                List<ReportPaymentPo> originReceipts = reportQueryBaseService.queryReportPaymentByReceiptId(receiptIds);

                ResultObject<List<ApplyReportResponse>> applyListReportReqFc = applyServiceInterface.queryListPaymentBo(listApplyId.stream().distinct().collect(Collectors.toList()));

                // 查询保全数据
                ResultObject<List<EndorseAcceptResponse>> endorseAcceptReportData = endorseReportApi.listEndorseReport(listEndorseNo.stream().distinct().collect(Collectors.toList()));
                if (!AssertUtils.isResultObjectListDataNull(endorseAcceptReportData)) {
                    endorseAcceptReportData.getData().forEach(endorseAcceptReportReqFc -> {
                        //合并到保单中
                        listPolicyNo.add(endorseAcceptReportReqFc.getApplyNo());
                    });
                }
                //封装一块统一查询保单信息
                ResultObject<List<com.gclife.policy.model.response.PolicyReportResponse>> policyListReportReqFc = policyApi.queryListPolicyBo(listPolicyNo.stream().distinct().collect(Collectors.toList()));
                //整合  保单->保全
                if (!AssertUtils.isResultObjectListDataNull(endorseAcceptReportData)) {
                    endorseAcceptReportData.getData().forEach(endorseAcceptReportReqFc -> {
                        if (!AssertUtils.isResultObjectListDataNull(policyListReportReqFc)) {
                            policyListReportReqFc.getData().forEach(policyReportResponse -> {
                                if (policyReportResponse.getPolicyNo().equals(endorseAcceptReportReqFc.getApplyNo())) {
                                    endorseAcceptReportReqFc.setNewApplyId(policyReportResponse.getApplyId());
                                    endorseAcceptReportReqFc.setPolicyId(policyReportResponse.getPolicyId());
                                    endorseAcceptReportReqFc.setPolicyNo(policyReportResponse.getPolicyNo());
                                    endorseAcceptReportReqFc.setNewApplyNo(policyReportResponse.getApplyNo());
                                    endorseAcceptReportReqFc.setPremiumFrequency(policyReportResponse.getPremiumFrequency());
                                    endorseAcceptReportReqFc.setProductName(LanguageUtils.getENProductName(policyReportResponse.getProductCode(), policyReportResponse.getProductName()));
                                    endorseAcceptReportReqFc.setProductCode(policyReportResponse.getProductCode());
                                    endorseAcceptReportReqFc.setProductLevel(policyReportResponse.getProductLevel());
                                    endorseAcceptReportReqFc.setApplicantName(policyReportResponse.getApplicantName());
                                    endorseAcceptReportReqFc.setInsuredName(policyReportResponse.getInsuredName());
                                    endorseAcceptReportReqFc.setInsuredBirthday(policyReportResponse.getInsuredBirthday());
                                    endorseAcceptReportReqFc.setEffectiveDate(policyReportResponse.getEffectiveDate());
                                    endorseAcceptReportReqFc.setPolicyYear(policyReportResponse.getPolicyYear());
                                }
                            });
                        }
                    });

                }
                reportReqFcsData.forEach(paymentReportReqFc -> {
                    ReportPaymentBo paymentResponses = (ReportPaymentBo) this.converterObject(paymentReportReqFc, ReportPaymentBo.class);
                    paymentResponses.setConfirmDate(paymentReportReqFc.getPaymentSuccessTime());
                    paymentResponses.setBusinessDate(paymentReportReqFc.getCreateDate());
//                    TODO
//                    paymentResponses.setAgentCode(paymentReportReqFc.getAgentCode());
//                    paymentResponses.setAgentId(paymentReportReqFc.getAgentId());
                    //3.匹配原有数据，存入主键ID以便更新
                    if (AssertUtils.isNotEmpty(originPayments)) {
                        originPayments.stream().filter(originPayment -> originPayment.getPaymentId().equals(paymentReportReqFc.getPaymentId()))
                                .findFirst().ifPresent(originPayment -> {
                                    paymentResponses.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    paymentResponses.setCreateDate(originPayment.getCreateDate());
                                    paymentResponses.setCreateUserId(originPayment.getCreateUserId());
                                    paymentResponses.setReportPaymentId(originPayment.getReportPaymentId());
                                });
                    }
                    if (AssertUtils.isNotEmpty(originReceipts)) {
                        originReceipts.stream().filter(originReceipt -> originReceipt.getReceiptId().equals(paymentReportReqFc.getReceiptId()))
                                .findFirst().ifPresent(originReceipt -> {
                                    paymentResponses.setCreateDate(originReceipt.getCreateDate());
                                    paymentResponses.setCreateUserId(originReceipt.getCreateUserId());
                                    paymentResponses.setReportPaymentId(originReceipt.getReportPaymentId());
                                });
                    }


                    // 根据财务的支付状态判断是投保单保单
                    if (!AssertUtils.isResultObjectListDataNull(applyListReportReqFc)) {
                        applyListReportReqFc.getData().forEach(applyReportReqFc -> {
                            if (paymentReportReqFc.getBusinessId().equals(applyReportReqFc.getApplyId())) {
                                paymentResponses.setApplyId(applyReportReqFc.getApplyId());
                                paymentResponses.setApplyNo(applyReportReqFc.getApplyNo());
                                paymentResponses.setPremiumFrequency(applyReportReqFc.getPremiumFrequency());
                                paymentResponses.setProductName(LanguageUtils.getENProductName(applyReportReqFc.getProductCode(), applyReportReqFc.getProductName()));
                                paymentResponses.setProductCode(applyReportReqFc.getProductCode());
                                paymentResponses.setProductLevel(applyReportReqFc.getProductLevel());
                                paymentResponses.setApplicantName(applyReportReqFc.getApplicantName());
                                paymentResponses.setInsuredName(applyReportReqFc.getInsuredName());
                                paymentResponses.setInsuredBirthday(applyReportReqFc.getInsuredBirthday());
                                paymentResponses.setSpecialDiscount(applyReportReqFc.getSpecialDiscount());
                                paymentResponses.setPromotionType(applyReportReqFc.getPromotionType());
                                paymentResponses.setDiscountType(applyReportReqFc.getDiscountType());
                                paymentResponses.setDiscountModel(applyReportReqFc.getDiscountModel());
                                paymentResponses.setPremiumPeriod(applyReportReqFc.getPremiumPeriod());
                                paymentResponses.setOriginalPremium(applyReportReqFc.getOriginalPremium());
                            }
                        });
                    }
                    if (!AssertUtils.isResultObjectListDataNull(policyListReportReqFc)) {
                        policyListReportReqFc.getData().forEach(policyReportReqFc -> {
                            if (paymentReportReqFc.getBusinessNo().equals(policyReportReqFc.getPolicyNo())) {
                                paymentResponses.setPolicyId(policyReportReqFc.getPolicyId());
                                paymentResponses.setPolicyNo(policyReportReqFc.getPolicyNo());
                                paymentResponses.setApplyId(policyReportReqFc.getApplyId());
                                paymentResponses.setApplyNo(policyReportReqFc.getApplyNo());
                                paymentResponses.setPremiumFrequency(policyReportReqFc.getPremiumFrequency());
                                paymentResponses.setProductName(LanguageUtils.getENProductName(policyReportReqFc.getProductCode(), policyReportReqFc.getProductName()));
                                paymentResponses.setProductCode(policyReportReqFc.getProductCode());
                                paymentResponses.setProductLevel(policyReportReqFc.getProductLevel());
                                paymentResponses.setApplicantName(policyReportReqFc.getApplicantName());
                                paymentResponses.setInsuredName(policyReportReqFc.getInsuredName());
                                paymentResponses.setInsuredBirthday(policyReportReqFc.getInsuredBirthday());
                                paymentResponses.setPremiumPeriod(policyReportReqFc.getPremiumPeriod());
                                paymentResponses.setOriginalPremium(policyReportReqFc.getOriginalPremium());
                                paymentResponses.setPolicyEffectiveDate(policyReportReqFc.getEffectiveDate());
                                paymentResponses.setSpecialDiscount(policyReportReqFc.getSpecialDiscount());
                                paymentResponses.setPromotionType(policyReportReqFc.getPromotionType());
                                paymentResponses.setDiscountType(policyReportReqFc.getDiscountType());
                                paymentResponses.setDiscountModel(policyReportReqFc.getDiscountModel());
                                paymentResponses.setPolicyYear(policyReportReqFc.getPolicyYear());
                            }
                        });
                    }

                    if (!AssertUtils.isResultObjectListDataNull(endorseAcceptReportData)) {
                        endorseAcceptReportData.getData().forEach(endorseAcceptReportReqFc -> {
                            if (paymentReportReqFc.getBusinessNo().equals(endorseAcceptReportReqFc.getAcceptNo())) {
                                paymentResponses.setPolicyId(endorseAcceptReportReqFc.getPolicyId());
                                paymentResponses.setPolicyNo(endorseAcceptReportReqFc.getPolicyNo());
                                paymentResponses.setApplyId(endorseAcceptReportReqFc.getNewApplyId());
                                paymentResponses.setApplyNo(endorseAcceptReportReqFc.getNewApplyNo());
                                paymentResponses.setPremiumFrequency(endorseAcceptReportReqFc.getPremiumFrequency());
                                paymentResponses.setProductName(LanguageUtils.getENProductName(endorseAcceptReportReqFc.getProductCode(), endorseAcceptReportReqFc.getProductName()));
                                paymentResponses.setProductCode(endorseAcceptReportReqFc.getProductCode());
                                paymentResponses.setProductLevel(endorseAcceptReportReqFc.getProductLevel());
                                paymentResponses.setApplicantName(endorseAcceptReportReqFc.getApplicantName());
                                paymentResponses.setInsuredName(endorseAcceptReportReqFc.getInsuredName());
                                paymentResponses.setInsuredBirthday(endorseAcceptReportReqFc.getInsuredBirthday());
                                paymentResponses.setPolicyYear(endorseAcceptReportReqFc.getPolicyYear());
                            }
                        });
                    }

                    reportPaymentPos.add(paymentResponses);
                });

                //4.删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.PAYMENT.name(), startDate, currentTime, allPaymentIds);

                reportBaseService.saveReportPayment(reportPaymentPos, null);
                // 保存实收业绩明细
                this.syncActualPerformance(reportPaymentPos);
                // 保存现金交易
                if (AssertUtils.isNotEmpty(reportReqFcsData) && reportReqFcsData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            errorFlag = true;
            reportCollectPo.setIsSuccess("FAILED");
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 实收业绩报表实收时间特殊处理(暂收转实收)
     *
     * @param paymentId     支付id
     * @param actualPayDate 实收时间
     * @return Void
     */
    @Override
    @Transactional
    public ResultObject<Void> actualPerformanceSpecialTreatment(String paymentId, Long actualPayDate) {
        if (!AssertUtils.isNotEmpty(paymentId) || !AssertUtils.isNotNull(actualPayDate)) {
            return ResultObject.success();
        }
        List<ReportActualPerformancePo> reportActualPerformancePos = reportActualPerformanceDao.fetchByPaymentId(paymentId);
        if (AssertUtils.isNotEmpty(reportActualPerformancePos)) {
            reportActualPerformancePos.forEach(reportActualPerformancePo -> reportActualPerformancePo.setActualPayDate(actualPayDate));
            reportActualPerformanceDao.update(reportActualPerformancePos);
        }
        return ResultObject.success();
    }

    /**
     * 批量同步业务报表（投保人资料）
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportCustomer(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步投保人数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.CUSTOMER.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.CUSTOMER.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<PolicyApplicantReportResponse>> policyApplicantReqFcs = policyReportApi.listPolicyApplicantRecord(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(policyApplicantReqFcs)) {


                List<PolicyApplicantReportResponse> applicantReqFcsData = policyApplicantReqFcs.getData();
                reportCollectPo.setTotal((long) applicantReqFcsData.size());

                //将同步到的ID查原有续期数据
                List<String> customerIds = applicantReqFcsData.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getCustomerId()))
                        .map(PolicyApplicantReportResponse::getCustomerId).distinct().collect(Collectors.toList());
                List<ReportCustomerPo> originReportCustomerPos = reportQueryBaseService.queryReportCustomerByCustomerId(customerIds);

                List<ReportCustomerPo> reportCustomerPos = (List<ReportCustomerPo>) this.converterList(applicantReqFcsData, new TypeToken<List<ReportCustomerPo>>() {
                }.getType());
                reportCustomerPos.forEach(reportCustomerPo -> {
                    if (AssertUtils.isNotEmpty(originReportCustomerPos)) {
                        originReportCustomerPos.stream().filter(originReportCustomerPo -> originReportCustomerPo.getCustomerId().equals(reportCustomerPo.getCustomerId()))
                                .findFirst().ifPresent(originReportCustomerPo -> {
                                    reportCustomerPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportCustomerPo.setCreatedDate(originReportCustomerPo.getCreatedDate());
                                    reportCustomerPo.setCreatedUserId(originReportCustomerPo.getCreatedUserId());
                                    reportCustomerPo.setReportCustomerId(originReportCustomerPo.getReportCustomerId());
                                });
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.CUSTOMER.name(), startDate, currentTime, customerIds);

                reportBaseService.saveReportCustomer(reportCustomerPos, null);
                if (AssertUtils.isNotEmpty(applicantReqFcsData) && applicantReqFcsData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 同步被保人资料
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportInsured(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步被保人资料开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.INSURED.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.INSURED.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<PolicyInsuredReportResponse>> listPolicyInsuredsRecord = policyReportApi.listPolicyInsuredsRecord(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(listPolicyInsuredsRecord)) {
                List<PolicyInsuredReportResponse> listPolicyInsuredsRecordData = listPolicyInsuredsRecord.getData();
                reportCollectPo.setTotal((long) listPolicyInsuredsRecordData.size());

                //将同步到的ID查原有续期数据
                List<String> customerIds = listPolicyInsuredsRecordData.stream().filter(policyInsuredReportResponse -> AssertUtils.isNotEmpty(policyInsuredReportResponse.getCustomerId()))
                        .map(PolicyInsuredReportResponse::getCustomerId).distinct().collect(Collectors.toList());
                List<ReportInsuredPo> originReportInsuredPos = reportQueryBaseService.queryReportInsuredByCustomerId(customerIds);

                List<ReportInsuredPo> reportInsuredPos = (List<ReportInsuredPo>) this.converterList(listPolicyInsuredsRecordData, new TypeToken<List<ReportInsuredPo>>() {
                }.getType());
                reportInsuredPos.forEach(reportInsuredPo -> {
                    if (AssertUtils.isNotEmpty(originReportInsuredPos)) {
                        originReportInsuredPos.stream().filter(originReportCustomerPo -> originReportCustomerPo.getCustomerId().equals(reportInsuredPo.getCustomerId()))
                                .findFirst().ifPresent(originReportCustomerPo -> {
                                    reportInsuredPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportInsuredPo.setCreatedDate(originReportCustomerPo.getCreatedDate());
                                    reportInsuredPo.setCreatedUserId(originReportCustomerPo.getCreatedUserId());
                                    reportInsuredPo.setReportInsuredId(originReportCustomerPo.getReportInsuredId());
                                });
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.INSURED.name(), startDate, currentTime, customerIds);

                reportBaseService.saveReportInsured(reportInsuredPos);
                if (AssertUtils.isNotEmpty(listPolicyInsuredsRecordData) && listPolicyInsuredsRecordData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 批量同步承保清单
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportPolicy(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步承保清单数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.POLICY.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.POLICY.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<PolicyReportUnderwritingResponse>> reportPolicyBos = policyReportApi.listPolicyReportUnderwriting(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate, REPORT_TYPE.POLICY.name());
            if (!AssertUtils.isResultObjectListDataNull(reportPolicyBos)) {
                List<PolicyReportUnderwritingResponse> reportPolicyBoData = reportPolicyBos.getData();
                reportCollectPo.setTotal((long) reportPolicyBoData.size());
                List<String> applyIds = reportPolicyBoData.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getApplyId()))
                        .map(PolicyReportUnderwritingResponse::getApplyId).distinct().collect(Collectors.toList());

                //将同步到的ID查原有续期数据
                List<String> policyIds = reportPolicyBoData.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getPolicyId()))
                        .map(PolicyReportUnderwritingResponse::getPolicyId).distinct().collect(Collectors.toList());
                List<ReportPolicyPo> originReportPolicyPos = reportQueryBaseService.queryReportPolicyByPolicyId(policyIds);

                ResultObject<List<PolicyReportResponse>> policyReportRespResultObject = paymentReportApi.paymentItem(applyIds);

                reportPolicyBoData.forEach(reportPolicy -> {
                    if (!AssertUtils.isNotEmpty(reportPolicy.getApplyId())) {
                        return;
                    }
                    if (!AssertUtils.isResultObjectDataNull(policyReportRespResultObject)) {
                        policyReportRespResultObject.getData().stream().filter(policyReportRespFc -> policyReportRespFc.getBusinessId().equals(reportPolicy.getApplyId()))
                                .findFirst().ifPresent(value -> {
                                    reportPolicy.setPaymentMethodCode(value.getPaymentMethodCodes());
                                    reportPolicy.setTotalPremium(value.getDuePayAmount());
                                });
                    }
                    //3.匹配原有数据，存入主键ID以便更新
                    if (AssertUtils.isNotEmpty(originReportPolicyPos)) {
                        originReportPolicyPos.stream().filter(originReportPolicyPo -> originReportPolicyPo.getPolicyId().equals(reportPolicy.getPolicyId()))
                                .findFirst().ifPresent(originReportPolicyPo -> {
                                    reportPolicy.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportPolicy.setCreateDate(originReportPolicyPo.getCreateDate());
                                    reportPolicy.setCreateUserId(originReportPolicyPo.getCreateUserId());
                                    reportPolicy.setReportPolicyId(originReportPolicyPo.getReportPolicyId());
                                });
                    }

                    //判断主险的险种类型（长期，短期）附加险跟着主险
                    String mainCoveragePeriodUnit = reportPolicy.getMainCoveragePeriodUnit();
                    String mainCoveragePeriod = reportPolicy.getMainCoveragePeriod();
                    if ("YEAR".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 1) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else if ("MONTH".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 12) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else if ("DAY".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 365) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else {
                        //长期
                        reportPolicy.setProductType("LONG");
                    }

                    //保存首单记录
                    if (AssertUtils.isNotNull(reportPolicy.getApplicantFirstPolicyDate())) {
                        ReportCustomerAgentPo reportCustomerAgentPo = reportBaseService.queryReportCustomerAgentByPolicyId(reportPolicy.getPolicyId(), ReportTermEnum.CUSTOMER_TYPE.APPLICANT.name());
                        if (!AssertUtils.isNotNull(reportCustomerAgentPo)) {
                            reportBaseService.saveReportCustomerAgent(reportListTransData.transReportCustomerAgent(reportPolicy, ReportTermEnum.CUSTOMER_TYPE.APPLICANT.name()));
                        }
                    }
                    if (AssertUtils.isNotNull(reportPolicy.getInsuredFirstPolicyDate())) {
                        ReportCustomerAgentPo reportCustomerAgentPo = reportBaseService.queryReportCustomerAgentByPolicyId(reportPolicy.getPolicyId(), ReportTermEnum.CUSTOMER_TYPE.INSURED.name());
                        if (!AssertUtils.isNotNull(reportCustomerAgentPo)) {
                            reportBaseService.saveReportCustomerAgent(reportListTransData.transReportCustomerAgent(reportPolicy, ReportTermEnum.CUSTOMER_TYPE.INSURED.name()));
                        }
                    }
                });

                //4.删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.POLICY.name(), startDate, currentTime, policyIds);

                List<ReportPolicyPo> reportPolicyPos = (List<ReportPolicyPo>) this.converterList(reportPolicyBoData, new TypeToken<List<ReportPolicyPo>>() {
                }.getType());
                reportBaseService.saveReportPolicy(reportPolicyPos, null);

                //同步推荐信息报表
                syncReferralPolicy(reportPolicyBoData);
                if (AssertUtils.isNotEmpty(reportPolicyBoData) && reportPolicyBoData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            errorFlag = true;
            reportCollectPo.setIsSuccess("FAILED");
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    private void syncReferralPolicy(List<PolicyReportUnderwritingResponse> data) {
        if (!AssertUtils.isNotEmpty(data)) {
            return;
        }
        List<PolicyReportUnderwritingResponse> responses = data.stream().filter(pr ->
                AssertUtils.isNotEmpty(pr.getPolicyId()) && AssertUtils.isNotEmpty(pr.getReferralName())).collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(responses)) {
            return;
        }
        List<String> applyIds = responses.stream().map(PolicyReportUnderwritingResponse::getApplyId).distinct().collect(Collectors.toList());
        List<String> policyIds = responses.stream().map(PolicyReportUnderwritingResponse::getPolicyId).distinct().collect(Collectors.toList());
        List<PolicyPaymentResponse> policyPaymentResponses = policyApi.queryReferral(policyIds);
        List<ReferralActivityPo> referralActivityPos = new ArrayList<>();
        responses.forEach(pr -> {
            ReferralActivityPo referralActivityPo = new ReferralActivityPo();
            if (AssertUtils.isNotEmpty(policyPaymentResponses)) {
                policyPaymentResponses.stream().filter(policyPaymentResponse -> policyPaymentResponse.getPolicyId().equals(pr.getPolicyId())).findFirst().ifPresent(policyPaymentResponse -> {
                    referralActivityPo.setReferralFee(policyPaymentResponse.getReferralFee());
                });
            }
            referralActivityPo.setCreatedDate(DateUtils.getCurrentTime());
            referralActivityPo.setPolicyId(pr.getPolicyId());
            referralActivityPo.setPolicyNo(pr.getPolicyNo());
            referralActivityPo.setApplyId(pr.getApplyId());
            referralActivityPo.setReferralName(pr.getReferralName());
            referralActivityPo.setBankAccount(pr.getAbaAccount());
            referralActivityPo.setRiskCommenmentDate(pr.getRiskCommencementDate());
            referralActivityPo.setInitialPremium(pr.getPremiumActualPremium());
            referralActivityPo.setAgentName(pr.getAgentName());
            referralActivityPo.setHesitationEndDate(pr.getHesitationEndDate());
            referralActivityPo.setApproveDate(pr.getApproveDate());
            referralActivityPo.setAgentId(pr.getAgentId());
            referralActivityPos.add(referralActivityPo);
        });
        referralActivityBaseDao.deleteByApplyIds(applyIds);

        List<ReferralActivityPo> referralActivityPoList = JSONArray.parseArray(JSON.toJSONString(referralActivityPos), ReferralActivityPo.class);
        referralActivityPoList.forEach(reportSaleApplyPolicyPo -> {
            reportSaleApplyPolicyPo.setReferralActivityId(UUIDUtils.getUUIDShort());
            reportSaleApplyPolicyPo.setValidFlag("effective");
        });
        referralActivityDao.insert(referralActivityPoList);
    }

    private void syncReferralGroupPolicy(List<PolicyGroupReportUnderwritingResponse> data) {
        if (!AssertUtils.isNotEmpty(data)) {
            return;
        }
        List<String> applyIds = data.stream().map(PolicyGroupReportUnderwritingResponse::getApplyId).distinct().collect(Collectors.toList());
        List<String> policyIds = data.stream().map(PolicyGroupReportUnderwritingResponse::getPolicyId).distinct().collect(Collectors.toList());

        List<PolicyPaymentResponse> policyPaymentResponses = policyApi.queryReferral(policyIds);

        List<PolicyGroupReportUnderwritingResponse> underwritingResponses = data.stream().filter(pr ->
                AssertUtils.isNotEmpty(pr.getPolicyId()) && AssertUtils.isNotEmpty(pr.getReferralName())).collect(Collectors.toList());

        List<ReferralActivityPo> referralActivityPos = new ArrayList<>();
        if (AssertUtils.isNotNull(underwritingResponses)) {
            underwritingResponses.forEach(pr -> {
                ReferralActivityPo referralActivityPo = new ReferralActivityPo();

                if (AssertUtils.isNotEmpty(policyPaymentResponses)) {
                    policyPaymentResponses.stream().filter(policyPaymentResponse -> policyPaymentResponse.getPolicyId().equals(pr.getPolicyId())).findFirst().ifPresent(policyPaymentResponse -> {
                        referralActivityPo.setReferralFee(policyPaymentResponse.getReferralFee());
                    });
                }
                referralActivityPo.setCreatedDate(DateUtils.getCurrentTime());
                referralActivityPo.setPolicyId(pr.getPolicyId());
                referralActivityPo.setPolicyNo(pr.getPolicyNo());
                referralActivityPo.setApplyId(pr.getApplyId());
                referralActivityPo.setReferralName(pr.getReferralName());
                referralActivityPo.setBankAccount(pr.getAbaAccount());
                referralActivityPo.setRiskCommenmentDate(pr.getRiskCommencementDate());
                referralActivityPo.setInitialPremium(pr.getActualPremium());

                referralActivityPos.add(referralActivityPo);
            });
        }
        referralActivityBaseDao.deleteByApplyIds(applyIds);

        List<ReferralActivityPo> referralActivityPoList = JSONArray.parseArray(JSON.toJSONString(referralActivityPos), ReferralActivityPo.class);
        referralActivityPoList.forEach(reportSaleApplyPolicyPo -> {
            reportSaleApplyPolicyPo.setReferralActivityId(UUIDUtils.getUUIDShort());
        });
        referralActivityDao.insert(referralActivityPoList);
    }


    /**
     * 批量同步查询监管报表-承保清单
     *
     * @param basePageRequest 分页参数
     * @return 同步结果
     */
    @Override
    public String syncReportRegulatoryPolicy(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步监管报表-承保清单数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.REGULATORY_POLICY.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.REGULATORY_POLICY.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<PolicyReportUnderwritingResponse>> reportPolicyBos = policyReportApi.listPolicyReportUnderwriting(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate, REPORT_TYPE.REGULATORY_POLICY.name());
            if (!AssertUtils.isResultObjectListDataNull(reportPolicyBos)) {
                List<PolicyReportUnderwritingResponse> reportPolicyBoData = reportPolicyBos.getData();
                reportCollectPo.setTotal((long) reportPolicyBoData.size());
                List<String> applyIds = reportPolicyBoData.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getApplyId()))
                        .map(PolicyReportUnderwritingResponse::getApplyId).distinct().collect(Collectors.toList());

                //将同步到的ID查原有续期数据
                List<String> coverageIds = reportPolicyBoData.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getCoverageId()))
                        .map(PolicyReportUnderwritingResponse::getCoverageId).distinct().collect(Collectors.toList());
                List<ReportRegulatoryPolicyPo> originReportRegulatoryPolicyPos = reportQueryBaseService.queryReportRegulatoryPolicyByCoverageId(coverageIds);

                ResultObject<List<PolicyReportResponse>> policyReportRespResultObject = paymentReportApi.paymentItem(applyIds);

                reportPolicyBoData.forEach(reportPolicy -> {
                    if (!AssertUtils.isNotEmpty(reportPolicy.getApplyId())) {
                        return;
                    }
                    if (!AssertUtils.isResultObjectDataNull(policyReportRespResultObject)) {
                        policyReportRespResultObject.getData().stream().filter(policyReportRespFc -> policyReportRespFc.getBusinessId().equals(reportPolicy.getApplyId()))
                                .findFirst().ifPresent(value -> {
                                    reportPolicy.setPaymentMethodCode(value.getPaymentMethodCodes());
                                });
                    }
                    //3.匹配原有数据，存入主键ID以便更新
                    if (AssertUtils.isNotEmpty(originReportRegulatoryPolicyPos)) {
                        originReportRegulatoryPolicyPos.stream().filter(originReportPolicyPo -> originReportPolicyPo.getCoverageId().equals(reportPolicy.getCoverageId()))
                                .findFirst().ifPresent(originReportPolicyPo -> {
                                    reportPolicy.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportPolicy.setCreateDate(originReportPolicyPo.getCreateDate());
                                    reportPolicy.setCreateUserId(originReportPolicyPo.getCreateUserId());
                                    reportPolicy.setReportRegulatoryPolicyId(originReportPolicyPo.getReportRegulatoryPolicyId());
                                });
                    }

                    //判断主险的险种类型（长期，短期）附加险跟着主险
                    String mainCoveragePeriodUnit = reportPolicy.getMainCoveragePeriodUnit();
                    String mainCoveragePeriod = reportPolicy.getMainCoveragePeriod();
                    if ("YEAR".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 1) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else if ("MONTH".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 12) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else if ("DAY".equals(mainCoveragePeriodUnit) && Integer.parseInt(mainCoveragePeriod) <= 365) {
                        //短期
                        reportPolicy.setProductType("SHORT");
                    } else {
                        //长期
                        reportPolicy.setProductType("LONG");
                    }
                });

                //4.删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.REGULATORY_POLICY.name(), startDate, currentTime, coverageIds);

                List<ReportRegulatoryPolicyPo> reportRegulatoryPolicyPos = (List<ReportRegulatoryPolicyPo>) this.converterList(reportPolicyBoData, new TypeToken<List<ReportRegulatoryPolicyPo>>() {
                }.getType());
                reportBaseService.saveReportRegulatoryPolicy(reportRegulatoryPolicyPos, null);
                if (AssertUtils.isNotEmpty(reportPolicyBoData) && reportPolicyBoData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            errorFlag = true;
            reportCollectPo.setIsSuccess("FAILED");
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 删除续期失效数据(由renewal服务调用)
     *
     * @param renewalIds 续期IDs
     * @return ResultObject
     */
    @Override
    public ResultObject deleteInvalidRenewal(List<String> renewalIds) {
        ResultObject resultObject = new ResultObject();
        if (AssertUtils.isNotEmpty(renewalIds)) {
            reportBaseDao.deleteInvalidRenewal(renewalIds);
        }
        return resultObject;
    }

    @Override
    public String syncReportGroupPolicy(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步团险承保清单数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.POLICY_GROUP.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.POLICY_GROUP.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<PolicyGroupReportUnderwritingResponse>> policyGroupReportUnderwritings = policyReportApi.listPolicyGroupReportUnderwriting(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(policyGroupReportUnderwritings)) {
                List<PolicyGroupReportUnderwritingResponse> reportUnderwritingResponses = policyGroupReportUnderwritings.getData();
                reportCollectPo.setTotal((long) reportUnderwritingResponses.size());

                List<String> applyIds = reportUnderwritingResponses.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getApplyId()))
                        .map(PolicyGroupReportUnderwritingResponse::getApplyId).distinct().collect(Collectors.toList());

                //将同步到的ID查原有续期数据
                List<String> policyIds = reportUnderwritingResponses.stream().filter(reportPolicyPo1 -> AssertUtils.isNotEmpty(reportPolicyPo1.getPolicyId()))
                        .map(PolicyGroupReportUnderwritingResponse::getPolicyId).distinct().collect(Collectors.toList());
                List<ReportGroupPolicyPo> originReportGroupPolicyPos = reportQueryBaseService.queryReportGroupPolicyByPolicyId(policyIds);

                ResultObject<List<PolicyReportResponse>> policyReportRespResultObject = paymentReportApi.paymentItem(applyIds);

                List<ReportGroupPolicyPo> reportGroupPolicyPos = new ArrayList<>();
                List<ReportPolicyCoveragePo> reportPolicyCoveragePos = new ArrayList<>();
                List<ReportPolicyCoverageLevelPo> reportPolicyCoverageLevelPos = new ArrayList<>();
                List<ReportPolicyCoverageDutyPo> reportPolicyCoverageDutyPos = new ArrayList<>();
                List<ReportPolicyInsuredPo> policyInsuredPoList = new ArrayList<>();
                reportUnderwritingResponses.forEach(policyGroupReportUnderwritingResponse -> {
                    if (!AssertUtils.isNotEmpty(policyGroupReportUnderwritingResponse.getApplyId())) {
                        return;
                    }

                    ReportGroupPolicyPo reportGroupPolicyPo = new ReportGroupPolicyPo();
                    ClazzUtils.copyPropertiesIgnoreNull(policyGroupReportUnderwritingResponse, reportGroupPolicyPo);
                    if (!AssertUtils.isResultObjectDataNull(policyReportRespResultObject)) {
                        policyReportRespResultObject.getData().stream().filter(policyReportRespFc -> policyReportRespFc.getBusinessId().equals(reportGroupPolicyPo.getApplyId()))
                                .findFirst().ifPresent(policyReportResponse -> {
                                    reportGroupPolicyPo.setPaymentMethodCode(policyReportResponse.getPaymentMethodCodes());
                                    reportGroupPolicyPo.setActualPremium(policyReportResponse.getDuePayAmount());
                                });
                    }
                    //3.匹配原有数据，存入主键ID以便更新
                    if (AssertUtils.isNotEmpty(originReportGroupPolicyPos)) {
                        originReportGroupPolicyPos.stream().filter(originReportPolicyPo -> originReportPolicyPo.getPolicyId().equals(reportGroupPolicyPo.getPolicyId()))
                                .findFirst().ifPresent(originReportPolicyPo -> {
                                    reportGroupPolicyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportGroupPolicyPo.setCreatedDate(originReportPolicyPo.getCreatedDate());
                                    reportGroupPolicyPo.setCreatedUserId(originReportPolicyPo.getCreatedUserId());
                                    reportGroupPolicyPo.setReportGroupPolicyId(originReportPolicyPo.getReportGroupPolicyId());
                                });
                    }

                    //保存首单记录
                    if (AssertUtils.isNotNull(policyGroupReportUnderwritingResponse.getApplicantFirstPolicyDate())) {
                        ReportCustomerAgentPo reportCustomerAgentPo = reportBaseService.queryReportCustomerAgentByPolicyId(policyGroupReportUnderwritingResponse.getPolicyId(), ReportTermEnum.CUSTOMER_TYPE.APPLICANT.name());
                        if (!AssertUtils.isNotNull(reportCustomerAgentPo)) {
                            reportBaseService.saveReportCustomerAgent(reportListTransData.transReportCustomerAgent(policyGroupReportUnderwritingResponse, ReportTermEnum.CUSTOMER_TYPE.APPLICANT.name()));
                        }
                    }
                    //险种
                    if (AssertUtils.isNotEmpty(policyGroupReportUnderwritingResponse.getPolicyGroupReportCoverageBos())) {
                        List<ReportPolicyCoveragePo> reportPolicyCoveragePos1 = (List<ReportPolicyCoveragePo>) converterList(policyGroupReportUnderwritingResponse.getPolicyGroupReportCoverageBos(), new TypeToken<List<ReportPolicyCoveragePo>>() {
                        }.getType());
                        reportPolicyCoveragePos.addAll(reportPolicyCoveragePos1);
                    }
                    //险种责任
                    if (AssertUtils.isNotEmpty(policyGroupReportUnderwritingResponse.getPolicyCoverageDutyBos())) {
                        List<ReportPolicyCoverageDutyPo> coverageDutyPos = (List<ReportPolicyCoverageDutyPo>) converterList(policyGroupReportUnderwritingResponse.getPolicyCoverageDutyBos(), new TypeToken<List<ReportPolicyCoverageDutyPo>>() {
                        }.getType());
                        reportPolicyCoverageDutyPos.addAll(coverageDutyPos);
                    }
                    //险种档次
                    if (AssertUtils.isNotEmpty(policyGroupReportUnderwritingResponse.getPolicyCoverageLevelBos())) {
                        List<ReportPolicyCoverageLevelPo> coverageLevelPos = (List<ReportPolicyCoverageLevelPo>) converterList(policyGroupReportUnderwritingResponse.getPolicyCoverageLevelBos(), new TypeToken<List<ReportPolicyCoverageLevelPo>>() {
                        }.getType());
                        reportPolicyCoverageLevelPos.addAll(coverageLevelPos);
                    }
                    //保单被保人
                    if (AssertUtils.isNotEmpty(policyGroupReportUnderwritingResponse.getPolicyInsuredBos())) {
                        List<ReportPolicyInsuredPo> policyInsuredPos = (List<ReportPolicyInsuredPo>) converterList(policyGroupReportUnderwritingResponse.getPolicyInsuredBos(), new TypeToken<List<ReportPolicyInsuredPo>>() {
                        }.getType());
                        policyInsuredPoList.addAll(policyInsuredPos);
                    }
                    reportGroupPolicyPos.add(reportGroupPolicyPo);
                });

                //4.删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.POLICY_GROUP.name(), startDate, currentTime, policyIds);


                reportBaseService.saveReportGroupPolicy(reportGroupPolicyPos, null);
                reportBaseService.saveReportPolicyCoverage(reportPolicyCoveragePos, null);
                reportBaseService.saveReportPolicyCoverageDuty(reportPolicyCoverageDutyPos, null);
                reportBaseService.saveReportPolicyCoverageLevel(reportPolicyCoverageLevelPos, null);
                reportBaseService.saveReportPolicyInsured(policyInsuredPoList, null);

                //同步团险推荐活动报表
                //syncReferralGroupPolicy(reportUnderwritingResponses);

                if (AssertUtils.isNotEmpty(reportUnderwritingResponses) && reportUnderwritingResponses.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            errorFlag = true;
            reportCollectPo.setIsSuccess("FAILED");
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 批量同步理赔报表
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportClaim(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步理赔报表开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.CLAIM.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.CLAIM.name());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<ClaimReportListResponse> listClaimReportData = claimReportApi.listClaimReportData(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectDataNull(listClaimReportData)) {
                ClaimReportListResponse claimReportDataData = listClaimReportData.getData();

                List<ClaimReportResponse> claimReportResponses = claimReportDataData.getClaimList();
                reportCollectPo.setTotal((long) claimReportResponses.size());

                //将同步到的ID查原有理赔数据
                List<String> claimIds = claimReportResponses.stream().filter(claimReportResponse -> AssertUtils.isNotEmpty(claimReportResponse.getClaimId()))
                        .map(ClaimReportResponse::getClaimId).distinct().collect(Collectors.toList());

                List<ReportClaimPo> reportClaimPos = (List<ReportClaimPo>) this.converterList(claimReportResponses, new TypeToken<List<ReportClaimPo>>() {
                }.getType());
                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.CLAIM.name(), startDate, currentTime, claimIds);

                reportBaseService.saveReportClaim(reportClaimPos);
                List<ClaimReportPolicyResponse> claimPolicyList = claimReportDataData.getClaimPolicyList();
                if (AssertUtils.isNotEmpty(claimPolicyList)) {
                    List<ReportClaimPolicyPo> reportClaimPolicyPoList = JSON.parseArray(JSON.toJSONString(claimPolicyList), ReportClaimPolicyPo.class);
                    reportClaimPolicyDao.insert(reportClaimPolicyPoList);
                }
                List<ClaimReportPolicyCoverageResponse> claimPolicyCoverageList = claimReportDataData.getClaimPolicyCoverageList();
                if (AssertUtils.isNotEmpty(claimPolicyCoverageList)) {
                    List<ReportClaimPolicyCoveragePo> reportClaimPolicyCoveragePoList = JSON.parseArray(JSON.toJSONString(claimPolicyCoverageList), ReportClaimPolicyCoveragePo.class);
                    reportClaimPolicyCoverageDao.insert(reportClaimPolicyCoveragePoList);
                }
                List<ClaimReportPolicyCoverageFeeResponse> claimPolicyCoverageFeeList = claimReportDataData.getClaimPolicyCoverageFeeList();
                if (AssertUtils.isNotEmpty(claimPolicyCoverageFeeList)) {
                    List<ReportClaimPolicyCoverageFeePo> reportClaimPolicyCoverageFeePoList = JSON.parseArray(JSON.toJSONString(claimPolicyCoverageFeeList), ReportClaimPolicyCoverageFeePo.class);
                    reportClaimPolicyCoverageFeeDao.insert(reportClaimPolicyCoverageFeePoList);
                }
                if (AssertUtils.isNotEmpty(claimReportResponses) && claimReportResponses.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 批量同步业务员报表
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportAgent(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步业务员报表开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.AGENT.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.AGENT.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<AgentReportResponse>> listAgentReportData = agentReportApi.listAgentReportData(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(listAgentReportData)) {

                List<AgentReportResponse> agentReportResponses = listAgentReportData.getData();
                reportCollectPo.setTotal((long) agentReportResponses.size());

                //将同步到的ID查原有业务员数据
                List<String> agentIds = agentReportResponses.stream().filter(claimReportResponse -> AssertUtils.isNotEmpty(claimReportResponse.getAgentId()))
                        .map(AgentReportResponse::getAgentId).distinct().collect(Collectors.toList());
                List<ReportAgentPo> originReportAgentPos = reportQueryBaseService.queryReportAgentPoByAgentId(agentIds);

                List<ReportAgentPo> reportAgentPos = (List<ReportAgentPo>) this.converterList(agentReportResponses, new TypeToken<List<ReportAgentPo>>() {
                }.getType());
                reportAgentPos.forEach(reportAgentPo -> {
                    if (AssertUtils.isNotEmpty(originReportAgentPos)) {
                        originReportAgentPos.stream().filter(originReportClaimPo -> originReportClaimPo.getAgentId().equals(reportAgentPo.getAgentId()))
                                .findFirst().ifPresent(originReportCustomerPo -> {
                                    reportAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportAgentPo.setCreatedDate(originReportCustomerPo.getCreatedDate());
                                    reportAgentPo.setCreatedUserId(originReportCustomerPo.getCreatedUserId());
                                    reportAgentPo.setReportAgentId(originReportCustomerPo.getReportAgentId());
                                });
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.AGENT.name(), startDate, currentTime, agentIds);

                reportBaseService.saveReportAgent(reportAgentPos);
                if (AssertUtils.isNotEmpty(agentReportResponses) && agentReportResponses.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 批量同步客户回访报表
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportReturnVisit(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步客户回访报表开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.RETURN_VISIT.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.RETURN_VISIT.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<ReturnVisitReportResponse>> listReturnVisitReportData = partyReportApi.listReturnVisitReportData(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(listReturnVisitReportData)) {

                List<ReturnVisitReportResponse> returnVisitReportResponses = listReturnVisitReportData.getData();
                reportCollectPo.setTotal((long) returnVisitReportResponses.size());

                //将同步到的ID查原有客户回访数据
                List<String> returnVisitIds = returnVisitReportResponses.stream().filter(returnVisitReportResponse -> AssertUtils.isNotEmpty(returnVisitReportResponse.getReturnVisitId()))
                        .map(ReturnVisitReportResponse::getReturnVisitId).distinct().collect(Collectors.toList());
                List<ReportReturnVisitPo> originReportReturnVisitPos = reportQueryBaseService.queryReportReturnVisitPoById(returnVisitIds);

                List<ReportReturnVisitPo> reportReturnVisitPos = (List<ReportReturnVisitPo>) this.converterList(returnVisitReportResponses, new TypeToken<List<ReportReturnVisitPo>>() {
                }.getType());
                reportReturnVisitPos.forEach(reportReturnVisitPo -> {
                    if (AssertUtils.isNotEmpty(originReportReturnVisitPos)) {
                        originReportReturnVisitPos.stream().filter(originReportReturnVisitPo -> originReportReturnVisitPo.getReturnVisitId().equals(reportReturnVisitPo.getReturnVisitId()))
                                .findFirst().ifPresent(originReportReturnVisitPo -> {
                                    reportReturnVisitPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportReturnVisitPo.setCreatedDate(originReportReturnVisitPo.getCreatedDate());
                                    reportReturnVisitPo.setCreatedUserId(originReportReturnVisitPo.getCreatedUserId());
                                    reportReturnVisitPo.setReportReturnVisitId(originReportReturnVisitPo.getReportReturnVisitId());
                                });
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(REPORT_TYPE.RETURN_VISIT.name(), startDate, currentTime, returnVisitIds);

                reportBaseService.saveReportReturnVisit(reportReturnVisitPos);
                if (AssertUtils.isNotEmpty(returnVisitReportResponses) && returnVisitReportResponses.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 批量同步保全报表
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public String syncReportEndorse(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("批量同步保全报表开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.ENDORSE.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            //List<PaymentStatusResponse> paymentStatusResponses = new ArrayList<>();
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.ENDORSE.name());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<ReportEndorseListResponse> reportEndorseListResponseResultObject = endorseReportApi.listReportEndorseData(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectDataNull(reportEndorseListResponseResultObject)) {
                ReportEndorseListResponse endorseListResponse = reportEndorseListResponseResultObject.getData();
                //将同步到的ID查原有保全数据
                List<ReportEndorseResponse> reportEndorseList = endorseListResponse.getReportEndorseList();

                List<String> endorseIds = reportEndorseList.stream()
                        .filter(returnVisitReportResponse -> AssertUtils.isNotEmpty(returnVisitReportResponse.getEndorseId()))
                        .map(ReportEndorseResponse::getEndorseId)
                        .distinct()
                        .collect(Collectors.toList());
                ResultObject<List<PolicyReportResponse>> policyReportRespResultObject = paymentReportApi.paymentItem(endorseIds);
                reportEndorseList.forEach(reportEndorseResponse -> {
                    //保全实收日期
                    if (!AssertUtils.isResultObjectDataNull(policyReportRespResultObject)) {
                        policyReportRespResultObject.getData().stream().filter(policyReportRespFc -> policyReportRespFc.getBusinessId().equals(reportEndorseResponse.getEndorseId()))
                                .findFirst().ifPresent(policyReportResponse -> reportEndorseResponse.setActualPayDate(policyReportResponse.getActualPayDate()));
                    }
                });


                //List<String> endorseIds2 = reportEndorseList.stream().map(ReportEndorseResponse::getEndorseId).distinct().collect(Collectors.toList());
//                //保全增减员的生效日期取实收日期
//                reportEndorseList.forEach(reportEndorseResponse -> {
//                    if (AssertUtils.isNotEmpty(reportEndorseResponse.getEndorseId()) && AssertUtils.isNotEmpty(reportEndorseResponse.getFeeType())
//                            && "gprj20200304004".equals(reportEndorseResponse.getProjectId())) {
//                        //默认初始化为保全生效时间(针对没有收费和付费记录的增减员)
//                        reportEndorseResponse.setActualPaymentDate(reportEndorseResponse.getEffectiveDate());
//
//                        //根据收付费查询财务收费和付费接口
//
//                        ResultObject<ReceiptStatusResponse> receiptStatusResponseResultObject = receiptBaseApi.queryOneReceiptDoByBusinessId(reportEndorseResponse.getEndorseId());
//                        if (!AssertUtils.isResultObjectDataNull(receiptStatusResponseResultObject)) {
//                            ReceiptStatusResponse receiptStatusResponse = receiptStatusResponseResultObject.getData();
//                            //针对于增减员的团险保全有实收日期取实收日期,没有取支付成功时间，两者都没取生效时间
//                            if (AssertUtils.isNotNull(receiptStatusResponse.getActualPayDate())) {
//                                reportEndorseResponse.setActualPaymentDate(receiptStatusResponse.getActualPayDate());
//                            } else if (AssertUtils.isNotNull(receiptStatusResponse.getPaymentSuccessTime())) {
//                                reportEndorseResponse.setActualPaymentDate(receiptStatusResponse.getPaymentSuccessTime());
//                            }
//                        }
//
//
//                        ResultObject<PaymentStatusResponse> paymentStatusResponseResultObject = paymentBaseApi.queryOnePaymentDoByBusinessId(reportEndorseResponse.getEndorseId());
//                        if (!AssertUtils.isResultObjectDataNull(paymentStatusResponseResultObject)) {
//                            PaymentStatusResponse paymentStatusResponse = paymentStatusResponseResultObject.getData();
//                            //针对于增减员的团险保全有实收日期取实收日期,没有取支付成功时间，两者都没取生效时间
//                            if (AssertUtils.isNotNull(paymentStatusResponse.getActualPayDate())) {
//                                reportEndorseResponse.setActualPaymentDate(paymentStatusResponse.getActualPayDate());
//                            } else if (AssertUtils.isNotNull(paymentStatusResponse.getPaymentSuccessTime())) {
//                                reportEndorseResponse.setActualPaymentDate(paymentStatusResponse.getPaymentSuccessTime());
//                            }
//                        }
//
//                    }
//                });

                /*endorseIds2.forEach(s -> {
                    ResultObject<PaymentStatusResponse> paymentStatusResponseResultObject = paymentBaseApi.queryOnePaymentDoByBusinessId(s);
                    if (!AssertUtils.isResultObjectDataNull(paymentStatusResponseResultObject)) {
                        PaymentStatusResponse paymentStatusResponse = paymentStatusResponseResultObject.getData();
                        paymentStatusResponses.add(paymentStatusResponse);
                    }
                });

                reportEndorseList.forEach(reportEndorseResponse -> {
                    paymentStatusResponses.stream().filter(paymentStatusResponse -> reportEndorseResponse.getEndorseId().equals(paymentStatusResponse.getBusinessId())).findFirst().
                            ifPresent(paymentStatusResponse -> {
                                if ("gprj20200304004".equals(reportEndorseResponse.getProjectId())) {
                                    if (AssertUtils.isNotNull(paymentStatusResponse.getActualPayDate())) {
                                        reportEndorseResponse.setActualPaymentDate(paymentStatusResponse.getActualPayDate());
                                    }else {
                                        reportEndorseResponse.setActualPaymentDate(paymentStatusResponse.getAuditDate());
                                    }
                                }
                            });
                });*/

                List<String> endorseItemIds = reportEndorseList.stream()
                        .filter(returnVisitReportResponse -> AssertUtils.isNotEmpty(returnVisitReportResponse.getEndorseItemId()))
                        .map(ReportEndorseResponse::getEndorseItemId)
                        .distinct()
                        .collect(Collectors.toList());
                //删除指定数据
                endorseIds.addAll(endorseItemIds);
                reportBaseService.deleteReport(REPORT_TYPE.ENDORSE.name(), startDate, currentTime, endorseIds);
                //保存保全数据
                List<ReportEndorsePo> reportEndorsePos = JSON.parseArray(JSON.toJSONString(reportEndorseList), ReportEndorsePo.class);
                reportBaseService.saveReportEndorse(reportEndorsePos);
                //保存保全支付数据
                List<ReportEndorsePaymentResponse> reportEndorsePaymentList = endorseListResponse.getReportEndorsePaymentList();
                if (AssertUtils.isNotEmpty(reportEndorsePaymentList)) {
                    List<ReportEndorsePaymentPo> reportEndorsePaymentPos = JSON.parseArray(JSON.toJSONString(reportEndorsePaymentList), ReportEndorsePaymentPo.class);
                    reportBaseService.saveReportEndorsePayment(reportEndorsePaymentPos);
                }
                Integer totalLine = AssertUtils.isNotEmpty(reportEndorseList) ? reportEndorseList.get(0).getTotalLine() : 0;
                if (totalLine > basePageRequest.getCurrentPage() * basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    /**
     * 执行月度统计报表
     *
     * @return Void
     */
    @Override
    public ResultObject<Void> syncMonthlyStatistics() {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("开始保存月度统计报表:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.MONTHLY_STATISTICS.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(1L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.MONTHLY_STATISTICS.name());
            String startDate = reportBatchPo.getStartBatchDate();
            //开始保存月度统计报表
            reportMonthlyStatisticsTransData.transReportMonthlyStatistics(startDate);

            long startDateTime = DateUtils.stringToTime(startDate, DateUtils.FORMATE2);
            long nextMonthDate = DateUtils.addStringMonthRT(startDateTime, 1);
            reportBatchPo.setStartBatchDate(DateUtils.timeStrToString(nextMonthDate, DateUtils.FORMATE2));
            reportBaseService.saveReportBatch(reportBatchPo);
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return ResultObject.success();
    }

    /**
     * 月度统计特殊处理
     *
     * @param reportUWSpecialTreatmentRequest
     * @return Void
     */
    @Override
    @Transactional
    public ResultObject<Void> monthlyStatisticsSpecialTreatment(ReportUWSpecialTreatmentRequest reportUWSpecialTreatmentRequest) {
        LOGGER.info("月度统计特殊处理:" + JackSonUtils.toJson(reportUWSpecialTreatmentRequest));
        if (!AssertUtils.isNotNull(reportUWSpecialTreatmentRequest.getBusinessTime())
                || !AssertUtils.isNotNull(reportUWSpecialTreatmentRequest.getCurrentTime())
                || !AssertUtils.isNotNull(reportUWSpecialTreatmentRequest.getTotalPremium())
                || !AssertUtils.isNotEmpty(reportUWSpecialTreatmentRequest.getProductType())
                || !AssertUtils.isNotEmpty(reportUWSpecialTreatmentRequest.getBusinessType())
        ) {
            return ResultObject.success();
        }
        String businessTimeMonth = DateUtils.timeStrToString(reportUWSpecialTreatmentRequest.getBusinessTime(), DateUtils.FORMATE2);
        String currentTimeMonth = DateUtils.timeStrToString(DateUtils.addStringMonthRT(reportUWSpecialTreatmentRequest.getCurrentTime(), -1), DateUtils.FORMATE2);
        if (!businessTimeMonth.equals(currentTimeMonth)) {
            LOGGER.info("无需特殊处理月度统计报表");
            return ResultObject.success();
        }
        ReportMonthlyStatisticsPo reportMonthlyStatisticsPo = reportMonthlyStatisticsDao.fetchOneByStatisticalDateFormat(businessTimeMonth);
        if (AssertUtils.isNotNull(reportMonthlyStatisticsPo)) {
            BigDecimal totalPremium = reportUWSpecialTreatmentRequest.getTotalPremium();
            String businessType = reportUWSpecialTreatmentRequest.getBusinessType();
            boolean equalsPolicy = REPORT_TYPE.POLICY.name().equals(businessType);
            //新单业务量
            reportMonthlyStatisticsPo.setApplyPolicyTotalPremiumSum(reportMonthlyStatisticsPo.getApplyPolicyTotalPremiumSum().add(totalPremium));
            if (ReportTermEnum.PRODUCT_TYPE.SHORT.name().equals(reportUWSpecialTreatmentRequest.getProductType())) {
                //短期险业务量  短期有效保单积存
                reportMonthlyStatisticsPo.setApplyPolicyShortTotalPremiumSum(reportMonthlyStatisticsPo.getApplyPolicyShortTotalPremiumSum().add(totalPremium));
                //承保时算上有效保单积存
                if (equalsPolicy) {
                    reportMonthlyStatisticsPo.setShortTermInsuranceSum(reportMonthlyStatisticsPo.getShortTermInsuranceSum() + 1);
                }
            }
            if (ReportTermEnum.PRODUCT_TYPE.LONG.name().equals(reportUWSpecialTreatmentRequest.getProductType())) {
                //长期险业务量  长期有效保单积存
                reportMonthlyStatisticsPo.setApplyPolicyLongTotalPremiumSum(reportMonthlyStatisticsPo.getApplyPolicyLongTotalPremiumSum().add(totalPremium));
                //承保时算上有效保单积存
                if (equalsPolicy) {
                    reportMonthlyStatisticsPo.setLongTermLifeInsuranceSum(reportMonthlyStatisticsPo.getLongTermLifeInsuranceSum() + 1);
                }
            }
            //新单处理件数
            reportMonthlyStatisticsPo.setNewPolicyNum(reportMonthlyStatisticsPo.getNewPolicyNum() + 1);
            //处理件数合计
            reportMonthlyStatisticsPo.setProcessedTotalSum(reportMonthlyStatisticsPo.getProcessedTotalSum() + 1);
            //历年投保人累计
            if (equalsPolicy && AssertUtils.isNotNull(reportUWSpecialTreatmentRequest.getApplicantNum()) && 0 != reportUWSpecialTreatmentRequest.getApplicantNum()) {
                reportMonthlyStatisticsPo.setApplicantAccumulatedOverTheYears(reportMonthlyStatisticsPo.getApplicantAccumulatedOverTheYears() + reportUWSpecialTreatmentRequest.getApplicantNum());
            }
            //历年被保人累计
            if (equalsPolicy && AssertUtils.isNotNull(reportUWSpecialTreatmentRequest.getInsuredNum()) && 0 != reportUWSpecialTreatmentRequest.getInsuredNum()) {
                reportMonthlyStatisticsPo.setInsuredAccumulatedOverTheYears(reportMonthlyStatisticsPo.getInsuredAccumulatedOverTheYears() + reportUWSpecialTreatmentRequest.getInsuredNum());
            }
            reportMonthlyStatisticsDao.update(reportMonthlyStatisticsPo);
        }
        return ResultObject.success();
    }

    private void finallyDone(ReportCollectPo reportCollectPo, boolean errorFlag) {
        //没报错,更新数目为0就不保存了
        if (!errorFlag && reportCollectPo.getTotal() == 0) {
            return;
        }
        TransactionStatus finallyTransactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            ReportCollectPo newReportCollectPo = new ReportCollectPo();
            ClazzUtils.copyPropertiesIgnoreNull(reportCollectPo, newReportCollectPo);
            if (!errorFlag) {
                //存入成功记录
                newReportCollectPo.setIsSuccess("SUCCESS");
            }
            reportBaseService.saveReportCollect(newReportCollectPo, null);
            //提交事物
            platformTransactionManager.commit(finallyTransactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            platformTransactionManager.rollback(finallyTransactionStatus);
        }
    }

    /**
     * 批量同步续期清单
     *
     * @param basePageRequest 分页参数
     * @return 同步结果
     */
    @Override
    public String syncReportRenewal(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步续期数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(RENEWAL.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        List<ReportRenewalPo> reportRenewalPos = new ArrayList<>();
        List<ReportRenewalCoveragePo> reportRenewalCoveragePos = new ArrayList<>();
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(RENEWAL.name());
            //处理时间往前推一个小时
//            String startDate = this.subtractOneHour(reportBatchPo.getStartBatchDate());
            String startDate = reportBatchPo.getStartBatchDate();
            // 个险续期
            ResultObject<List<ReportRenewalResponse>> renewalReportReqFcs = renewalReportApi.listRenewalReport(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            // 团险续期
            ResultObject<Map<String, String>> mapResultObject = renewalReportApi.listGroupInstallmentReport(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            if (!AssertUtils.isResultObjectListDataNull(renewalReportReqFcs)) {
                List<ReportRenewalResponse> renewalReportData = renewalReportReqFcs.getData();
                reportCollectPo.setTotal((long) renewalReportData.size());

                //组装数据
                List<String> businessNo = renewalReportData.stream().filter(reportRenewalBoPredicate -> AssertUtils.isNotEmpty(reportRenewalBoPredicate.getPolicyId()))
                        .map(ReportRenewalResponse::getPolicyNo).distinct().collect(Collectors.toList());

                List<String> renewalIds = renewalReportData.stream().filter(reportRenewalBoPredicate -> AssertUtils.isNotEmpty(reportRenewalBoPredicate.getRenewalId()))
                        .map(ReportRenewalResponse::getRenewalId).distinct().collect(Collectors.toList());
                //将同步到的ID查原有续期数据
                List<ReportRenewalPo> originReportRenewalPos = reportQueryBaseService.queryReportRenewalByRenewalId(renewalIds);

                ResultObject<List<com.gclife.policy.model.response.PolicyReportResponse>> policyListReportReqFc = policyApi.queryListPolicyBo(businessNo);

                renewalReportData.forEach(reportRenewalResponse -> {
                    if (!AssertUtils.isNotEmpty(reportRenewalResponse.getRenewalId())) {
                        return;
                    }
                    ReportRenewalPo reportRenewalPo = new ReportRenewalPo();
                    //匹配原有数据，存入主键ID以便更新
                    if (AssertUtils.isNotEmpty(originReportRenewalPos)) {
                        originReportRenewalPos.stream().filter(originReportRenewalPo -> originReportRenewalPo.getRenewalId().equals(reportRenewalResponse.getRenewalId()))
                                .findFirst().ifPresent(originReportRenewalPo -> {
                                    reportRenewalPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                    reportRenewalPo.setCreateDate(originReportRenewalPo.getCreateDate());
                                    reportRenewalPo.setCreateUserId(originReportRenewalPo.getCreateUserId());
                                    reportRenewalPo.setReportRenewalId(originReportRenewalPo.getReportRenewalId());
                                });
                    }

                    //保单数据
                    if (!AssertUtils.isResultObjectListDataNull(policyListReportReqFc)) {
                        policyListReportReqFc.getData().forEach(policyReportReqFc -> {
                            if (policyReportReqFc.getPolicyId().equals(reportRenewalResponse.getPolicyId())) {
                                ClazzUtils.copyPropertiesIgnoreNull(policyReportReqFc, reportRenewalPo);
                            }
                        });
                    }

                    ClazzUtils.copyPropertiesIgnoreNull(reportRenewalResponse, reportRenewalPo);
                    //20190117   只有缴费成功的才保留缴费时间和方式
                    if (ReportTermEnum.RENEWAL_STATUS.ACTUAL_PAY.name().equals(reportRenewalResponse.getRenewalStatus()) ||
                            ReportTermEnum.RENEWAL_STATUS.RENEWALED.name().equals(reportRenewalResponse.getRenewalStatus()) ||
                            ReportTermEnum.RENEWAL_STATUS.EFFECTIVE.name().equals(reportRenewalResponse.getRenewalStatus())
                    ) {
                        reportRenewalPo.setPaymentMethodCode(reportRenewalResponse.getPaymentMethodCode());
                        reportRenewalPo.setPaymentDate(reportRenewalResponse.getPaymentDate());
                    } else {
                        reportRenewalPo.setPaymentMethodCode(null);
                        reportRenewalPo.setPaymentDate(null);
                    }
                    reportRenewalPos.add(reportRenewalPo);
                    List<ReportRenewalCoverageResponse> renewalCoverageList = reportRenewalResponse.getRenewalCoverageList();
                    if (AssertUtils.isNotEmpty(renewalCoverageList)) {
                        List<ReportRenewalCoveragePo> renewalCoveragePoList = (List<ReportRenewalCoveragePo>) this.converterList(renewalCoverageList, new TypeToken<List<ReportRenewalCoveragePo>>() {
                        }.getType());
                        reportRenewalCoveragePos.addAll(renewalCoveragePoList);
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportBaseService.deleteReport(RENEWAL.name(), startDate, currentTime, renewalIds);

                reportBaseService.saveReportRenewal(reportRenewalPos, null);
                reportBaseService.saveReportRenewalCoverage(reportRenewalCoveragePos, null);

                List<ReportGroupInstallmentPo> reportGroupInstallmentPos = new ArrayList<>();
                Map<String, String> mapResultObjectData = mapResultObject.getData();
                if (!AssertUtils.isResultObjectError(mapResultObject) && AssertUtils.isNotEmpty(mapResultObjectData)) {
                    List<ReportGroupRenewalBo> reportGroupRenewalBos = JSON.parseArray(mapResultObjectData.get("reportGroupInstallBos"), ReportGroupRenewalBo.class);

                    List<String> groupRenewalIds = reportGroupRenewalBos.stream().map(ReportGroupRenewalBo::getGroupRenewalId).collect(Collectors.toList());

                    //将同步到的ID查原有续期数据
                    List<ReportGroupInstallmentPo> originReportGroupInstallmentPos = reportQueryBaseService.listReportGroupInstallByRenewalId(groupRenewalIds);

                    reportGroupRenewalBos.forEach(reportGroupRenewalBo -> {
                        if (!AssertUtils.isNotEmpty(reportGroupRenewalBo.getGroupRenewalId())) {
                            return;
                        }
                        ReportGroupInstallmentPo reportGroupInstallmentPo = new ReportGroupInstallmentPo();
                        // 匹配原有数据，存入主键ID以便更新
                        if (AssertUtils.isNotEmpty(originReportGroupInstallmentPos)) {
                            originReportGroupInstallmentPos.stream().filter(originReportGroupInstallmentPo -> originReportGroupInstallmentPo.getGroupRenewalId().equals(reportGroupInstallmentPo.getGroupRenewalId()))
                                    .findFirst().ifPresent(originReportGroupInstallmentPo -> {
                                        reportGroupInstallmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                        reportGroupInstallmentPo.setCreatedDate(originReportGroupInstallmentPo.getCreatedDate());
                                        reportGroupInstallmentPo.setCreatedUserId(originReportGroupInstallmentPo.getCreatedUserId());
                                        reportGroupInstallmentPo.setReportGroupRenewalId(originReportGroupInstallmentPo.getReportGroupRenewalId());
                                    });
                        }

                        BeanUtils.copyProperties(reportGroupRenewalBo, reportGroupInstallmentPo);
                        reportGroupInstallmentPos.add(reportGroupInstallmentPo);
                    });

                    // 删除团险续期指定时间段的数据,不删除已存在待更新的数据
                    reportBaseService.deleteReport(GROUP_INSTALLMENT.name(), startDate, currentTime, groupRenewalIds);
                    reportBaseService.saveReportGroupInstall(reportGroupInstallmentPos, null);
                }
                if (AssertUtils.isNotEmpty(renewalReportData) && renewalReportData.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }


    /**
     * 公共方法---查询对应开始时间
     */
    private ReportBatchPo queryReportBatchPo(String reportType) {
        return reportBaseService.queryOneReportBatchByType(reportType);
    }

    private void saveBatch(ReportBatchPo reportBatchPo, Long currentTime) {
        reportBatchPo.setStartBatchDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE51));
        reportBaseService.saveReportBatch(reportBatchPo);
    }

    /**
     * 减去一小时
     *
     * @param dateStr 日期字符串
     * @return String
     * @throws Exception 异常
     */
    private String subtractOneHour(String dateStr) throws Exception {
        return DateUtils.dateToString(DateUtils.addHours(DateUtils.stringToDate(dateStr, DateUtils.FORMATE51), -1), DateUtils.FORMATE51);
    }

    /**
     * 同步 实收业绩明细表 数据
     *
     * @param reportPaymentPos
     */
    public void syncActualPerformance(List<ReportPaymentBo> reportPaymentPos) {
        reportPaymentPos.removeIf(reportPaymentPo -> !(PAYMENT_SUCCESS.name().equals(reportPaymentPo.getStatus()) && AssertUtils.isNotEmpty(reportPaymentPo.getReceiptNoJson())));
        reportPaymentPos.forEach(reportPaymentPo -> {
            if (!AssertUtils.isNotEmpty(reportPaymentPo.getPaymentId())) {
                reportPaymentPo.setPaymentId(reportPaymentPo.getReceiptId());
            }
        });
        List<ActualPerformanceReportBo> reportPaymentPoList = (List<ActualPerformanceReportBo>) this.converterList(reportPaymentPos, new TypeToken<List<ActualPerformanceReportBo>>() {
        }.getType());
        if (!AssertUtils.isNotEmpty(reportPaymentPoList)) {
            return;
        }

        List<String> agentIds = reportPaymentPoList.stream().map(ActualPerformanceReportBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(agentIds);
        ResultObject<List<AgentResponse>> agentsGet = agentApi.agentsGet(agentApplyQueryRequest);

        for (ActualPerformanceReportBo actualPerformanceReportBo : reportPaymentPoList) {
            if (!AssertUtils.isResultObjectListDataNull(agentsGet)) {
                agentsGet.getData().stream().filter(agentResponse -> agentResponse.getAgentId().equals(actualPerformanceReportBo.getAgentId()))
                        .findFirst().ifPresent(agentResponse -> actualPerformanceReportBo.setAgentName(agentResponse.getAgentName()));
            }
        }
        if (!AssertUtils.isNotEmpty(reportPaymentPoList)) {
            return;
        }
        List<String> paymentIdList = reportPaymentPoList.stream().map(ActualPerformanceReportBo::getPaymentId).collect(Collectors.toList());
        reportActualPerformanceBaseDao.deleteByPaymentId(paymentIdList);

        List<String> policyTypeList = Arrays.asList(APPLY.name(), APPLY_GROUP.name());
        List<String> renewalTypeList = Arrays.asList(POLICY_RENEWAL_INSURANCE.name(), POLICY_RENEWAL_PAYMENT.name(), GROUP_RENEWAL.name());
        List<String> endorseTypeList = Arrays.asList(GROUP_ADD_INSURED.name(), GROUP_SUBTRACT_INSURED.name(), GROUP_ADD_ADDITIONAL.name(), GROUP_ADD_SUBTRACT_INSURED.name(), POLICY_ENDORSE.name());

        //查询当时代理人职级
        List<Map<String, Object>> agentListMap = new ArrayList<>();
        for (ActualPerformanceReportBo actualPerformanceReportBo : reportPaymentPoList) {
            String agentId = actualPerformanceReportBo.getAgentId();
            Long actualPayDate = actualPerformanceReportBo.getActualPayDate();
            if (!(AssertUtils.isNotEmpty(agentId) && (AssertUtils.isNotNull(actualPayDate)))) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("agentId", actualPerformanceReportBo.getAgentId());
            map.put("date", actualPerformanceReportBo.getActualPayDate());
            agentListMap.add(map);
        }
        if (AssertUtils.isNotEmpty(agentListMap)) {
            ResultObject<List<Map<String, Object>>> resultObject = agentReportApi.queryAgentDateStatus(agentListMap);
            AssertUtils.isResultObjectError(this.getLogger(), resultObject);
            agentListMap = resultObject.getData();
            for (ActualPerformanceReportBo aprBo : reportPaymentPoList) {
                agentListMap.forEach(map -> {
                    String agentId = map.get("agentId") + "";
                    String date = map.get("date") + "";
                    if (agentId.equals(aprBo.getAgentId()) && (aprBo.getActualPayDate() + "").equals(date)) {
                        aprBo.setAgentTypeCode(map.get("agentTypeCode") + "");
                        aprBo.setAgentLevelId(map.get("agentLevelId") + "");
                    }
                });
            }
        }

        List<String> applyIdList = reportPaymentPoList.stream()
                .filter(aprBo -> policyTypeList.contains(aprBo.getBusinessType()) && AssertUtils.isNotEmpty(aprBo.getApplyId()))
                .map(ActualPerformanceReportBo::getApplyId).collect(Collectors.toList());
        List<String> renewalIdList = reportPaymentPoList.stream()
                .filter(aprBo -> renewalTypeList.contains(aprBo.getBusinessType()) && AssertUtils.isNotEmpty(aprBo.getBusinessId()))
                .map(ActualPerformanceReportBo::getBusinessId).collect(Collectors.toList());
        List<String> endorseIdList = reportPaymentPoList.stream()
                .filter(aprBo -> endorseTypeList.contains(aprBo.getBusinessType()) && AssertUtils.isNotEmpty(aprBo.getBusinessId()))
                .map(ActualPerformanceReportBo::getBusinessId).collect(Collectors.toList());
        //获取 新契约 数据
        List<ActualPerformanceReportBo> actualPerformanceReportBoList = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyIdList)) {
            ResultObject<List<ActualPerformanceReportBo>> exportActualPerformance = applyReportApi.queryActualPerformance(applyIdList);
            AssertUtils.isResultObjectError(this.getLogger(), exportActualPerformance);
            List<ActualPerformanceReportBo> policyReportBoList = exportActualPerformance.getData();
            List<ActualPerformanceReportBo> renewalReportBoList = new ArrayList<>();
            policyReportBoList.forEach(aprBo -> {
                reportPaymentPoList.stream().filter(actualPerformanceReportBo ->
                                actualPerformanceReportBo.getBusinessId().equals(aprBo.getApplyId())
                                        && policyTypeList.contains(actualPerformanceReportBo.getBusinessType()))
                        .findFirst()
                        .ifPresent(actualPerformanceReportBo -> {
                            aprBo.setReceiptNo(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setReceiptNoJson(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setBusinessId(actualPerformanceReportBo.getBusinessId());
                            aprBo.setBusinessType(actualPerformanceReportBo.getBusinessType());
                            aprBo.setActualPayDate(actualPerformanceReportBo.getActualPayDate());
                            aprBo.setPaymentMethodCode(actualPerformanceReportBo.getPaymentMethodCode());
                            aprBo.setAgentId(actualPerformanceReportBo.getAgentId());
                            aprBo.setAgentCode(actualPerformanceReportBo.getAgentCode());
                            aprBo.setAgentTypeCode(actualPerformanceReportBo.getAgentTypeCode());
                            aprBo.setAgentLevelId(actualPerformanceReportBo.getAgentLevelId());
                            aprBo.setPaymentId(actualPerformanceReportBo.getPaymentId());
                            aprBo.setReportPaymentId(actualPerformanceReportBo.getReportPaymentId());
                            aprBo.setAgentName(actualPerformanceReportBo.getAgentName());
                            //若是现金折扣，则只设置主险，不用设置附加险
                            if (AssertUtils.isNotNull(actualPerformanceReportBo.getSpecialDiscount()) && AssertUtils.isNotNull(actualPerformanceReportBo.getDiscountModel())) {
                                if (ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(actualPerformanceReportBo.getDiscountModel())) {
                                    aprBo.setSpecialDiscount(actualPerformanceReportBo.getSpecialDiscount());
                                    aprBo.setPromotionType(actualPerformanceReportBo.getPromotionType());
                                    aprBo.setDiscountType(actualPerformanceReportBo.getDiscountType());
                                    aprBo.setDiscountModel(actualPerformanceReportBo.getDiscountModel());
                                }
                                if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(actualPerformanceReportBo.getDiscountModel()) && ReportTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(aprBo.getPrimaryFlag())) {
                                    aprBo.setSpecialDiscount(actualPerformanceReportBo.getSpecialDiscount());
                                    aprBo.setPromotionType(actualPerformanceReportBo.getPromotionType());
                                    aprBo.setDiscountType(actualPerformanceReportBo.getDiscountType());
                                    aprBo.setDiscountModel(actualPerformanceReportBo.getDiscountModel());
                                }
                            }
                            // 实收业绩报表实收时间特殊处理(暂收转实收)
                            reportPaymentPos.stream().filter(reportPaymentBo -> PaymentTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM.name().equals(reportPaymentBo.getPaymentType())
                                    && reportPaymentBo.getPaymentId().equals(aprBo.getPaymentId()) && AssertUtils.isNotEmpty(aprBo.getPolicyNo())
                            ).findFirst().ifPresent(reportPaymentBo -> aprBo.setActualPayDate(aprBo.getEffectiveDate()));

                            //aprBo.setOriginalPremium(actualPerformanceReportBo.getOriginalPremium());
                            aprBo.setRemarks(actualPerformanceReportBo.getRemarks());
                            if (!AssertUtils.isNotEmpty(aprBo.getPremiumPeriod())) {
                                aprBo.setPremiumPeriod(actualPerformanceReportBo.getPremiumPeriod());
                            }
                            aprBo.setFrequency(1L);
                            aprBo.setPolicyYear(1L);
                            aprBo.setPolicyEffectiveDate(aprBo.getEffectiveDate());
                        });
                String receiptNoJson = aprBo.getReceiptNoJson();
                if (AssertUtils.isNotNull(receiptNoJson) && receiptNoJson.indexOf(",") > 0) {
                    String[] receiptNos = receiptNoJson.split(",");
                    for (int i = 0; i < receiptNos.length; i++) {
                        String receiptNo = receiptNos[i];
                        if (i == 0) {
                            aprBo.setReceiptNo(receiptNo);
                        } else {
                            ActualPerformanceReportBo actualPerformanceReportBo = new ActualPerformanceReportBo();
                            ClazzUtils.copyPropertiesIgnoreNull(aprBo, actualPerformanceReportBo);
                            actualPerformanceReportBo.setReceiptNo(receiptNo);
                            actualPerformanceReportBo.setRenewalType(RENEWAL.name());
                            actualPerformanceReportBo.setFrequency(i + 1l);
                            actualPerformanceReportBo.setPolicyYear(1l);
                            if (AssertUtils.isNotNull(actualPerformanceReportBo.getActualPayDate())) {
                                actualPerformanceReportBo.setRenewalDate(DateUtils.addStringMonthRT(actualPerformanceReportBo.getActualPayDate(), i));
                            }
                            actualPerformanceReportBo.setBusinessType(POLICY_RENEWAL_PAYMENT.name());
                            renewalReportBoList.add(actualPerformanceReportBo);
                        }
                    }
                }
            });
            actualPerformanceReportBoList.addAll(policyReportBoList);
            actualPerformanceReportBoList.addAll(renewalReportBoList);
        }

        //续期 续保
        if (AssertUtils.isNotEmpty(renewalIdList)) {
            ResultObject<List<ActualPerformanceReportBo>> exportActualPerformance = renewalReportApi.queryActualPerformance(renewalIdList);
            AssertUtils.isResultObjectError(this.getLogger(), exportActualPerformance);
            List<ActualPerformanceReportBo> renewalReportBoList = exportActualPerformance.getData();
            Map<String, Object> receiptNoJsonMap = new HashMap<>();
            renewalReportBoList.forEach(aprBo -> {
                reportPaymentPoList.stream().filter(actualPerformanceReportBo ->
                                (actualPerformanceReportBo.getBusinessId().equals(aprBo.getRenewalId())
                                        || actualPerformanceReportBo.getBusinessId().equals(aprBo.getPaymentTransactionId()))
                                        && renewalTypeList.contains(actualPerformanceReportBo.getBusinessType()))
                        .findFirst()
                        .ifPresent(actualPerformanceReportBo -> {
                            aprBo.setReceiptNo(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setReceiptNoJson(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setBusinessId(actualPerformanceReportBo.getBusinessId());
                            aprBo.setBusinessType(actualPerformanceReportBo.getBusinessType());
                            aprBo.setActualPayDate(actualPerformanceReportBo.getActualPayDate());
                            aprBo.setPaymentMethodCode(actualPerformanceReportBo.getPaymentMethodCode());
                            aprBo.setReceiptNo(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setAgentId(actualPerformanceReportBo.getAgentId());
                            aprBo.setAgentCode(actualPerformanceReportBo.getAgentCode());
                            aprBo.setAgentTypeCode(actualPerformanceReportBo.getAgentTypeCode());
                            aprBo.setAgentLevelId(actualPerformanceReportBo.getAgentLevelId());
                            aprBo.setApplyId(actualPerformanceReportBo.getApplyId());
                            aprBo.setApplyNo(actualPerformanceReportBo.getApplyNo());
                            aprBo.setPolicyId(actualPerformanceReportBo.getPolicyId());
                            aprBo.setPolicyNo(actualPerformanceReportBo.getPolicyNo());
                            aprBo.setApplicantName(actualPerformanceReportBo.getApplicantName());
                            aprBo.setPaymentId(actualPerformanceReportBo.getPaymentId());
                            aprBo.setReportPaymentId(actualPerformanceReportBo.getReportPaymentId());
                            aprBo.setAgentName(actualPerformanceReportBo.getAgentName());
                            aprBo.setDutyName(actualPerformanceReportBo.getDutyName());
                            //aprBo.setSpecialDiscount(actualPerformanceReportBo.getSpecialDiscount());
                            //aprBo.setOriginalPremium(actualPerformanceReportBo.getOriginalPremium());
                            if (!AssertUtils.isNotEmpty(aprBo.getPremiumPeriod())) {
                                aprBo.setPremiumPeriod(actualPerformanceReportBo.getPremiumPeriod());
                            }
                            aprBo.setPolicyEffectiveDate(actualPerformanceReportBo.getPolicyEffectiveDate());
                            aprBo.setRemarks(actualPerformanceReportBo.getRemarks());
                            if (AssertUtils.isNotEmpty(actualPerformanceReportBo.getReceiptNoJson()) && actualPerformanceReportBo.getReceiptNoJson().indexOf(",") > 0) {
                                Object receiptNo = receiptNoJsonMap.get(aprBo.getRenewalId());
                                if (!AssertUtils.isNotNull(receiptNo)) {
                                    Object receiptNoList = receiptNoJsonMap.get(aprBo.getPaymentTransactionId());
                                    if (!AssertUtils.isNotNull(receiptNoList)) {
                                        receiptNoList = Arrays.asList(actualPerformanceReportBo.getReceiptNoJson().split(","));
                                    }
                                    List<String> receiptNos = new ArrayList<String>((List<String>) receiptNoList);
                                    receiptNo = receiptNos.get(0);
                                    receiptNos.remove(receiptNo + "");
                                    receiptNoJsonMap.put(aprBo.getPaymentTransactionId(), receiptNos);
                                    receiptNoJsonMap.put(aprBo.getRenewalId(), receiptNo);
                                }
                                aprBo.setReceiptNo(receiptNo + "");
                            }
                        });
            });
            actualPerformanceReportBoList.addAll(renewalReportBoList);
        }

        //保全
        if (AssertUtils.isNotEmpty(endorseIdList)) {
            ResultObject<List<ActualPerformanceReportBo>> exportActualPerformance = endorseReportApi.queryActualPerformance(endorseIdList);
            AssertUtils.isResultObjectError(this.getLogger(), exportActualPerformance);
            List<ActualPerformanceReportBo> endorseReportBoList = exportActualPerformance.getData();
            endorseReportBoList.forEach(aprBo -> {
                reportPaymentPoList.stream().filter(actualPerformanceReportBo ->
                                actualPerformanceReportBo.getBusinessId().equals(aprBo.getEndorseId())
                                        && endorseTypeList.contains(actualPerformanceReportBo.getBusinessType()))
                        .findFirst()
                        .ifPresent(actualPerformanceReportBo -> {
                            aprBo.setReceiptNo(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setReceiptNoJson(actualPerformanceReportBo.getReceiptNoJson());
                            aprBo.setBusinessId(actualPerformanceReportBo.getBusinessId());
                            aprBo.setBusinessType(actualPerformanceReportBo.getBusinessType());
                            aprBo.setActualPayDate(actualPerformanceReportBo.getActualPayDate());
                            aprBo.setPaymentMethodCode(actualPerformanceReportBo.getPaymentMethodCode());
                            aprBo.setAgentId(actualPerformanceReportBo.getAgentId());
                            aprBo.setAgentCode(actualPerformanceReportBo.getAgentCode());
                            aprBo.setAgentTypeCode(actualPerformanceReportBo.getAgentTypeCode());
                            aprBo.setAgentLevelId(actualPerformanceReportBo.getAgentLevelId());
                            aprBo.setApplyId(actualPerformanceReportBo.getApplyId());
                            aprBo.setApplyNo(actualPerformanceReportBo.getApplyNo());
                            aprBo.setPolicyId(actualPerformanceReportBo.getPolicyId());
                            aprBo.setPolicyNo(actualPerformanceReportBo.getPolicyNo());
                            aprBo.setApplicantName(actualPerformanceReportBo.getApplicantName());
                            aprBo.setPaymentId(actualPerformanceReportBo.getPaymentId());
                            aprBo.setReportPaymentId(actualPerformanceReportBo.getReportPaymentId());
                            //aprBo.setSpecialDiscount(actualPerformanceReportBo.getSpecialDiscount());
                            //aprBo.setOriginalPremium(actualPerformanceReportBo.getOriginalPremium());
                            aprBo.setAgentName(actualPerformanceReportBo.getAgentName());
                            if (!AssertUtils.isNotEmpty(aprBo.getPremiumPeriod())) {
                                aprBo.setPremiumPeriod(actualPerformanceReportBo.getPremiumPeriod());
                            }
                            aprBo.setPolicyEffectiveDate(actualPerformanceReportBo.getPolicyEffectiveDate());
                            if (!AssertUtils.isNotNull(aprBo.getPolicyYear())) {
                                aprBo.setPolicyYear(actualPerformanceReportBo.getPolicyYear());
                            }
                            aprBo.setRemarks(actualPerformanceReportBo.getRemarks());
                        });
            });
            actualPerformanceReportBoList.addAll(endorseReportBoList);
        }
        //插入数据
        if (!AssertUtils.isNotEmpty(actualPerformanceReportBoList)) {
            return;
        }
        List<ReportActualPerformancePo> reportActualPerformancePoList = (List<ReportActualPerformancePo>) this.converterList(actualPerformanceReportBoList, new TypeToken<List<ReportActualPerformancePo>>() {
        }.getType());
        Long currentTime = DateUtils.getCurrentTime();
        reportActualPerformancePoList.forEach(reportActualPerformancePo -> {
            reportActualPerformancePo.setReportActualPerformanceId(UUIDUtils.getUUIDShort());
            reportActualPerformancePo.setCreateDate(currentTime);
        });
        reportActualPerformanceDao.insert(reportActualPerformancePoList);
    }

    @Override
    public ResultObject<Void> syncReserveWithdrawalReport(List<ReserveWithdrawalReportBo> reserveWithdrawalReportBoList) {
        ResultObject<Void> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(reserveWithdrawalReportBoList)) {
            return resultObject;
        }
        List<ReportQuarterlyReserveWithdrawalPo> reportQuarterlyReserveWithdrawalPoList = (List<ReportQuarterlyReserveWithdrawalPo>) this.converterList(reserveWithdrawalReportBoList, new TypeToken<List<ReportQuarterlyReserveWithdrawalPo>>() {
        }.getType());
        Long currentTime = DateUtils.getCurrentTime();
        reportQuarterlyReserveWithdrawalPoList.forEach(reportQuarterlyReserveWithdrawalPo -> {
            reportQuarterlyReserveWithdrawalPo.setReportQuarterlyReserveWithdrawalId(UUIDUtils.getUUIDShort());
            reportQuarterlyReserveWithdrawalPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            reportQuarterlyReserveWithdrawalPo.setCreateDate(currentTime);
        });
        reportQuarterlyReserveWithdrawalDao.insert(reportQuarterlyReserveWithdrawalPoList);
        return resultObject;
    }

    @Override
    public ResultObject saveReserveWithdrawalAttachment(String quarterDate, String languageName) throws IOException {
        ResultObject resultObject = new ResultObject();
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_RESERVE_WITHDRAWAL_TEMPLATE.name());
        AttachmentResponse attachmentResponse = attachmentRespFcResultObject.getData();
        URL url = new URL(attachmentResponse.getUrl());
        InputStream inputStream = url.openStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(workbook);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
        //国际化
        List<String> codeTypes = Arrays.asList(
                TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(),
                TerminologyTypeEnum.ID_TYPE.name(),
                TerminologyTypeEnum.COMPANY_ID_TYPE.name(),
                TerminologyTypeEnum.POLICY_STATUS.name(),
                TerminologyTypeEnum.COVERAGE_STATUS.name(),
                TerminologyTypeEnum.GENDER.name(),
                TerminologyTypeEnum.INSURED_STATUS.name(),
                TerminologyTypeEnum.CHANNEL_TYPE.name(),
                TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name(),
                TerminologyTypeEnum.PAYMENT_WAY.name(),
                ANNUAL_EARNED_INCOME.name(),
                PAYMENT_METHODS.name(),
                "PRODUCT_SALES_PACKAGES",
                "INSURED_TYPE"
        );
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(languageName, codeTypes);
        Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

        paymentWaySyscodeList(languageName, data);
        reportInsuredStatusSyscodeList(languageName);

        productLevelSyscodeList = data.get(TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name());
        productMainProductFlagSyscodeList = data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name());
        productPremiumPeriodUnitSyscodeList = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());
        productPremiumFrequencySyscodeList = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
        productCoveragePeriodUnitSyscodeList = data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());
        idTypeSyscodeList = data.get(TerminologyTypeEnum.ID_TYPE.name());
        companyIdTypeSyscodeList = data.get(TerminologyTypeEnum.COMPANY_ID_TYPE.name());
        policyStatusSyscodeList = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
        coverageStatusSyscodeList = data.get(TerminologyTypeEnum.COVERAGE_STATUS.name());
        gendeSyscodeList = data.get(TerminologyTypeEnum.GENDER.name());
        insuredStatusSyscodeList = data.get(TerminologyTypeEnum.INSURED_STATUS.name());
        //渠道类型
        channelTypeSyscodeList = data.get(TerminologyTypeEnum.CHANNEL_TYPE.name());
        paymentMethodsSyscodeList = data.get(PAYMENT_METHODS.name());
        annualEarnedIncomeSyscodeList = data.get(ANNUAL_EARNED_INCOME.name());
        productSalesPackages = data.get("PRODUCT_SALES_PACKAGES");
        insuredTypesSyscodeList = data.get("INSURED_TYPE");

        ResultObject<List<ProductResponse>> queryProductByIds = productApi.queryProductByIds(new ArrayList<>());
        AssertUtils.isResultObjectError(this.getLogger(), queryProductByIds);
        List<ProductResponse> productResponseList = queryProductByIds.getData();
        printReserveWithdrawal(quarterDate, LONG.name(), sxssfWorkbook.getSheetAt(0), cellStyle, productResponseList);
        printReserveWithdrawal(quarterDate, SHORT.name(), sxssfWorkbook.getSheetAt(1), cellStyle, productResponseList);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        sxssfWorkbook.write(byteArrayOutputStream);
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(byteArrayOutputStream.toByteArray()));
        attachmentRequest.setFileSuffix(attachmentResponse.getFileSuffix());
        byteArrayOutputStream.close();
        ResultObject<AttachmentResponse> responseResultObject = attachmentBase64Api.attachmentPost("DOCUMENT", attachmentRequest);
        AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);
        AttachmentResponse resultObjectData = responseResultObject.getData();

        ReportAttachmentPo reportAttachmentPo = reportAttachmentBaseService.queryReportAttachmentByTypeAndDate(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_RESERVE_WITHDRAWAL_TEMPLATE.name(), quarterDate, languageName);
        if (AssertUtils.isNotNull(reportAttachmentPo)) {
            reportAttachmentPo.setAttachmentId(resultObjectData.getMediaId());
            reportAttachmentPo.setUpdatedDate(DateUtils.getCurrentTime());
            reportAttachmentDao.update(reportAttachmentPo);
        } else {
            reportAttachmentPo = new ReportAttachmentPo();
            reportAttachmentPo.setReportAttachmentId(UUIDUtils.getUUIDShort());
            reportAttachmentPo.setAttachmentTypeCode(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_RESERVE_WITHDRAWAL_TEMPLATE.name());
            reportAttachmentPo.setAttachmentId(resultObjectData.getMediaId());
            reportAttachmentPo.setAttachmentSeq(1l);
            reportAttachmentPo.setQuarterDate(quarterDate);
            reportAttachmentPo.setCreatedDate(DateUtils.getCurrentTime());
            reportAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            reportAttachmentPo.setLanguage(languageName);
            reportAttachmentDao.insert(reportAttachmentPo);
        }

        // 保留三个季度的删除以前季度的数据

        long deleteQuarterDateLong = DateUtils.addStringMonthRT(DateUtils.stringToTime(quarterDate, DateUtils.FORMATE2), -3 * 3);
        String deleteQuarterDate = DateUtils.timeStrToString(deleteQuarterDateLong, DateUtils.FORMATE2);
        reportQuarterlyReserveWithdrawalBaseService.deleteReportQuarterlyReserveWithdrawal(deleteQuarterDate);
        return resultObject;
    }

    private void paymentWaySyscodeList(String languageName, Map<String, List<SyscodeResponse>> data) {
        paymentWaySyscodeList = data.get(TerminologyTypeEnum.PAYMENT_WAY.name());
        if (EN_US.name().equals(languageName)) {
            // 张杰说：只有准备金报表需要这样显示调整
            List<SyscodeResponse> syscodeResponseList = new ArrayList<>();
            SyscodeResponse syscodeResponse = new SyscodeResponse();
            syscodeResponse.setCodeKey("FIXED_LEVEL");
            syscodeResponse.setCodeName("Fixed Level");
            SyscodeResponse syscodeResponse1 = new SyscodeResponse();
            syscodeResponse1.setCodeKey("CONSTANT_PAYMENT");
            syscodeResponse1.setCodeName("Constant payment");
            SyscodeResponse syscodeResponse2 = new SyscodeResponse();
            syscodeResponse2.setCodeKey("FIXED_BASIS");
            syscodeResponse2.setCodeName("fixed-basis");
            syscodeResponseList.add(syscodeResponse);
            syscodeResponseList.add(syscodeResponse1);
            syscodeResponseList.add(syscodeResponse2);
            paymentWaySyscodeList = syscodeResponseList;
        }
    }

    enum REPORT_INSURED_STATUS {

        REPLACED("Replaced", "替换"),
        REPLACED_ADD("Replaced+", "替换+"),
        REPLACED_SUBTRACT("Replaced-", "替换-"),
        ADD("Add", "新员"),
        SUBTRACT("Surrender", "减员"),
        CLAIM("Claim", "理赔");


        private String EN_US;
        private String ZH_CN;

        private REPORT_INSURED_STATUS(String EN_US, String ZH_CN) {
            this.EN_US = EN_US;
            this.ZH_CN = ZH_CN;
        }

        public String getEN_US() {
            return EN_US;
        }

        public String getZH_CN() {
            return ZH_CN;
        }
    }

    private void reportInsuredStatusSyscodeList(String languageName) {
        List<SyscodeResponse> reportInsuredStatusSyscodeList = new ArrayList<>();
        REPORT_INSURED_STATUS[] values = REPORT_INSURED_STATUS.values();
        for (REPORT_INSURED_STATUS report_insured_status : values) {
            SyscodeResponse syscodeResponse = new SyscodeResponse();
            syscodeResponse.setCodeKey(report_insured_status.name());
            if (languageName.equals(ZH_CN.name())) {
                syscodeResponse.setCodeName(report_insured_status.getZH_CN());
            } else {
                syscodeResponse.setCodeName(report_insured_status.getEN_US());
            }
            reportInsuredStatusSyscodeList.add(syscodeResponse);
        }
        this.reportInsuredStatusSyscodeList = reportInsuredStatusSyscodeList;
    }

    private void printReserveWithdrawal(String quarterDate, String productType, Sheet sheetAt, CellStyle cellStyle, List<ProductResponse> productResponseList) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(1);
        basePageRequest.setPageSize(10000);
        do {
            List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBoList = reportQuarterlyReserveWithdrawalBaseService.queryReserveWithdrawalList(quarterDate, productType, basePageRequest);
            if (LONG.name().equals(productType)) {
                printReserveWithdrawalLong(sheetAt, cellStyle, basePageRequest, reportQuarterlyReserveWithdrawalBoList, productResponseList, productType);
            }
            if (SHORT.name().equals(productType)) {
                printReserveWithdrawalShort(sheetAt, cellStyle, basePageRequest, reportQuarterlyReserveWithdrawalBoList, productResponseList, productType);
            }
            int totalLine = AssertUtils.isNotNull(reportQuarterlyReserveWithdrawalBoList) ? AssertUtils.isNotNull(reportQuarterlyReserveWithdrawalBoList.get(0)) ?
                    reportQuarterlyReserveWithdrawalBoList.get(0).getTotalLine() : 0 : 0;
            if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                break;
            }
            basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
        } while (true);
    }

    /**
     * 准备金报表长期险打印
     *
     * @param sheetAt
     * @param cellStyle
     * @param basePageRequest
     * @param reportQuarterlyReserveWithdrawalBoList
     * @param productResponseList
     * @param productType
     */
    private void printReserveWithdrawalLong(Sheet sheetAt, CellStyle cellStyle, BasePageRequest basePageRequest, List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBoList, List<ProductResponse> productResponseList, String productType) {
        if (!AssertUtils.isNotNull(reportQuarterlyReserveWithdrawalBoList)) {
            return;
        }
        List<String> salesBranchIdList = reportQuarterlyReserveWithdrawalBoList.stream().map(ReportQuarterlyReserveWithdrawalPo::getSalesBranchId).collect(Collectors.toList());
        ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchByIds(salesBranchIdList);
        AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
        List<BranchResponse> branchResponseList = listResultObject.getData();

        Map<String, List<ReportQuarterlyReserveWithdrawalBo>> boMap = reportQuarterlyReserveWithdrawalBoList.stream().collect(Collectors.groupingBy(ReportQuarterlyReserveWithdrawalBo::getPolicyId));

        List<ReportQuarterlyReserveWithdrawalBo> reserveWithdrawalBos = reportQuarterlyReserveWithdrawalBoList.stream().filter(r -> AssertUtils.isNotEmpty(r.getProductId())
                && Arrays.asList("PRO880000000000016A","PRO880000000000016B").contains(r.getProductId())).collect(Collectors.toList());

        //16A,16B计算年化保费
        for (ReportQuarterlyReserveWithdrawalBo withdrawalBo : reserveWithdrawalBos) {
            this.transYearPremium(withdrawalBo,boMap);
        }

        int i = ((basePageRequest.getCurrentPage() - 1) * basePageRequest.getPageSize()) + 1;
        int rownum = 1;
        int index = 1;
        for (ReportQuarterlyReserveWithdrawalBo reserveWithdrawalBo : reportQuarterlyReserveWithdrawalBoList) {
            Row writeRow = sheetAt.getRow(i + 1 + rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheetAt.createRow(i + 1 + rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(index + i - 1);
            getCell(cellStyle, writeRow, column++).setCellValue(reserveWithdrawalBo.getPolicyNo());
            getCell(cellStyle, writeRow, column++).setCellValue(reserveWithdrawalBo.getApplyNo());
            Optional<ProductResponse> responseOptional = productResponseList.stream().filter(product -> product.getProductId().equals(reserveWithdrawalBo.getProductId())).findFirst();
            ProductResponse productResponse = new ProductResponse();
            if (responseOptional.isPresent()) {
                productResponse = responseOptional.get();
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(reserveWithdrawalBo.getProductId(), productResponse.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(productMainProductFlagSyscodeList, reserveWithdrawalBo.getPrimaryFlag())));

            //险种编号
            String productNo = getProductNo(productResponseList, reserveWithdrawalBo, productResponse);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(productNo));

            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getPolicyYearStartDate(), DateUtils.FORMATE3)));

            //天正说趸缴设为1，不需要类型
            String premiumPeriodUnit = null;
            if (SINGLE.name().equals(reserveWithdrawalBo.getPremiumPeriodUnit())) {
                premiumPeriodUnit = "1";
            } else {
                premiumPeriodUnit = reserveWithdrawalBo.getPremiumPeriod();

            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(premiumPeriodUnit));
            String oriPremiumFrequency = AssertUtils.isNotEmpty(reserveWithdrawalBo.getOriPremiumFrequency()) ? reserveWithdrawalBo.getOriPremiumFrequency() : reserveWithdrawalBo.getPremiumFrequency();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(productPremiumFrequencySyscodeList, oriPremiumFrequency)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(productPremiumFrequencySyscodeList, reserveWithdrawalBo.getPremiumFrequency())));
            Integer insuredAgeYear = getAgeYear(reserveWithdrawalBo.getInsuredBirthday(), reserveWithdrawalBo.getApproveDate());
            Integer insuredAgeYearAfter = null;
            if ("YEAR".equals(reserveWithdrawalBo.getCoveragePeriodUnit())) {
                insuredAgeYearAfter = Integer.parseInt(reserveWithdrawalBo.getCoveragePeriod());
            }
            if ("AGE".equals(reserveWithdrawalBo.getCoveragePeriodUnit())) {
                insuredAgeYearAfter = Integer.parseInt(reserveWithdrawalBo.getCoveragePeriod()) - insuredAgeYear;
            }
            String coveragePeriod = insuredAgeYearAfter.toString();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(coveragePeriod));
            BigDecimal oriOriginalPremium = AssertUtils.isNotNull(reserveWithdrawalBo.getOriOriginalPremium()) ? reserveWithdrawalBo.getOriOriginalPremium() : reserveWithdrawalBo.getOriginalPremium();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(oriOriginalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getOriginalPremium()));
            BigDecimal yearPremium = BigDecimal.valueOf(ReportTermEnum.PREMIUM_FREQUENCY_CONVERSION_MONTH.valueOf(reserveWithdrawalBo.getPremiumFrequency()).value()).multiply(reserveWithdrawalBo.getOriginalPremium());
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(yearPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getApproveDate(), DateUtils.FORMATE3)));
            Long riskCommencementDate = AssertUtils.isNotNull(reserveWithdrawalBo.getRiskCommencementDate()) ? reserveWithdrawalBo.getRiskCommencementDate() : reserveWithdrawalBo.getApproveDate();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(riskCommencementDate, DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getFrequency()));
            String paymentWay = null;
            String typeOfSumInsured = null;
            if (Arrays.asList(ReportTermEnum.PRODUCT.PRODUCT_5.id(), ReportTermEnum.PRODUCT.PRODUCT_28.id()).contains(reserveWithdrawalBo.getProductMainId())) {
                paymentWay = languageUtils.findCodeName(paymentWaySyscodeList, reserveWithdrawalBo.getPaymentWay());
                typeOfSumInsured = languageUtils.findCodeName(productLevelSyscodeList, reserveWithdrawalBo.getProductLevel());
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(paymentWay));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(typeOfSumInsured));

            String loanInterestRate = AssertUtils.isNotNull(reserveWithdrawalBo.getLoanInterestRate()) ? exportPrint(reserveWithdrawalBo.getLoanInterestRate()) + "%" : null;
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(loanInterestRate));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getReceivableDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInitialAmount()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAssessAmount()));
            //TODO张杰说 保额固定年限
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(Arrays.asList(ReportTermEnum.PRODUCT.PRODUCT_5.id(), ReportTermEnum.PRODUCT.PRODUCT_28.id()).contains(reserveWithdrawalBo.getProductMainId()) ? "1" : null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getTotalActualPremium()));
            //未按期缴纳保费导致保单效力中止。
            //我们有注意到当前准备金报表里的长期险的失效日期是在保费应收日期+宽限期后的日期，即宽限期满的日期。但我们在评估退还客户的现金价值时，是根据保费应收日期（实际未收到，所以保单失效）来计算的 （6.6.0开发任务天正要求）有失效日期才展示
            if ("POLICY_STATUS_IEXPIRE".equals(reserveWithdrawalBo.getPolicyStatus())) {
                if (AssertUtils.isNotNull(reserveWithdrawalBo.getPolicyInvalidDate())) {
                    if (AssertUtils.isNotNull(reserveWithdrawalBo.getReinstatementDate())) {
                        if (reserveWithdrawalBo.getPolicyInvalidDate() > reserveWithdrawalBo.getReinstatementDate()) {
                            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
                        }else {
                            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getPolicyInvalidDate(), DateUtils.FORMATE3)));
                        }
                    }else {
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
                    }
                }else {
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
                }
            }else {
                if (AssertUtils.isNotNull(reserveWithdrawalBo.getPolicyInvalidDate())) {
                    if (AssertUtils.isNotNull(reserveWithdrawalBo.getReinstatementDate())) {
                        if (reserveWithdrawalBo.getPolicyInvalidDate() > reserveWithdrawalBo.getReinstatementDate()) {
                            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
                        }else {
                            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getPolicyInvalidDate(), DateUtils.FORMATE3)));
                        }
                    }else {
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getNextReceivableDate(), DateUtils.FORMATE3)));
                    }
                }else {
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
                }
            }

            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getReinstatementDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getSurrenderDate(), DateUtils.FORMATE3)));
            if ("LIFE_INSURANCE_PERSONAL".equals(reserveWithdrawalBo.getPolicyType())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getApplicantName()));
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(idTypeSyscodeList, reserveWithdrawalBo.getApplicantIdType())));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getCompanyName()));
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(companyIdTypeSyscodeList, reserveWithdrawalBo.getCompanyIdType())));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getApplicantIdNo()));
            String applicantIncomeCodeName = languageUtils.findCodeName(annualEarnedIncomeSyscodeList, reserveWithdrawalBo.getApplicantIncome());
            String applicantIncome = AssertUtils.isNotNull(applicantIncomeCodeName) ? applicantIncomeCodeName : reserveWithdrawalBo.getApplicantIncome();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(applicantIncome));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getLoanAmount()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(idTypeSyscodeList, reserveWithdrawalBo.getInsuredIdType())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredIdNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getInsuredBirthday(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(insuredAgeYear));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(gendeSyscodeList, reserveWithdrawalBo.getInsuredSex())));
            BranchResponse branch = new BranchResponse();
            if (AssertUtils.isNotEmpty(branchResponseList)) {
                Optional<BranchResponse> optionalBranchResponse = branchResponseList.stream().filter(branchResponse -> branchResponse.getBranchId().equals(reserveWithdrawalBo.getSalesBranchId()))
                        .findFirst();
                if (optionalBranchResponse.isPresent()) {
                    branch = optionalBranchResponse.get();
                }
            }
            String channelTypeName = languageUtils.findCodeName(channelTypeSyscodeList, reserveWithdrawalBo.getChannelTypeCode());
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(branch.getBranchName()) + "/" + exportPrint(channelTypeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInitialAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAgentCode()));
            String occupation = AssertUtils.isNotEmpty(reserveWithdrawalBo.getInsuredOccupationCode()) ? "/" : "";
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredOccupationCode()) + occupation + exportPrint(reserveWithdrawalBo.getInsuredOccupationType()));
            //DEATH加费信息
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getDeathTotalAddPremium()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getDeathAddPremiumObjectValue()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getDeathAddPremiumPeriod()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getDeathAddPremiumStartDate(), DateUtils.FORMATE3)));
            //TPD加费信息
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getTpdTotalAddPremium()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getTpdAddPremiumObjectValue()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getTpdAddPremiumPeriod()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getTpdAddPremiumStartDate(), DateUtils.FORMATE3)));
            //ADB加费信息
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAdbTotalAddPremium()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAdbAddPremiumObjectValue()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAdbAddPremiumPeriod()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getAdbAddPremiumStartDate(), DateUtils.FORMATE3)));
            //CI加费信息
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getCiTotalAddPremium()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getCiAddPremiumObjectValue()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getCiAddPremiumPeriod()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getCiAddPremiumStartDate(), DateUtils.FORMATE3)));
            /*getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getEmp()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getEmRate()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getEpy()));*/
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getFep()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getFer()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getFey()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getFerEffectiveDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(paymentMethodsSyscodeList, reserveWithdrawalBo.getPaymentModeCode())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(policyStatusSyscodeList, reserveWithdrawalBo.getPolicyStatus())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getApplicantBirthday(),DateUtils.FORMATE3)));
            Integer applicantAge = getAgeYear(reserveWithdrawalBo.getApplicantBirthday(), reserveWithdrawalBo.getApproveDate());
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(applicantAge));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(gendeSyscodeList, reserveWithdrawalBo.getApplicantSex())));
            //被保险人编号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredCustomerNo()));
            //豁免的 年化保费
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getYearPremium()));
            rownum++;
            index++;
        }
    }

    public void transYearPremium(ReportQuarterlyReserveWithdrawalBo reportQuarterlyReserveWithdrawalBo,Map<String, List<ReportQuarterlyReserveWithdrawalBo>> boMap) {
        List<String> aOneList = Arrays.asList("PRO880000000000013", "PRO880000000000021", "PRO880000000000015", "PRO88000000000008", "PRO88000000000007", "PRO880000000000024");
        List<String> aTwoList = Arrays.asList("PRO880000000000013", "PRO880000000000015", "PRO88000000000008", "PRO88000000000007", "PRO880000000000024");
        List<String> bThreeList = Arrays.asList("PRO880000000000013", "PRO880000000000014", "PRO880000000000015","PRO88000000000009", "PRO88000000000008",
                "PRO88000000000007", "PRO88000000000004",  "PRO880000000000019", "PRO880000000000021", "PRO880000000000022","PRO880000000000024");

        //16A,16B计算年化保费
        if ("PRO880000000000016A".equals(reportQuarterlyReserveWithdrawalBo.getProductId()) && "OPTION_ONE".equals(reportQuarterlyReserveWithdrawalBo.getProductLevel())) {
            List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBos = boMap.get(reportQuarterlyReserveWithdrawalBo.getPolicyId());
            BigDecimal decimal = reportQuarterlyReserveWithdrawalBos.stream().filter(r -> AssertUtils.isNotEmpty(r.getProductId()) && aOneList.contains(r.getProductId()))
                    .map(ReportQuarterlyReserveWithdrawalBo::getPaymentActualPremium).filter(AssertUtils::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            this.calYearPremium(reportQuarterlyReserveWithdrawalBo,decimal);
        }

        if ("PRO880000000000016A".equals(reportQuarterlyReserveWithdrawalBo.getProductId()) && "OPTION_TWO".equals(reportQuarterlyReserveWithdrawalBo.getProductLevel())) {
            List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBos = boMap.get(reportQuarterlyReserveWithdrawalBo.getPolicyId());
            BigDecimal decimal = reportQuarterlyReserveWithdrawalBos.stream().filter(r -> AssertUtils.isNotEmpty(r.getProductId()) && aTwoList.contains(r.getProductId()))
                    .map(ReportQuarterlyReserveWithdrawalBo::getPaymentActualPremium).filter(AssertUtils::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            this.calYearPremium(reportQuarterlyReserveWithdrawalBo,decimal);
        }

        if ("PRO880000000000016B".equals(reportQuarterlyReserveWithdrawalBo.getProductId()) && "OPTION_THREE".equals(reportQuarterlyReserveWithdrawalBo.getProductLevel())) {
            List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBos = boMap.get(reportQuarterlyReserveWithdrawalBo.getPolicyId());
            BigDecimal decimal = reportQuarterlyReserveWithdrawalBos.stream().filter(r -> AssertUtils.isNotEmpty(r.getProductId()) && bThreeList.contains(r.getProductId()))
                    .map(ReportQuarterlyReserveWithdrawalBo::getPaymentActualPremium).filter(AssertUtils::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            this.calYearPremium(reportQuarterlyReserveWithdrawalBo,decimal);
        }
    }

    public void calYearPremium(ReportQuarterlyReserveWithdrawalBo reportQuarterlyReserveWithdrawalBo,BigDecimal decimal) {
        String premiumFrequency = reportQuarterlyReserveWithdrawalBo.getPremiumFrequency();
        if (ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            BigDecimal bigDecimal = decimal.multiply(new BigDecimal("12"));
            reportQuarterlyReserveWithdrawalBo.setYearPremium(bigDecimal);
        } else if (ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
            BigDecimal bigDecimal = decimal.multiply(new BigDecimal("4"));
            reportQuarterlyReserveWithdrawalBo.setYearPremium(bigDecimal);
        } else if (ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
            BigDecimal bigDecimal = decimal.multiply(new BigDecimal("2"));
            reportQuarterlyReserveWithdrawalBo.setYearPremium(bigDecimal);
        } else if (ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            reportQuarterlyReserveWithdrawalBo.setYearPremium(decimal);
        } else if (ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
            reportQuarterlyReserveWithdrawalBo.setYearPremium(decimal);
        }
    }

    private Integer getAgeYear(Long insuredBirthday, Long approveDate) {
        try {
            if (AssertUtils.isNotNull(insuredBirthday) && AssertUtils.isNotNull(approveDate)) {
                return DateUtils.getAgeYear(new Date(insuredBirthday), new Date(approveDate));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getProductNo(List<ProductResponse> productResponseList, ReportQuarterlyReserveWithdrawalBo reserveWithdrawalBo, ProductResponse productResponse) {
        String productNo = productResponse.getProductNo();
        if (ProductTermEnum.PRODUCT.PRODUCT_3.id().equals(reserveWithdrawalBo.getProductId())) {
            productNo = productNo + reserveWithdrawalBo.getProductLevel();
        }
        if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_4.id(), ProductTermEnum.PRODUCT.PRODUCT_7.id()).contains(reserveWithdrawalBo.getProductId())) {
            // 保留37/87    去掉17改为7
            Optional<ProductResponse> productOptional = productResponseList.stream().filter(product -> !ProductTermEnum.PRODUCT.PRODUCT_1.id().equals(reserveWithdrawalBo.getProductMainId())
                    && product.getProductId().equals(reserveWithdrawalBo.getProductMainId())).findFirst();
            if (productOptional.isPresent()) {
                productNo = productOptional.get().getProductNo() + productNo;
                if (ProductTermEnum.PRODUCT.PRODUCT_4.id().equals(reserveWithdrawalBo.getProductId())) {
                    productNo = productNo + (reserveWithdrawalBo.getProductLevel().equals("AMOUNT_UP") ? "1" : reserveWithdrawalBo.getProductLevel().equals("AMOUNT_FIXED") ? "0" : "");
                }
            }
        }
        if (AssertUtils.isNotEmpty(reserveWithdrawalBo.getDutyId())) {
            productNo = productNo +
                    (reserveWithdrawalBo.getDutyId().equals("PRO8800000000000G12_DUTY_1") ? "A" :
                            reserveWithdrawalBo.getDutyId().equals("PRO8800000000000G12_DUTY_2") ? "B" :
                                    reserveWithdrawalBo.getDutyId().equals("PRO8800000000000G12_DUTY_3") ? "C" : "");
        }
        if (ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(reserveWithdrawalBo.getProductId()) && "TA".equals(reserveWithdrawalBo.getProductLevel())) {
            productNo = productNo + "a";
        }
        if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), ProductTermEnum.PRODUCT.PRODUCT_23B.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(reserveWithdrawalBo.getProductId())) {
            productNo = productNo + ("OPTION_ONE".equals(reserveWithdrawalBo.getProductLevel()) ? "(1)" : "(2)");
        }
        return productNo;
    }

    /**
     * 准备金报表短期险打印
     *
     * @param sheetAt
     * @param cellStyle
     * @param basePageRequest
     * @param reportQuarterlyReserveWithdrawalBoList
     * @param productResponseList
     * @param productType
     */
    private void printReserveWithdrawalShort(Sheet sheetAt, CellStyle cellStyle, BasePageRequest basePageRequest, List<ReportQuarterlyReserveWithdrawalBo> reportQuarterlyReserveWithdrawalBoList, List<ProductResponse> productResponseList, String productType) {
        if (!AssertUtils.isNotNull(reportQuarterlyReserveWithdrawalBoList)) {
            return;
        }
        List<String> salesBranchIdList = reportQuarterlyReserveWithdrawalBoList.stream().map(ReportQuarterlyReserveWithdrawalPo::getSalesBranchId).collect(Collectors.toList());
        ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchByIds(salesBranchIdList);
        AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
        List<BranchResponse> branchResponseList = listResultObject.getData();

        int i = ((basePageRequest.getCurrentPage() - 1) * basePageRequest.getPageSize()) + 1;
        int rownum = 1;
        for (ReportQuarterlyReserveWithdrawalBo reserveWithdrawalBo : reportQuarterlyReserveWithdrawalBoList) {
            Row writeRow = sheetAt.getRow(i + 1 + rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheetAt.createRow(i + 1 + rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i - 1 + rownum);
            getCell(cellStyle, writeRow, column++).setCellValue(reserveWithdrawalBo.getPolicyNo());
            getCell(cellStyle, writeRow, column++).setCellValue(reserveWithdrawalBo.getApplyNo());
            Optional<ProductResponse> responseOptional = productResponseList.stream().filter(product -> product.getProductId().equals(reserveWithdrawalBo.getProductId())).findFirst();
            ProductResponse productResponse = new ProductResponse();
            if (responseOptional.isPresent()) {
                productResponse = responseOptional.get();
            }
            String productName = LanguageUtils.getENProductName(reserveWithdrawalBo.getProductId(), productResponse.getProductName());
            String primaryFlag = reserveWithdrawalBo.getPrimaryFlag();
            if (ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(reserveWithdrawalBo.getProductId()) && "TA".equals(reserveWithdrawalBo.getProductLevel())) {
                productName = productName + " - Accidental Death/Disability";
//                primaryFlag = ReportTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name();
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(productName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(productMainProductFlagSyscodeList, primaryFlag)));

            //险种编号
            String productNo = getProductNo(productResponseList, reserveWithdrawalBo, productResponse);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(productNo));

            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getPolicyYearStartDate(), DateUtils.FORMATE3)));
            String premiumPeriodUnit;
            if (SINGLE.name().equals(reserveWithdrawalBo.getPremiumPeriodUnit())) {
                premiumPeriodUnit = languageUtils.findCodeName(productPremiumPeriodUnitSyscodeList, reserveWithdrawalBo.getPremiumPeriodUnit());
            } else {
                premiumPeriodUnit = reserveWithdrawalBo.getPremiumPeriod() + languageUtils.findCodeName(productPremiumPeriodUnitSyscodeList, reserveWithdrawalBo.getPremiumPeriodUnit());
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(premiumPeriodUnit));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(productPremiumFrequencySyscodeList, reserveWithdrawalBo.getPremiumFrequency())));
            String coveragePeriod = reserveWithdrawalBo.getCoveragePeriod() + languageUtils.findCodeName(productCoveragePeriodUnitSyscodeList, reserveWithdrawalBo.getCoveragePeriodUnit());
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(coveragePeriod));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getOriginalPremium()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getApproveDate(), DateUtils.FORMATE3)));
            Long riskCommencementDate = AssertUtils.isNotNull(reserveWithdrawalBo.getRiskCommencementDate()) ? reserveWithdrawalBo.getRiskCommencementDate() : reserveWithdrawalBo.getApproveDate();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(riskCommencementDate, DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getFrequency()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getCoveragePeriodStartDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getCoveragePeriodEndDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInitialAmount()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAssessAmount()));
            if ("LIFE_INSURANCE_PERSONAL".equals(reserveWithdrawalBo.getPolicyType())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getApplicantName()));
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(idTypeSyscodeList, reserveWithdrawalBo.getApplicantIdType())));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getCompanyName()));
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(companyIdTypeSyscodeList, reserveWithdrawalBo.getCompanyIdType())));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getApplicantIdNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getPolicyInvalidDate(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(idTypeSyscodeList, reserveWithdrawalBo.getInsuredIdType())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredIdNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getInsuredBirthday(), DateUtils.FORMATE3)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(getAgeYear(reserveWithdrawalBo.getInsuredBirthday(), reserveWithdrawalBo.getInsuredEffectiveDate())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(gendeSyscodeList, reserveWithdrawalBo.getInsuredSex())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredMobile()));
            String productLevel = reserveWithdrawalBo.getProductLevel();
            String mult = reserveWithdrawalBo.getMult();
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_17.id(), ProductTermEnum.PRODUCT.PRODUCT_18.id(), ProductTermEnum.PRODUCT.PRODUCT_26.id(), ProductTermEnum.PRODUCT.PRODUCT_27.id(), ProductTermEnum.PRODUCT.PRODUCT_29.id(), ProductTermEnum.PRODUCT.PRODUCT_33.id()).contains(reserveWithdrawalBo.getProductId())) {
                mult = null;
                if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_18.id(),ProductTermEnum.PRODUCT.PRODUCT_27.id(), ProductTermEnum.PRODUCT.PRODUCT_29.id()).contains(reserveWithdrawalBo.getProductId())) {
                    productLevel = languageUtils.findCodeName(productLevelSyscodeList, reserveWithdrawalBo.getProductLevel());
                }
            }
            if (ProductTermEnum.PRODUCT.PRODUCT_34.id().equals(reserveWithdrawalBo.getProductId()) && AssertUtils.isNotEmpty(productLevel)) {
                // 34产品特殊处理  JUNIOR_PACKAGES/5 -> Junior Packages/5
                String[] split = productLevel.split("/");
                String packageCode = split[0];
                productLevel = languageUtils.findCodeName(productSalesPackages, packageCode) + "/" + split[1];
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(productLevel));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(mult));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getPaymentActualPremium()));
            String occupation = AssertUtils.isNotEmpty(reserveWithdrawalBo.getInsuredOccupationCode()) ? "/" : "";
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredOccupationCode()) + occupation + exportPrint(reserveWithdrawalBo.getInsuredOccupationType()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(insuredTypesSyscodeList, reserveWithdrawalBo.getInsuredType())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(insuredStatusSyscodeList, reserveWithdrawalBo.getInsuredStatus())));
            String insuredChangeStatus = reserveWithdrawalBo.getInsuredChangeStatus();
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(formatInsuredChangeStatus(insuredChangeStatus)));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(DateUtils.timeStrToString(reserveWithdrawalBo.getInvalidDate(), DateUtils.FORMATE3)));
            BranchResponse branch = new BranchResponse();
            if (AssertUtils.isNotEmpty(branchResponseList)) {
                Optional<BranchResponse> optionalBranchResponse = branchResponseList.stream().filter(branchResponse -> branchResponse.getBranchId().equals(reserveWithdrawalBo.getSalesBranchId()))
                        .findFirst();
                if (optionalBranchResponse.isPresent()) {
                    branch = optionalBranchResponse.get();
                }
            }
            String channelTypeName = languageUtils.findCodeName(channelTypeSyscodeList, reserveWithdrawalBo.getChannelTypeCode());
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(branch.getBranchName()) + "/" + exportPrint(channelTypeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(paymentMethodsSyscodeList, reserveWithdrawalBo.getPaymentModeCode())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(coverageStatusSyscodeList, reserveWithdrawalBo.getCoverageStatus())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.findCodeName(policyStatusSyscodeList, reserveWithdrawalBo.getPolicyStatus())));
            //被保险人编号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reserveWithdrawalBo.getInsuredCustomerNo()));
            rownum++;
        }
    }


    private String formatInsuredChangeStatus(String insuredChangeStatus) {
        if (!AssertUtils.isNotEmpty(insuredChangeStatus)) {
            return insuredChangeStatus;
        }
        String[] split = insuredChangeStatus.split(",");
        insuredChangeStatus = "";
        int length = split.length;
        for (int i = 0; i < length; i++) {
            String codeName = languageUtils.findCodeName(reportInsuredStatusSyscodeList, split[i]);
            insuredChangeStatus = insuredChangeStatus + codeName + (length == (i + 1) ? "" : "_");
        }
        return insuredChangeStatus;
    }


    //金额格式化
    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0.00");

    public static String exportPrint(Object o) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat.format(o);
            }
            return o + "";
        }
        return "-";
    }

    public static Cell getCell(CellStyle cellStyle, Row writeRow, int column) {
        if (AssertUtils.isNotNull(writeRow.getCell(column))) {
            return writeRow.getCell(column);
        }
        Cell cell = writeRow.createCell(column);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    @Override
    public String syncReportGroupRenewal(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步续期数据开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(GROUP_RENEWAL.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(GROUP_RENEWAL.name());
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<Map<String, String>> mapResultObject = renewalReportApi.listGroupRenewalReport(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            Map<String, String> mapResultObjectData = mapResultObject.getData();
            if (!AssertUtils.isResultObjectError(mapResultObject) && AssertUtils.isNotEmpty(mapResultObjectData)) {
                List<ReportGroupRenewalCoverageLevelPo> groupRenewalCoverageLevelPos = JSON.parseArray(mapResultObjectData.get("groupRenewalCoverageLevelPos"), ReportGroupRenewalCoverageLevelPo.class);
                List<ReportGroupRenewalCoverageDutyPo> groupRenewalCoverageDutyBos = JSON.parseArray(mapResultObjectData.get("groupRenewalCoverageDutyBos"), ReportGroupRenewalCoverageDutyPo.class);
                List<ReportGroupRenewalCoveragePo> groupRenewalCoverageList = JSON.parseArray(mapResultObjectData.get("groupRenewalCoverageList"), ReportGroupRenewalCoveragePo.class);
                List<ReportGroupRenewalPo> groupReportRenewalBos = JSON.parseArray(mapResultObjectData.get("groupReportRenewalBos"), ReportGroupRenewalPo.class);

                reportCollectPo.setTotal((long) groupReportRenewalBos.size());

                //组装数据
                List<String> businessNo = groupReportRenewalBos.stream().filter(reportRenewalBoPredicate -> AssertUtils.isNotEmpty(reportRenewalBoPredicate.getPolicyId()))
                        .map(ReportGroupRenewalPo::getPolicyNo).distinct().collect(Collectors.toList());

                List<String> renewalIds = groupReportRenewalBos.stream().filter(reportRenewalBoPredicate -> AssertUtils.isNotEmpty(reportRenewalBoPredicate.getGroupRenewalId()))
                        .map(ReportGroupRenewalPo::getGroupRenewalId).distinct().collect(Collectors.toList());

                ResultObject<List<com.gclife.policy.model.response.PolicyReportResponse>> policyListReportReqFc = policyApi.queryListPolicyBo(businessNo);

                groupReportRenewalBos.forEach(reportRenewalResponse -> {
                    if (!AssertUtils.isNotEmpty(reportRenewalResponse.getGroupRenewalId())) {
                        return;
                    }
                    //保单数据
                    if (!AssertUtils.isResultObjectListDataNull(policyListReportReqFc)) {
                        policyListReportReqFc.getData().forEach(policyReportReqFc -> {
                            if (policyReportReqFc.getPolicyId().equals(reportRenewalResponse.getPolicyId())) {
                                ClazzUtils.copyPropertiesIgnoreNull(policyReportReqFc, reportRenewalResponse);
                            }
                        });
                    }
                });

                //删除指定时间段的数据,不删除已存在待更新的数据
                reportGroupRenewalBaseService.deleteSyncReportGroupRenewal(renewalIds);
                //开始保存数据
                reportGroupRenewalDao.insert(groupReportRenewalBos);
                reportGroupRenewalCoverageDao.insert(groupRenewalCoverageList);
                reportGroupRenewalCoverageLevelDao.insert(groupRenewalCoverageLevelPos);
                reportGroupRenewalCoverageDutyDao.insert(groupRenewalCoverageDutyBos);

                if (AssertUtils.isNotEmpty(groupReportRenewalBos) && groupReportRenewalBos.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }

    @Override
    public ResultObject<Void> syncServiceChargeBankChannel() {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("执行同步银保渠道手续费费用明细表:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.SERVICE_CHARGE_BANK_CHANNEL.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(1L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.SERVICE_CHARGE_BANK_CHANNEL.name());
            BasePageRequest basePageRequest = new BasePageRequest();
            basePageRequest.setCurrentPage(1);
            basePageRequest.setPageSize(10000);
            Integer totalLine;
            do {
                ResultObject<List<ServiceChargeBankChannelBo>> policyServiceChargeBankChannel = policyReportApi.syncPolicyServiceChargeBankChannel(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), reportBatchPo.getStartBatchDate());
                AssertUtils.isResultObjectError(this.getLogger(), policyServiceChargeBankChannel);
                List<ServiceChargeBankChannelBo> data = policyServiceChargeBankChannel.getData();
                if (!AssertUtils.isNotEmpty(data)) {
                    break;
                }
                //数据处理
                insertReportServiceChargeBankChannel(data, reportBatchPo);
                totalLine = data.get(0).getTotalLine();
                if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                    break;
                }
                basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
            } while (true);
            basePageRequest.setCurrentPage(1);
            do {
                ResultObject<List<ServiceChargeBankChannelBo>> policyServiceChargeBankChannel = applyReportApi.syncApplyServiceChargeBankChannel(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), reportBatchPo.getStartBatchDate());
                AssertUtils.isResultObjectError(this.getLogger(), policyServiceChargeBankChannel);
                List<ServiceChargeBankChannelBo> data = policyServiceChargeBankChannel.getData();
                if (!AssertUtils.isNotEmpty(data)) {
                    break;
                }
                //数据处理
                insertReportServiceChargeBankChannel(data, reportBatchPo);
                totalLine = data.get(0).getTotalLine();
                if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                    break;
                }
                basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
            } while (true);
            //加一个月
            reportBatchPo.setStartBatchDate(DateUtils.timeStrToString(DateUtils.addStringMonthRT(DateUtils.stringToTime(reportBatchPo.getStartBatchDate(), DateUtils.FORMATE2), 1), DateUtils.FORMATE2));
            reportBaseService.saveReportBatch(reportBatchPo);

            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return ResultObject.success();
    }

    private void insertReportServiceChargeBankChannel(List<ServiceChargeBankChannelBo> data, ReportBatchPo reportBatchPo) {
        List<ReportServiceChargeBankChannelPo> reportServiceChargeBankChannelPoList = (List<ReportServiceChargeBankChannelPo>) this.converterList(data, new TypeToken<List<ReportServiceChargeBankChannelPo>>() {
        }.getType());
        reportServiceChargeBankChannelPoList.forEach(reportServiceChargeBankChannelPo -> {
            reportServiceChargeBankChannelPo.setReportServiceChargeBankChannelId(UUIDUtils.getUUIDShort());
            reportServiceChargeBankChannelPo.setQuarterDate(reportBatchPo.getStartBatchDate());
        });
        reportServiceChargeBankChannelDao.insert(reportServiceChargeBankChannelPoList);
    }


    @Override
    public ResultObject<Void> syncServiceChargeBankChannelPayment() {

        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("执行同步银保渠道手续费费用明细表-支付明细: {}", DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.SERVICE_CHARGE_BANK_CHANNEL_PAYMENT.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(1L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        try {

            ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.SERVICE_CHARGE_BANK_CHANNEL_PAYMENT.name());
            BasePageRequest basePageRequest = new BasePageRequest();
            basePageRequest.setCurrentPage(1);
            basePageRequest.setPageSize(1000);
            Integer totalLine;
            do {
                ResultObject<List<ServiceChargeBankChannelBo>> policyServiceChargeBankChannel = policyReportApi.syncPolicyServiceChargeBankChannelPayment(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), reportBatchPo.getStartBatchDate());
                AssertUtils.isResultObjectError(this.getLogger(), policyServiceChargeBankChannel);
                List<ServiceChargeBankChannelBo> data = policyServiceChargeBankChannel.getData();
                if (!AssertUtils.isNotEmpty(data)) {
                    break;
                }
                //数据处理
                syncServiceChargeBankChannelPayment(data);
                totalLine = data.get(0).getTotalLine();
                if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                    break;
                }
                basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
            } while (true);

            //更新批次开始时间
            reportBatchPo.setStartBatchDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE51));
            reportBaseService.saveReportBatch(reportBatchPo);


            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return ResultObject.success();
    }

    private void syncServiceChargeBankChannelPayment(List<ServiceChargeBankChannelBo> data) {
        //删除新单承保的统计月份投保单
        List<String> quarterDateApplyNo = data.stream().filter(serviceChargeBankChannelBo -> BUSINESS_TYPE_NEW_CONTRACT.name().equals(serviceChargeBankChannelBo.getPaymentBusinessType()))
                .map(serviceChargeBankChannelBo -> serviceChargeBankChannelBo.getQuarterDate() + serviceChargeBankChannelBo.getApplyNo()).collect(Collectors.toList());
        reportServiceChargeBankChannelBaseService.deleteByQuarterDateApplyNo(quarterDateApplyNo);
        //保存支付手续费数据
        List<String> policyPaymentIdList = data.stream().map(ServiceChargeBankChannelBo::getPolicyPaymentId).collect(Collectors.toList());
        //删除犹豫期撤单的续期缴费
        List<ServiceChargeBankChannelBo> policyStatusHesitationRevokBankChannelList = data.stream()
                .filter(serviceChargeBankChannelBo -> POLICY_STATUS_HESITATION_REVOKE.name().equals(serviceChargeBankChannelBo.getPolicyStatus()))
                .collect(Collectors.toList());

        List<String> policyStatusHesitationRevokeNoList = policyStatusHesitationRevokBankChannelList.stream().map(ServiceChargeBankChannelBo::getPolicyNo).collect(Collectors.toList());
        data.removeIf(serviceChargeBankChannelBo -> policyStatusHesitationRevokeNoList.contains(serviceChargeBankChannelBo.getPolicyNo()) && BUSINESS_TYPE_RENEWAL.name().equals(serviceChargeBankChannelBo.getPaymentBusinessType()));

        policyPaymentIdList.addAll(policyStatusHesitationRevokBankChannelList.stream().map(ServiceChargeBankChannelBo::getPolicyPaymentId).collect(Collectors.toList()));
        reportServiceChargeBankChannelBaseService.deleteByPolicyPaymentId(policyPaymentIdList);
        //保存数据
        List<ReportServiceChargeBankChannelPo> reportServiceChargeBankChannelPoList = (List<ReportServiceChargeBankChannelPo>) this.converterList(data, new TypeToken<List<ReportServiceChargeBankChannelPo>>() {
        }.getType());
        reportServiceChargeBankChannelPoList.forEach(reportServiceChargeBankChannelPo -> {
            reportServiceChargeBankChannelPo.setReportServiceChargeBankChannelId(UUIDUtils.getUUIDShort());
        });
        reportServiceChargeBankChannelDao.insert(reportServiceChargeBankChannelPoList);
    }

    @Override
    @Transactional
    public ResultObject<Void> syncSaleApply() {
        ResultObject resultObject = new ResultObject();

        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步销售报表-投保单: {}" , DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.SALE_APPLY_POLICY.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(1L);
        reportCollectPo.setIsSuccess("INIT");

        ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.SALE_APPLY_POLICY.name());
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(1);
        basePageRequest.setPageSize(20000);
        Integer totalLine;

        do {
            //同步投保单数据
            ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy = applyReportApi.syncSaleApplyPolicy(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), reportBatchPo.getStartBatchDate());
            this.getLogger().info("同步销售报表-投保单数量: {}" , syncSaleApplyPolicy.getData().size());
            AssertUtils.isResultObjectError(this.getLogger(), syncSaleApplyPolicy);
            List<SaleApplyPolicyBo> data = syncSaleApplyPolicy.getData();
            if (!AssertUtils.isNotEmpty(data)) {
                break;
            }
            //数据处理
            totalLine = data.get(0).getTotalLine();
            syncSaveSaleApply(data);
            if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                break;
            }
            basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
        } while (true);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<Void> syncSalePolicy() {
        ResultObject resultObject = new ResultObject();
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步销售报表-保单: {}", DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType(REPORT_TYPE.SALE_APPLY_POLICY.name());
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(1L);
        reportCollectPo.setIsSuccess("INIT");

        ReportBatchPo reportBatchPo = queryReportBatchPo(REPORT_TYPE.SALE_APPLY_POLICY.name());
        BasePageRequest basePageRequest = new BasePageRequest();
        Integer totalLine;
        basePageRequest.setCurrentPage(1);
        basePageRequest.setPageSize(20000);
        do {
            //同步保单数据
            ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy = policyReportApi.syncSaleApplyPolicy(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), reportBatchPo.getStartBatchDate());
            this.getLogger().info("同步销售报表-保单数量: {}", syncSaleApplyPolicy.getData().size());
            AssertUtils.isResultObjectError(this.getLogger(), syncSaleApplyPolicy);
            List<SaleApplyPolicyBo> data = syncSaleApplyPolicy.getData();
            if (!AssertUtils.isNotEmpty(data)) {
                break;
            }
            //数据处理
            totalLine = data.get(0).getTotalLine();
            syncSaveSalePolicy(data);
            if (totalLine <= (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
                break;
            }
            basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
        } while (true);

        //更新批次开始时间
        reportBatchPo.setStartBatchDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE51));
        reportBaseService.saveReportBatch(reportBatchPo);

        return resultObject;
    }

    /**
     * 同步保存销售申请信息
     * 该方法主要用于同步和保存销售申请相关的数据，包括处理投保单备注信息的合并和更新
     *
     * @param data 包含销售申请策略信息的列表
     */
    private void syncSaveSaleApply(List<SaleApplyPolicyBo> data) {
        if (!AssertUtils.isNotEmpty(data)) {
            return;
        }
        // 提取并去重获取所有的申请ID和保单号
        List<String> applyIds = data.stream().map(SaleApplyPolicyBo::getApplyId).distinct().collect(Collectors.toList());
        List<String> policyNos = data.stream().map(SaleApplyPolicyBo::getPolicyNo).distinct().collect(Collectors.toList());

        // 查询数据库中已存在的销售申请信息
        List<ReportSaleApplyPolicyPo> reportSaleApplyPolicyPoList = reportSaleApplyPolicyBaseDao.queryByApplyIds(applyIds);

        //通过投保单id获取投保单备注的所有备注信息
        ResultObject<Map<String, List<ApplyRemarksResponse>>> applyRemark= baseApplyApi.getApplyRemarkByIds(applyIds);
        ResultObject<Map<String, List<ApplyRemarksResponse>>> endorseRemark = groupEndorseApi.getRemark(policyNos);
        ResultObject<Map<String, List<ApplyRemarksResponse>>> renewalRemark = renewalBaseApi.getRemark(policyNos);

        // 如果查询到的销售申请信息不为空，并且有相关的投保单备注信
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyPoList)) {
            // 合并从不同来源获取的投保单备注信息
            if (AssertUtils.isNotEmpty(applyRemark.getData())) {
                Map<String, List<ApplyRemarksResponse>> data1 = applyRemark.getData();
                Map<String, List<ApplyRemarksResponse>> data2 = endorseRemark.getData();
                Map<String, List<ApplyRemarksResponse>> data3 = renewalRemark.getData();

                // 遍历输入数据，合并备注信息并更新到销售申请对象中
                data.stream().filter(saleApplyPolicyBo -> AssertUtils.isNotEmpty(saleApplyPolicyBo.getApplyId()))
                        .forEach(saleApplyPolicyBo -> {
                            List<ApplyRemarksResponse> all = new ArrayList<>();
                            if (AssertUtils.isNotEmpty(data1)) {
                                List<ApplyRemarksResponse> applyRemarksResponses = data1.get(saleApplyPolicyBo.getApplyId());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses)) {
                                    all.addAll(applyRemarksResponses);
                                }
                            }
                            if (AssertUtils.isNotEmpty(saleApplyPolicyBo.getPolicyNo()) && AssertUtils.isNotEmpty(data2)) {
                                List<ApplyRemarksResponse> applyRemarksResponses1 = data2.get(saleApplyPolicyBo.getPolicyNo());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses1)) {
                                    all.addAll(applyRemarksResponses1);
                                }
                            }
                            if (AssertUtils.isNotEmpty(saleApplyPolicyBo.getPolicyNo()) && AssertUtils.isNotEmpty(data3)) {
                                List<ApplyRemarksResponse> applyRemarksResponses2 = data3.get(saleApplyPolicyBo.getPolicyNo());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses2)) {
                                    all.addAll(applyRemarksResponses2);
                                }
                            }
                            if (AssertUtils.isNotEmpty(all)) {
                                saleApplyPolicyBo.setRemark(JSON.toJSONString(all));
                            }
                        });
            }

            // 更新已存在的销售申请信息
            reportSaleApplyPolicyPoList.forEach(reportSaleApplyPolicyPo -> {
                data.stream().filter(saleApplyPolicyBo -> saleApplyPolicyBo.getApplyId().equals(reportSaleApplyPolicyPo.getApplyId()) &&
                                saleApplyPolicyBo.getProductId().equals(reportSaleApplyPolicyPo.getProductId()) &&
                                saleApplyPolicyBo.getPrimaryFlag().equals(reportSaleApplyPolicyPo.getPrimaryFlag())
                        )
                        .findFirst()
                        .ifPresent(saleApplyPolicyBo -> {
                            ClazzUtils.copyPropertiesIgnoreNull(saleApplyPolicyBo, reportSaleApplyPolicyPo);
                            if (AssertUtils.isNotNull(saleApplyPolicyBo.getAppSubmitUnderwritingDate())) {
                                reportSaleApplyPolicyPo.setAppSubmitUnderwritingDate(saleApplyPolicyBo.getAppSubmitUnderwritingDate());
                            } else {
                                reportSaleApplyPolicyPo.setAppSubmitUnderwritingDate(null);
                            }
                            if (AssertUtils.isNotNull(saleApplyPolicyBo.getUnderwriteStartDate())) {
                                reportSaleApplyPolicyPo.setUnderwriteStartDate(saleApplyPolicyBo.getUnderwriteStartDate());
                            } else {
                                reportSaleApplyPolicyPo.setUnderwriteStartDate(null);
                            }
                            if (AssertUtils.isNotNull(saleApplyPolicyBo.getUnderwriteEndDate())) {
                                reportSaleApplyPolicyPo.setUnderwriteEndDate(saleApplyPolicyBo.getUnderwriteEndDate());
                            } else {
                                reportSaleApplyPolicyPo.setUnderwriteEndDate(null);
                            }
                            reportSaleApplyPolicyPo.setApplyStatus(saleApplyPolicyBo.getApplyStatus());
                            reportSaleApplyPolicyPo.setApplyInvalidDate(saleApplyPolicyBo.getApplyInvalidDate());
                            reportSaleApplyPolicyPo.setSuspensePremiumDate(saleApplyPolicyBo.getSuspensePremiumDate());
                            reportSaleApplyPolicyPo.setSuspensePremium(saleApplyPolicyBo.getSuspensePremium());
                            reportSaleApplyPolicyPo.setSuspensePremiumAddDate(saleApplyPolicyBo.getSuspensePremiumAddDate());
                            reportSaleApplyPolicyPo.setSuspensePremiumAdd(saleApplyPolicyBo.getSuspensePremiumAdd());
                            reportSaleApplyPolicyPo.setSuspensePremiumRefundDate(saleApplyPolicyBo.getSuspensePremiumRefundDate());
                            reportSaleApplyPolicyPo.setSuspensePremiumRefund(saleApplyPolicyBo.getSuspensePremiumRefund());
                            reportSaleApplyPolicyPo.setPaymentBankCode(saleApplyPolicyBo.getPaymentBankCode());
                            reportSaleApplyPolicyPo.setPaymentMethodCode(saleApplyPolicyBo.getPaymentMethodCode());
                            reportSaleApplyPolicyPo.setSuspensePaymentBankCode(saleApplyPolicyBo.getSuspensePaymentBankCode());
                            reportSaleApplyPolicyPo.setSuspensePaymentMethodCode(saleApplyPolicyBo.getSuspensePaymentMethodCode());
                            reportSaleApplyPolicyPo.setReceiptNoJson(saleApplyPolicyBo.getReceiptNoJson());
                            reportSaleApplyPolicyPo.setApplyDate(saleApplyPolicyBo.getApplyDate());
                            reportSaleApplyPolicyPo.setSuspensePremiumRefundStatus(saleApplyPolicyBo.getSuspensePremiumRefundStatus());
                        });
            });
            reportSaleApplyPolicyDao.update(reportSaleApplyPolicyPoList);

            // 移除已经处理的数据
            List<String> rmApplyIdList = reportSaleApplyPolicyPoList.stream().map(ReportSaleApplyPolicyPo::getApplyId).collect(Collectors.toList());
            data.removeIf(saleApplyPolicyBo -> rmApplyIdList.contains(saleApplyPolicyBo.getApplyId()));
        }

        // 将剩余的数据转换为需要保存的格式并保存到数据库
        List<ReportSaleApplyPolicyPo> saveReportSaleApplyPolicyList = JSONArray.parseArray(JSON.toJSONString(data), ReportSaleApplyPolicyPo.class);
        saveReportSaleApplyPolicyList.forEach(reportSaleApplyPolicyPo -> {
            reportSaleApplyPolicyPo.setReportSaleApplyPolicyId(UUIDUtils.getUUIDShort());
        });
        reportSaleApplyPolicyDao.insert(saveReportSaleApplyPolicyList);
    }

    /**
     * 同步保存销售政策信息
     * 该方法主要用于同步和保存销售申请政策的相关信息，包括处理投保单和保单的备注信息，以及更新销售申请政策对象的相关数据
     *
     * @param data 一个包含销售申请政策信息的列表
     */
    private void syncSaveSalePolicy(List<SaleApplyPolicyBo> data) {
        if (!AssertUtils.isNotEmpty(data)) {
            return;
        }
        // 提取并去重投保单ID，用于后续获取备注信息
        List<String> applyIds = data.stream().map(SaleApplyPolicyBo::getApplyId).distinct().collect(Collectors.toList());

        // 通过投保单id获取备注信息数据
        List<String> policyNos = data.stream().map(SaleApplyPolicyBo::getPolicyNo).distinct().collect(Collectors.toList());

        // 获取投保单、保全、保单的备注信息
        ResultObject<Map<String, List<ApplyRemarksResponse>>> applyRemark= baseApplyApi.getApplyRemarkByIds(applyIds);
        ResultObject<Map<String, List<ApplyRemarksResponse>>> endorseRemark = groupEndorseApi.getRemark(policyNos);
        ResultObject<Map<String, List<ApplyRemarksResponse>>> renewalRemark = renewalBaseApi.getRemark(policyNos);

        // 提取并去重投保单号
        List<String> applyNos = data.stream().map(SaleApplyPolicyBo::getApplyNo).distinct().collect(Collectors.toList());

        // 查询数据库中存在的投保单号
        List<ReportSaleApplyPolicyPo> reportSaleApplyPolicyPoList = reportSaleApplyPolicyBaseDao.queryByApplyNos(applyNos);

        // 如果查询到的数据不为空，则进行数据处理和更新
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyPoList)) {
            // 处理并合并投保单和保单的备注信息
            if (AssertUtils.isNotEmpty(applyRemark.getData())) {
                Map<String, List<ApplyRemarksResponse>> data1 = applyRemark.getData();
                Map<String, List<ApplyRemarksResponse>> data2 = endorseRemark.getData();
                Map<String, List<ApplyRemarksResponse>> data3 = renewalRemark.getData();
                data.stream().filter(saleApplyPolicyBo -> AssertUtils.isNotEmpty(saleApplyPolicyBo.getApplyId()))
                        .forEach(saleApplyPolicyBo -> {
                            List<ApplyRemarksResponse> all = new ArrayList<>();
                            if (AssertUtils.isNotEmpty(data1)) {
                                List<ApplyRemarksResponse> applyRemarksResponses = data1.get(saleApplyPolicyBo.getApplyId());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses)) {
                                    all.addAll(applyRemarksResponses);
                                }
                            }
                            if (AssertUtils.isNotEmpty(saleApplyPolicyBo.getPolicyNo()) && AssertUtils.isNotEmpty(data2)) {
                                List<ApplyRemarksResponse> applyRemarksResponses1 = data2.get(saleApplyPolicyBo.getPolicyNo());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses1)) {
                                    all.addAll(applyRemarksResponses1);
                                }
                            }
                            if (AssertUtils.isNotEmpty(saleApplyPolicyBo.getPolicyNo()) && AssertUtils.isNotEmpty(data3)) {
                                List<ApplyRemarksResponse> applyRemarksResponses2 = data3.get(saleApplyPolicyBo.getPolicyNo());
                                if (AssertUtils.isNotEmpty(applyRemarksResponses2)) {
                                    all.addAll(applyRemarksResponses2);
                                }
                            }
                            if (AssertUtils.isNotEmpty(all)) {
                                saleApplyPolicyBo.setRemark(JSON.toJSONString(all));
                            }
                        });
            }

            // 更新销售申请政策对象的数据
            data.stream().filter(saleApplyPolicyBo -> AssertUtils.isNotNull(saleApplyPolicyBo.getPolicyId())).forEach(saleApplyPolicyBo -> {
                reportSaleApplyPolicyPoList.stream().filter(reportSaleApplyPolicyPo ->
//                                (reportSaleApplyPolicyPo.getApplyId().equals(saleApplyPolicyBo.getApplyId()) || reportSaleApplyPolicyPo.getApplyNo().equals(saleApplyPolicyBo.getApplyNo())) &&
                                (saleApplyPolicyBo.getPolicyId().equals(reportSaleApplyPolicyPo.getPolicyId()) || saleApplyPolicyBo.getPolicyNo().equals(reportSaleApplyPolicyPo.getPolicyNo())) &&
                                saleApplyPolicyBo.getProductId().equals(reportSaleApplyPolicyPo.getProductId()) &&
                                saleApplyPolicyBo.getPrimaryFlag().equals(reportSaleApplyPolicyPo.getPrimaryFlag()))
                        .findFirst().ifPresent(reportSaleApplyPolicyPo -> {
                            if (!AssertUtils.isNotNull(saleApplyPolicyBo.getActualPremium())) {
                                saleApplyPolicyBo.setActualPremium(reportSaleApplyPolicyPo.getActualPremium());
                            }
                            if (!AssertUtils.isNotNull(saleApplyPolicyBo.getAppSubmitUnderwritingDate())) {
                                saleApplyPolicyBo.setAppSubmitUnderwritingDate(reportSaleApplyPolicyPo.getAppSubmitUnderwritingDate());
                            }
                            if (!AssertUtils.isNotNull(saleApplyPolicyBo.getUnderwriteStartDate())) {
                                saleApplyPolicyBo.setUnderwriteStartDate(reportSaleApplyPolicyPo.getUnderwriteStartDate());
                            }
                            if (!AssertUtils.isNotNull(saleApplyPolicyBo.getUnderwriteEndDate())) {
                                saleApplyPolicyBo.setUnderwriteEndDate(reportSaleApplyPolicyPo.getUnderwriteEndDate());
                            }
                            saleApplyPolicyBo.setApplyStatus(reportSaleApplyPolicyPo.getApplyStatus());
                            saleApplyPolicyBo.setApplyInvalidDate(reportSaleApplyPolicyPo.getApplyInvalidDate());
                            saleApplyPolicyBo.setSuspensePremiumDate(reportSaleApplyPolicyPo.getSuspensePremiumDate());
                            saleApplyPolicyBo.setSuspensePremium(reportSaleApplyPolicyPo.getSuspensePremium());
                            saleApplyPolicyBo.setSuspensePremiumAddDate(reportSaleApplyPolicyPo.getSuspensePremiumAddDate());
                            saleApplyPolicyBo.setSuspensePremiumAdd(reportSaleApplyPolicyPo.getSuspensePremiumAdd());
                            saleApplyPolicyBo.setSuspensePremiumRefundDate(reportSaleApplyPolicyPo.getSuspensePremiumRefundDate());
                            saleApplyPolicyBo.setSuspensePremiumRefund(reportSaleApplyPolicyPo.getSuspensePremiumRefund());
                            saleApplyPolicyBo.setPaymentBankCode(reportSaleApplyPolicyPo.getPaymentBankCode());
                            saleApplyPolicyBo.setPaymentMethodCode(reportSaleApplyPolicyPo.getPaymentMethodCode());
                            saleApplyPolicyBo.setSuspensePaymentBankCode(reportSaleApplyPolicyPo.getSuspensePaymentBankCode());
                            saleApplyPolicyBo.setSuspensePaymentMethodCode(reportSaleApplyPolicyPo.getSuspensePaymentMethodCode());
                            saleApplyPolicyBo.setReceiptNoJson(reportSaleApplyPolicyPo.getReceiptNoJson());
                            saleApplyPolicyBo.setApplyDate(reportSaleApplyPolicyPo.getApplyDate());
                            saleApplyPolicyBo.setSuspensePremiumRefundStatus(reportSaleApplyPolicyPo.getSuspensePremiumRefundStatus());
                        });
            });
            // 删除已处理的投保单数据
            reportSaleApplyPolicyBaseDao.deleteByApplyIds(applyIds);
        }
        // 准备保存的销售申请政策列表，并设置唯一的ID
        List<ReportSaleApplyPolicyPo> saveReportSaleApplyPolicyList = JSONArray.parseArray(JSON.toJSONString(data), ReportSaleApplyPolicyPo.class);
        saveReportSaleApplyPolicyList.forEach(reportSaleApplyPolicyPo -> {
            reportSaleApplyPolicyPo.setReportSaleApplyPolicyId(UUIDUtils.getUUIDShort());
        });
        // 插入新的销售报表数据到数据库
        reportSaleApplyPolicyDao.insert(saveReportSaleApplyPolicyList);
    }


    @Override
    public ResultObject syncSuspense() {
        ResultObject resultObject = new ResultObject();
        ReportSaleApplyPolicyVo reportSaleApplyPolicyVo = new ReportSaleApplyPolicyVo();
        String quarterDate = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE2);
        reportSaleApplyPolicyVo.setQuarterDate(DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE2));
        reportSaleApplyPolicyVo.setCurrentPage(1);
        reportSaleApplyPolicyVo.setPageSize(1000);
        do {
            List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = reportSaleApplyPolicyBaseService.querySyncSuspense(reportSaleApplyPolicyVo);
            if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBoList)) {
                break;
            }
            List<ReportSuspensePo> reportSuspensePoList = (List<ReportSuspensePo>) this.converterList(reportSaleApplyPolicyBoList, new TypeToken<List<ReportSuspensePo>>() {
            }.getType());
            reportSuspensePoList.forEach(reportSuspensePo -> {
                reportSuspensePo.setReportSuspenseId(UUIDUtils.getUUIDShort());
                reportSuspensePo.setQuarterDate(quarterDate);
            });
            reportSuspenseDao.insert(reportSuspensePoList);
            int totalLine = AssertUtils.isNotNull(reportSaleApplyPolicyBoList) ? AssertUtils.isNotNull(reportSaleApplyPolicyBoList.get(0)) ?
                    reportSaleApplyPolicyBoList.get(0).getTotalLine() : 0 : 0;
            if (totalLine <= (reportSaleApplyPolicyVo.getCurrentPage() * reportSaleApplyPolicyVo.getPageSize())) {
                break;
            }
            reportSaleApplyPolicyVo.setCurrentPage(reportSaleApplyPolicyVo.getCurrentPage() + 1);
        } while (true);
        return resultObject;
    }

    @Override
    public String syncReportGroupPolicySdf(BasePageRequest basePageRequest) {
        //获取事物状态
        Long currentTime = DateUtils.getCurrentTime();
        LOGGER.info("同步学生发展基金报表开始:" + DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        ReportCollectPo reportCollectPo = new ReportCollectPo();
        reportCollectPo.setReportType("GROUP_POLICY_SDF");
        reportCollectPo.setProcessDate(DateUtils.timeStrToString(currentTime, DateUtils.FORMATE6));
        reportCollectPo.setTotal(0L);
        reportCollectPo.setIsSuccess("INIT");
        boolean errorFlag = false;
        String flag = null;
        try {
            ReportBatchPo reportBatchPo = queryReportBatchPo("GROUP_POLICY_SDF");
            String startDate = reportBatchPo.getStartBatchDate();
            ResultObject<List<GroupSdfReportResponse>> listResultObject = policyReportApi.listPolicyGroupSdfReport(basePageRequest.getCurrentPage(), basePageRequest.getPageSize(), startDate);
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            List<GroupSdfReportResponse> groupSdfReportResponses = listResultObject.getData();
            if (AssertUtils.isNotNull(groupSdfReportResponses)) {
                List<ReportGroupPolicySdfPo> reportGroupPolicySdfPos = (List<ReportGroupPolicySdfPo>) this.converterList(groupSdfReportResponses, new TypeToken<List<ReportGroupPolicySdfPo>>() {
                }.getType());
                List<String> endorseIds = reportGroupPolicySdfPos.stream().filter(reportGroupPolicySdfPo ->
                        "BUSINESS_TYPE_ENDORSE".equals(reportGroupPolicySdfPo.getPaymentBusinessType())).map(ReportGroupPolicySdfPo::getBusinessId).collect(Collectors.toList());
                List<ReportGroupPolicySdfPo> groupEndorsePolicySdfPos = reportGroupPolicySdfPos.stream().filter(reportGroupPolicySdfPo ->
                        "BUSINESS_TYPE_ENDORSE".equals(reportGroupPolicySdfPo.getPaymentBusinessType())).collect(Collectors.toList());
                List<ReportGroupPolicySdfPo> groupNewPolicySdfPos = reportGroupPolicySdfPos.stream().filter(reportGroupPolicySdfPo ->
                        "BUSINESS_TYPE_NEW_CONTRACT".equals(reportGroupPolicySdfPo.getPaymentBusinessType())).collect(Collectors.toList());

                //保全类型的需要重新查询保全费用表设置费用和生效日期
                if (AssertUtils.isNotEmpty(endorseIds)) {
                    ResultObject<List<ReportEndorseResponse>> listResultObject1 = endorseReportApi.queryEndorseFeeListByEndorseIds(endorseIds);
                    if (!AssertUtils.isResultObjectListDataNull(listResultObject1)) {
                        List<ReportEndorseResponse> endorseResponses = listResultObject1.getData();
                        List<String> endorseFeeIds = endorseResponses.stream().filter(reportEndorseResponse ->
                                        AssertUtils.isNotEmpty(reportEndorseResponse.getEndorseId()))
                                .map(ReportEndorseResponse::getEndorseId).collect(Collectors.toList());
                        if (AssertUtils.isNotEmpty(endorseFeeIds)) {
                            //查询不到保全费用的保全全部移除掉
                            groupEndorsePolicySdfPos.removeIf(reportGroupPolicySdfPo ->
                                    !endorseFeeIds.contains(reportGroupPolicySdfPo.getBusinessId()));
                        }

                        if (AssertUtils.isNotEmpty(groupEndorsePolicySdfPos)) {
                            //移除费用为0的保全
                            groupEndorsePolicySdfPos.removeIf(reportGroupPolicySdfPo -> new BigDecimal("0.00").equals(reportGroupPolicySdfPo.getActualPremium()));
                            //设置保全支付类型和实收金额
                            groupEndorsePolicySdfPos.forEach(reportGroupPolicySdfPo -> {
                                endorseResponses.stream().filter(reportEndorseResponse ->
                                        reportEndorseResponse.getEndorseId().equals(reportGroupPolicySdfPo.getBusinessId()))
                                        .findFirst().ifPresent(reportEndorseResponse -> {
                                            reportGroupPolicySdfPo.setActualPremium(reportEndorseResponse.getTotalFee());
                                            reportGroupPolicySdfPo.setFeeType(reportEndorseResponse.getFeeType());
                                            if ("GET".equals(reportGroupPolicySdfPo.getFeeType())) {
                                                ResultObject<ReceiptStatusResponse> receiptStatusResponseResultObject = receiptBaseApi.queryOneReceiptDoByBusinessId(reportEndorseResponse.getEndorseId());
                                                if (!AssertUtils.isResultObjectDataNull(receiptStatusResponseResultObject)) {
                                                    reportGroupPolicySdfPo.setBusinessType(receiptStatusResponseResultObject.getData().getBusinessType());
                                                }
                                            }
                                            if ("PAY".equals(reportGroupPolicySdfPo.getFeeType())) {
                                                ResultObject<PaymentStatusResponse> paymentStatusResponseResultObject = paymentBaseApi.queryOnePaymentDoByBusinessId(reportEndorseResponse.getEndorseId());
                                                if (!AssertUtils.isResultObjectDataNull(paymentStatusResponseResultObject)) {
                                                    reportGroupPolicySdfPo.setBusinessType(paymentStatusResponseResultObject.getData().getBusinessType());
                                                }
                                            }

                                        });
                            });
                            groupNewPolicySdfPos.addAll(groupEndorsePolicySdfPos);
                        }
                    }
                }

                //删除指定时间段的数据
                List<String> businessIds = groupNewPolicySdfPos.stream().filter(ReportGroupPolicySdfPo ->
                                AssertUtils.isNotEmpty(ReportGroupPolicySdfPo.getBusinessId())).map(ReportGroupPolicySdfPo::getBusinessId)
                        .distinct().collect(Collectors.toList());
                reportGroupPolicySdfBaseService.deleteSyncReportGroupPolicySdf(businessIds);
                //开始保存数据
                groupNewPolicySdfPos.forEach(reportGroupPolicySdfPo -> {
                    if ("BUSINESS_TYPE_NEW_CONTRACT".equals(reportGroupPolicySdfPo.getPaymentBusinessType())) {
                        reportGroupPolicySdfPo.setBusinessType("APPLY_GROUP");
                        reportGroupPolicySdfPo.setFeeType("PAY");
                    }
                    reportGroupPolicySdfPo.setReportGroupPolicySdfId(UUIDUtils.getUUIDShort());
                    reportGroupPolicySdfPo.setCreatedDate(DateUtils.getCurrentTime());
                    reportGroupPolicySdfPo.setValidFlag("effective");
                });
                reportGroupPolicySdfDao.insert(groupNewPolicySdfPos);

                if (AssertUtils.isNotEmpty(groupNewPolicySdfPos) && groupNewPolicySdfPos.size() == basePageRequest.getPageSize()) {
                    flag = TerminologyConfigEnum.WHETHER.NO.name();
                } else {
                    this.saveBatch(reportBatchPo, currentTime);
                    flag = TerminologyConfigEnum.WHETHER.YES.name();
                }
            }
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            e.printStackTrace();
            //手动事物回滚
            reportCollectPo.setIsSuccess("FAILED");
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
        } finally {
            this.finallyDone(reportCollectPo, errorFlag);
        }
        return flag;
    }
}
