package com.gclife.attachment.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentAttachmentRequest;
import com.gclife.attachment.core.jooq.tables.daos.CoiBatchDao;
import com.gclife.attachment.core.jooq.tables.pojos.CoiBatchPo;
import com.gclife.attachment.dao.CoiBatchExtDao;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.model.agent.AgentBo;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentPdfEnum;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.attachment.model.policy.endorse.AddSubtractInsuredPrintBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.group.GroupAttachPolicyBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceConfirmPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.validate.transform.PdfTransData;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.SpringContextUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.endorse.api.GroupEndorseApi;
import com.gclife.endorse.model.request.EndorseAttachmentVo;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.vo.PolicyAttachmentVo;
import com.gclife.renewal.api.GroupRenewalApi;
import com.gclife.renewal.model.vo.GroupRenewalAttachmentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.config.rabbitmq.RabbitMQTopicConfiguration.PREVENT_OOM_QUEUE;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.APPLICATION_FORM;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.APPLY;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.CONTRACT;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.EMAIL_ACCEPTANCE_FORM;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.PLAN;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.PLAN_BOOK;
import static com.gclife.attachment.model.config.AttachmentTermEnum.MESSAGE_PROCESSING_TYPE.GENERATE_DOCUMENTS;
import static com.gclife.attachment.model.config.AttachmentTermEnum.MESSAGE_PROCESSING_TYPE.PDF2IMAGE;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
@Slf4j
@Component
public class AttachmentBusinessMessageReceiver {
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private GroupRenewalApi groupRenewalApi;
    @Autowired
    private GroupEndorseApi groupEndorseApi;
    @Autowired
    private PdfTransData pdfTransData;
    @Autowired
    private CoiBatchExtDao coiBatchExtDao;
    @Autowired
    private CoiBatchDao coiBatchDao;

    /**
     * 防止OOM
     *
     * @param message
     */
    @RabbitListener(queues = PREVENT_OOM_QUEUE)
    public void preventOOMProcess(Message message) throws Throwable {
        String messageBodyString = null;
        Map<String, Object> messageHeaders = null;
        try {
            // 消息数据
            byte[] messageBody = message.getBody();
            messageHeaders = message.getMessageProperties().getHeaders();
            // 处理类型
            Object processingType = messageHeaders.get("processingType");

            messageBodyString = new String(messageBody, StandardCharsets.UTF_8);
            log.info("防止OOM队列 消息头：{}", JSONObject.toJSON(messageHeaders));
            // PDF转图片
            if (PDF2IMAGE.name().equals(processingType)) {
                log.info("PDF转图片开始 ...");
                pdfTransData.pdf2Image(messageBodyString);
            }
            // 生成单证
            if (GENERATE_DOCUMENTS.name().equals(processingType)) {
                log.info("生成单证开始 ...");
                generateDocuments(messageBodyString);
            }
        } catch (Throwable e) {
            log.error("防止OOM队列 异常重试... 消息头: {} 消息参数: {}", JSONObject.toJSONString(messageHeaders), messageBodyString);
            throw new Throwable(e);
        }
    }

    /**
     * 生成单证
     *
     * @param messageBodyString
     * @throws Exception
     */
    private void generateDocuments(String messageBodyString) throws Exception {
        // 生成单证
        ElectronicPolicyGeneratorRequest electronicRequest = JSONObject.parseObject(messageBodyString, ElectronicPolicyGeneratorRequest.class);
        // 参数校验
        AssertUtils.isNotEmpty(log, electronicRequest.getPdfType(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_PDF_TYPE_IS_NOT_NULL);

        // 获取pdf模板
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicRequest);

        // 获取实现类
        PrintDao policyDao = (PrintDao) SpringContextUtils.getApplicationContext().getBean(pdfTemplateConfigBo.getPdfTypeDetails());

        policyDao.print(electronicRequest);

        // 保存生成的单证到子系统
        save2Subsystem(electronicRequest);

        List<AttachmentResponse> attachmentResponseList = electronicRequest.getAttachmentResponseList();
        // 将生成的单证转图片
        Boolean convertImageFlag = electronicRequest.getConvertImageFlag();
        if (AssertUtils.isNotNull(convertImageFlag) && convertImageFlag) {
            for (AttachmentResponse attachmentResponse : attachmentResponseList) {
                pdfTransData.pdf2Image(attachmentResponse.getMediaId());
            }
        }

    }

    /**
     * 保存生成的单证到子系统
     *
     * @param electronicRequest
     */
    private void save2Subsystem(ElectronicPolicyGeneratorRequest electronicRequest) {
        String pdfType = electronicRequest.getPdfType();
        List<AttachmentResponse> attachmentResponseList = electronicRequest.getAttachmentResponseList();
        String content = electronicRequest.getContent();
        String language = electronicRequest.getLanguage();
        if (PLAN.name().equals(pdfType)) {
            ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);

            AttachmentResponse attachmentResponse = attachmentResponseList.get(0);
            PolicyAttachmentVo policyAttachmentReqFc = new PolicyAttachmentVo();
            policyAttachmentReqFc.setPolicyId(planPrintBo.getApplyPlanId());
            policyAttachmentReqFc.setAttachmentId(attachmentResponse.getMediaId());
            policyAttachmentReqFc.setAttachmentTypeCode(PLAN_BOOK.name());
            policyAttachmentReqFc.setLanguage(language);
            policyApi.saveAttachment(policyAttachmentReqFc);
        }
        if (APPLY.name().equals(pdfType)) {
            ApplyBo applyBo = JSON.parseObject(content, ApplyBo.class);

            attachmentResponseList.forEach(attachmentResponse -> {
                String applyId = applyBo.getApplyId();

                PolicyAttachmentVo policyAttachmentVo = new PolicyAttachmentVo();
                policyAttachmentVo.setPolicyId(applyId);
                policyAttachmentVo.setAttachmentId(attachmentResponse.getMediaId());
                policyAttachmentVo.setAttachmentTypeCode(attachmentResponse.getTemplateType());
                policyAttachmentVo.setLanguage(language);
                policyApi.saveAttachment(policyAttachmentVo);
                // 设置单证生成状态为完成
                policyApi.updateAttachmentGenerateStatus(applyId, AttachmentPolicyEnum.APPLY_BOOK.name(), language, AttachmentPdfEnum.FINISH.name());
            });
        }
        if (CONTRACT.name().equals(pdfType)
                || APPLICATION_FORM.name().equals(pdfType)
                || EMAIL_ACCEPTANCE_FORM.name().equals(pdfType)) {
            AgentBo agentBo = JSON.parseObject(content, AgentBo.class);

            attachmentResponseList.forEach(attachmentResponse -> {
                AgentAttachmentRequest agentAttachmentRequest = new AgentAttachmentRequest();
                agentAttachmentRequest.setAgentId(agentBo.getAgentId());
                agentAttachmentRequest.setAttachmentTypeCode(attachmentResponse.getTemplateType());
                agentAttachmentRequest.setAttachmentId(attachmentResponse.getMediaId());
                agentAttachmentRequest.setAttachmentSeq(attachmentResponse.getSeq());
                agentAttachmentRequest.setLanguage(language);
                agentBaseAgentApi.saveAttachment(agentAttachmentRequest);
            });
        }

        //29号coi保存附件
        if ("POLICY_GC_GROUP_STUDENT_CARE_COI".equals(pdfType)) {
            GroupAttachPolicyBo groupAttachPolicyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);
            attachmentResponseList.forEach(attachmentResponse -> {
                CoiBatchPo coiBatchPo = new CoiBatchPo();
                coiBatchPo.setCoiBatchId(UUIDUtils.getUUIDShort());
                coiBatchPo.setAttachmentId(attachmentResponse.getMediaId());
                coiBatchPo.setBatchid(groupAttachPolicyBo.getBatchId());
                coiBatchPo.setCreatedDate(DateUtils.getCurrentTime());
                coiBatchPo.setAttachmentSeq(groupAttachPolicyBo.getAttachmentSeq());
                coiBatchPo.setPdfType(pdfType);
                coiBatchDao.insert(coiBatchPo);
            });
            log.info("groupAttachPolicyBo.getIsLast=====================" + groupAttachPolicyBo.getIsLast());
            if (AssertUtils.isNotNull(groupAttachPolicyBo.getIsLast()) && "YES".equals(groupAttachPolicyBo.getIsLast())) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                //保存总的pdf到系统
                PolicyAttachmentVo policyAttachmentReqFc = new PolicyAttachmentVo();
                policyAttachmentReqFc.setPolicyId(groupAttachPolicyBo.getPolicyId());
                policyAttachmentReqFc.setAttachmentId(groupAttachPolicyBo.getBatchId());
                policyAttachmentReqFc.setAttachmentTypeCode("COI_BOOK");
                policyAttachmentReqFc.setLanguage(language);
                log.info("groupAttachPolicyBo policyAttachmentReqFc=====================" + JSON.toJSON(policyAttachmentReqFc));
                policyApi.saveAttachment(policyAttachmentReqFc);
            }
        }

        //29号续保coi保存附件
        if ("GROUP_RENEWAL_GC_GROUP_STUDENT_CARE_COI".equals(pdfType)) {
            RenewalInsuranceConfirmPrintBo renewalInsuranceConfirmPrintBo = JSON.parseObject(content, RenewalInsuranceConfirmPrintBo.class);
            attachmentResponseList.forEach(attachmentResponse -> {
                CoiBatchPo coiBatchPo = new CoiBatchPo();
                coiBatchPo.setCoiBatchId(UUIDUtils.getUUIDShort());
                coiBatchPo.setAttachmentId(attachmentResponse.getMediaId());
                coiBatchPo.setBatchid(renewalInsuranceConfirmPrintBo.getBatchId());
                coiBatchPo.setCreatedDate(DateUtils.getCurrentTime());
                coiBatchPo.setAttachmentSeq(renewalInsuranceConfirmPrintBo.getAttachmentSeq());
                coiBatchPo.setPdfType(pdfType);
                coiBatchDao.insert(coiBatchPo);
            });
            log.info("renewalInsuranceConfirmPrintBo.getIsLast=====================" + renewalInsuranceConfirmPrintBo.getIsLast());
            if (AssertUtils.isNotNull(renewalInsuranceConfirmPrintBo.getIsLast()) && "YES".equals(renewalInsuranceConfirmPrintBo.getIsLast())) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                //保存总的pdf到系统
                GroupRenewalAttachmentVo groupRenewalAttachmentVo = new GroupRenewalAttachmentVo();
                groupRenewalAttachmentVo.setGroupRenewalId(renewalInsuranceConfirmPrintBo.getGroupRenewalId());
                groupRenewalAttachmentVo.setAttachmentTypeCode("GROUP_RENEWAL_GC_GROUP_STUDENT_CARE_COI");
                groupRenewalAttachmentVo.setAttachmentId(renewalInsuranceConfirmPrintBo.getBatchId());
                groupRenewalAttachmentVo.setLanguage(language);
                groupRenewalApi.saveGroupRenewalAttachment(groupRenewalAttachmentVo);
            }
        }


        //29号保全coi保存附件
        if ("ENDORSE_GC_GROUP_STUDENT_CARE_COI".equals(pdfType)) {
            AddSubtractInsuredPrintBo addSubtractInsuredPrintBo = JSON.parseObject(content, AddSubtractInsuredPrintBo.class);
            attachmentResponseList.forEach(attachmentResponse -> {
                CoiBatchPo coiBatchPo = new CoiBatchPo();
                coiBatchPo.setCoiBatchId(UUIDUtils.getUUIDShort());
                coiBatchPo.setAttachmentId(attachmentResponse.getMediaId());
                coiBatchPo.setBatchid(addSubtractInsuredPrintBo.getBatchId());
                coiBatchPo.setCreatedDate(DateUtils.getCurrentTime());
                coiBatchPo.setAttachmentSeq(addSubtractInsuredPrintBo.getAttachmentSeq());
                coiBatchPo.setPdfType(pdfType);
                coiBatchDao.insert(coiBatchPo);
            });
            log.info("addSubtractInsuredPrintBo.getIsLast=====================" + addSubtractInsuredPrintBo.getIsLast());
            if (AssertUtils.isNotNull(addSubtractInsuredPrintBo.getIsLast()) && "YES".equals(addSubtractInsuredPrintBo.getIsLast())) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                //保存总的pdf到系统
                EndorseAttachmentVo endorseAttachmentVo = new EndorseAttachmentVo();
                endorseAttachmentVo.setEndorseId(addSubtractInsuredPrintBo.getEndorseId());
                endorseAttachmentVo.setEndorseItemId(addSubtractInsuredPrintBo.getEndorseItemId());
                endorseAttachmentVo.setLanguage(language);
                endorseAttachmentVo.setAttachmentId(addSubtractInsuredPrintBo.getBatchId());
                endorseAttachmentVo.setAttachmentTypeCode(pdfType);
                groupEndorseApi.saveGroupEndorseAttachmentAuto(endorseAttachmentVo);
            }
        }
    }
}
