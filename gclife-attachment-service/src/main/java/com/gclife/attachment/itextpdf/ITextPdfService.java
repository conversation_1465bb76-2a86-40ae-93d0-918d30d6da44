package com.gclife.attachment.itextpdf;

import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * create 17-10-16
 * description: itext pdf打印接口
 */

public interface ITextPdfService {


    /**
     * 根据模板生成PDF并签名
     *
     * @param bytes 对应于文件流
     * @return
     */
    byte[] generatePdfFromTemplate(byte[] bytes);

    /**
     * 增加健康告知书
     *
     * @param policyData
     * @param electronicPolicyGeneratorRequest
     * @param healthBook
     * @throws Exception
     */
    void mergeHealth(Map<String, Object> policyData, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, String... healthBook) throws Exception;

    /**
     * 保单打印合并
     *
     * @param electronicPolicyGeneratorRequest
     * @param printObjectList
     * @return
     * @throws Exception
     */
    void printPolicys(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, List<PrintObject> printObjectList) throws Exception;

    /**
     * 保存附件
     *
     * @param bytes
     * @return
     */
    AttachmentResponse savePdfAndUpload(byte[] bytes);

    /**
     * 保存附件
     *
     * @param bytes
     * @return
     */
    AttachmentResponse savePdfAndUploadCoi(byte[] bytes);

    /**
     * 指定文件名称保存附件
     *
     * @param bytes
     * @param fileName
     * @return
     */
    AttachmentResponse savePdfAndUpload(byte[] bytes,String fileName);

    /**
     * 保存附件
     *
     * @param bytes
     * @return
     */
    AttachmentResponse saveAttachment(byte[] bytes, String fileType);

    /**
     * 增加签收回执
     *
     * @param policyData
     * @param electronicPolicyGeneratorRequest
     */
    void addAcknowledgmentLetter(Map<String, Object> policyData, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;

    /**
     * 打印签收回执
     *
     * @param policyData
     * @param language
     * @return
     * @throws Exception
     */
    AttachmentResponse addAcknowledgmentLetter(Map<String, Object> policyData, String language) throws Exception;

    /**
     * 添加保险证确认书
     *
     * @param electronicPolicyGeneratorRequest
     */
    void addPolicyConfirm(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;

    /**
     * 添加网销保险证确认书
     *
     * @param electronicPolicyGeneratorRequest
     */
    void addPolicyConfirmOnline(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;
    /**
     * 添加34号产品几乎书
     *
     * @param electronicPolicyGeneratorRequest
     */
    void addPolicyGcSokSanConfirm(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;
}
