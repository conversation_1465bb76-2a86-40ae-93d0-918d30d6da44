package com.gclife.attachment.itextpdf.impl;

import com.gclife.attachment.certificate.CertificateKey;
import com.gclife.attachment.certificate.CertificateService;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.DigitalCertificateConfigExtDao;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.impl.policy.confirm.PolicyConfirmOnlineDaoImpl;
import com.gclife.attachment.dao.policy.impl.policy.confirm.PolicyConfirmPersonaDaoImpl;
import com.gclife.attachment.dao.policy.impl.policy.confirm.PolicyGcSokSanConfirmImpl;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.DigitalCertificateConfigBo;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfSignatureAppearance;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.security.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.common.PrintCommon.*;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.ACKNOWLEDGMENT_LETTER;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.*;


/**
 * <AUTHOR>
 * create 17-10-16
 * description: itext pdf操作实现类
 */

@Service
@Slf4j
public class ITextPdfServiceImpl extends BaseBusinessServiceImpl implements ITextPdfService {

    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private CertificateService certificateService;
    @Autowired
    private DigitalCertificateConfigExtDao digitalCertificateConfigExtDao;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private PolicyConfirmPersonaDaoImpl policyConfirmPersonaDaoImpl;
    @Autowired
    private PolicyConfirmOnlineDaoImpl policyConfirmOnlineDaoImpl;

    @Autowired
    private PolicyGcSokSanConfirmImpl policyGcSokSanConfirm;

    /**
     * 根据模板生成PDF并签名
     *
     * @param bytes 签名文件
     * @return 无返回值
     */
    @Override
    public byte[] generatePdfFromTemplate(byte[] bytes) {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            DigitalCertificateConfigBo digitalCertificateConfigBo = digitalCertificateConfigExtDao.loadDigitalCertificateConfig(AttachmentTermEnum.USAGE.USAGE_POLICY.code());
            String digestAlgorithm = DigestAlgorithms.SHA1;
            BouncyCastleProvider provider = new BouncyCastleProvider();
            Security.addProvider(provider);
            MakeSignature.CryptoStandard standard = MakeSignature.CryptoStandard.CMS;
            String providerName = provider.getName();

            InputStream inputStream = new ByteArrayInputStream(attachmentBusinessService.loadOssObjectByAttachmentId(digitalCertificateConfigBo.getKeystoreAttachmentId()));
            CertificateKey certificateKey = certificateService.getCertificateKey(inputStream,
                    digitalCertificateConfigBo.getAlias(), digitalCertificateConfigBo.getPassword());
            //印章图片数据
            //先从阿里云上读取附件数据
            String stampAttachmentId = "POLICY_SIGNATURE_GRAPHIC";
//            InputStream stampInputStream = attachmentBusinessService.loadOssObjectByAttachmentId(stampAttachmentId).getObjectContent();
//            byte[] stampData = ByteToInputStream.input2byte(stampInputStream);
            byte[] stampData = attachmentBusinessService.loadOssObjectByAttachmentId(stampAttachmentId);

            PdfStamper stamper = PdfStamper.createSignature(new PdfReader(bytes), byteArrayOutputStream, '\0');

            // Creating the appearance
            PdfSignatureAppearance appearance = stamper.getSignatureAppearance();
            appearance.setRenderingMode(PdfSignatureAppearance.RenderingMode.GRAPHIC);
            appearance.setVisibleSignature(new Rectangle(0, 0, 0, 0), 1, "sig");
            appearance.setSignatureGraphic(Image.getInstance(stampData));
            ExternalDigest digest = new BouncyCastleDigest();
            ExternalSignature signature = new PrivateKeySignature(certificateKey.getPrivateKey(), digestAlgorithm, providerName);
            MakeSignature.signDetached(appearance, digest, signature, certificateKey.getCertificateChain(), null, null, null, 0, standard);
            stamper.close();
        } catch (Exception e) {
            log.error("根据模板生成PDF并签名异常,{}", ExceptionUtils.getFullStackTrace(e));
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 二进制流 增加健康告知书
     *
     * @return
     */
    @Override
    public void mergeHealth(Map<String, Object> policyData, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, String... healthBook) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        List<String> healthBookTypeList = Arrays.asList(healthBook);
        if (healthBookTypeList.contains(APPLICANT_HEALTH_BOOK.name())) {
            byte[] healthBytes = attachmentBusinessService.loadOssObjectByAttachmentId("APPLICANT_HEALTH_" + language);
            healthBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, healthBytes);
            AttachmentResponse healthAttachmentResponse = this.savePdfAndUpload(PrintCommon.pdfEvenPage(healthBytes, language));
            healthAttachmentResponse.setTemplateType(APPLICANT_HEALTH_BOOK.name());
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(healthAttachmentResponse);
        }

        if (healthBookTypeList.contains(INSURED_HEALTH_BOOK.name())) {
            byte[] healthBytes = attachmentBusinessService.loadOssObjectByAttachmentId("INSURED_HEALTH_" + language);
            healthBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, healthBytes);
            AttachmentResponse healthAttachmentResponse = this.savePdfAndUpload(PrintCommon.pdfEvenPage(healthBytes, language));
            healthAttachmentResponse.setTemplateType(INSURED_HEALTH_BOOK.name());
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(healthAttachmentResponse);
        }
    }

    @Override
    public void addAcknowledgmentLetter(Map<String, Object> policyData, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(addAcknowledgmentLetter(policyData, electronicPolicyGeneratorRequest.getLanguage()));
    }


    @Override
    public AttachmentResponse addAcknowledgmentLetter(Map<String, Object> policyData, String language) throws Exception {
        byte[] acknowledgmentLetterBytes = attachmentBusinessService.loadOssObjectByAttachmentId(ACKNOWLEDGMENT_LETTER.name() + "_" + language);
        acknowledgmentLetterBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, acknowledgmentLetterBytes);

        AttachmentResponse attachmentResponse = this.savePdfAndUpload(acknowledgmentLetterBytes);
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.ACKNOWLEDGMENT_LETTER_BOOK.name());
        return attachmentResponse;
    }

    @Override
    public void addPolicyConfirm(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        // 源PDF类型
        String sourcePdfType = electronicPolicyGeneratorRequest.getPdfType();
        electronicPolicyGeneratorRequest.setPdfType(POLICY_CONFIRM.name());
        policyConfirmPersonaDaoImpl.print(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(sourcePdfType);
    }

    @Override
    public void addPolicyConfirmOnline(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        // 源PDF类型
        String sourcePdfType = electronicPolicyGeneratorRequest.getPdfType();
        electronicPolicyGeneratorRequest.setPdfType(POLICY_CONFIRM.name());
        policyConfirmOnlineDaoImpl.print(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(sourcePdfType);
    }

    @Override
    public void addPolicyGcSokSanConfirm(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        // 源PDF类型
        String sourcePdfType = electronicPolicyGeneratorRequest.getPdfType();
        electronicPolicyGeneratorRequest.setPdfType(POLICY_CONFIRM.name());
        policyGcSokSanConfirm.print(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(sourcePdfType);
    }

    /**
     * 保险证打印合并
     *
     * @param electronicPolicyGeneratorRequest
     * @param printObjectList
     * @return
     * @throws Exception
     */
    @Override
    public void printPolicys(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, List<PrintObject> printObjectList) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String productId = electronicPolicyGeneratorRequest.getProductId();
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] bytes = PrintCommon.fillData(attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId()), printObjectList);
        List<PrintObject> objectList = printObjectList.stream().filter(printObject -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(printObject.getKey())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(objectList)) {
            List<byte[]> byteList = new ArrayList<>();
            byteList.add(bytes);
            for (PrintObject printObject : objectList) {
                if ("PRO880000000000016".equals(printObject.getValue())) {
                    Map<String, Object> policyData = new HashMap<>();
                    printObjectList.forEach(p -> {
                        policyData.put(p.getKey(), p.getValue());
                    });
                    byte[] applyBookRtf16Bytes = attachmentBusinessService.loadOssObjectByAttachmentId("PRO880000000000016_POLICY_" + language);
                    byte[] bytes16 = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtf16Bytes);
                    bytes16 = PrintCommon.pdfEvenPage(bytes16, language);
                    byteList.add(bytes16);
                    break;
                }
                if (AssertUtils.isNotEmpty(printObject.getValue())) {
                    electronicPolicyGeneratorRequest.setProductId(printObject.getValue());
                    PdfTemplateConfigBo additionalPdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
                    byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(additionalPdfTemplateConfigBo.getAttachmentId());
                    byte[] additionalBytes = PrintCommon.fillData(inputBytes, printObjectList);
                    byteList.add(additionalBytes);
                }
            }
            bytes = PrintCommon.mergePdfFiles(byteList);
        }
        AttachmentResponse attachmentResponse = this.savePdfAndUpload(bytes);
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        /*
        // 个险签收回执改为保险证确认书
        //增加签收回执
        String content = electronicPolicyGeneratorRequest.getContent();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        Map<String, Object> map = new HashMap<>();
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        this.addAcknowledgmentLetter(map, electronicPolicyGeneratorRequest); */
        //增加 保单条款
        this.mergeTerms(printObjectList, electronicPolicyGeneratorRequest);
        // 封面
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCover(productId, language));
        // 客户服务指南
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCustomerServiceInstruction(language));
        // 添加 保险证确认书
        this.addPolicyConfirm(electronicPolicyGeneratorRequest);

    }

    /**
     * 电子签名-4.7.7 个险首刊统一
     *
     * @param printObjectList
     * @param electronicPolicyGeneratorRequest
     */
    private void mergeFirstIssue(List<PrintObject> printObjectList, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(new AttachmentResponse("FIRST_ISSUE_LONG", FIRST_ISSUE_BOOK.name()));
    }

    /**
     * 合并 保单条款 和 现金价值费率表
     *
     * @param printObjectList
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    private void mergeTerms(List<PrintObject> printObjectList, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String productMainId = electronicPolicyGeneratorRequest.getProductMainId();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String terms = TERMS_VERSION + "TERMS_";
        String premiumPeriod = null;
        Optional<PrintObject> objectOptional = printObjectList.stream().filter(printObject -> "premiumPeriod".equals(printObject.getKey())).findFirst();
        if (objectOptional.isPresent()) {
            premiumPeriod = objectOptional.get().getValue();
        }
        Long seq = 0l;
        List<PrintObject> objectList = printObjectList.stream().filter(printObject -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(printObject.getKey())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(objectList) && "PRO88000000000003".equals(productMainId) && "KM_KH".equals(language)) {
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(
                    new AttachmentResponse(terms + productMainId + "_PRO88000000000007_" + language, POLICY_TERMS_BOOK.name(), seq++));
            return;
        }
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(
                new AttachmentResponse(terms + productMainId + "_" + language, POLICY_TERMS_BOOK.name(), seq++));
        //添加 现金价值费率表
        String attachmentId = null;
        if ("PRO88000000000008".equals(productMainId) && AssertUtils.isNotEmpty(premiumPeriod)) {
            attachmentId = PREMIUM_RATE_AND_CASH_VALUE_VERSION + PREMIUM_RATE_AND_CASH_VALUE.name() + "_" + productMainId + "_" + premiumPeriod;
        }
        if ("PRO88000000000001V2018".equals(productMainId) && AssertUtils.isNotEmpty(premiumPeriod)) {
            String productLevelMain = printObjectList.stream().filter(printObject -> "productLevelMain".equals(printObject.getKey())).findFirst().get().getValue();
            attachmentId = PREMIUM_RATE_AND_CASH_VALUE_VERSION + PREMIUM_RATE_AND_CASH_VALUE.name() + "_" + productMainId + "_" + premiumPeriod + "_" + productLevelMain;
        }
        if (AssertUtils.isNotEmpty(attachmentId)) {
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(
                    new AttachmentResponse(attachmentId + "_" + language, PREMIUM_RATE_AND_CASH_VALUE.name(), seq++));
        }

        if (AssertUtils.isNotEmpty(objectList)) {
            for (PrintObject printObject : objectList) {
                String productIdAdditional = printObject.getValue();
                String value = null;
                if (productIdAdditional.indexOf("PRO88000000000004") >= 0) {
                    value = "PRO88000000000004";
                }
                if (productIdAdditional.indexOf("PRO88000000000007") >= 0) {
                    value = "PRO88000000000007";
                }
                if ("PRO880000000000016".equals(productIdAdditional)) {
                    value = "PRO880000000000016";
                }
                if (AssertUtils.isNotEmpty(value)) {
                    electronicPolicyGeneratorRequest.getAttachmentResponseList().add(
                            new AttachmentResponse(terms + value + "_" + language, POLICY_TERMS_BOOK.name(), seq++));
                }
                if ("PRO88000000000004".equals(value)) {
                    String productLevel4 = printObjectList.stream().filter(object -> "productLevel4".equals(object.getKey())).findFirst().get().getValue();
                    electronicPolicyGeneratorRequest.getAttachmentResponseList().add(
                            new AttachmentResponse(PREMIUM_RATE_AND_CASH_VALUE_VERSION + PREMIUM_RATE_AND_CASH_VALUE.name() + "_" + productMainId + "_" + value + "_" + premiumPeriod + "_" + productLevel4 + "_" + language, PREMIUM_RATE_AND_CASH_VALUE.name(), seq++));
                }

            }
        }
    }


    /**
     * 合并客户服务指南
     *
     * @param printObjectList
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    private void mergeCustomerServiceInstruction(List<PrintObject> printObjectList, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String productId = electronicPolicyGeneratorRequest.getProductMainId();
        String attachmentId = "CUSTOMER_SERVICE_INSTRUCTION_LONG";
        if ("PRO88000000000003".equals(productId)) {
            attachmentId = "CUSTOMER_SERVICE_INSTRUCTION_SHORT";
        }
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(new AttachmentResponse(attachmentId, AttachmentPolicyEnum.CUSTOMER_SERVICE_INSTRUCTION_BOOK.name()));
    }

    /**
     * @param bytes
     * @return
     */
    @Override
    public AttachmentResponse savePdfAndUpload(byte[] bytes) {
        byte[] bytes1 = this.generatePdfFromTemplate(bytes);
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(bytes1));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        return attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
    }

    @Override
    public AttachmentResponse savePdfAndUploadCoi(byte[] bytes) {
        //byte[] bytes1 = this.generatePdfFromTemplate(bytes);
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(bytes));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        //attachmentRequest.setIsCoi("YES");
        return attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
    }

    @Override
    public AttachmentResponse savePdfAndUpload(byte[] bytes, String fileName) {
        byte[] bytes1 = this.generatePdfFromTemplate(bytes);
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(bytes1));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        attachmentRequest.setFileName(fileName);
        return attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
    }

    @Override
    public AttachmentResponse saveAttachment(byte[] bytes, String fileSuffix) {
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(bytes));
        attachmentRequest.setFileSuffix(fileSuffix);
        return attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
    }
}
