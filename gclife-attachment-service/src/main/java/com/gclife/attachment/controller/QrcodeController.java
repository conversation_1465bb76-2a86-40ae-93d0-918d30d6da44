package com.gclife.attachment.controller;

import com.gclife.attachment.model.request.QRCodeRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件操作控制器
 */

@Api(tags = "二维码生产API(2018)", description = "二维码生产API(2018)")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class QrcodeController extends BaseController {

    @Autowired
    AttachmentBusinessService attachmentBusinessService;


    @ApiOperation(value = "二维码生产API", notes = "二维码生产API")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "qrcode/create")
    public ResultObject<AttachmentResponse> attachmentPost(@RequestBody QRCodeRequest qrCodeRequest) {
        return attachmentBusinessService.generateQrcode(qrCodeRequest);
    }



}