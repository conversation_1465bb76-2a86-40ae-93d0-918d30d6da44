package com.gclife.attachment.controller;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.configuration.system.permissions.AutoSaveURL;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-1-5
 * description:PDF文档
 */
@Api(tags = "PDFDocument", description = "PDF文档处理")
@RefreshScope
@RestController
@RequestMapping("v1/pdfdocument")
public class PDFDocumentController extends BaseController {

    @Autowired
    AttachmentBusinessService attachmentBusinessService;

    @ApiOperation(value = "policy/generator", notes = "PDF电子保单生成并上传")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @PostMapping(value = "policy/generator")
    public ResultObject<List<AttachmentResponse>> electronicPolicyGenerator(@RequestBody ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        return attachmentBusinessService.electronicPolicyGenerator(this.getCurrentLoginUsers(), electronicPolicyGeneratorRequest);
    }

    @ApiOperation(value = "policy/generator", notes = "PDF电子保单生成并上传")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @PostMapping(value = "policy/generator/a")
    public ResultObject<List<AttachmentResponse>> electronicPolicyGeneratortest(@RequestBody ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        System.out.println(JSON.toJSONString(electronicPolicyGeneratorRequest));
        return ResultObject.success();
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "policy/lockup/{attachmentId}")
    public ResultObject<AttachmentByteResponse> electronicPolicyDownloadLockUp(HttpServletResponse response, @RequestParam(value = "password", required = false) String password, @PathVariable(value = "attachmentId") String... attachmentId) {
        return attachmentBusinessService.electronicPolicyDownload(response, password, attachmentId);
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "policy/{attachmentId}")
    public ResultObject<AttachmentByteResponse> electronicPolicyDownload(HttpServletResponse response, @PathVariable(value = "attachmentId") String... attachmentId) {
        return attachmentBusinessService.electronicPolicyDownload(response, null, attachmentId);
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "policy/coi/{attachmentId}")
    public ResultObject<AttachmentByteResponse> electronicPolicyDownloadCoi(HttpServletResponse response, @PathVariable(value = "attachmentId") String... attachmentId) {
        ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = attachmentBusinessService.electronicPolicyDownloadCoi(response, null, attachmentId);
        //attachmentByteResponseResultObject.getData().setFileByte(null);
        return attachmentByteResponseResultObject;
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "policy/display")
    public ResultObject electronicPolicyDisplay(HttpServletResponse response, @RequestParam(value = "attachmentId") String... attachmentId) throws Exception {
        return attachmentBusinessService.electronicPolicyDisplay(response, attachmentId);
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "merge/pdf/save")
    public ResultObject<AttachmentByteResponse> mergePdfSave(HttpServletResponse response, @RequestParam(value = "attachmentId") String... attachmentId){
        return attachmentBusinessService.mergePdfSave(response, attachmentId);
    }

    @ApiOperation(value = "policy", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "merge/pdf/save/coi")
    public ResultObject<AttachmentByteResponse> mergePdfSaveCoi(HttpServletResponse response, @RequestParam(value = "attachmentId") String... attachmentId){
        return attachmentBusinessService.mergePdfSaveCoi(response, attachmentId);
    }

    @ApiOperation(value = "pdf转图片", notes = "pdf转图片")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "pdf/transform/image")
    public ResultObject<String> pdfTransformImage(String attachmentId) {
        return attachmentBusinessService.pdfTransformImage(attachmentId);
    }
}
