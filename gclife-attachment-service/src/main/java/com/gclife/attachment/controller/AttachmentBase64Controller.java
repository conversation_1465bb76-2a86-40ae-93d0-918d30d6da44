package com.gclife.attachment.controller;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.model.request.AttachmentBatchRequest;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.request.PdfAttachmentRequest;
import com.gclife.attachment.model.request.QrCodeAttachmentRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: Base64附件操作控制器
 */

@Api(tags = "Attachment_Upload", description = "附件上传处理")
@RefreshScope
@RestController
@RequestMapping("v1")
public class AttachmentBase64Controller extends BaseController {

    @Autowired
    AttachmentBusinessService attachmentBusinessService;

    @ApiOperation(value = "media", notes = "附件base64后上传接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type", value = "类型(IMAGE:图片,VOICE：语音,DOCUMENT:文档,THUMB:缩略图,VIDEO:视频)", defaultValue = "IMAGE", paramType = "query"),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "media")
    public ResultObject<AttachmentResponse> attachmentPost(@RequestParam(name = "type") String type, String attachmentId, @RequestBody AttachmentRequest attachmentRequest) {
        if (AssertUtils.isNotEmpty(attachmentId)) {
            attachmentRequest.setAttachmentId(attachmentId);
        } else {
            attachmentRequest.setAttachmentId(null);
        }
        System.out.println("attachmentRequest ::: " + attachmentRequest.toString());
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.uploadMediaBase64(users, type, attachmentRequest);
    }


    @ApiOperation(value = "medias", notes = "附件base64后批量上传接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type", value = "类型(IMAGE:图片,VOICE：语音,DOCUMENT:文档,THUMB:缩略图,VIDEO:视频)", defaultValue = "IMAGE", paramType = "query"),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "medias")
    public ResultObject<List<AttachmentResponse>> attachmentBatchPost(String type, @RequestBody AttachmentBatchRequest attachmentBatchRequest) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.uploadMediaBatchBase64(users, type, attachmentBatchRequest);
    }

    @ApiOperation(value = "qrcode", notes = "二维码生成并上传接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type", value = "类型(IMAGE:图片,VOICE：语音,DOCUMENT:文档,THUMB:缩略图,VIDEO:视频)", example = "image", paramType = "query"),
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "qrcode")
    public ResultObject<AttachmentResponse> qrcodeGenerate(@RequestBody QrCodeAttachmentRequest attachmentRequest) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.attachmentQrCodeGenerate(users, attachmentRequest);
    }

}
