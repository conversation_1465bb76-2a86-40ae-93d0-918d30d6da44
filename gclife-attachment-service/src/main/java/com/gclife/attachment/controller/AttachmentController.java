package com.gclife.attachment.controller;

import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.model.response.CoiBatchResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.permissions.AutoSaveURL;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件操作控制器
 */

@Api(tags = "Attachment_Down", description = "附件下载")
@RefreshScope
@RestController
@RequestMapping("v1")
public class AttachmentController extends BaseController {

    @Autowired
    AttachmentBusinessService attachmentBusinessService;


    @ApiOperation(value = "media", notes = "附件上传接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "type", value = "类型", example = "IMAGE", paramType = "query"),
                    @ApiImplicitParam(name = "mark", value = "标识", example = "234232222222222", paramType = "query"),
                    @ApiImplicitParam(name = "file", value = "多媒体文件", example = "111", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "media/file")
    public ResultObject<AttachmentResponse> attachmentPost(String fileName, String type, String mark,String attachmentId, @RequestParam("file") MultipartFile file) {
        /*TODO:
          文件上传
          1.根据上传的文件数据，创建临时文件
          2.读取文件上传到oss
          3.获取oss路径，数据库附件系统中保存文件。
        */
        ResultObject<AttachmentResponse> resultObject = attachmentBusinessService.uploadMedia(this.getCurrentLoginUsers(),fileName, type, file, attachmentId);
        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            resultObject.getData().setMark(mark);
        }
        return resultObject;
    }


    @ApiOperation(value = "media", notes = "附件下载接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId", value = "多媒体ID", defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media")
    public ResultObject<AttachmentResponse> attachmentGet(String mediaId) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.loadMedia(users, mediaId);
    }

    @ApiOperation(value = "media", notes = "附件下载接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId", value = "多媒体ID", defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/auto")
    public ResultObject<AttachmentResponse> attachmentAutoGet(String mediaId) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.loadAutoMedia(users, mediaId);
    }



    @ApiOperation(value = "media/byte", notes = "附件下载字节数组接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId", value = "多媒体ID", defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/byte")
    public ResultObject<AttachmentByteResponse> attachmentByteGet(String mediaId) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.loadMediaByte(users, mediaId);
    }

    @ApiOperation(value = "template", notes = "模版下载接口")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "templateCode", value = "templateCode", defaultValue = "AGENT_EXPORT_TEMPLATE", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "template")
    public ResultObject<AttachmentResponse> templateGet(@RequestParam String templateCode) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.templateGet(users, templateCode);
    }

    @ApiOperation(value = "删除", notes = "删除附件")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId", value = "mediaId", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/delete")
    public ResultObject deleteAttachment(@RequestParam String mediaId) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.deleteOneAttachment(users, mediaId);
    }

    @ApiOperation(value = "批量删除附件", notes = "批量删除附件")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaIds", value = "mediaIds", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/deleteList")
    public ResultObject deleteAttachmentList(@RequestParam List<String> mediaIds) {
        Users users = this.getCurrentLoginUsers();
        return attachmentBusinessService.deleteAttachment(users, mediaIds);
    }


    @ApiOperation(value = "download/attachments", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "download/attachments/{attachmentId}")
    public void downloadAttachments(HttpServletResponse response, @PathVariable(value = "attachmentId") String attachmentId) {
        attachmentBusinessService.downloadAttachments(response, attachmentId);
    }

    @ApiOperation(value = "download/attachments", notes = "PDF电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(value = "附件ID", name = "attachmentId", example = "ATTACHMENT_7439655a-780d-49f7-90cf-08a8556bcc41", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoSaveURL()
    @GetMapping(value = "download/attachments/inline/{attachmentId}")
    public void downloadInlineAttachments(HttpServletResponse response, @PathVariable(value = "attachmentId") String attachmentId) {
        attachmentBusinessService.downloadInlineAttachments(response, attachmentId);
    }

    @ApiOperation(value = "通过媒体id批量查询附件信息", notes = "通过媒体id批量查询附件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaIds", value = "mediaIds", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "query/attachments")
    public ResultObject<List<AttachmentResponse>> attachmentList(@RequestBody List<String> mediaIds) {
        return attachmentBusinessService.attachmentList(this.getCurrentLoginUsers(), mediaIds);
    }

    @ApiOperation(value = "通过批次id批量查询附件信息", notes = "通过批次id批量查询附件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaIds", value = "mediaIds", paramType = "query")
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "query/coi/batch")
    public ResultObject<List<CoiBatchResponse>> attachmentCoiList(@RequestParam String batchId) {
        return attachmentBusinessService.attachmentCoiList(this.getCurrentLoginUsers(), batchId);
    }


}