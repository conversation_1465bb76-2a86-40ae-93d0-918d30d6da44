package com.gclife.attachment.controller;

import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.business.ImageZoomBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.awt.*;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:图片缩放控制器
 */

@Api(tags = "Attachment_ZOOM", description = "图片缩放处理")
@RefreshScope
@RestController
@RequestMapping("v1")
public class ImageZoomController extends BaseController {

    @Autowired
    AttachmentBusinessService attachmentBusinessService;

    @Autowired
    ImageZoomBusinessService imageZoomBusinessService;


    @ApiOperation(value = "media/{0}", notes = "图片缩放比例(百分比,50标示缩放到原图的50%)")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId",value = "多媒体ID",defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7",paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/{proportion}")
    public ResultObject<AttachmentResponse> attachmentZoomGet(@ApiParam(value = "缩放百分比",name = "proportion",defaultValue = "50")
                                                                  @PathVariable(value = "proportion")long proportion,String mediaId) {
        Users users =this.getCurrentLoginUsers();
        return attachmentBusinessService.loadMediaProportion(users,proportion,mediaId);
    }



    @ApiOperation(value = "media/byte/{0}", notes = "图片缩放比例(百分比,50标示缩放到原图的50%)")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId",value = "多媒体ID",defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7",paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/byte/{proportion}")
    public ResultObject<AttachmentByteResponse> attachmentZoomByteGet(@ApiParam(value = "缩放比例",name = "proportion",defaultValue = "50")
                                                              @PathVariable(value = "proportion")long proportion,String mediaId) {
        Users users =this.getCurrentLoginUsers();
        return attachmentBusinessService.loadMediaByteProportion(users,proportion,mediaId);
    }

    @ApiOperation(value = "media/byte/width/{100}/height/{100}", notes = "将图缩略成宽度为100,高度为100,返回字节流")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId",value = "多媒体ID",defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7",paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/byte/width/{width}/height/{height}")
    public ResultObject<AttachmentByteResponse> attachmentZoomhwidthByteGet(@ApiParam(value = "固定宽度",name = "width",defaultValue = "50")@PathVariable(value = "width")long width,
                                                                            @ApiParam(value = "固定宽度",name = "height",defaultValue = "50")@PathVariable(value = "height")long height,
                                                                            String mediaId) {
          /*TODO:
          文件缩放处理
        */
        Users users =this.getCurrentLoginUsers();
        return null;
    }



    @ApiOperation(value = "media/route/width/{100}/height/{100}", notes = "将图缩略成宽度为100,高度为100,返回路径")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "mediaId",value = "多媒体ID",defaultValue = "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7",paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "media/route/width/{width}/height/{height}")
    public ResultObject<AttachmentResponse> attachmentZoomhwidthRouteGet(@ApiParam(value = "固定宽度",name = "width",defaultValue = "50")@PathVariable(value = "width")long width,
                                                                            @ApiParam(value = "固定宽度",name = "height",defaultValue = "50")@PathVariable(value = "height")long height,
                                                                            String mediaId) {
          /*TODO:
          文件缩放处理
        */
        Users users =this.getCurrentLoginUsers();
        return imageZoomBusinessService.loadMediaRouteFixedFrame(users,mediaId,width,height);
    }


}