package com.gclife.attachment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:attachment入口
 */
@EnableRetry // 启用重试
@EnableTransactionManagement
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.gclife"})
@ComponentScan(basePackages = {"com.gclife"})
public class ServiceApplication {

    public static void main(String[] args) {
        // 解决 和oracle jar 包冲突的问题
        System.setProperty("javax.xml.parsers.DocumentBuilderFactory","com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl");
        System.setProperty("javax.xml.parsers.SAXParserFactory","com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl");

        ConfigurableApplicationContext context = SpringApplication.run(ServiceApplication.class, args);
        String port = context.getBean(Environment.class).getProperty("server.port");
        System.out.println("服务启动成功，访问端口为:"+port);
    }
}
