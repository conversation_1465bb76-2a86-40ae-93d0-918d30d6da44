package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.ApplyPlanLoanBo;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.common.PrintCommon.decimalFormat1;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;

/**
 * <AUTHOR>
 * @description
 * @date 2020/5/12 4:53 下午
 */
@Component
public class GCLifeProtectData {


    /**
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        Long backTrackDate =  planPrintBo.getCreatedDate();
        if(AssertUtils.isNotNull(planPrintBo.getBackTrackDate())){
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            map.put("backTrackDateYearZH_CN", PrintCommon.getPrintString("年 ", 3));
            map.put("backTrackDateMonthZH_CN", PrintCommon.getPrintString("月 ", 3));
            map.put("backTrackDateDayZH_CN", PrintCommon.getPrintString("日 ", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()),new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()),new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /*********************************************贷款信息***************************************************/
        ApplyPlanLoanBo planLoanContract = planPrintBo.getPlanLoanContract();
        map.put("loanContractNo", PrintCommon.getPrintString(planLoanContract.getLoanContractNo(), 3));
        map.put("notConvertedLoanAmount", PrintCommon.getPrintString(new BigDecimal(planLoanContract.getLoanAmount()), 3));
        map.put("currencyName", PrintCommon.getPrintString(planLoanContract.getCurrencyName(), 3));
        map.put("currency", PrintCommon.getPrintString(planLoanContract.getCurrency(), 3));
        String exchangeRate = null;
        if (AssertUtils.isNotNull(planLoanContract.getExchangeRate())) {
            exchangeRate = decimalFormat1.format(planLoanContract.getExchangeRate());
        }
        map.put("exchangeRate", PrintCommon.getPrintString(exchangeRate, 3));
        map.put("paymentWayName", PrintCommon.getPrintString(planLoanContract.getPaymentWayName(), 3));
        map.put("loanTerm", PrintCommon.getPrintString(planLoanContract.getLoanTerm(), 3));
        map.put("loanTermName", PrintCommon.getPrintString(planLoanContract.getLoanTermName(), 3));
        map.put("loanInterestRate", PrintCommon.getPrintString(planLoanContract.getLoanInterestRate(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        String productLevel = null;
        BigDecimal totalAmount = null;
        if (AssertUtils.isNotEmpty(listCoverage)) {
            for (ApplyCoveragePlanBo coverageBo : listCoverage) {
                Map<String, Object> coverageMap = new HashMap<>();
                map.put(coverageBo.getProductId(), coverageBo.getProductId());
                coverageMap.put("productId", coverageBo.getProductId());
                PrintCommon.setProductName(coverageMap, coverageBo.getProductId());
                coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
                coverageMap.put("productLevel", coverageBo.getProductLevel());
                if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                    totalAmount = new BigDecimal(coverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                coverageMap.put("productLevelName", PrintCommon.getPrintString(coverageBo.getProductLevelName(), 2));
                //保险期间
                String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
                String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
                String insurancePeriod = coveragePeriod + " " + coveragePeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                    insurancePeriod = coveragePeriodUnitName + " " + coveragePeriod;
                }
                coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
                //交费期限
                String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
                String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
                String premiumPeriodAndUnitName = premiumPeriod + " " + premiumPeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                    premiumPeriodAndUnitName = premiumPeriodUnitName + " " + premiumPeriod;
                }
                //交费类型
                String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
                if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "一次性全额缴清";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "Single Payment";
                    }
                    premiumPeriodAndUnitName = premiumFrequencyName;
                }
                coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
                coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
                //每期保费
                coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
                coverageListMap.add(coverageMap);
            }
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /****************************************************************************可选其他缴费周期***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        if (AssertUtils.isNotEmpty(coveragePremiumFrequencyMap)) {
            List<Map<String, Object>> coveragePremiumFrequencyListMap = new ArrayList<>();
            for (String productId : coveragePremiumFrequencyMap.keySet()) {
                List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
                CoveragePremiumFrequencyResponse coveragePremiumFrequencyResponse = cpfList.get(0);
                if (AssertUtils.isNotNull(coveragePremiumFrequencyResponse)) {
                    Map<String, Object> cpfMap = new HashMap<>();
                    PrintCommon.setProductName(cpfMap, productId);
                    cpfMap.put("productLevelName", PrintCommon.getPrintString(coveragePremiumFrequencyResponse.getProductLevelName(), 2));
                    cpfMap.put("totalPremium", PrintCommon.getPrintString(coveragePremiumFrequencyResponse.getTotalPremium(), 2));
                    cpfMap.put("totalAmount", PrintCommon.getPrintString(coveragePremiumFrequencyResponse.getAmount(), 2));
                    coveragePremiumFrequencyListMap.add(cpfMap);
                    map.put("coveragePremiumFrequencyListMap", coveragePremiumFrequencyListMap);
                }
            }
        }
        /****************************************************************************保险利益***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();

        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        listCashValue.forEach(productCashValueBo -> {
            Map<String, Object> interestMap = new HashMap<>();
            interestMap.put("policyYear", productCashValueBo.getPolicyYear());
            interestMap.put("age", productCashValueBo.getAge());
            interestMap.put("cashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
            interestMap.put("amount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
            interestListMap.add(interestMap);
        });
        map.put("interestListMap", interestListMap);
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Map<String, Object> map = new HashMap<String, Object>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);

        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()),new Date(policyBo.getApproveDate()));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        /**********************************受益人***************************************/
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
        if (!AssertUtils.isNotNull(listPolicyBeneficiary)) {
            listPolicyBeneficiary = new ArrayList<>();
        }
        listPolicyBeneficiary.forEach(beneficiary -> {
            Map<String, Object> beneficiaryMap = new HashMap<>();
            PolicyBeneficiaryBo policyBeneficiary = beneficiary.getPolicyBeneficiary();
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            beneficiaryMap.put("beneficiaryNoOrderName", PrintCommon.getPrintString(beneficiary.getBeneficiaryNoOrderName(), 3));

            String beneficiaryBranchId = policyBeneficiary.getBeneficiaryBranchId();
            if (AssertUtils.isNotEmpty(beneficiaryBranchId)) {
                beneficiaryMap.put("beneficiaryBranchId", beneficiaryBranchId);
            }
            String beneficiaryName = AssertUtils.isNotEmpty(beneficiaryBranchId) ? policyBeneficiary.getBeneficiaryBranchName() : policyBeneficiary.getName();
            beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            beneficiaryIdNo = AssertUtils.isNotEmpty(beneficiaryBranchId) ? policyBeneficiary.getBeneficiaryBranchCode() : beneficiaryIdNo;
            beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(beneficiaryIdNo, 3));

            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(beneficiary.getBeneficiaryProportion())) {
                beneficiaryProportion = beneficiary.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(beneficiaryProportion, 2));
            beneficiaryListMap.add(beneficiaryMap);
        });
        map.put("beneficiaryListMap", beneficiaryListMap);
        /**********************************保险***************************************/
        Long contractStartDate = policyBo.getApproveDate();
        Long contractEndDate = policyBo.getMaturityDate();
        PrintCommon.setPrintDateTime(map, "contractStartDate", contractStartDate, 3);
        PrintCommon.setPrintDateTime(map, "contractEndDate", contractEndDate, 3);
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        //保险期限
        String coveragePeriodUnitName = null;
        if (AssertUtils.isNotEmpty(mainCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(mainCoverageBo.getCoveragePeriodUnitName())) {
            coveragePeriodUnitName = mainCoverageBo.getCoveragePeriod() + " " + mainCoverageBo.getCoveragePeriodUnitName();
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(mainCoverageBo.getCoveragePeriodUnit())) {
                coveragePeriodUnitName = mainCoverageBo.getCoveragePeriodUnitName() + " " + mainCoverageBo.getCoveragePeriod();
            }
        }
        map.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
        //交费期限
        BigDecimal totalAmount = new BigDecimal(mainCoverageBo.getTotalAmount());
        map.put("totalAmount", PrintCommon.getPrintString(totalAmount, 3));
        map.put("totalPremium", PrintCommon.getPrintString(mainCoverageBo.getTotalPremium(), 3));
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        map.put("firstTotalPremium", PrintCommon.getPrintString(policyPremium.getActualPremium(), 3));
        String premiumPeriod = AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "";
        String premiumPeriodUnitName = AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriodUnitName()) ? mainCoverageBo.getPremiumPeriodUnitName() : "";
        String ppun = premiumPeriod + premiumPeriodUnitName;
        if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(mainCoverageBo.getPremiumPeriodUnit())) {
            ppun = premiumPeriodUnitName + premiumPeriod;
        }
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "Single Payment";
            }
            ppun = premiumFrequencyName;
        }
        map.put("ppun", PrintCommon.getPrintString(ppun, 3));
        map.put("productLevelName", PrintCommon.getPrintString(mainCoverageBo.getProductLevelName(), 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        Long gainedDate = policyPayment.getGainedDate();
        PrintCommon.setPrintDateTime(map, "gainedDate", gainedDate, 3);
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        /***********************************现金价值******************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                Map<String, Object> productCashValue = new HashMap<>();
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                policyCashValuesListMap.add(productCashValue);
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }

        /***********************************特别约定******************************************/
        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getPolicySpecialContractContent(map, specialContractContent);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

}
