package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.endorse.CustomerPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 2018/9/26
 * description: 客户联系方式变更
 */
@Component
public class EndorseCustomerContactChangeData {

    public List<PrintObject> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = new ArrayList<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        CustomerPrintBo customerPrintBo = JSON.parseObject(content, CustomerPrintBo.class);

        PrintCommon.setPrintData(printObjectList, "acceptNo", customerPrintBo.getAcceptNo(), 3);
        PrintCommon.setPrintData(printObjectList, "policyNo", customerPrintBo.getPolicyNo(), 3);
        PrintCommon.setPrintData(printObjectList, "applicantName", customerPrintBo.getApplicantName(), 3);
        PrintCommon.setPrintData(printObjectList, "insuredName", customerPrintBo.getInsuredName(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "applyDate", customerPrintBo.getApplyDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "acceptDate", customerPrintBo.getAcceptDate(), 3);

        PrintCommon.setPrintData(printObjectList, "name", customerPrintBo.getName(), 3);
        PrintCommon.setPrintData(printObjectList, "mobile", customerPrintBo.getMobile(), 3);
        PrintCommon.setPrintData(printObjectList, "homePhone", customerPrintBo.getHomePhone(), 3);

        String homeAddress = AssertUtils.isNotEmpty(customerPrintBo.getHomeAddress()) ? customerPrintBo.getHomeAddress() : "";
        String homeAreaName = AssertUtils.isNotEmpty(customerPrintBo.getHomeAreaName()) ? customerPrintBo.getHomeAreaName() : "";
        PrintCommon.setPrintData(printObjectList, "homeAddress", homeAreaName +" "+ homeAddress, 3);

        PrintCommon.setPrintData(printObjectList, "homeZipCode", customerPrintBo.getHomeZipCode(), 3);
        PrintCommon.setPrintData(printObjectList, "email", customerPrintBo.getEmail(), 3);
        PrintCommon.setPrintData(printObjectList, "wechatNo", customerPrintBo.getWechatNo(), 3);
        PrintCommon.setPrintData(printObjectList, "facebookNo", customerPrintBo.getFacebookNo(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "endorseFinishDate", customerPrintBo.getEndorseFinishDate(), 3);

        return printObjectList;
    }

}
