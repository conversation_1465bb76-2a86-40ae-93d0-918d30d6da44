package com.gclife.attachment.service.common;


import com.gclife.attachment.aliyun.OssOptionService;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.config.AttachmentTypeConfigEnum;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.ImageZoomBusinessService;
import com.gclife.attachment.service.data.AttachmentService;
import com.gclife.attachment.validate.parameter.AttachmentParameterValidate;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.sql.Struct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:附件公共处理类
 */
@Component
public class AttachmentCommonBusinessService extends BaseBusinessServiceImpl {

    /**
     * 获取缩放后缀
     * @param imageZoomType 缩放类型
     * @param height 高度
     * @param width 宽度
     * @param proportion 缩放比例
     * @return String
     */
    public String getImageZoomSuffix(AttachmentTermEnum.IMAGE_ZOOM_TYPE imageZoomType,String height,String width,String proportion){
        String suffix = "";
        try {
            Map map=new HashMap();
            if(!StringUtil.isNullString(height)) {
                map.put(AttachmentTermEnum.IMAGE_ZOOM_ATTRIBUTE.HEIGHT.code(), height);
            }
            if(!StringUtil.isNullString(width)) {
                map.put(AttachmentTermEnum.IMAGE_ZOOM_ATTRIBUTE.WIDTH.code(), width);
            }
            if(!StringUtil.isNullString(proportion)) {
                map.put(AttachmentTermEnum.IMAGE_ZOOM_ATTRIBUTE.PROPORTION.code(), proportion);
            }
            suffix= StringUtil.replaceMapKeyStr(imageZoomType.code(),map);
        }catch(Exception e){
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ZOOMSUFFIX_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ZOOMSUFFIX_ERROR);
        }
        return suffix;
    }




    /**
     * 获取OSS保存图片key
     *
     * @param ossConfigBo oss_config表中记录
     * @param imageTypeCode　图片类型　
     * @param fileName 文件名
     * @return String
     */
    public String getImageSaveOssKey(OssConfigBo ossConfigBo,String imageTypeCode,String fileName){

        String saveKey="";
        try {
            String yearmonthStr= DateUtils.timeStrToString(System.currentTimeMillis(),"yyyyMM");
            String dayStr=DateUtils.timeStrToString(System.currentTimeMillis(),"dd");
            Map map=new HashMap();
            map.put(AttachmentTypeConfigEnum.YEAR_MONTH.getCode(),yearmonthStr);
            map.put(AttachmentTypeConfigEnum.DAY.getCode(),dayStr);
            map.put(AttachmentTypeConfigEnum.MEDIA_TYPE.getCode(),imageTypeCode.toLowerCase());
            map.put(AttachmentTypeConfigEnum.FILE_NAME.getCode(),fileName);
            saveKey= StringUtil.replaceMapKeyStr(ossConfigBo.getStorageRouteTemplate(),map);
        }catch(Exception e){
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_GETKEY_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_GETKEY_ERROR);
        }
        return saveKey;
    }

}
