package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.gclife.attachment.model.config.AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;

/**
 * <AUTHOR>
 * @description
 * @date 2020/5/12 4:53 下午
 */
@Component
public class ApplyData {

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Map<String, Object> map = new HashMap<>();
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        Long backTrackDate = applyPrintBo.getApplyDate();
        if(AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())){
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);

        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));
        /*****************************************投保人********************************************************************/
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        String applicantIncome = null;
        if (!AssertUtils.isNotEmpty(applicant.getIncomeName())&&AssertUtils.isNotEmpty(applicant.getIncome())) {
            applicantIncome = PrintCommon.getPrintString(new BigDecimal(applicant.getIncome()).setScale(2, BigDecimal.ROUND_HALF_UP),3);
        }else {
            applicantIncome = PrintCommon.getPrintString(applicant.getIncomeName(),3);
        }
        map.put("applicantIncome", PrintCommon.getPrintString(applicantIncome, 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        /******************************************************被保人********************************************************************/
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        String insuredRelationship = insured.getRelationship();
        if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.PARENTS.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.SPOUSE.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.CHILD.name().equals(insuredRelationship)) {
        } else {
            insuredRelationship = OTHER.name();
        }
        PrintCommon.setSelectionBox(map, "insuredRelationship" + insuredRelationship, insuredRelationship);
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(null, 2));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        /******************************************************受益人信息***************************************************************/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });
            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        /**********************************************************险种信息**************************************************************/
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel());
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000009".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }
        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));
        /*****************************************交费*******************************************************************/
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);

        Long receiveAge = null;
        if ("PRO88000000000001V2018".equals(productId.get()) || "PRO88000000000001".equals(equals(productId.get()))) {
            //教育金算法
            Date policyEffectiveDateCalCul = DateUtils.addYears(new Date(backTrackDate), Integer.valueOf(premiumPeriod.get()));
            //获取计算岁数
            long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), policyEffectiveDateCalCul);
            receiveAge = getReceiveAge(Integer.valueOf(insuredAgeYear + ""), productLevel.get());
        }
        map.put("receiveAge", PrintCommon.getPrintString(receiveAge, 1));
        /********************************************投保申请日期*********************************************************/
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        PrintCommon.setInsuredHealthRemark(map, applyPrintBo.getInsuredHealthRemark());
        List<ApplySpecialContractBo> applySpecialContractList = applyPrintBo.getListPolicySpecialContract();

        ApplyLoanBo loanContract = applyPrintBo.getLoanContract();
        String specialContractContent = this.getSpecialContractContent(productId.get(), financingMethod.get(), loanContract, language, map);
        if (AssertUtils.isNotEmpty(applySpecialContractList)) {
            Optional<ApplySpecialContractBo> first = applySpecialContractList.stream().filter(applySpecialContractBo -> "OTHER".equals(applySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                ApplySpecialContractBo applySpecialContractBo = first.get();
                specialContractContent += AssertUtils.isNotEmpty(specialContractContent) ? "\n" + applySpecialContractBo.getSpecialContractContent() : applySpecialContractBo.getSpecialContractContent();
            }
        }

        PrintCommon.getApplySpecialContractContent(map, specialContractContent);
        /********************************************健康告知书*********************************************************/
        ProductCalculation.setHealthRemark(map, applyPrintBo);
        return map;
    }

    private String getSpecialContractContent(String productId, String financingMethod, ApplyLoanBo loanContract, String language, Map<String, Object> map) {
        String specialContractContent = "";

        if ("PRO88000000000005".equals(productId)) {
            String loanContractNo = PrintCommon.getPrintString(loanContract.getLoanContractNo(), 2);
            String paymentWayName = PrintCommon.getPrintString(loanContract.getPaymentWayName(), 2);
            String loanInterestRate = PrintCommon.getPrintString(loanContract.getLoanInterestRate(), 2);
            String financingMethodName = null;
            boolean financingMethodEquals = "SELF-FINANCED".equals(financingMethod);
            if (KM_KH.name().equals(language)) {
                specialContractContent = "លេខកិច្ចព្រមព្រៀងប្រាក់កម្ចី: " + loanContractNo + "\n" +
                        "វិធីសាស្រ្តទូទាត់សងប្រាក់កម្ចី: " + paymentWayName + "\n" +
                        "អត្រាការប្រាក់នៃប្រាក់កម្ចី: " + loanInterestRate + "% p.a.";

                financingMethodName = financingMethodEquals ? "ផ្តល់ហិរញ្ញប្បទានដោយខ្លួនឯង (Self-Financed)" : "ផ្តល់ហិរញ្ញប្បទានដោយធនាគារ (Bank-Financed)";
            }
            if (ZH_CN.name().equals(language)) {
                specialContractContent = "贷款合同号码：" + loanContractNo + "\n" +
                        "还款方式: " + paymentWayName + "\n" +
                        "贷款利率：" + loanInterestRate + "% p.a. ";

                financingMethodName = financingMethodEquals ? "自付" : "贷款付";
            }
            if (EN_US.name().equals(language)) {
                specialContractContent = "Loan Agreement No.: " + loanContractNo + "\n" +
                        "Repayment Method: " + paymentWayName + "\n" +
                        "Loan Interest Rate: " + loanInterestRate + "% p.a. ";

                financingMethodName = financingMethodEquals ? "Self-Financed" : "Bank-Financed";
            }
            map.put("financingMethodName", financingMethodName);
            map.put("loanBranchName", PrintCommon.getPrintString(loanContract.getLoanBranchName(), 2));
            map.put("loanBranchCode", PrintCommon.getPrintString(loanContract.getLoanBranchCode(), 2));
        }
        return specialContractContent;
    }

    public static Long getReceiveAge(int age, String productLevel) {
        Long receiveAge = null;
        if (age < 13 && "A".equals(productLevel) && !AssertUtils.isNotNull(receiveAge)) {
            receiveAge = (age > 7l ? age : 7l);
        }
        if (age < 19 && ("B".equals(productLevel) || "A".equals(productLevel)) && !AssertUtils.isNotNull(receiveAge)) {
            receiveAge = (age > 13l ? age : 13l);
        }
        if (age <= 22 && ("C".equals(productLevel) || "B".equals(productLevel) || "A".equals(productLevel)) && !AssertUtils.isNotNull(receiveAge)) {
            receiveAge = (age > 19l ? age : 19l);
        }
        return receiveAge;
    }

}
