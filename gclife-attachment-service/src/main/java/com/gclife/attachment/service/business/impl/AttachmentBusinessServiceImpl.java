package com.gclife.attachment.service.business.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ResponseHeaderOverrides;
import com.gclife.attachment.aliyun.OssOptionService;
import com.gclife.attachment.common.FileContentTypesConstant;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.core.jooq.tables.pojos.CoiBatchPo;
import com.gclife.attachment.core.jooq.tables.pojos.ImportExportTemplatePo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.dao.CoiBatchExtDao;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.request.*;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.model.response.CoiBatchResponse;
import com.gclife.attachment.qrcode.QrCodeService;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.common.AttachmentCommonBusinessService;
import com.gclife.attachment.service.data.AttachmentService;
import com.gclife.attachment.util.AsyncUtil;
import com.gclife.attachment.validate.parameter.AttachmentParameterValidate;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfCopy;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.gclife.attachment.config.rabbitmq.RabbitMQTopicConfiguration.PREVENT_OOM_ROUTING_KEY;
import static com.gclife.attachment.config.rabbitmq.RabbitMQTopicConfiguration.TOPIC_EXCHANGE;
import static com.gclife.attachment.model.config.AttachmentErrorConfigEnum.*;
import static com.gclife.attachment.model.config.AttachmentTermEnum.MESSAGE_PROCESSING_TYPE.GENERATE_DOCUMENTS;
import static com.gclife.attachment.model.config.AttachmentTermEnum.MESSAGE_PROCESSING_TYPE.PDF2IMAGE;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件操作服务实现类
 */
@Service
public class AttachmentBusinessServiceImpl extends BaseBusinessServiceImpl implements AttachmentBusinessService {

    @Autowired
    private OssOptionService ossOptionService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private AttachmentExtDao attachmentExtDao;

    @Autowired
    private AttachmentParameterValidate attachmentParameterValidate;

    @Autowired
    private OssConfigExtDao ossConfigExtDao;

    @Autowired
    private AttachmentCommonBusinessService attachmentCommonBusinessService;

    @Autowired
    private ITextPdfService iTextPdfService;


    @Autowired
    private QrCodeService qrCodeService;


    @Autowired
    private AttachmentBusinessService attachmentBusinessService;

    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AsyncUtil asyncUtil;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private CoiBatchExtDao coiBatchExtDao;


    /**
     * 上传多媒体文件
     *
     * @param users         当前登录用户
     * @param fileName
     * @param type          文件类型
     * @param multipartFile 文件集合
     * @param attachmentId
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<AttachmentResponse> uploadMedia(Users users, String fileName, String type, MultipartFile multipartFile, String attachmentId) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<AttachmentResponse>();
        try {
            AttachmentResponse attachmentResponse = new AttachmentResponse();
            //            System.err.println(media.getOriginalFilename());
            //创建临时文件 不给定路径，默认存储在 /tmp 目录下
            System.err.println(multipartFile.getOriginalFilename());
            System.err.println(multipartFile.getName());
            System.err.println(multipartFile.getContentType());
            String name = new String(multipartFile.getOriginalFilename().getBytes(), "UTF-8");
            // 文件流
            InputStream multipartFileInputStream = multipartFile.getInputStream();

            this.getLogger().info("文件名称=============================" + name);
            if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                String substringName = name.substring(name.indexOf("."), name.length());
                if (AssertUtils.isNotEmpty(substringName) && ".xls".equals(substringName)) {
                    resultObject.setData(attachmentResponse);
                    throw new RequestException(ATTACHMENT_UPLOAD_XLS_ERROR);
                }
            }
            String suffix = "";
            if (AssertUtils.isNotEmpty(fileName) && fileName.contains(".")) {
                suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
                fileName = fileName.substring(0, fileName.lastIndexOf("."));
            } else if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                suffix = name.substring(name.lastIndexOf(".") + 1, name.length());
            }

            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            File newWebm2mp4TempFile = null;
            if (multipartFile.getContentType().equals("image/png")) {
                suffix = "png";
                type = "image";
            } else if (multipartFile.getContentType().equals("image/gif")) {
                suffix = "gif";
                type = "image";
            } else if (multipartFile.getContentType().equals("image/jpeg")) {
                suffix = "jpg";
                type = "image";
            } else if (multipartFile.getContentType().equals("image/bmp")) {
                suffix = "bmp";
                type = "image";
            } else if (multipartFile.getContentType().equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
                suffix = "xlsx";
                type = "document";
            } else if (multipartFile.getContentType().equals("application/vnd.android.package-archive")) {
                suffix = "apk";
                type = "app";
                if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                    fileName = name.substring(0, name.indexOf("."));
                }
            } else if (multipartFile.getContentType().equals("application/octet-stream")) {
                suffix = "ipa";
                type = "app";
                if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                    fileName = name.substring(0, name.indexOf("."));
                }
            } else if (multipartFile.getContentType().equals("video/mp4")) {
                suffix = "mp4";
                type = "video";
                if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                    fileName = name.substring(0, name.indexOf("."));
                }
            } else if (multipartFile.getContentType().contains("audio/")) {
                type = "audio";
                if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                    fileName = name.substring(0, name.indexOf("."));
                }
            } else if (multipartFile.getContentType().contains("video/webm")) {
                suffix = "mp4";
                type = "video";

                String saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, name);
                getLogger().info("webm转mp4 先异步上传webm视频: {}", saveKey);
                asyncUtil.uploadSourceWebm(ossConfigBo, saveKey, multipartFile.getInputStream());

                if (AssertUtils.isNotEmpty(name) && name.contains(".")) {
                    fileName = name.substring(0, name.indexOf("."));
                }
                FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(multipartFileInputStream);
                // 获取存放临时文件的文件夹
                String tempPath = System.getProperty("java.io.tmpdir") + File.separator + "web2mp4";
                File webm2mp4Path = new File(tempPath);
                // 不存在则创建
                if (!webm2mp4Path.exists()) {
                    webm2mp4Path.mkdirs();
                }
                // 创建随机文件名称
                tempPath = tempPath + File.separator + UUIDUtils.getUUIDShort() + ".mp4";
                Frame captured_frame = null;
                FFmpegFrameRecorder recorder = null;
                // FFmpegLogCallback.set(); // 打开日志
                try {
                    frameGrabber.start();
                    recorder = new FFmpegFrameRecorder(tempPath, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
                    recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
                    recorder.setFormat("mp4");
                    double frameRate = frameGrabber.getFrameRate();
                    if (frameRate > 30.00) {
                        frameRate = frameRate * 0.6;
                    }
                    recorder.setFrameRate(frameRate);
                    recorder.setVideoBitrate(frameGrabber.getVideoBitrate());
                    recorder.setAspectRatio(frameGrabber.getAspectRatio());
                    recorder.setAudioOptions(frameGrabber.getAudioOptions());
                    recorder.setSampleRate(frameGrabber.getSampleRate());
                    recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
                    recorder.start();
                    while ((captured_frame = frameGrabber.grabFrame()) != null) {
                        // recorder.setTimestamp(frameGrabber.getTimestamp());
                        recorder.record(captured_frame);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (recorder != null) {
                        try {
                            recorder.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        frameGrabber.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                newWebm2mp4TempFile = new File(tempPath);
                multipartFileInputStream = Files.newInputStream(newWebm2mp4TempFile.toPath());
            } else if (multipartFile.getContentType().contains("video/quicktime")) {
                suffix = "MOV";
                type = "video";
            } else if (multipartFile.getContentType().contains("application/zip")) {
                suffix = "zip";
                type = "zip";
            }

            //&& !AssertUtils.isNotEmpty(suffix)
            if (!AssertUtils.isNotEmpty(fileName) && AssertUtils.isNotEmpty(name) && name.contains(".")) {
                fileName = name.substring(0, name.lastIndexOf("."));
                suffix = name.substring(name.lastIndexOf(".") + 1, name.length());
            }

            //上传到阿里云
            String saveKey = this.uploadMediaOss(ossConfigBo, type, multipartFileInputStream, fileName, suffix);
            getLogger().info("name: {} 上传到OSS url: {}", name, saveKey);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), saveKey, AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);

            //保存key到数据库
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(attachmentId);
            boolean insert = false;
            boolean update = true;
            if (!AssertUtils.isNotNull(attachmentPo)) {
                attachmentPo = new AttachmentPo();
                insert = true;
                update = false;
            }
            attachmentPo.setUrl(saveKey);
            attachmentPo.setAttachmentDomain(ossConfigBo.getDomain());
            attachmentPo.setFileSuffix(suffix);
            if (AssertUtils.isNotEmpty(fileName)) {
                attachmentPo.setFileName(fileName);
            }
            if (AssertUtils.isNotEmpty(attachmentId)) {
                attachmentPo.setAttachmentId(attachmentId);
                attachmentPo.setForceSave(true);
            }
            if (insert) {
                //保存
                attachmentService.insertAttachmentPo(attachmentPo);
            }
            if (update) {
                //保存
                attachmentService.updateAttachmentPo(attachmentPo);
            }
            //设置返回值
            attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
            attachmentResponse.setFileSuffix(suffix);
            // 将webm转mp4的文件删除
            if (AssertUtils.isNotNull(newWebm2mp4TempFile)) {
                if (newWebm2mp4TempFile.exists()) {
                    newWebm2mp4TempFile.delete();
                }
            }
            resultObject.setData(attachmentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 下载媒体文件 返回url
     *
     * @param users   当前登录用户
     * @param mediaId 媒体ID
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> loadMedia(Users users, String mediaId) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<AttachmentResponse>();
        try {

            if (!AssertUtils.isNotEmpty(mediaId)) {
                return resultObject;
            }
            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //查询资源
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
            //资源验证
//            AssertUtils.isNotNull(this.getLogger(), attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //设置返回数据
            if (AssertUtils.isNotNull(attachmentPo)) {
                if (AssertUtils.isNotNull(attachmentPo.getExpiresIn()) && AssertUtils.isNotNull(attachmentPo.getCreatedDate()) && DateUtils.getCurrentTime() - attachmentPo.getCreatedDate() > attachmentPo.getExpiresIn()) {
                    throwsException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_EXPIRES_IN_IS_INVALID);
                }
                AttachmentResponse attachmentResponse = new AttachmentResponse();
                attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
                attachmentResponse.setFileName(attachmentPo.getFileName());
                attachmentResponse.setFileSuffix(attachmentPo.getFileSuffix());
                attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl());
                attachmentResponse.setPdfTransformImageUrl(JSONObject.parseObject(attachmentPo.getPdfTransformImageUrl(), List.class));
                resultObject.setData(attachmentResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 下载媒体文件 可预览可下载
     *
     * @param users   当前登录用户
     * @param mediaId 媒体ID
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> loadAutoMedia(Users users, String mediaId) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<AttachmentResponse>();
        OSSClient ossClient = null;
        try {

            if (!AssertUtils.isNotEmpty(mediaId)) {
                return resultObject;
            }

            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //查询资源
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
            //资源验证
            AssertUtils.isNotNull(this.getLogger(), attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //设置返回数据
            if (AssertUtils.isNotNull(attachmentPo)) {
                if (AssertUtils.isNotNull(attachmentPo.getExpiresIn()) && AssertUtils.isNotNull(attachmentPo.getCreatedDate()) && DateUtils.getCurrentTime() - attachmentPo.getCreatedDate() > attachmentPo.getExpiresIn()) {
                    throwsException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_EXPIRES_IN_IS_INVALID);
                }
                AttachmentResponse attachmentResponse = new AttachmentResponse();
                attachmentResponse.setMediaId(attachmentPo.getAttachmentId());

                ossClient = ossOptionService.getOssClient(ossConfigBo);

                // 创建bucket
                String bucket = getConfigValue(ossConfigBo.getBucket(), "oss_config.bucket.");
                GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, attachmentPo.getUrl());
                request.setExpiration(DateUtils.addDays(new Date(), 1));
                ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
                responseHeaderOverrides.setContentDisposition("attachment");
                request.setResponseHeaders(responseHeaderOverrides);
                URL url = ossClient.generatePresignedUrl(request);
                this.getLogger().info("转换后URL：==========" + url.toURI().toString());
                attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl());
                attachmentResponse.setAttachmentUrl(url.toURI().toString().replaceAll(url.getProtocol() + "://" + url.getHost() + "/", getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.")));
                resultObject.setData(attachmentResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        } finally {
            if (AssertUtils.isNotNull(ossClient)) {
                ossClient.shutdown();
            }
        }
        return resultObject;
    }

    /**
     * 通过attachment表中的ID获取一个OSSObject
     *
     * @param mediaId attachment表中的主键ID
     * @return OSSObject
     */
    @Override
    public byte[] loadOssObjectByAttachmentId(String mediaId) {
        /**
         * TODO:
         * 1.根据传入的媒体ID获取资源路径
         */
        /**
         *获取配置文件
         */
        this.getLogger().info("loadOssObjectByAttachmentId mediaId : {}", mediaId);
        OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
        AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

        //数据验证
        AssertUtils.isNotEmpty(this.getLogger(), mediaId, AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_MEDIA_ID_IS_NOT_NULL);
        //查询资源
        AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
        //资源验证
        AssertUtils.isNotNull(this.getLogger(), attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
        //阿里云读取流
        byte[] bytes = null;
        try {
            bytes = this.ossOptionService.getFileObject(ossConfigBo, attachmentPo.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_ALIYUN_IS_NOT_FOUND_OBJECT);
        }
        //未找到资源
        AssertUtils.isNotNull(this.getLogger(), bytes, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
        return bytes;
    }

    /**
     * 下载媒体文件 返回字节流
     *
     * @param users   当前登录用户
     * @param mediaId 媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    @Override
    public ResultObject<AttachmentByteResponse> loadMediaByte(Users users, String mediaId) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<AttachmentByteResponse>();
        //设置返回数据
        AttachmentByteResponse attachmentByteResponse = new AttachmentByteResponse();
        try {
//            InputStream inputStream = loadOssObjectByAttachmentId(mediaId).getObjectContent();
//            byte[] bt = new byte[]{};
//            if (inputStream != null) {
//                try {
//                    bt = ByteToInputStream.input2byte(inputStream);
//                    inputStream.close();
//                } catch (Exception e) {
//                    throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
//                }
//            }
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
            if (AssertUtils.isNotNull(attachmentPo)) {
                //查询资源
                if (AssertUtils.isNotNull(attachmentPo.getExpiresIn()) && AssertUtils.isNotNull(attachmentPo.getCreatedDate()) && DateUtils.getCurrentTime() - attachmentPo.getCreatedDate() > attachmentPo.getExpiresIn()) {
                    throwsException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_EXPIRES_IN_IS_INVALID);
                }
                // pdf文件下载时不返回字节
                if (!attachmentPo.getUrl().endsWith(".pdf") && !attachmentPo.getUrl().endsWith(".mp4")) {
                    byte[] bt = loadOssObjectByAttachmentId(mediaId);
                    attachmentByteResponse.setFileByte(bt);
                }
                attachmentByteResponse.setFileSuffix(attachmentPo.getFileSuffix());
                attachmentByteResponse.setFileName(attachmentPo.getFileName());
//            if (AttachmentTermEnum.MEDIA_TYPE_SUFFIX.MP4.desc().equals(attachmentPo.getFileSuffix())) {
                attachmentByteResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl());
                attachmentByteResponse.setPdfTransformImageUrl(JSONObject.parseObject(attachmentPo.getPdfTransformImageUrl(), List.class));
//            }
            }
            attachmentByteResponse.setMediaId(mediaId);
            resultObject.setData(attachmentByteResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 下载媒体文件 (缩放) 返回url
     *
     * @param users      当前登录用户
     * @param proportion 缩放比例
     * @param mediaId    媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> loadMediaProportion(Users users, long proportion, String mediaId) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<AttachmentResponse>();
        try {
            if (!AssertUtils.isNotEmpty(mediaId)) {
                return resultObject;
            }
            /**
             * TODO:
             * 1.根据传入的媒体ID获取资源路径
             */
            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //查询资源
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
            //资源验证
//            AssertUtils.isNotNull(this.getLogger(), attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //查看文件后缀
            if (AssertUtils.isNotNull(attachmentPo)) {
                List<String> suffixList = Arrays.stream(AttachmentTermEnum.MEDIA_TYPE_SUFFIX.values()).filter(e -> e.group().equals(AttachmentTermEnum.MEDIA_TYPE.IMAGE.name())).map(AttachmentTermEnum.MEDIA_TYPE_SUFFIX::name).collect(Collectors.toList());
                //验证后缀
                if (suffixList.stream().noneMatch(e -> e.equals(attachmentPo.getFileSuffix().toUpperCase()))) {
                    throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_ZOOM);
                }
                //设置返回数据
                AttachmentResponse attachmentResponse = new AttachmentResponse();
                attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
                attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl() + AttachmentTermEnum.IMAGE_ZOOM_TYPE.ZOOM_ANALOGY.code() + proportion);
                resultObject.setData(attachmentResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 下载媒体文件 (缩放) 返回字节流
     *
     * @param users      当前登录用户
     * @param proportion 缩放比例
     * @param mediaId    媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    @Override
    public ResultObject<AttachmentByteResponse> loadMediaByteProportion(Users users, long proportion, String mediaId) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<AttachmentByteResponse>();
        try {
            if (!AssertUtils.isNotEmpty(mediaId)) {
                return resultObject;
            }
            /**
             * TODO:
             * 1.根据传入的媒体ID获取资源路径
             */

            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //查询资源
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(mediaId);
            //资源验证
            AssertUtils.isNotNull(this.getLogger(), attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //查看文件后缀
            List<String> suffixList = Arrays.stream(AttachmentTermEnum.MEDIA_TYPE_SUFFIX.values()).filter(e -> e.group().equals
                    (AttachmentTermEnum.MEDIA_TYPE.IMAGE.name())).map(AttachmentTermEnum.MEDIA_TYPE_SUFFIX::name).collect(Collectors.toList());
            //验证后缀
            if (suffixList.stream().noneMatch(e -> e.equals(attachmentPo.getFileSuffix().toUpperCase()))) {
                throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_ZOOM);
            }

            //阿里云读取流
            byte[] bt = this.ossOptionService.getFileObject(ossConfigBo, attachmentPo.getUrl());
            //未找到资源
            AssertUtils.isNotNull(this.getLogger(), bt, AttachmentErrorConfigEnum
                    .ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
//            InputStream inputStream = ossObject.getObjectContent();
//            byte[] bt = new byte[]{};
//            if (inputStream != null) {
//                try {
//                    bt = ByteToInputStream.input2byte(inputStream);
//                    inputStream.close();
//                } catch (Exception e) {
//                }
//            }
//            //设置返回数据
            AttachmentByteResponse attachmentByteResponse = new AttachmentByteResponse();
            attachmentByteResponse.setFileByte(bt);
            attachmentByteResponse.setMediaId(attachmentPo.getAttachmentId());
            resultObject.setData(attachmentByteResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * Base64上传多媒体文件
     *
     * @param users             当前登录用户
     * @param type              文件类型
     * @param attachmentRequest 文件信息
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    @Transactional
    public ResultObject<AttachmentResponse> uploadMediaBase64(Users users, String type, AttachmentRequest attachmentRequest) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<AttachmentResponse>();
        resultObject.setData(this.uploadMediaBase64(type, attachmentRequest));
        return resultObject;
    }


    /**
     * Base64批量上传多媒体文件
     *
     * @param users                  当前登录用户
     * @param type                   文件类型
     * @param attachmentBatchRequest 文件集合信息
     * @return ResultObject<ListAttachmentResponse>>
     */
    @Override
    @Transactional
    public ResultObject<List<AttachmentResponse>> uploadMediaBatchBase64(Users users, String type, AttachmentBatchRequest
            attachmentBatchRequest) {
        ResultObject<List<AttachmentResponse>> resultObject = new ResultObject<List<AttachmentResponse>>();
        try {
            List<AttachmentRequest> attachmentRequests = null;
            try {
                attachmentRequests = JSONObject.parseArray(attachmentBatchRequest.getListAttachment(), AttachmentRequest.class);
            } catch (Exception e) {
                throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_FILES_FORMAT_ERROR);
            }
            //校验附件信息
            attachmentParameterValidate.validParameterUploadMediaBatchBase64(type, attachmentRequests);

            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //上传到阿里云
            List<String> saveKeys = uploadMediaOssBatchBase64(ossConfigBo, type, attachmentRequests);
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), saveKeys, AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
            //保存到本地数据库
            List<AttachmentPo> attachmentPoList = new ArrayList<>();
            saveKeys.stream().map(e -> {
                AttachmentPo attachmentPo = new AttachmentPo();
                attachmentPo.setUrl(e);
                attachmentPo.setAttachmentDomain(ossConfigBo.getDomain());
                attachmentPoList.add(attachmentPo);
                return null;
            }).collect(Collectors.toList());
            attachmentService.saveAttachmentPo(attachmentPoList);

            //设置返回值
            List<AttachmentResponse> responses = new ArrayList<>();
            attachmentPoList.stream().map(e -> {
                AttachmentResponse attachmentResponse = new AttachmentResponse();
                attachmentResponse.setMediaId(e.getAttachmentId());
                responses.add(attachmentResponse);
                return null;
            }).collect(Collectors.toList());

            resultObject.setData(responses);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 上传文件到阿里云
     *
     * @param ossConfigBo 对应oss_config表中记录
     * @param type        类型
     * @param inputStream 文件
     * @param fileName    文件名
     * @return String
     */
    private String uploadMediaOss(OssConfigBo ossConfigBo, String type, InputStream inputStream, String fileName, String suffix) {
        String saveKey = "";
        try {
            if (AttachmentTermEnum.MEDIA_TYPE_SUFFIX.APP.name().equalsIgnoreCase(type)) {
                //判断为app则重新定义命名规则选择app名字+时分秒
                String time = DateUtils.timeStrToString(System.currentTimeMillis(), "HHmmss");
                saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, fileName + time + "."
                        + suffix);
            } else {
                //时间戳改成uuid
                saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, UUIDUtils.getUUIDShort() + "."
                        + suffix);
            }
            //获取文流
            boolean uploadFlag = ossOptionService.putObject(ossConfigBo, saveKey, inputStream);
            if (uploadFlag == false) {
                throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
            } else {
                return saveKey;
            }
        } catch (Exception e) {
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
    }


    /**
     * 上传文件到阿里云
     *
     * @param ossConfigBo       对应oss_config表中记录
     * @param type              类型
     * @param attachmentRequest 附件上传接口请求参数
     * @return String
     */
    private String uploadMediaOssBase64(OssConfigBo ossConfigBo, String type, AttachmentRequest attachmentRequest) {
        try {
            // 时间戳改成uuid
            String saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, UUIDUtils.getUUIDShort() + "."
                    + attachmentRequest.getFileSuffix());
            //获取文流
            byte[] bt = IoUtils.getBASE64DecoderFileContent(attachmentRequest.getFileContent());
            InputStream inputStream = ByteToInputStream.byte2Input(bt);
            boolean uploadFlag = ossOptionService.putObject(ossConfigBo, saveKey, inputStream);
            inputStream.close();
            if (uploadFlag == false) {
                throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
            } else {
                return saveKey;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
    }

    /**
     * 异步上传多个文件到阿里云
     *
     * @param ossConfigBo
     * @param type
     * @param attachmentRequestMap
     */
    private void uploadMediaOssBase64(OssConfigBo ossConfigBo, String type, Map<Integer, AttachmentRequest> attachmentRequestMap) {
        try {
            ConcurrentHashMap<String, InputStream> keyAndStream = new ConcurrentHashMap<>();
            for (Integer index : attachmentRequestMap.keySet()) {
                AttachmentRequest attachmentRequest = attachmentRequestMap.get(index);
                // 时间戳改成uuid
                String saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, UUIDUtils.getUUIDShort() + "."
                        + attachmentRequest.getFileSuffix());
                attachmentRequest.setUrl(saveKey);

                //获取文流
                byte[] bt = IoUtils.getBASE64DecoderFileContent(attachmentRequest.getFileContent());
                InputStream inputStream = ByteToInputStream.byte2Input(bt);
                keyAndStream.put(saveKey, inputStream);
            }
            // 异步上传
            asyncUtil.putObjects(ossConfigBo, keyAndStream);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
    }


    /**
     * 上传文件到阿里云
     *
     * @param ossConfigBo        对应oss_config表中记录
     * @param type               类型
     * @param attachmentRequests 附件上传接口请求参数集
     * @return List<String>
     */
    private List<String> uploadMediaOssBatchBase64(OssConfigBo ossConfigBo, String type, List<AttachmentRequest> attachmentRequests) {
        List<String> listKeys = new ArrayList<>();
        try {
            for (AttachmentRequest attachmentRequest : attachmentRequests) {
                //时间戳改成uuid
                String saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo, type, UUIDUtils.getUUIDShort() + "." + attachmentRequest.getFileSuffix());
                //获取文流
                byte[] bt = IoUtils.getBASE64DecoderFileContent(attachmentRequest.getFileContent());
                InputStream inputStream = ByteToInputStream.byte2Input(bt);
                boolean uploadFlag = ossOptionService.putObject(ossConfigBo, saveKey, inputStream);
                if (uploadFlag == false) {
                    throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
                } else {
                    listKeys.add(saveKey);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
        return listKeys;
    }

    /**
     * 电子保单生成，加密，并上传到阿里云
     *
     * @param users             当前登录用户
     * @param electronicRequest 电子保单生成请求
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public ResultObject<List<AttachmentResponse>> electronicPolicyGenerator(Users users, ElectronicPolicyGeneratorRequest electronicRequest) {
        ResultObject<List<AttachmentResponse>> resultObject = new ResultObject<>();
        Boolean asyncFlag = electronicRequest.getAsyncFlag();
        this.getLogger().info("是否需要异步生成 {} 29号产品生成coi", asyncFlag);
        // 异步生成
        if (AssertUtils.isNotNull(asyncFlag) && asyncFlag) {
            Message message = MessageBuilder.withBody(JSONObject.toJSONString(electronicRequest).getBytes())
                    .setHeader("processingType", GENERATE_DOCUMENTS.name())
                    .build();
            this.getLogger().info("29号产品生成coi生成开始 pdfType : {}, productId: {}", electronicRequest.getPdfType(), electronicRequest.getProductId());
            rabbitTemplate.convertAndSend(TOPIC_EXCHANGE, PREVENT_OOM_ROUTING_KEY, message);
            return ResultObject.success();
        }

        try {
            //记录　请求数据
            this.getLogger().info("pdfType : {}, productId: {}", electronicRequest.getPdfType(), electronicRequest.getProductId());
            // 参数校验
            attachmentParameterValidate.validParameterElectronicPolicyGenerator(electronicRequest);

            // 获取pdf模板
            PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicRequest);

            //定义pdf二进制输出流
            this.getLogger().info("pdfTemplateConfigBo pdfTypeDetails {}", pdfTemplateConfigBo.getPdfTypeDetails());
            PrintDao policyDao = (PrintDao) SpringContextUtils.getApplicationContext().getBean(pdfTemplateConfigBo.getPdfTypeDetails());
            this.getLogger().info("生成电子保单 PrintDao 的实现类是:{}, {}", policyDao.getClass().getSimpleName(), policyDao.getClass().getCanonicalName());

            String language = electronicRequest.getLanguage();
            //英文
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                policyDao.printEN_US(electronicRequest);
            }
            //中文
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                policyDao.printZH_CN(electronicRequest);
            }
            //柬埔寨文
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                policyDao.printKM_KH(electronicRequest);
            }

            resultObject.setData(electronicRequest.getAttachmentResponseList());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
        }
        return resultObject;
    }

    /**
     * 电子保单下载并解密
     *
     * @param attachmentId 附件ID
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public ResultObject<AttachmentByteResponse> electronicPolicyDownload(HttpServletResponse response, String password, String... attachmentId) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<>();
        AttachmentByteResponse attachmentByteResponse = new AttachmentByteResponse();

        try {
            //记录请求数据
            getLogger().error("attachmentId {}", JSON.toJSON(attachmentId));

            AssertUtils.isNotNull(this.getLogger(), attachmentId, AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_MEDIA_ID_IS_NOT_NULL);

            List<byte[]> decryptedDataList = new ArrayList<>();
            // 添加页码集合
            List<byte[]> sortDataList = new ArrayList<>();
            // 添加空白页集合
            List<byte[]> addBlankPageList = new ArrayList<>();
            String language = "";
            //循环遍历保单　获取保单集合
            boolean UPDATE_PAGE = false;
            for (String id : attachmentId) {
                if (!AssertUtils.isNotEmpty(id)) {
                    continue;
                }
                if (id.equals("EN_US") || id.equals("ZH_CN") || id.equals("KM_KH")) {
                    language = id;
                    continue;
                }
                //是否需要修改页码
                if (id.equals("UPDATE_PAGE")) {
                    UPDATE_PAGE = !UPDATE_PAGE;
                    if (AssertUtils.isNotEmpty(addBlankPageList)) {
                        byte[] mergePdfFileBytes;
                        if (AssertUtils.isNotEmpty(sortDataList)) {
                            // 合并pdf且添加页码
                            mergePdfFileBytes = PrintCommon.updateAndSavePageNumber(PrintCommon.mergePdfFiles(sortDataList));
                            sortDataList = new ArrayList<>();
                        } else {
                            // 合并pdf
                            mergePdfFileBytes = PrintCommon.mergePdfFiles(addBlankPageList);
                        }

                        // 添加空白页
                        decryptedDataList.add(PrintCommon.pdfEvenPage(mergePdfFileBytes, language));
                        addBlankPageList = new ArrayList<>();
                    }
                    continue;
                }
                //先从阿里云上读取附件数据
                byte[] encrypedData = loadOssObjectByAttachmentId(id);
                // 是否加空白页
                if (UPDATE_PAGE) {
                    addBlankPageList.add(encrypedData);
                    //打印页码排序
                    // 第一版与第二版的条款页码需要代码生成 第三版不用
                    // if (UPDATE_PAGE) {
                    if (id.startsWith("180926_") || id.startsWith("211028_")) {
                        sortDataList.add(encrypedData);
                    }
                    continue;
                }
                decryptedDataList.add(encrypedData);
            }
            //附件集合进行　合并
            byte[] mergePdfBytes = PrintCommon.mergePdfFiles(decryptedDataList);
            //加密
            if (attachmentId.length > 1) {
                mergePdfBytes = iTextPdfService.generatePdfFromTemplate(mergePdfBytes);
            }
            if (AssertUtils.isNotEmpty(password)) {
                mergePdfBytes = PrintCommon.lockUp(mergePdfBytes, password);
            }
            attachmentByteResponse.setFileByte(mergePdfBytes);

/*
            response.setHeader("Content-Type", "application/pdf");
            response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode("attachment.pdf", "UTF-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "POST,GET");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(attachmentByteResponse.getFileByte());
            outputStream.close();
            */

            resultObject.setData(attachmentByteResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ATTACHMENT_DOWNLOAD_ERROR.getValue());
            throw new RequestException(ATTACHMENT_DOWNLOAD_ERROR);
        }

        return resultObject;
    }

    @Override
    public ResultObject<AttachmentByteResponse> electronicPolicyDownloadCoi(HttpServletResponse response, String password, String... attachmentId) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<>();
        AttachmentByteResponse attachmentByteResponse = new AttachmentByteResponse();

        try {
            //记录　请求数据
            getLogger().info("-- > attachmentId {}", JSON.toJSON(attachmentId));
            AssertUtils.isNotNull(this.getLogger(), attachmentId, AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_MEDIA_ID_IS_NOT_NULL);

            byte[] bytes = null;
            // 创建一个新的 PDF 文档
            Document document = new Document(PageSize.A4);
            Path tempFile = Files.createTempFile("pdf", "pdf");
            File file = tempFile.toFile();
            FileOutputStream mergeOut = new FileOutputStream(file);
            //ByteArrayOutputStream mergeOut = new ByteArrayOutputStream();
            // 创建一个 PDF 写入器
            PdfCopy copy = new PdfCopy(document, mergeOut);
            // 打开文档
            document.open();
            for (String id : attachmentId) {
                //先从阿里云上读取附件数据
                byte[] encrypedData = loadOssObjectByAttachmentId(id);
                //附件集合进行　合并
                PrintCommon.addPdfFileCoi(copy, encrypedData);
            }
            //附件集合进行　合并
            attachmentByteResponse.setFileByte(Files.readAllBytes(tempFile));
            getLogger().info("file bite {}", attachmentByteResponse.getFileByte().length);
            //attachmentByteResponse.setFileByte(mergeOut.toByteArray());
            // 关闭文档
            document.close();
            file.deleteOnExit();
            Files.deleteIfExists(tempFile);
            mergeOut.close();
            copy.close();

            //pdf过大返回路径特殊处理
            if (attachmentByteResponse.getFileByte().length > 41943040) {
                this.getLogger().info("pdf过大返回路径特殊处理开始");
                AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUploadCoi(attachmentByteResponse.getFileByte());
                this.getLogger().info("pdf过大返回路径特殊处理完成 {}", JSON.toJSON(attachmentResponse));
                //设置返回值
                attachmentByteResponse.setUrl(getConfigValue(attachmentResponse.getAttachmentDomain(), "oss_config.domain.") + attachmentResponse.getUrl());
                attachmentByteResponse.setFileByte(null);
                this.getLogger().info("pdf过大返回路径特殊处理完成返回路径 {}", JSON.toJSON(attachmentByteResponse));
            }
            resultObject.setData(attachmentByteResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ATTACHMENT_DOWNLOAD_ERROR.getValue());
            throw new RequestException(ATTACHMENT_DOWNLOAD_ERROR);
        }
        //this.getLogger().info("执行完毕..." + resultObject.getData().getFileByte().length);
        return resultObject;
    }

    /**
     * 二维码生成并保存到阿里云
     *
     * @param qrCodeAttachmentRequest 附件请求
     * @return ResultObject<AttachmentByteResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> attachmentQrCodeGenerate(Users users, QrCodeAttachmentRequest qrCodeAttachmentRequest) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<>();

        try {
            String codeText = qrCodeAttachmentRequest.getCodeText();
            AssertUtils.isNotEmpty(codeText);
            AttachmentRequest attachmentRequest = new AttachmentRequest();
            String fileSuffix = "png";
            String filePath = UUIDUtils.getUUIDShort() + "." + fileSuffix;
            String fileType = "IMAGE";
            int size = 250;
            if (AssertUtils.isNotNull(qrCodeAttachmentRequest.getSize())) {
                size = qrCodeAttachmentRequest.getSize();
            }

            byte[] qrCodeBytes = qrCodeService.GenerateQrCode(codeText, fileSuffix, size);

            attachmentRequest.setFileContent(new BASE64Encoder().encode(qrCodeBytes));
            attachmentRequest.setFileSuffix(fileSuffix);
            attachmentRequest.setFileName(filePath);
            attachmentRequest.setFileType(fileType);
            resultObject = uploadMediaBase64(users, fileType, attachmentRequest);

            //获取url路径
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(resultObject.getData().getMediaId());
            String url = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl();
            resultObject.getData().setUrl(url);
        } catch (Exception e) {
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
        return resultObject;
    }


    @Override
    public ResultObject<AttachmentResponse> templateGet(Users users, String templateCode) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<>();
        try {
            ImportExportTemplatePo importExportTemplatePo = attachmentExtDao.getTemplate(templateCode);
            if (!AssertUtils.isNotNull(importExportTemplatePo)) {
                throw new RequestException(ATTACHMENT_IMPORT_EXPORT_TEMPLATE_IS_NOT_FOUND_OBJECT);
            }
            AttachmentResponse attachmentResponse = new AttachmentResponse();
            attachmentResponse.setUrl(getConfigValue(importExportTemplatePo.getTemplateHttps(), "oss_config.domain.") + importExportTemplatePo.getTemplateOsskey());
            attachmentResponse.setTemplateName(importExportTemplatePo.getTemplateName());
            String url = attachmentResponse.getUrl();
            attachmentResponse.setFileSuffix(url.substring(url.lastIndexOf(".") + 1));
            resultObject.setData(attachmentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 二维码生成并保存到阿里云
     *
     * @param qrCodeRequest 附件请求
     * @return ResultObject<AttachmentByteResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> generateQrcode(QRCodeRequest qrCodeRequest) {
        ResultObject<AttachmentResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), qrCodeRequest.getSceneStr(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_SECEN_STR_IS_NOT_NULL);
            AttachmentRequest attachmentRequest = new AttachmentRequest();
            String fileSuffix = "png";
            String filePath = UUIDUtils.getUUIDShort() + "." + fileSuffix;
            String fileType = "IMAGE";
            qrCodeService.GenerateQrCode(qrCodeRequest.getSceneStr(), filePath, fileSuffix, 600);
            attachmentRequest.setFileContent(new BASE64Encoder().encode(Files.readAllBytes(Paths.get(filePath))));
            attachmentRequest.setFileSuffix(fileSuffix);
            attachmentRequest.setFileName(filePath);
            attachmentRequest.setFileType(fileType);
            attachmentRequest.setExpiresIn(qrCodeRequest.getExpiresIn());
            AttachmentResponse attachmentResponse = uploadMediaBase64(fileType, attachmentRequest);
            resultObject.setData(attachmentResponse);
            //获取url路径
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(attachmentResponse.getMediaId());
            String url = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl();
            resultObject.getData().setUrl(url);
            Files.deleteIfExists(Paths.get(filePath));
        } catch (Exception e) {
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject deleteOneAttachment(Users users, String mediaId) {
        //TODO
        return null;
    }

    @Override
    public ResultObject deleteAttachment(Users users, List<String> mediaIds) {
        //TODO
        return null;
    }

    /**
     * Base64上传多媒体文件
     *
     * @param type              文件类型
     * @param attachmentRequest 文件信息
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public AttachmentResponse uploadMediaBase64(String type, AttachmentRequest attachmentRequest) {
        this.getLogger().info("*************************文件上传开始时间S:{}*************************************", System.currentTimeMillis());
        //校验附件信息
        attachmentParameterValidate.validParameterUploadMediaBase64(type, attachmentRequest);
        /**
         *获取配置文件
         */
        OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
        AssertUtils.isNotNull(this.getLogger(), ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);
        //上传到阿里云
        String saveKey = uploadMediaOssBase64(ossConfigBo, type, attachmentRequest);
//            String saveKey = "http://www.baidu.com";
        //数据验证
        AssertUtils.isNotEmpty(this.getLogger(), saveKey, AttachmentErrorConfigEnum.ATTACHMENT_UPLOAD_ERROR);
        //保存key到数据库
        AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(attachmentRequest.getAttachmentId());
        boolean insert = false;
        boolean update = true;
        if (!AssertUtils.isNotNull(attachmentPo)) {
            attachmentPo = new AttachmentPo();
            insert = true;
            update = false;
        }
        attachmentPo.setUrl(saveKey);
        attachmentPo.setAttachmentDomain(ossConfigBo.getDomain());
        attachmentPo.setFileSuffix(attachmentRequest.getFileSuffix().toUpperCase());
        attachmentPo.setExpiresIn(attachmentRequest.getExpiresIn());
        attachmentPo.setFileName(attachmentRequest.getFileName());
        if (AssertUtils.isNotEmpty(attachmentRequest.getAttachmentId())) {
            attachmentPo.setAttachmentId(attachmentRequest.getAttachmentId());
            attachmentPo.setForceSave(true);
        }
        if (insert) {
            //保存
            attachmentService.insertAttachmentPo(attachmentPo);
        }
        if (update) {
            //保存
            attachmentService.updateAttachmentPo(attachmentPo);
        }
        //设置返回值
        AttachmentResponse attachmentResponse = new AttachmentResponse();
        attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
        attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl());
        this.getLogger().info("*************************文件上传结束时间E:{}*************************************{}",System.currentTimeMillis(), JSON.toJSONString(attachmentResponse));
        return attachmentResponse;
    }

    @Override
    public void downloadAttachments(HttpServletResponse response, String attachmentId) {
        try {
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(attachmentId);
            AssertUtils.isNotNull(this.getLogger(), attachmentPo, ATTACHMENT_EXPIRES_IN_NULL);
            byte[] bytes = loadOssObjectByAttachmentId(attachmentId);
            String fileName = attachmentPo.getFileName();
            if (!AssertUtils.isNotEmpty(fileName)) {
                fileName = attachmentPo.getCreatedDate() + "";
            }
            fileName += "." + attachmentPo.getFileSuffix();
            response.setCharacterEncoding("UTF-8");

            String contentType = "application/x-download";

            if (AssertUtils.isNotEmpty(attachmentPo.getFileSuffix())) {
                if (FileContentTypesConstant.fileContentTypeMap.containsKey(attachmentPo.getFileSuffix().toLowerCase())) {
                    contentType = FileContentTypesConstant.fileContentTypeMap.get(attachmentPo.getFileSuffix().toLowerCase());
                }
            }
            response.setContentType(contentType);
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(ATTACHMENT_DOWNLOAD_ERROR);
        }
    }

    @Override
    public void downloadInlineAttachments(HttpServletResponse response, String attachmentId) {
        try {
            AttachmentPo attachmentPo = attachmentExtDao.loadAttachmentPo(attachmentId);
            AssertUtils.isNotNull(this.getLogger(), attachmentPo, ATTACHMENT_EXPIRES_IN_NULL);
            byte[] bytes = loadOssObjectByAttachmentId(attachmentId);
            String fileName = attachmentPo.getFileName();
            if (!AssertUtils.isNotEmpty(fileName)) {
                fileName = attachmentPo.getCreatedDate() + "";
            }
            fileName += "." + attachmentPo.getFileSuffix();
            response.setCharacterEncoding("UTF-8");

            String contentType = "application/x-download";

            if (AssertUtils.isNotEmpty(attachmentPo.getFileSuffix())) {
                if (FileContentTypesConstant.fileContentTypeMap.containsKey(attachmentPo.getFileSuffix().toLowerCase())) {
                    contentType = FileContentTypesConstant.fileContentTypeMap.get(attachmentPo.getFileSuffix().toLowerCase());
                }
            }
            response.setContentType(contentType);
            response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(ATTACHMENT_DOWNLOAD_ERROR);
        }
    }


    @Override
    public ResultObject<List<AttachmentResponse>> attachmentList(Users currentLoginUsers, List<String> mediaIds) {
        ResultObject<List<AttachmentResponse>> resultObject = ResultObject.success();
        if (!AssertUtils.isNotEmpty(mediaIds)) {
            return resultObject;
        }
        List<AttachmentPo> attachmentPoList = attachmentExtDao.queryAttachment(mediaIds);
        if (!AssertUtils.isNotEmpty(attachmentPoList)) {
            return resultObject;
        }
        List<AttachmentResponse> attachmentResponseList = new ArrayList<>();
        attachmentPoList.forEach(attachmentPo -> {
            AttachmentResponse attachmentResponse = new AttachmentResponse();
            attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
            attachmentResponse.setFileSuffix(attachmentPo.getFileSuffix());
            attachmentResponse.setFileName(attachmentPo.getFileName());
            attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.") + attachmentPo.getUrl());
            attachmentResponse.setPdfTransformImageUrl(JSON.parseArray(attachmentPo.getPdfTransformImageUrl(), String.class));
            attachmentResponseList.add(attachmentResponse);
        });
        resultObject.setData(attachmentResponseList);
        return resultObject;
    }

    @Override
    public ResultObject<List<CoiBatchResponse>> attachmentCoiList(Users currentLoginUsers, String batchId) {
        ResultObject<List<CoiBatchResponse>> resultObject = ResultObject.success();
        if (!AssertUtils.isNotEmpty(batchId)) {
            return resultObject;
        }
        List<CoiBatchPo> coiBatchPos = coiBatchExtDao.queryAttachment(batchId);
        if (!AssertUtils.isNotEmpty(coiBatchPos)) {
            return resultObject;
        }
        List<String> mediaIds = coiBatchPos.stream().map(CoiBatchPo::getAttachmentId).distinct().collect(Collectors.toList());

        List<AttachmentPo> attachmentPoList = attachmentExtDao.queryAttachment(mediaIds);
        List<CoiBatchResponse> attachmentResponseList = new ArrayList<>();
        coiBatchPos.forEach(attachmentPo -> {
            CoiBatchResponse attachmentResponse = new CoiBatchResponse();
            attachmentResponse.setAttachmentId(attachmentPo.getAttachmentId());
            attachmentResponse.setAttachmentSeq(attachmentPo.getAttachmentSeq());
            attachmentResponse.setBatchid(attachmentPo.getBatchid());
            attachmentResponse.setPdfType(attachmentPo.getPdfType());

            if (AssertUtils.isNotEmpty(attachmentPoList)) {
               attachmentPoList.stream().filter(
                       attachmentPo1 -> attachmentPo.getAttachmentId().equals(attachmentPo1.getAttachmentId())).findFirst()
                       .ifPresent(attachmentPo1 -> {
                           attachmentResponse.setUrl(getConfigValue(attachmentPo1.getAttachmentDomain(), "oss_config.domain.") + attachmentPo1.getUrl());
               });
            }
            attachmentResponseList.add(attachmentResponse);
        });

        resultObject.setData(attachmentResponseList);
        return resultObject;
    }

    @Override
    public ResultObject electronicPolicyDisplay(HttpServletResponse response, String... attachmentId) throws Exception {
        ResultObject resultObject = new ResultObject();

        ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = electronicPolicyDownload(response, null, attachmentId);
        AssertUtils.isResultObjectDataNull(this.getLogger(), attachmentByteResponseResultObject);
        byte[] fileByte = attachmentByteResponseResultObject.getData().getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode("attachment.pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(fileByte);
        outputStream.close();
        return resultObject;
    }

    @Override
    public ResultObject<AttachmentByteResponse> mergePdfSave(HttpServletResponse response, String... attachmentId) {
        ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = electronicPolicyDownload(response, null, attachmentId);
        //保存
        AttachmentByteResponse data = attachmentByteResponseResultObject.getData();
        byte[] fileByte = data.getFileByte();
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(fileByte));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        AttachmentResponse attachmentResponse = uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
        data.setMediaId(attachmentResponse.getMediaId());
        // 上传到阿里云后 将保单字节数组置空
        data.setFileByte(null);
        //设置返回值
        data.setUrl(getConfigValue(attachmentResponse.getAttachmentDomain(), "oss_config.domain.") + attachmentResponse.getUrl());
        attachmentByteResponseResultObject.setData(data);
        return attachmentByteResponseResultObject;
    }

    @Override
    public ResultObject<AttachmentByteResponse> mergePdfSaveCoi(HttpServletResponse response, String... attachmentId) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<>();
        AttachmentByteResponse attachmentByteResponse = new AttachmentByteResponse();
        byte[] bytes = null;
        //ByteArrayOutputStream mergeOut = null;
        // 创建一个新的 PDF 文档
        try {
            /*Document document = new Document(PageSize.A4);
            // 创建一个 PDF 写入器
            PdfCopy copy = null;
            mergeOut = new ByteArrayOutputStream();
            copy = new PdfCopy(document, mergeOut);

            // 打开文档
            document.open();
            for (String id : attachmentId) {
                //先从阿里云上读取附件数据
                byte[] encrypedData = loadOssObjectByAttachmentId(id);
                //附件集合进行　合并
                PrintCommon.addPdfFileCoi(copy, encrypedData);
            }
            // 关闭文档
            document.close();
            mergeOut.close();*/
            // 创建一个新的 PDF 文档
            Document document = new Document(PageSize.A4);
            Path tempFile = Files.createTempFile("pdf", "pdf");
            File file = tempFile.toFile();
            FileOutputStream mergeOut = new FileOutputStream(file);
            //ByteArrayOutputStream mergeOut = new ByteArrayOutputStream();
            // 创建一个 PDF 写入器
            PdfCopy copy = new PdfCopy(document, mergeOut);
            // 打开文档
            document.open();
            for (String id : attachmentId) {
                //先从阿里云上读取附件数据
                byte[] encrypedData = loadOssObjectByAttachmentId(id);
                //附件集合进行　合并
                PrintCommon.addPdfFileCoi(copy, encrypedData);
            }
            //附件集合进行　合并
            attachmentByteResponse.setFileByte(Files.readAllBytes(tempFile));
            //attachmentByteResponse.setFileByte(mergeOut.toByteArray());
            // 关闭文档
            document.close();
            //resultObject.setData(attachmentByteResponse);
            file.deleteOnExit();
            Files.deleteIfExists(tempFile);
            mergeOut.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        //ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = electronicPolicyDownloadCoi(response, null, attachmentId);
        //保存
        this.getLogger().info("执行完毕1111...");
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        this.getLogger().info("读取字节数据开始========================" + attachmentByteResponse.getFileByte().length);
        attachmentRequest.setFileContent(new BASE64Encoder().encode(attachmentByteResponse.getFileByte()));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        AttachmentResponse attachmentResponse = uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
        this.getLogger().info("上传oss数据结束========================" + JSON.toJSON(attachmentResponse));
        attachmentByteResponse.setMediaId(attachmentResponse.getMediaId());
        // 上传到阿里云后 将保单字节数组置空
        attachmentByteResponse.setFileByte(null);
        //设置返回值
        attachmentByteResponse.setUrl(getConfigValue(attachmentResponse.getAttachmentDomain(), "oss_config.domain.") + attachmentResponse.getUrl());
        resultObject.setData(attachmentByteResponse);
        this.getLogger().info("执行完毕2222..." + JSON.toJSON(attachmentByteResponse));
        return resultObject;
    }

    @Override
    public ResultObject<String> pdfTransformImage(String attachmentId) {
        Message message = MessageBuilder.withBody(attachmentId.getBytes())
                .setHeader("processingType", PDF2IMAGE.name())
                .build();
        rabbitTemplate.convertAndSend(TOPIC_EXCHANGE, PREVENT_OOM_ROUTING_KEY, message);
        return ResultObject.success();
    }

}
