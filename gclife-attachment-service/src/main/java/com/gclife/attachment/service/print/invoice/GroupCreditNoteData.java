package com.gclife.attachment.service.print.invoice;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.api.PaymentConversionRateApi;
import com.gclife.payment.model.response.ConversionRateResponse;
import com.gclife.payment.model.response.CreditNoteApplicantResponse;
import com.gclife.payment.model.response.CreditNoteDataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.gclife.common.TerminologyConfigEnum.CURRENCY.KHR;
import static com.gclife.common.TerminologyConfigEnum.CURRENCY.USD;

@Slf4j
@Component
public class GroupCreditNoteData {

    @Autowired
    private PaymentConversionRateApi paymentConversionRateApi;
    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        CreditNoteDataResponse creditNoteDataResponse = JSON.parseObject(content, CreditNoteDataResponse.class);
        log.info("数据:" + JSON.toJSONString(creditNoteDataResponse));
        CreditNoteApplicantResponse applicant = creditNoteDataResponse.getApplicant();
        map.put("customerName", PrintCommon.getPrintString(applicant.getCustomerName(), 3));
        map.put("policyNo", PrintCommon.getPrintString(creditNoteDataResponse.getPolicyNo(), 3));
        map.put("creditNoteNo", PrintCommon.getPrintString(creditNoteDataResponse.getCreditNoteNo(), 3));
        map.put("taxRegistrationNo", PrintCommon.getPrintString(creditNoteDataResponse.getTaxRegistrationNo(), 3));
        Long currentTime = DateUtils.getCurrentTime();
        String[] k = new Date(currentTime).toString().split(" ");
        String printDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        map.put("printDate", PrintCommon.getPrintString(printDate, 3));
        map.put("customerMobile", PrintCommon.getPrintString(applicant.getCustomerMobile(), 3));
        map.put("customerEmail", PrintCommon.getPrintString(applicant.getCustomerEmail(), 3));
        map.put("customerAddress", PrintCommon.getPrintString(applicant.getCustomerAddress(), 3));
        map.put("totalPremium", PrintCommon.getPrintString(creditNoteDataResponse.getSpecialDiscount(), 3));
        ResultObject<ConversionRateResponse> conversionRateResponseResultObject = paymentConversionRateApi.queryUseOneConversionRate(USD.name(), KHR.name());
        AssertUtils.isResultObjectError(log, conversionRateResponseResultObject);
        ConversionRateResponse conversionRateResponse = conversionRateResponseResultObject.getData();
        BigDecimal rate = conversionRateResponse.getRate();
        map.put("rate",PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(rate), 3));
        map.put("allTotalPremiumKM_KH", PrintCommon.getPrintString(creditNoteDataResponse.getSpecialDiscount().multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP), 3));
        // CFO签名 + 公司印章
        map.put("signSealCFOPicture", PrintCommon.SIGN_SEAL_CFO);
        log.info("map:" + map);
        return map;
    }
}
