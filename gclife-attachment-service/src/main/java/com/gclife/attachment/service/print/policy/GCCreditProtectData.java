package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.ApplyPlanLoanBo;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.common.PrintCommon.decimalFormat1;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.common.InternationalTypeEnum.BANK;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * create 2023/4/4
 * description:
 */

@Component
public class GCCreditProtectData {

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    /**
     * 28号信贷保
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            map.put("backTrackDateYearZH_CN", PrintCommon.getPrintString("年 ", 3));
            map.put("backTrackDateMonthZH_CN", PrintCommon.getPrintString("月 ", 3));
            map.put("backTrackDateDayZH_CN", PrintCommon.getPrintString("日 ", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /*********************************************贷款信息***************************************************/
        ApplyPlanLoanBo planLoanContract = planPrintBo.getPlanLoanContract();
        map.put("loanContractNo", PrintCommon.getPrintString(planLoanContract.getLoanContractNo(), 3));
        map.put("notConvertedLoanAmount", PrintCommon.getPrintString(new BigDecimal(planLoanContract.getLoanAmount()), 3));
        map.put("currencyName", PrintCommon.getPrintString(planLoanContract.getCurrencyName(), 3));
        map.put("currency", PrintCommon.getPrintString(planLoanContract.getCurrency(), 3));
        String exchangeRate = null;
        if (AssertUtils.isNotNull(planLoanContract.getExchangeRate())) {
            exchangeRate = decimalFormat1.format(planLoanContract.getExchangeRate());
        }
        map.put("exchangeRate", PrintCommon.getPrintString(exchangeRate, 3));
        map.put("loanTerm", PrintCommon.getPrintString(planLoanContract.getLoanTerm(), 3));
        map.put("loanTermName", PrintCommon.getPrintString(planLoanContract.getLoanTermName(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal mainTotalAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = null;
        String mainCoveragePeriod = null;
        SyscodeResponse payment_way = null;
        if (AssertUtils.isNotEmpty(listCoverage)) {
            for (ApplyCoveragePlanBo coverageBo : listCoverage) {
                Map<String, Object> coverageMap = new HashMap<>();
                map.put(coverageBo.getProductId(), coverageBo.getProductId());
                coverageMap.put("productId", coverageBo.getProductId());
                PrintCommon.setProductName(coverageMap, coverageBo.getProductId());
                coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
                coverageMap.put("productLevel", coverageBo.getProductLevel());
                if (KM_KH.name().equals(language)) {
                    payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", planLoanContract.getPaymentWay(), KM_KH.name()).getData();
                }else if (EN_US.name().equals(language)){
                    payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", planLoanContract.getPaymentWay(), EN_US.name()).getData();
                }else if (ZH_CN.name().equals(language)){
                    payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", planLoanContract.getPaymentWay(), ZH_CN.name()).getData();
                }else {
                    payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", planLoanContract.getPaymentWay(), EN_US.name()).getData();
                }
                planLoanContract.setPaymentWayName(payment_way.getCodeName());
                coverageMap.put("paymentWayName", PrintCommon.getPrintString(planLoanContract.getPaymentWayName(), 3));
                coverageMap.put("loanInterestRate", PrintCommon.getPrintString(planLoanContract.getLoanInterestRate(), 3));
                if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                    totalAmount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                    mainTotalAmount = totalAmount;
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                coverageMap.put("productLevelName", PrintCommon.getPrintString(coverageBo.getProductLevelName(), 2));
                //保险期间
                String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
                String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
                String insurancePeriod = coveragePeriod + " " + coveragePeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                    insurancePeriod = coveragePeriodUnitName + " " + coveragePeriod;
                }
                coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
                mainCoveragePeriod = coveragePeriod;
                //交费期限
                String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
                String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
                String premiumPeriodAndUnitName = premiumPeriod + " " + premiumPeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                    premiumPeriodAndUnitName = premiumPeriodUnitName + " " + premiumPeriod;
                }
                //交费类型
                String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
                if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "一次性全额缴清";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "Single Payment";
                    }
                    premiumPeriodAndUnitName = premiumFrequencyName;
                }
                coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
                coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
                //保费
                coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
                coverageListMap.add(coverageMap);
            }
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /****************************************************************************保险利益***********************************************************************************/
        ApplyCoveragePlanBo applyCpb = listCoverage.stream().filter(applyCoveragePlanBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePlanBo.getPrimaryFlag())).findFirst().get();
        BigDecimal tpd_benefit_due_to_accident = new BigDecimal(applyCpb.getAmount());
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 2));
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 2));

        // 主险 死亡或高度残疾保险金
        BigDecimal death_benefit_due_to_accident = new BigDecimal(applyCpb.getAmount());
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintAmountString(death_benefit_due_to_accident, 2));

        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int mainCoveragePeriodi = Integer.parseInt(mainCoveragePeriod);
        BigDecimal accumulatedPremiumsPaid = BigDecimal.ZERO;
        for (int i = 1; i <= mainCoveragePeriodi; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            /*interestMap.put("interestMapMainTotalAmount", PrintCommon.getPrintAmountString(mainTotalAmount, 1));
            //意外高度残疾保险金
            interestMap.put("mainTotalAmountAddTotalAmount28", PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 1));
            // 意外死亡保险金
            interestMap.put("mainTotalAmountAddTotalAmount28Multiply3", PrintCommon.getPrintAmountString(death_benefit_due_to_accident, 1));*/
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, BigDecimal.ZERO);
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            accumulatedPremiumsPaid = policyYearTotalPremium;// policyYearTotalPremium 就是累计已交保费
            // 累计已交保费
            interestMap.put("accumulatedPremiumsPaid", PrintCommon.getPrintString("$", accumulatedPremiumsPaid, 1));

            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO880000000000028".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                ProductCashValueBo productCashValueBo = first.get();
                BigDecimal mainCashValue = ProductCalculation.getCashValue(listCashValue, "PRO880000000000028", i);
                interestMap.put("mainCashValue", PrintCommon.getPrintAmountString("$", mainCashValue, 1));
                interestMap.put("interestMapMainTotalAmount", PrintCommon.getPrintAmountString(productCashValueBo.getAmount(), 1));
                //意外高度残疾保险金
                interestMap.put("mainTotalAmountAddTotalAmount28", PrintCommon.getPrintAmountString(productCashValueBo.getAmount(), 1));
                // 意外死亡保险金
                interestMap.put("mainTotalAmountAddTotalAmount28Multiply3", PrintCommon.getPrintAmountString(productCashValueBo.getAmount(), 1));
            }
            interestListMap.add(interestMap);
        }

        map.put("interestListMap", interestListMap);
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 28号信贷保
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        SyscodeResponse payment_way = null;
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        PolicyLoanBo loanContract = policyBo.getLoanContract();
        if (KM_KH.name().equals(language)) {
            payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", loanContract.getPaymentWay(), KM_KH.name()).getData();
        }else if (EN_US.name().equals(language)){
            payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", loanContract.getPaymentWay(), EN_US.name()).getData();
        }else if (ZH_CN.name().equals(language)){
            payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", loanContract.getPaymentWay(), ZH_CN.name()).getData();
        }else {
            payment_way = platformInternationalBaseApi.queryOneInternational("PAYMENT_WAY", loanContract.getPaymentWay(), EN_US.name()).getData();
        }
        loanContract.setPaymentWayName(payment_way.getCodeName());
        map.put("paymentWayName", PrintCommon.getPrintString(loanContract.getPaymentWayName(), 3));
        String mainProductId = electronicPolicyGeneratorRequest.getProductId();
        PrintCommon.setProductName(map, mainProductId, "Main", null, null);
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = null;
        if (AssertUtils.isNotNull(policyApplicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(riskCommencementDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(policyInsuredBo.getBirthday(), 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(riskCommencementDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        BigDecimal totalAmount = null;
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        Long contractStartDate = policyBo.getApproveDate();
        Long contractEndDate = policyBo.getMaturityDate();
        PrintCommon.setPrintDateTime(map, "contractStartDate", contractStartDate, 3);
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            PrintCommon.setPrintDateTime(coverageMap, "contractEndDate", contractEndDate, 3);
            //保险期限
            String coveragePeriodUnitName = null;
            if (AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName())) {
                coveragePeriodUnitName = coverageBo.getCoveragePeriod() + " " + coverageBo.getCoveragePeriodUnitName();
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                    coveragePeriodUnitName = coverageBo.getCoveragePeriodUnitName() + " " + coverageBo.getCoveragePeriod();
                }
            }
            map.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
            //交费期限
            totalAmount = new BigDecimal(coverageBo.getTotalAmount());
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 3));
            //额外加费
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));
            PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
            if (!AssertUtils.isNotNull(policyPremium)) {
                //首期保费合计
                policyPremium = new PolicyPremiumBo();
            }
            map.put("firstTotalPremium", PrintCommon.getPrintString(policyPremium.getActualPremium(), 3));
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String ppun = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                ppun = premiumPeriodUnitName + premiumPeriod;
            }
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "Single Payment";
                }
                ppun = premiumFrequencyName;
            }
            map.put("ppun", PrintCommon.getPrintString(ppun, 3));
            map.put("productLevelName", PrintCommon.getPrintString(coverageBo.getProductLevelName(), 3));
            map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
            PolicyPaymentBo policyPayment = new PolicyPaymentBo();
            if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
                policyPayment = policyPremium.getPolicyPayment();
            }
            Long gainedDate = policyPayment.getGainedDate();
            PrintCommon.setPrintDateTime(map, "gainedDate", gainedDate, 3);
            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        /***********************************现金价值******************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            BigDecimal finalTotalAmount = totalAmount;
            for (int i = 1; i < policyCashValues.size(); i++) {
                Map<String, Object> productCashValue = new HashMap<>();
                ProductCashValueBo productCashValueBo = policyCashValues.get(i);
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintAmountString(productCashValueBo.getAmount(), 1));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintAmountString(productCashValueBo.getCashValue(), 1));
                policyCashValuesListMap.add(productCashValue);
            }
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }
}
