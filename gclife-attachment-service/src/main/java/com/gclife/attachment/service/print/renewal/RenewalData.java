package com.gclife.attachment.service.print.renewal;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.renewal.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@Component
public class RenewalData {


    /**
     * 续期打印数据
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public List<PrintObject> getRenewalData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        List<PrintObject> printObjectList = new ArrayList<>();
        ReceivableBo receivableBo = JSON.parseObject(content, ReceivableBo.class);
        /*****************************************保单信息****************************************************/
        //保单号码
        PrintCommon.setPrintData(printObjectList, "policyNo", receivableBo.getPolicyNo(), 3);
        //应缴保费  本期应缴保费
        PrintCommon.setPrintData(printObjectList, "receivablePremium", receivableBo.getActualPremium(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "renewalDate", receivableBo.getRenewalDate(), 3);

        /*****************************************投保人信息****************************************************/
        PolicyApplicantBo applicant = receivableBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new PolicyApplicantBo();
        }
        String applicantName = applicant.getName();
        PrintCommon.setPrintData(printObjectList, "applicantName", applicantName, 3);

        /*****************************************被保人信息信息****************************************************/
        List<PolicyInsuredBo> insuredList = receivableBo.getInsuredList();
        PolicyInsuredBo policyInsured = null;
        if (AssertUtils.isNotEmpty(insuredList)) {
            policyInsured = insuredList.get(0);
        } else {
            policyInsured = new PolicyInsuredBo();
        }
        String insuredName = policyInsured.getName();
        PrintCommon.setPrintData(printObjectList, "insuredName", insuredName, 3);
        Long birthday = policyInsured.getBirthday();
        /*****************************************险种信息****************************************************/
        List<RenewalCoveragePremiumBo> listCoverage = receivableBo.getRenewalCoveragePremiumBoList();
        if(AssertUtils.isNotEmpty(listCoverage)){
            if (AssertUtils.isNotEmpty(listCoverage)) {
                listCoverage.sort(((applyCoverageBo, applyCoverageBo2) -> {
                    if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                        return -1;
                    } else {
                        return 1;
                    }
                }));
            }
        }
        for (int i = 0; i < 4; i++) {
            RenewalCoveragePremiumBo policyCoverageBo = null;
            if (AssertUtils.isNotEmpty(listCoverage) && listCoverage.size() > i) {
                policyCoverageBo = listCoverage.get(i);
            } else {
                policyCoverageBo = new RenewalCoveragePremiumBo();
            }
            //产品名称
            PrintCommon.setProductName(printObjectList, policyCoverageBo.getProductId(), i + "",policyCoverageBo.getProductLevel());
            //保障期限
            String coveragePeriodUnitName = null;
            if (AssertUtils.isNotEmpty(policyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(policyCoverageBo.getCoveragePeriodUnitName())) {
                coveragePeriodUnitName = policyCoverageBo.getCoveragePeriod() + " " + policyCoverageBo.getCoveragePeriodUnitName();
            }
            PrintCommon.setPrintData(printObjectList, "coveragePeriodKM_KH" + i, coveragePeriodUnitName, 2);
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(policyCoverageBo.getCoveragePeriodUnit())
                    && AssertUtils.isNotEmpty(coveragePeriodUnitName)) {
                PrintCommon.setPrintData(printObjectList, "coveragePeriodKM_KH" + i, policyCoverageBo.getCoveragePeriodUnitName() + " " + policyCoverageBo.getCoveragePeriod(), 2);
            }
            PrintCommon.setPrintData(printObjectList, "coveragePeriodZH_CN" + i, coveragePeriodUnitName, 2);
            PrintCommon.setPrintData(printObjectList, "coveragePeriodEN_US" + i, coveragePeriodUnitName, 2);
            //缴费期限
            String premiumPeriodName = null;
            if (AssertUtils.isNotEmpty(policyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(policyCoverageBo.getPremiumPeriodUnitName())) {
                premiumPeriodName = policyCoverageBo.getPremiumPeriod() + " " + policyCoverageBo.getPremiumPeriodUnitName();
            }
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoverageBo.getPremiumPeriodUnit())) {
                premiumPeriodName = policyCoverageBo.getPremiumPeriodUnitName();
            }
            PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, premiumPeriodName, 2);
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(policyCoverageBo.getPremiumPeriod())
                    && AssertUtils.isNotEmpty(coveragePeriodUnitName)) {
                PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, policyCoverageBo.getPremiumPeriodUnitName() + " " + policyCoverageBo.getPremiumPeriod(), 2);
            }
            PrintCommon.setPrintData(printObjectList, "premiumPeriodZH_CN" + i, premiumPeriodName, 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodEN_US" + i, premiumPeriodName, 2);            //缴费周期
            PrintCommon.setPrintData(printObjectList, "premiumFrequencyName"+i,policyCoverageBo.getPremiumFrequencyName(), 2);
            //保险金额 每期
            BigDecimal totalPremium = policyCoverageBo.getTotalPremium();
            PrintCommon.setPrintData(printObjectList, "totalPremium" + i, totalPremium, 2);

        }
        /*****************************************代理人信息****************************************************/
        AgentBo agentBo = receivableBo.getAgent();
        if(!AssertUtils.isNotNull(agentBo)){
            agentBo = new AgentBo();
        }
        PrintCommon.setPrintData(printObjectList, "agentCode", agentBo.getAgentCode(), 3);
        PrintCommon.setPrintData(printObjectList, "agentName", agentBo.getAgentName(), 3);
        AgentDetailBo agentDetail = agentBo.getAgentDetail();
        if(!AssertUtils.isNotNull(agentDetail)){
            agentDetail = new AgentDetailBo();
        }
        PrintCommon.setPrintData(printObjectList, "agentMobile", agentDetail.getMobile(), 3);


        //打印时间
        PrintCommon.setPrintDateTime(printObjectList, "printDate", System.currentTimeMillis(), 3);

        System.out.println("打印缴费通知单数据+printObjectList:::::" + JSON.toJSONString(printObjectList));

        return printObjectList;
    }

}
