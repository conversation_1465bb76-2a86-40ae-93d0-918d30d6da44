package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.BENEFICIARY_NO.ORDER_ONE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * create 2018/8/28
 * description:
 */

@Component
public class GoodFutureData {


    /**
     * 8号产品
     * 共计保健金 = （保险金额*%10）*多少次能领取
     * 满期保健金 = 保险金额
     * 意外高残 = 保险金额 * 3
     * 意外死亡 = 保险金额 * 3
     * 疾病高残 = 保险金额
     * 疾病死亡 = 保险金额
     * <p>
     * <p>
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            map.put("backTrackDateYearZH_CN", PrintCommon.getPrintString("年 ", 3));
            map.put("backTrackDateMonthZH_CN", PrintCommon.getPrintString("月 ", 3));
            map.put("backTrackDateDayZH_CN", PrintCommon.getPrintString("日 ", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount8 = new BigDecimal(0);
        BigDecimal totalAmount4 = new BigDecimal(0);
        String productLevel4 = null;
        String premiumPeriod8 = null;
        String premiumFrequency8 = null;
        String coveragePeriod8 = null;
        BigDecimal totalAmount7 = new BigDecimal(0);
        BigDecimal totalPremium7 = new BigDecimal(0);
        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            if ("PRO88000000000008".equals(coverageBo.getProductId())) {
                totalAmount8 = totalAmount;
                premiumPeriod8 = premiumPeriod;
                coveragePeriod8 = coveragePeriod;
                premiumFrequency8 = coverageBo.getPremiumFrequency();
            } else if ("PRO88000000000004".equals(coverageBo.getProductId())) {
                totalAmount4 = totalAmount;
                productLevel4 = productLevel;
                if ("AMOUNT_UP".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(递增型)");
                    coverageMap.put("productLevelKM_KH", "(ប្រភេទប្រែប្រួល)");
                    coverageMap.put("productLevelEN_US", "(Accelerated)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(固定型)");
                    coverageMap.put("productLevelKM_KH", "(ប្រភេទថេរ)");
                    coverageMap.put("productLevelEN_US", "(Fixed)");
                }
            } else if ("PRO88000000000007".equals(coverageBo.getProductId())) {
                ProductCalculation.calculation7(map, coverageBo.getProductLevel(), coverageBo.getMult());
                totalAmount7 = new BigDecimal((map.get("totalAmount7") + "").replace(",", ""));
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount7, 2));
                totalPremium7 = coverageBo.getTotalPremium();
                String multLevel = "(" + coverageBo.getMult() + coverageBo.getProductLevel() + ")";
                coverageMap.put("productLevelZH_CN", multLevel);
                coverageMap.put("productLevelKM_KH", multLevel);
                coverageMap.put("productLevelEN_US", multLevel);
            }
            //每期保费
            coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /****************************************************************************可选其他缴费周期***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<Map<String, Object>> coveragePremiumFrequencyListMap = new ArrayList<>();
        for (String productId : coveragePremiumFrequencyMap.keySet()) {
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            Map<String, Object> cpfMap = new HashMap<>();
            AtomicReference<String> productLevel = new AtomicReference<>();
            listCoverage.stream().filter(applyCoveragePlanBo -> applyCoveragePlanBo.getProductId().equals(productId)).findFirst().ifPresent(applyCoveragePlanBo -> {
                productLevel.set(applyCoveragePlanBo.getProductLevel());
            });
            PrintCommon.setProductName(cpfMap, productId, productLevel.get(), language);
            Integer column = 1;
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, YEAR.name(), premiumFrequency8);
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEMIANNUAL.name(), premiumFrequency8);
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEASON.name(), premiumFrequency8);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, MONTH.name(), premiumFrequency8);
            coveragePremiumFrequencyListMap.add(cpfMap);
        }
        PrintCommon.coverageSort(coveragePremiumFrequencyListMap);
        map.put("coveragePremiumFrequencyListMap", coveragePremiumFrequencyListMap);

        /****************************************************************************保险利益***********************************************************************************/
        map.put("totalAmount8", PrintCommon.getPrintString(totalAmount8, 2));
        BigDecimal totalAmount8multiply3 = totalAmount8.multiply(new BigDecimal(3));
        map.put("totalAmount8multiply3", PrintCommon.getPrintString(totalAmount8multiply3, 2));
        map.put("totalAmount4", PrintCommon.getPrintString(totalAmount4, 2));
        map.put("productLevel4", PrintCommon.getPrintString(productLevel4, 2));
        if ("AMOUNT_UP".equals(productLevel4)) {
            BigDecimal amountUpAmount = totalAmount4.multiply(new BigDecimal(0.03));
            BigDecimal totalAmount4AmountUpSum = totalAmount4.add(new BigDecimal(new Integer(coveragePeriod8) - 2).multiply(amountUpAmount));
            map.put("totalAmount4AmountUpSum", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount4AmountUpSum), 2));
        }
        map.put("totalAmount7_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount7), 2));
        /****************************************************************************利益显示***********************************************************************************/
        Integer premiumPeriodYearSum = Integer.valueOf(premiumPeriod8);
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        Object payAmount7 = map.get("payAmount7");
        BigDecimal healthCareFund = totalAmount8.multiply(new BigDecimal(0.1));
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        BigDecimal totalAmountAmountUp4 = totalAmount4.multiply(new BigDecimal(0.03));
        int hcfCount = 0;
        int coveragePeriod8Count = Integer.parseInt(coveragePeriod8);
        BigDecimal policyYearTotalPremium = new BigDecimal(0);
        for (int i = 1; i <= 21; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            /****************************************************************************保险费和现金价值*/
            if (i <= coveragePeriod8Count) {
                //保单年度
                interestMap.put("year1", i);
                //年龄
                interestMap.put("insuredAge", insuredAgeYear + i);
                BigDecimal hcf = new BigDecimal(0.00);
                if (i >= 6 & i % 3 == 0 && i != coveragePeriod8Count) {
                    hcfCount += 1;
                    hcf = healthCareFund;
                }
                //保健金
                interestMap.put("hcf", PrintCommon.getPrintString("$", hcf, 1));
                BigDecimal expiredHcf = new BigDecimal(0.00);
                if (i > 2 && "AMOUNT_UP".equals(productLevel4)) {
                    totalAmount4 = totalAmount4.add(totalAmountAmountUp4);
                }
                if (coveragePeriod8Count == i) {
                    expiredHcf = totalAmount8;
                }
                //共计保健金
                interestMap.put("expiredHcf", PrintCommon.getPrintString("$", expiredHcf, 1));
                //保额3倍保额数
                interestMap.put("totalAmount8multiply3", PrintCommon.getPrintString(totalAmount8multiply3, 1));
                interestMap.put("totalAmount8", PrintCommon.getPrintString("$", totalAmount8, 1));
                //4好产品保额
                interestMap.put("totalAmount4", PrintCommon.getPrintString("$", (i == 1) ? new BigDecimal(0.00) : totalAmount4, 1));
                interestMap.put("payAmount7", PrintCommon.getPrintString("$", payAmount7, 1));
            }
            /****************************************************************************利益显示*/
            //保险费
            //保单年度
            if (i <= coveragePeriod8Count) {
                interestMap.put("year2", i);
                //总保费
                policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
                interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            }
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO88000000000008".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                interestMap.put("year3", i);
                BigDecimal cashValue8 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000008", i);
                BigDecimal cashValue4 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000004", i);
                BigDecimal cashValue7 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000007", i);
                interestMap.put("cashValue8", PrintCommon.getPrintString("$", cashValue8, 1));
                interestMap.put("cashValue4", PrintCommon.getPrintString("$", cashValue4, 1));
                interestMap.put("cashValue7", PrintCommon.getPrintString("$", cashValue7, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue8.add(cashValue4).add(cashValue7), 1));
            }
            interestListMap.add(interestMap);
        }
        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("healthCareFund", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(healthCareFund.multiply(new BigDecimal(hcfCount))), 1));

        //保险利益
        map.put("healthCareFund10_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(healthCareFund), 1));
        map.put("healthCareFund10", PrintCommon.getPrintString(healthCareFund, 1));
        map.put("totalAmount8_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount8), 2));
        map.put("totalAmount8multiply3_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount8multiply3), 2));
        map.put("totalAmount4_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount4), 2));
        map.put("totalAmount4_2", PrintCommon.getPrintString(totalAmount4, 2));

        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public List<PrintObject> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(printObjectList, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        PrintCommon.setPrintData(printObjectList, "policyNo", policyBo.getPolicyNo(), 3);
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", policyApplicant.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "applicantSexName", policyApplicant.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", policyApplicant.getIdNo(), 3);
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "applicantIdNoAndIdTypeName", applicantIdNoAndIdTypeName, 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "applicantMobile", policyApplicant.getMobile(), 3);
        Integer applicantAgeYear = null;
        if (AssertUtils.isNotNull(policyApplicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(riskCommencementDate));
        }
        PrintCommon.setPrintData(printObjectList, "applicantAgeYear", applicantAgeYear, 3);
        PrintCommon.setPrintData(printObjectList, "applicantFullAddress", policyApplicant.getFullAddress(), 3);
        /**********************************被保人信息**********************************/

        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", policyInsuredBo.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", policyInsuredBo.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "insuredIdNoAndIdTypeName", insuredIdNoAndIdTypeName, 3);
        //与投保人什么关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", policyInsuredBo.getRelationshipName(), 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "insuredMobile", policyInsuredBo.getMobile(), 3);
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(riskCommencementDate));
        }
        PrintCommon.setPrintData(printObjectList, "insuredAgeYear", insuredAgeYear, 3);
        PrintCommon.setPrintData(printObjectList, "insuredFullAddress", policyInsuredBo.getFullAddress(), 3);
        /**********************************受益人***************************************/
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        if (AssertUtils.isNotEmpty(listPolicyBeneficiary)) {
            listPolicyBeneficiary.removeIf(policyBeneficiaryInfoBo -> !ORDER_ONE.name().equals(policyBeneficiaryInfoBo.getBeneficiaryNoOrder()));
        }
        for (int i = 0; i < 3; i++) {
            PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = null;
            PolicyBeneficiaryBo policyBeneficiary = null;
            if (AssertUtils.isNotEmpty(listPolicyBeneficiary) && i < listPolicyBeneficiary.size() && AssertUtils.isNotNull(listPolicyBeneficiary.get(i))) {
                policyBeneficiaryInfoBo = listPolicyBeneficiary.get(i);
                if (AssertUtils.isNotNull(policyBeneficiaryInfoBo) && AssertUtils.isNotNull(policyBeneficiaryInfoBo.getPolicyBeneficiary())) {
                    policyBeneficiary = policyBeneficiaryInfoBo.getPolicyBeneficiary();
                }
            }
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            if (!AssertUtils.isNotNull(policyBeneficiaryInfoBo)) {
                policyBeneficiaryInfoBo = new PolicyBeneficiaryInfoBo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryName" + i, policyBeneficiary.getName(), 3);
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdNo" + i, beneficiaryIdNo, 3);
            PrintCommon.setPrintData(printObjectList, "beneficiaryId-No" + i, policyBeneficiary.getIdNo(), 3);
            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                beneficiaryProportion = policyBeneficiaryInfoBo.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryProportion" + i, beneficiaryProportion, 2);
        }
        /**********************************保险***************************************/
        //主险
        String mult = null;
        String mult7 = null;
        String mainMult = null;
        String productLevelMain = null;
        String premiumFrequencyName = null;
        String premiumPeriod = null;
        String premiumPeriodUnit = null;
        String premiumPeriodUnitName = null;
        BigDecimal totalPremiumMain = null;
        BigDecimal totalAmountMain = null;
        List<ProductCashValueBo> policyCashValuesMain = null;
        Long effectiveDate = null;
        Long maturityDate = null;
        //附加险 7号
        String productLevelAdditional = null;
        BigDecimal totalPremiumAdditional = null;
        Long effectiveDate7 = null;
        Long maturityDate7 = null;
        //附加险 4号
        BigDecimal totalPremium4 = null;
        BigDecimal totalAmount4 = null;
        String productLevel4 = null;
        String premiumPeriod4 = null;
        String premiumPeriodUnit4 = null;
        String premiumPeriodUnitName4 = null;
        String premiumFrequencyName4 = null;
        List<ProductCashValueBo> policyCashValues4 = null;
        //获取保险期限
        PolicyCoverageBo policyCoverageMainBo = new PolicyCoverageBo();
        if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyCoverage())) {
            List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
            Optional<PolicyCoverageBo> optionalCoverageMainBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst();
            if (optionalCoverageMainBo.isPresent()) {
                policyCoverageMainBo = optionalCoverageMainBo.get();
                productLevelMain = policyCoverageMainBo.getProductLevel();
                mult = policyCoverageMainBo.getMult();
                mainMult = policyCoverageMainBo.getMult();
                totalPremiumMain = policyCoverageMainBo.getTotalPremium();
                totalAmountMain = AssertUtils.isNotEmpty(policyCoverageMainBo.getTotalAmount()) ? new BigDecimal(policyCoverageMainBo.getTotalAmount()) : null;
                premiumPeriodUnit = policyCoverageMainBo.getPremiumPeriod() + policyCoverageMainBo.getPremiumPeriodUnitName();
                premiumPeriod = policyCoverageMainBo.getPremiumPeriod();
                premiumPeriodUnitName = policyCoverageMainBo.getPremiumPeriodUnitName();
                premiumFrequencyName = policyCoverageMainBo.getPremiumFrequencyName();
                String productId = policyCoverageMainBo.getProductId();
                effectiveDate = policyCoverageMainBo.getEffectiveDate();
                maturityDate = policyCoverageMainBo.getMaturityDate();
                PrintCommon.setProductName(printObjectList, productId, "Main");
                List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValuesMain = policyCashValues.stream().filter(productCashValueBo -> productId.equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
            }

            List<PolicyCoverageBo> policyCoverageAdditionalBoList = listPolicyCoverage.stream().filter(coveragePlanBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(coveragePlanBo.getPrimaryFlag())).collect(Collectors.toList());
            Optional<PolicyCoverageBo> policyCoverageBo7 = policyCoverageAdditionalBoList.stream().filter(policyCoverageBo -> "PRO88000000000007".equals(policyCoverageBo.getProductId())).findFirst();
            if (policyCoverageBo7.isPresent()) {
                PolicyCoverageBo applyCoverageAdditionalBo = policyCoverageBo7.get();
                productLevelAdditional = applyCoverageAdditionalBo.getProductLevel();
                totalPremiumAdditional = applyCoverageAdditionalBo.getTotalPremium();
                effectiveDate7 = applyCoverageAdditionalBo.getEffectiveDate();
                maturityDate7 = applyCoverageAdditionalBo.getMaturityDate();
                mult7 = applyCoverageAdditionalBo.getMult();
                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), applyCoverageAdditionalBo.getProductId() + "_V"));
            }
            Optional<PolicyCoverageBo> policyCoverageBo4 = policyCoverageAdditionalBoList.stream().filter(policyCoverageBo -> "PRO88000000000004".equals(policyCoverageBo.getProductId())).findFirst();
            if (policyCoverageBo4.isPresent()) {
                PolicyCoverageBo applyCoverageAdditionalBo = policyCoverageBo4.get();
                totalPremium4 = applyCoverageAdditionalBo.getTotalPremium();
                productLevel4 = applyCoverageAdditionalBo.getProductLevel();
                totalAmount4 = AssertUtils.isNotEmpty(applyCoverageAdditionalBo.getTotalAmount()) ? new BigDecimal(applyCoverageAdditionalBo.getTotalAmount()) : null;
                premiumPeriod4 = applyCoverageAdditionalBo.getPremiumPeriod();
                premiumPeriodUnit4 = applyCoverageAdditionalBo.getPremiumPeriodUnit();
                premiumPeriodUnitName4 = applyCoverageAdditionalBo.getPremiumPeriodUnitName();
                premiumFrequencyName4 = applyCoverageAdditionalBo.getPremiumFrequencyName();
                List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), applyCoverageAdditionalBo.getProductId() + "_21"));
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValues4 = policyCashValues.stream().filter(productCashValueBo -> applyCoverageAdditionalBo.getProductId().equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
            }
            List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
            List<PolicyCoverageBo> policyCoverageBo16List = policyCoverageAdditionalBoList.stream().filter(policyCoverageBo -> policyCoverageBo.getProductId().indexOf("PRO880000000000016") >= 0).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(policyCoverageBo16List)) {
                final BigDecimal[] totalPremium16 = {new BigDecimal(0)};
                policyCoverageBo16List.forEach(policyCoverageBo -> {
                    totalPremium16[0] = totalPremium16[0].add(policyCoverageBo.getTotalPremium());
                    String premiumMonthFrequency = null;
                    if (YEAR.name().equals(policyCoverageBo.getPremiumFrequency())) {
                        premiumMonthFrequency = "12";
                    } else if (SEMIANNUAL.name().equals(policyCoverageBo.getPremiumFrequency())) {
                        premiumMonthFrequency = "06";
                    } else if (SEASON.name().equals(policyCoverageBo.getPremiumFrequency())) {
                        premiumMonthFrequency = "03";
                    } else if (MONTH.name().equals(policyCoverageBo.getPremiumFrequency())) {
                        premiumMonthFrequency = "01";
                    }
                    long premiumCessationDate = policyCoverageBo.getCoveragePeriodStartDate();
                    String premiumFrequency = policyCoverageBo.getPremiumFrequency();
                    int premiumPeriodInteger = Integer.parseInt(policyCoverageBo.getPremiumPeriod());

                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                        premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 1);
                    } else if (SEASON.name().equals(premiumFrequency)) {
                        premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 4);
                    } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                        premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 6);
                    } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                        premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 12);
                    }
                    //额外加费
                    BigDecimal extraPremium = null;
                    BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(policyCoverageBo.getPremiumFrequency()).value());
                    if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                        List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                                .filter(policyAddPremiumBo -> policyCoverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                        AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                        EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                        if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                            double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                            extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                        }
                    }
                    PrintCommon.setPrintData(printObjectList, "extraPremium16" + policyCoverageBo.getProductLevel(), extraPremium, 3);
                    PrintCommon.setPrintDateTime(printObjectList, "premiumCessationDate", premiumCessationDate, 3);

                    PrintCommon.setPrintData(printObjectList, "totalPremium16" + policyCoverageBo.getProductLevel(), policyCoverageBo.getTotalPremium(), 3);
                    PrintCommon.setPrintData(printObjectList, "premiumMonthFrequency16", premiumMonthFrequency, 3);
                    PrintCommon.setPrintData(printObjectList, policyCoverageBo.getProductId() + "ProductLevel", policyCoverageBo.getProductLevel(), 3);
                    PrintCommon.setPrintDateTime(printObjectList, "coveragePeriodStartDate", policyCoverageBo.getCoveragePeriodStartDate(), 3);
                    PrintCommon.setPrintDateTime(printObjectList, "coveragePeriodEndDate", policyCoverageBo.getCoveragePeriodEndDate(), 3);
                });
                PrintCommon.setPrintData(printObjectList, "totalPremium16", totalPremium16[0], 3);
                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), "PRO880000000000016"));
            }
        }
        /******************主险*/
        PrintCommon.setPrintData(printObjectList, "premiumPeriod", premiumPeriod, 3);
        String premiumPeriodName = null;
        if (AssertUtils.isNotEmpty(premiumPeriod) && AssertUtils.isNotEmpty(premiumPeriodUnitName)) {
            premiumPeriodName = premiumPeriod + " " + premiumPeriodUnitName;
        }
        PrintCommon.setPrintData(printObjectList, "premiumPeriodName", premiumPeriodName, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom", effectiveDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil", maturityDate, 3);
        Integer insuranceDuration = null;
        if (AssertUtils.isNotNull(effectiveDate) && AssertUtils.isNotNull(maturityDate)) {
            insuranceDuration = Integer.valueOf(DateUtils.timeStrToString(maturityDate, DateUtils.FORMATE1))
                    - Integer.valueOf(DateUtils.timeStrToString(effectiveDate, DateUtils.FORMATE1));
        }
        PrintCommon.setPrintData(printObjectList, "insuranceDuration", insuranceDuration, 2);
        /// 主险保险利益
        if (AssertUtils.isNotEmpty(policyCoverageMainBo.getTotalAmount())) {
            BigDecimal totalAmount = new BigDecimal(policyCoverageMainBo.getTotalAmount());
            this.queryProductIndividuationData(printObjectList, totalAmount, policyCoverageMainBo.getEffectiveDate(), policyCoverageMainBo.getMaturityDate(), mainMult);
        }
        ProductCalculation.policyCashValues(printObjectList, policyCashValuesMain);
        PrintCommon.setPrintData(printObjectList, "mult", mult, 3);
        PrintCommon.setPrintData(printObjectList, "premiumPeriodUnit", premiumPeriodUnit, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequency", premiumFrequencyName, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", premiumFrequencyName, 3);
        PrintCommon.setPrintData(printObjectList, "totalPremiumMain", totalPremiumMain, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmountMain", totalAmountMain, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelMain", productLevelMain, 3);
        /******************附加险7号*/
        ProductCalculation.calculation7(printObjectList, productLevelAdditional, mult7);
        PrintCommon.setPrintData(printObjectList, "totalPremiumAdditional", totalPremiumAdditional, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelAdditional", productLevelAdditional, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom7", effectiveDate7, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil7", maturityDate7, 3);
        /******************附加险4号*/
        PrintCommon.setPrintData(printObjectList, "policyYear", insuranceDuration, 2);
        PrintCommon.setPrintData(printObjectList, "productLevel4", productLevel4, 2);
        String totalAmountAdditional4EN_US = PrintCommon.getPrintString(totalAmount4, 3);
        String totalAmountAdditional4ZH_CN = PrintCommon.getPrintString(totalAmount4, 3);
        String totalAmountAdditional4KM_KH = PrintCommon.getPrintString(totalAmount4, 3);
        if (AssertUtils.isNotNull(totalAmount4) && "AMOUNT_UP".equals(productLevel4)) {
            totalAmountAdditional4EN_US = "(Initial) " + totalAmount4;
            totalAmountAdditional4ZH_CN = "(初始) " + totalAmount4;
            totalAmountAdditional4KM_KH = "(ដើមគ្រា) " + totalAmount4;
        }
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4EN_US", totalAmountAdditional4EN_US, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4ZH_CN", totalAmountAdditional4ZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4KM_KH", totalAmountAdditional4KM_KH, 3);
        PrintCommon.setPrintData(printObjectList, new String[]{"productLevel4AMOUNT_UP", "productLevel4AMOUNT_FIXED"}, "productLevel4" + productLevel4);
        PrintCommon.setPrintData(printObjectList, "premiumPeriodUnit4", premiumPeriod4 == null ? null : premiumPeriod4 + " " + premiumPeriodUnitName4, 3);
        //柬文岁特殊处理
        if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(premiumPeriodUnit4)) {
            PrintCommon.setPrintData(printObjectList, "premiumPeriodUnit4", premiumPeriod4 == null ? null : premiumPeriodUnitName4 + " " + premiumPeriod4, 3);
        }
        PrintCommon.setPrintData(printObjectList, "totalPremium4", totalPremium4, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmount4", totalAmount4, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName4", premiumFrequencyName4, 3);
        PrintCommon.setPrintData(printObjectList, "premiumPeriod4", premiumPeriod4, 3);
        ProductCalculation.policyCashValues(printObjectList, policyCashValues4, "4");

        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        PrintCommon.setPrintData(printObjectList, "periodTotalPremium", policyPremium.getActualPremium(), 3);
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        PrintCommon.setPrintDateTime(printObjectList, "gainedDate", policyPayment.getGainedDate(), 3);
        PolicyCoveragePremiumBo policyCoveragePremium = policyCoverageMainBo.getPolicyCoveragePremium();
        if (!AssertUtils.isNotNull(policyCoveragePremium)) {
            policyCoveragePremium = new PolicyCoveragePremiumBo();
        }
        //缴费年期
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", policyCoveragePremium.getPremiumFrequencyName(), 3);
        String premiumFrequency = policyCoveragePremium.getPremiumFrequency();
        PrintCommon.setPremiumFrequencyName(premiumFrequency, printObjectList);
        /****************************************************************************获取保险期限***********************************************************************************/


        /************************************************支付***********************************************************/
        PrintCommon.setPrintData(printObjectList, "periodTotalPremium", policyPremium.getActualPremium(), 3);
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        //支付方式
        PrintCommon.setPrintData(printObjectList, "payModeCodeName", policyPayment.getPayModeCodeName(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "gainedDate", policyPayment.getGainedDate(), 3);


        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        PrintCommon.setPrintData(printObjectList, "agentCode", policyAgent.getAgentCode(), 3);
        PrintCommon.setPrintData(printObjectList, "agentName", policyAgent.getAgentName(), 3);
        //签发日期
        PrintCommon.setPrintDateTime(printObjectList, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "approveDate", policyBo.getApproveDate(), 3);
        /***********************************现金价值******************************************/

        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getPolicySpecialContractContent(printObjectList, specialContractContent);
        // 公司基础信息
        printObjectList.addAll(CompanyInfo.getCompanyBaseInfoList(language));
        return printObjectList;
    }


    /**
     * 获取8号产品 保障金
     *
     * @param printObjectList
     * @param amount
     * @param effectiveDate
     * @param maturityDate
     * @throws Exception
     */
    public void queryProductIndividuationData(List<PrintObject> printObjectList, BigDecimal amount, Long effectiveDate, Long maturityDate, String mainMult) throws Exception {
        /// 保健金

        BigDecimal bigDecimalAmount = amount.multiply(new BigDecimal(0.1)).setScale(2, BigDecimal.ROUND_HALF_UP);
        PrintCommon.setPrintData(printObjectList, "insuranceRenteAmount", bigDecimalAmount, 3);
        PrintCommon.setPrintData(printObjectList, "insuranceRenteAmountLast", amount, 3);
        // 保健金时间
        Long insuranceRenteOneDate = DateUtils.addStringYearsRT(effectiveDate, 6);
        Long insuranceRenteTwoDate = DateUtils.addStringYearsRT(effectiveDate, 9);
        Long insuranceRenteThreeDate = DateUtils.addStringYearsRT(effectiveDate, 12);
        Long insuranceRenteFourDate = DateUtils.addStringYearsRT(effectiveDate, 15);
        Long insuranceRenteFiveDate = DateUtils.addStringYearsRT(effectiveDate, 18);
        Long insuranceRenteLastDate = maturityDate;
        //不显示的日期　
        if (DateUtils.timeToTimeLow(insuranceRenteTwoDate) == DateUtils.timeToTimeLow(maturityDate)) {
            insuranceRenteTwoDate = null;
            insuranceRenteThreeDate = null;
            insuranceRenteFourDate = null;
            insuranceRenteFiveDate = null;
        } else if (DateUtils.timeToTimeLow(insuranceRenteThreeDate) == DateUtils.timeToTimeLow(maturityDate)) {
            insuranceRenteThreeDate = null;
            insuranceRenteFourDate = null;
            insuranceRenteFiveDate = null;
        } else if (DateUtils.timeToTimeLow(insuranceRenteFourDate) == DateUtils.timeToTimeLow(maturityDate)) {
            insuranceRenteFourDate = null;
            insuranceRenteFiveDate = null;
        } else if (DateUtils.timeToTimeLow(insuranceRenteFiveDate) == DateUtils.timeToTimeLow(maturityDate)) {
            insuranceRenteFiveDate = null;
        }

        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteOneDate", insuranceRenteOneDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteTwoDate", insuranceRenteTwoDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteThreeDate", insuranceRenteThreeDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteFourDate", insuranceRenteFourDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteFiveDate", insuranceRenteFiveDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insuranceRenteLastDate", insuranceRenteLastDate, 3);

        // 疾病致死亡与疾病致高度残疾保险金相关
        // 疾病致死亡与疾病致高度残疾保险金时间
        long suddenDeathStartDate1 = DateUtils.addStringDayRT(effectiveDate, 180);
        long suddenDeathEndDate1 = maturityDate;
        PrintCommon.setPrintDateTime(printObjectList, "suddenDeathStartDate1", suddenDeathStartDate1, 3);
        PrintCommon.setPrintDateTime(printObjectList, "suddenDeathEndDate1", suddenDeathEndDate1, 3);
        // 疾病致死亡与疾病致高度残疾保险金
        PrintCommon.setPrintData(printObjectList, "suddenDeathAmount1", amount.multiply(new BigDecimal(1)).setScale(2, BigDecimal.ROUND_HALF_UP), 3);
        PrintCommon.setPrintData(printObjectList, "suddenHarmAmount1", amount.multiply(new BigDecimal(3)).setScale(2, BigDecimal.ROUND_HALF_UP), 3);
        PrintCommon.setPrintData(printObjectList, "suddenHarmAmount2", amount.multiply(new BigDecimal(1)).setScale(2, BigDecimal.ROUND_HALF_UP), 3);

        // 意外伤害致死亡和意外伤害致高度残疾保险金
        long suddenHarmStartDate = effectiveDate;
        long suddenHarmEndDate = maturityDate;
        PrintCommon.setPrintDateTime(printObjectList, "suddenHarmStartDate", suddenHarmStartDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "suddenHarmEndDate", suddenHarmEndDate, 3);
    }
}
