package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.endorse.CoveragePrintBo;
import com.gclife.attachment.model.policy.endorse.ReinstatementPrintBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * create 2018/9/26
 * description:复效
 */
@Component
public class EndorseReinstatementData {

    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> dateMap = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ReinstatementPrintBo reinstatementPrintBo = JSON.parseObject(content, ReinstatementPrintBo.class);
        dateMap.put("acceptNo", PrintCommon.getPrintString(reinstatementPrintBo.getAcceptNo(), 3));
        dateMap.put("policyNo", PrintCommon.getPrintString(reinstatementPrintBo.getPolicyNo(), 3));
        dateMap.put("applicantName", PrintCommon.getPrintString(reinstatementPrintBo.getApplicantName(), 3));
        dateMap.put("insuredName", PrintCommon.getPrintString(reinstatementPrintBo.getInsuredName(), 3));

        PrintCommon.setPrintDateTime(dateMap, "acceptDate", reinstatementPrintBo.getAcceptDate(), 3);
        PrintCommon.setPrintDateTime(dateMap, "applyDate", reinstatementPrintBo.getApplyDate(), 3);

        String policyPdfJson = reinstatementPrintBo.getPolicyPdfJson();
        ElectronicPolicyGeneratorRequest policyPdfJsonElectronicPolicyGenerator = JSON.parseObject(policyPdfJson, ElectronicPolicyGeneratorRequest.class);
        PolicyBo policyBo = JSON.parseObject(policyPdfJsonElectronicPolicyGenerator.getContent(), PolicyBo.class);
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        final BigDecimal[] allTotalPremium = {new BigDecimal(0)};
        List<CoveragePrintBo> coveragePrintList = reinstatementPrintBo.getCoveragePrintList();
        if (AssertUtils.isNotEmpty(listPolicyCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listPolicyCoverage.forEach(coverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                Optional<CoveragePrintBo> first = coveragePrintList.stream().filter(coveragePrintBo -> coveragePrintBo.getProductId().equals(coverageBo.getProductId())).findFirst();
                if(first.isPresent()){
                    coverageMap.put("product_1","*");
                }
                PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
                PrintCommon.setPrintDateTime(coverageMap, "endorseFinishDate", coverageBo.getCoveragePeriodStartDate(), 3);
                BigDecimal totalAmount = null;
                String totalAmountString = null;
                if (AssertUtils.isNotEmpty(coverageBo.getTotalAmount())) {
                    totalAmount = new BigDecimal(coverageBo.getTotalAmount());
                    totalAmountString = PrintCommon.decimalFormat1.format(totalAmount);
                }

                coverageMap.put("totalAmount",PrintCommon.getPrintString(totalAmountString, 3));

                //额外加费
                BigDecimal extraPremium = null;
                BigDecimal totalPremium = coverageBo.getTotalPremium();
                BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
                if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                    List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                            .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                    AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                    EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                        double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                        extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                        totalPremium = totalPremium.subtract(extraPremium);
                    }
                }
                coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));
                coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));

                long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
                int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
                String premiumFrequency = coverageBo.getPremiumFrequency();
                if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
                } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
                }
                PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
                PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);

                BigDecimal maturityAmount = null;
                List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
                if ("PRO88000000000009".equals(coverageBo.getProductId())) {
                    ProductCalculation.policyCashValues(dateMap, policyCashValues);
                    maturityAmount = totalAmount;
                }
                if (AssertUtils.isNotEmpty(productCashValueBos) && "PRO880000000000013".equals(coverageBo.getProductId())) {
                    ProductCalculation.policyCashValues13(dateMap, policyCashValueListMap);
                    OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                    if (max.isPresent()) {
                        maturityAmount = new BigDecimal(max.getAsDouble());
                    }
                }
                coverageMap.put("maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
                dateMap.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
                allTotalPremium[0] = allTotalPremium[0].add(totalPremium);
                String premiumPeriod = coverageBo.getPremiumPeriod();
                String premiumPeriodUnitName = coverageBo.getPremiumPeriodUnitName();
                String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                    premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
                }
                dateMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
                BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
                dateMap.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
                dateMap.put("yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
                dateMap.put("premiumFrequencyName", PrintCommon.getPrintString(coverageBo.getPremiumFrequencyName(), 3));
                coverageListMap.add(coverageMap);
            });
            PrintCommon.coverageSort(coverageListMap);
            dateMap.put("coverageListMap", coverageListMap);
        }

        dateMap.put("allTotalPremium", PrintCommon.getPrintString(allTotalPremium[0], 3));
        PrintCommon.setPrintDateTime(dateMap, "endorseFinishDate", reinstatementPrintBo.getEndorseFinishDate(), 3);
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(policyBo.getApproveDate()));
        }
        dateMap.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        dateMap.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        PrintCommon.setPrintDateTime(dateMap, "approveDate", policyBo.getApproveDate(), 3);
        PolicyApplicantBo applicant = policyBo.getApplicant();
        Integer applicantAgeYear = null;
        dateMap.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(policyBo.getApproveDate()));
        }
        dateMap.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        dateMap.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));

        // 公司基础信息
        dateMap.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return dateMap;
    }

    public List<PrintObject> getData1(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = new ArrayList<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        ReinstatementPrintBo reinstatementPrintBo = JSON.parseObject(content, ReinstatementPrintBo.class);

        PrintCommon.setPrintData(printObjectList, "acceptNo", reinstatementPrintBo.getAcceptNo(), 3);
        PrintCommon.setPrintData(printObjectList, "policyNo", reinstatementPrintBo.getPolicyNo(), 3);
        PrintCommon.setPrintData(printObjectList, "applicantName", reinstatementPrintBo.getApplicantName(), 3);
        PrintCommon.setPrintData(printObjectList, "insuredName", reinstatementPrintBo.getInsuredName(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "acceptDate", reinstatementPrintBo.getAcceptDate(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "applyDate", reinstatementPrintBo.getApplyDate(), 3);

        List<CoveragePrintBo> coveragePrintList = reinstatementPrintBo.getCoveragePrintList();
        if (!AssertUtils.isNotEmpty(coveragePrintList)) {
            coveragePrintList = new ArrayList<>();
        }
        for (int i = 0; i < 5; i++) {
            CoveragePrintBo coveragePrintBo = new CoveragePrintBo();
            if (coveragePrintList.size() > i) {
                coveragePrintBo = coveragePrintList.get(i);
            }
            PrintCommon.setProductName(printObjectList, coveragePrintBo.getProductId(), i + "", coveragePrintBo.getProductLevel());
            String premiumPeriodName = null;
            if (AssertUtils.isNotEmpty(coveragePrintBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(coveragePrintBo.getPremiumPeriodUnitName())) {
                premiumPeriodName = coveragePrintBo.getPremiumPeriod() + coveragePrintBo.getPremiumPeriodUnitName();
            }
            PrintCommon.setPrintData(printObjectList, "premiumFrequencyName" + i, coveragePrintBo.getPremiumFrequencyName(), 3);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodName" + i, premiumPeriodName, 3);
            String coveragePeriodName = null;
            if (AssertUtils.isNotEmpty(coveragePrintBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(coveragePrintBo.getCoveragePeriodUnitName())) {
                coveragePeriodName = coveragePrintBo.getCoveragePeriod() + " " + coveragePrintBo.getCoveragePeriodUnitName();
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())
                        && "AGE".equals(coveragePrintBo.getCoveragePeriodUnit())) {
                    coveragePeriodName = coveragePrintBo.getCoveragePeriodUnitName() + " " + coveragePrintBo.getCoveragePeriod();
                }
            }
            PrintCommon.setPrintData(printObjectList, "coveragePeriodName" + i, coveragePeriodName, 3);
            PrintCommon.setPrintData(printObjectList, "totalAmount" + i, coveragePrintBo.getTotalAmount(), 3);
            PrintCommon.setPrintData(printObjectList, "totalPremium" + i, coveragePrintBo.getTotalPremium(), 3);
        }

        PrintCommon.setPrintDateTime(printObjectList, "endorseFinishDate", reinstatementPrintBo.getEndorseFinishDate(), 3);

        return printObjectList;
    }

}
