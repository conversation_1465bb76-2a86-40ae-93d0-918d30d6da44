package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.common.ProductName;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.endorse.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @ Author     : lichongfu
 * @ Date       : Created in 15:51 2023/3/27
 * @ Description: 变更受益人转换类
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class ChgEndorseBeneficiaryData {

    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> beneficiaryMapList = new ArrayList<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();

        ChgEndorseBeneficiaryPrintBo printBo = JSON.parseObject(content, ChgEndorseBeneficiaryPrintBo.class);
        EndorseAcceptBeneficiaryBo endorseAcceptBo = printBo.getEndorseAcceptBo();
        List<AttaChgEndorseBeneficiaryBo> chgEndorseBeneficiaryBos = printBo.getChgEndorseBeneficiaryBos();

        if (AssertUtils.isNotNull(endorseAcceptBo)) {
            map.put("policyNo", PrintCommon.getPrintString(endorseAcceptBo.getApplyNo(), 3));
            map.put("applyName", PrintCommon.getPrintString(endorseAcceptBo.getApplyName(), 3));
            map.put("applyDate", PrintCommon.getPrintString(DateUtils.timeStrToString(endorseAcceptBo.getApplyDate(), DateUtils.FORMATE18), 3));
            map.put("effectiveDate", PrintCommon.getPrintString(DateUtils.timeStrToString(endorseAcceptBo.getEffectiveDate(), DateUtils.FORMATE18), 3));
        }

        //组装受益人数据
        if (AssertUtils.isNotEmpty(chgEndorseBeneficiaryBos)) {
            //设置受益人顺位坐标便于排序
            chgEndorseBeneficiaryBos.forEach(chgEndorseBeneficiaryBo -> {
                String noOrder = chgEndorseBeneficiaryBo.getBeneficiaryNoOrder();
                if ("ORDER_ONE".equals(noOrder)) {
                    chgEndorseBeneficiaryBo.setBeneficiaryOrderSeq(1L);
                } else if ("ORDER_TWO".equals(noOrder)) {
                    chgEndorseBeneficiaryBo.setBeneficiaryOrderSeq(2L);
                }else {
                    chgEndorseBeneficiaryBo.setBeneficiaryOrderSeq(3L);
                }
            });

            //根据被保人id和受益人顺位排序
            chgEndorseBeneficiaryBos.sort(Comparator.comparing(AttaChgEndorseBeneficiaryBo::getInsuredId).thenComparing(AttaChgEndorseBeneficiaryBo::getBeneficiaryOrderSeq));

            chgEndorseBeneficiaryBos.forEach(attaChgEndorseBeneficiaryBo ->{
                Map<String, Object> beneficiaryMap = new HashMap<>();
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getBeneficiaryNoOrderName(), 3));
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getName(), 3));
                beneficiaryMap.put("sexName", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getSexName(), 3));
                beneficiaryMap.put("birthdayDate", PrintCommon.getPrintString(DateUtils.timeStrToString(attaChgEndorseBeneficiaryBo.getBirthday(), DateUtils.FORMATE18), 3));
                beneficiaryMap.put("idTypeName", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getIdTypeName(), 3));
                beneficiaryMap.put("idNo", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getIdNo(), 3));
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getRelationshipName(), 3));
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(attaChgEndorseBeneficiaryBo.getBeneficiaryProportion(), 3));
                beneficiaryMapList.add(beneficiaryMap);
            } );

        }
        // CEO签名 + 公司印章
        map.put("signSealCEOPicture", PrintCommon.SIGN_SEAL_CEO);
        map.put("beneficiaryMapList", beneficiaryMapList);
        return map;
    }
}
