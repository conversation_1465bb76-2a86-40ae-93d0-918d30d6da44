package com.gclife.attachment.service.print.insured;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.common.ProductName;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.endorse.*;
import com.gclife.attachment.model.policy.renewal.AgentBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.gclife.attachment.common.PrintCommon.sdfEN_US29;

/**
 * <AUTHOR>
 * @date 2023/08/08
 */
@Component
public class EndorseGCGroupStudentCareInsuredData {
    /**
     * 获取增减员打印数据
     *
     * @return
     */
    public List<Map<String, Object>> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        EndorsePrintAttachmentData applyPrintBo = JSON.parseObject(content, EndorsePrintAttachmentData.class);
        map.put("policyNo", PrintCommon.getPrintString(applyPrintBo.getPolicyNo(), 3));
        EndorsePrintApplicantBo applicantBo = applyPrintBo.getApplicantBo();
        if (!AssertUtils.isNotNull(applicantBo)) {
            applicantBo = new EndorsePrintApplicantBo();
        }
        map.put("companyName", PrintCommon.getPrintString(applicantBo.getCompanyName(), 3));
        AgentBo agentBo = applyPrintBo.getAgentBo();
        if (!AssertUtils.isNotNull(applicantBo)) {
            agentBo = new AgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(agentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(agentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(agentBo.getMobile(), 3));
        String productName = ProductName.getProductName("PRO880000000000029" + language);
        map.put("productNameListString", PrintCommon.getPrintString(productName, 3));

        List<EndorsePrintBeneficiaryBo> beneficiaryBoList = applyPrintBo.getBeneficiaryBoList();
        List<EndorsePrintGroupHealthQuestionnaireAnswerBo> ghqaBoList = applyPrintBo.getGhqaBoList();
        List<EndorsePrintInsuredBo> insuredBoList = applyPrintBo.getInsuredBoList();
        if (!AssertUtils.isNotEmpty(insuredBoList)) {
            mapList.add(map);
            return mapList;
        }
        List<EndorsePrintCoverageBo> coverageBoList = applyPrintBo.getCoverageBoList();
        List<EndorsePrintCoverageLevelBo> coverageLevelBoList = applyPrintBo.getCoverageLevelBoList();
        List<EndorsePrintCoverageDutyBo> coverageDutyBoList = applyPrintBo.getCoverageDutyBoList();
        if (!AssertUtils.isNotEmpty(coverageBoList) || !AssertUtils.isNotEmpty(coverageLevelBoList)) {
            mapList.add(map);
            return mapList;
        }
        Map<String, List<EndorsePrintCoverageBo>> productMapListCoverageBo = coverageBoList.stream().collect(Collectors.groupingBy(EndorsePrintCoverageBo::getProductId));
        productMapListCoverageBo.forEach((productId, coverageList) -> {
            Map<String, Object> productMap = new HashMap<>();
            PrintCommon.setProductName(productMap, productId);
            List<String> coverageInsuredIdList = coverageList.stream().map(EndorsePrintCoverageBo::getInsuredId).collect(Collectors.toList());
            //增加被保人
            List<Map<String, Object>> addInsuredMapList = new ArrayList<>();
            List<String> addInsuredId = coverageList.stream().filter(coverageBo -> "ADD".equals(coverageBo.getChangeFlag())).map(EndorsePrintCoverageBo::getInsuredId).collect(Collectors.toList());
            final AtomicInteger[] insuredNo = {new AtomicInteger(0)};
            List<EndorsePrintInsuredBo> addInsuredList = insuredBoList.stream().filter(insuredBo -> {
                return ("ADD".equals(insuredBo.getChangeFlag()) && coverageInsuredIdList.contains(insuredBo.getInsuredId()))
                        || (AssertUtils.isNotEmpty(addInsuredId) && addInsuredId.contains(insuredBo.getInsuredId()));
            }).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(addInsuredList)) {
                addInsuredList.forEach(addInsuredBo -> {
                    //Map<String, Object> addInsuredMap = new HashMap<>();
                    this.setInsuredMap(productId, language/*, addInsuredMap*/, addInsuredBo, beneficiaryBoList, ghqaBoList, coverageList, coverageLevelBoList, coverageDutyBoList,applyPrintBo.getEffectiveDate(),addInsuredMapList,insuredNo);
                    /*addInsuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                    addInsuredMapList.add(addInsuredMap);*/
                });

                //只展示主险的受益人
                /*coverageList.forEach(endorsePrintCoverageBo -> {
                    if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(endorsePrintCoverageBo.getPrimaryFlag())) {
                        this.setAddInsuredBeneficiary(productMap, addInsuredList, beneficiaryBoList);
                    }
                });*/
            }
            //减少被保人
            insuredNo[0] = new AtomicInteger(0);
            List<Map<String, Object>> subtractInsuredMapList = new ArrayList<>();
            insuredBoList.stream().filter(insuredBo -> {
                return "SUBTRACT".equals(insuredBo.getChangeFlag()) && coverageInsuredIdList.contains(insuredBo.getInsuredId());
            }).forEach(subtractInsuredBo -> {
                //Map<String, Object> subtractInsuredMap = new HashMap<>();
                this.setInsuredMap(productId, language/*, subtractInsuredMap*/, subtractInsuredBo, beneficiaryBoList, ghqaBoList, coverageList, coverageLevelBoList, coverageDutyBoList,applyPrintBo.getEffectiveDate(),subtractInsuredMapList,insuredNo);
                /*subtractInsuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                subtractInsuredMapList.add(subtractInsuredMap);*/
            });
            if (AssertUtils.isNotEmpty(addInsuredMapList) || AssertUtils.isNotEmpty(subtractInsuredMapList)) {
                productMap.put("addInsuredMapList", addInsuredMapList);
                productMap.put("subtractInsuredMapList", subtractInsuredMapList);
                productMap.putAll(map);
                mapList.add(productMap);
            }
        });
        PrintCommon.coverageSort(mapList);
        if (!AssertUtils.isNotEmpty(mapList)) {
            mapList.add(map);
        }
        return mapList;
    }

    private void setAddInsuredBeneficiary(Map<String, Object> productMap, List<EndorsePrintInsuredBo> addInsuredList, List<EndorsePrintBeneficiaryBo> beneficiaryBoList) {
        if (!AssertUtils.isNotEmpty(beneficiaryBoList)) {
            return;
        }
        List<String> insuredIdList = addInsuredList.stream().map(EndorsePrintInsuredBo::getInsuredId).collect(Collectors.toList());
        List<EndorsePrintBeneficiaryBo> beneficiaryList = beneficiaryBoList.stream().filter(applyBeneficiaryBo -> insuredIdList.contains(applyBeneficiaryBo.getInsuredId())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(beneficiaryList)) {
            List<Map<String, Object>> beneficiaryMapList = new ArrayList<>();
            AtomicInteger insuredNo = new AtomicInteger(1);
            Map<String, List<EndorsePrintBeneficiaryBo>> insuredIdBeneficiaryListMap = beneficiaryList.stream().collect(Collectors.groupingBy(EndorsePrintBeneficiaryBo::getInsuredId));
            insuredIdBeneficiaryListMap.forEach((s, applyBeneficiaryBos) -> {
                applyBeneficiaryBos.forEach(beneficiaryBo -> {
                    Map<String, Object> beneficiaryMap = new HashMap<>();
                    Optional<EndorsePrintInsuredBo> applyInsuredBoOptional = addInsuredList.stream().filter(applyInsuredBo -> applyInsuredBo.getInsuredId().equals(beneficiaryBo.getInsuredId())).findFirst();
                    EndorsePrintInsuredBo applyInsuredBo = applyInsuredBoOptional.get();
                    beneficiaryMap.put("insuredNo", PrintCommon.getPrintString(insuredNo.getAndIncrement(), 3));
                    beneficiaryMap.put("insuredName", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                    beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryNoOrderName(), 3));
                    beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryBo.getName(), 3));
                    beneficiaryMap.put("sexName", PrintCommon.getPrintString(beneficiaryBo.getSexName(), 3));
                    if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(beneficiaryBo.getRelationship()) && AssertUtils.isNotEmpty(beneficiaryBo.getRelationshipInstructions())) {
                        beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipInstructions(), 3));
                    } else {
                        beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipName(), 3));
                    }
                    beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryProportion(), 3));
                    beneficiaryMap.put("idTypeName", PrintCommon.getPrintString(beneficiaryBo.getIdTypeName(), 3));
                    beneficiaryMap.put("idNo", PrintCommon.getPrintString(beneficiaryBo.getIdNo(), 3));
                    beneficiaryMapList.add(beneficiaryMap);
                });
            });
            productMap.put("beneficiaryMapList", beneficiaryMapList);
        }
    }

    private void setInsuredMap(String productId, String language,
                               /*Map<String, Object> insuredMap,*/
                               EndorsePrintInsuredBo addInsured,
                               List<EndorsePrintBeneficiaryBo> beneficiaryBoList,
                               List<EndorsePrintGroupHealthQuestionnaireAnswerBo> ghqaBoList,
                               List<EndorsePrintCoverageBo> coverageBoList,
                               List<EndorsePrintCoverageLevelBo> coverageLevelBoList,
                               List<EndorsePrintCoverageDutyBo> coverageDutyBoList,
                               long effectiveDate,List<Map<String, Object>> insuredMapList,AtomicInteger[] insuredNo) {

        if (AssertUtils.isNotEmpty(beneficiaryBoList)) {
            List<EndorsePrintBeneficiaryBo> endorsePrintBeneficiaryBos = beneficiaryBoList.stream().filter(beneficiaryBo -> addInsured.getInsuredId().equals(beneficiaryBo.getInsuredId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(endorsePrintBeneficiaryBos)) {
                int i = 1;
                for (EndorsePrintBeneficiaryBo endorsePrintBeneficiaryBo : endorsePrintBeneficiaryBos) {
                    Map<String, Object> insuredMap = new HashMap<>();
                    insuredMap.put("name", PrintCommon.getPrintString(addInsured.getName(), 3));
                    insuredMap.put("idNo", PrintCommon.getPrintString(addInsured.getIdNo(), 3));
                    String dateEN_US = sdfEN_US29.format(addInsured.getBirthday());
                    insuredMap.put("birthday" + "EN_US", PrintCommon.getPrintString(dateEN_US, 3));
                    insuredMap.put("birthday" + "ZH_CN", PrintCommon.getPrintString(dateEN_US, 3));
                    Integer age = null;
                    if(AssertUtils.isNotNull(addInsured.getBirthday())){
                        try{
                            age =  DateUtils.getAgeYear(new Date(addInsured.getBirthday()),new Date(effectiveDate));
                        }catch (Exception e){
                            e.printStackTrace();
                            throw new RequestException();
                        }
                    }
                    insuredMap.put("ageYear", PrintCommon.getPrintString(age, 3));
                    insuredMap.put("sexName", PrintCommon.getPrintString(addInsured.getSexName(), 3));
                    final String[] answerCodeJoin = {null};
                    if (AssertUtils.isNotEmpty(ghqaBoList)) {
                        ghqaBoList.stream().filter(ghqaBo -> addInsured.getInsuredId().equals(ghqaBo.getInsuredId()) && AssertUtils.isNotEmpty(ghqaBo.getAnswer())).forEach(ghqaBo -> {
                            answerCodeJoin[0] = (AssertUtils.isNotEmpty(answerCodeJoin[0]) ? answerCodeJoin[0] + "," : "") + ghqaBo.getAnswer();
                        });
                    }
                    insuredMap.put("answerCodeJoin", PrintCommon.getPrintString(answerCodeJoin[0], 3));
                    insuredMap.put("occupationCode", PrintCommon.getPrintString(addInsured.getOccupationCode(), 3));
                    final BigDecimal[] totalPremium = {new BigDecimal(0)};
                    final BigDecimal[] refundAmount = {new BigDecimal(0)};
                    final BigDecimal[] totalAmount = {new BigDecimal(0)};
                    final BigDecimal[] totalPremium29 = {new BigDecimal(0)};
                    final BigDecimal[] refundAmount29 = {new BigDecimal(0)};
                    final BigDecimal[] totalAmount29 = {new BigDecimal(0)};
                    final BigDecimal[] totalPremium33 = {new BigDecimal(0)};
                    final BigDecimal[] refundAmount33 = {new BigDecimal(0)};
                    final BigDecimal[] totalAmount33 = {new BigDecimal(0)};
                    if (i == 2) {
                        totalPremium[0] = totalPremium[0].add(new BigDecimal(0.0));
                        refundAmount[0] = refundAmount[0].add(new BigDecimal(0.0));
                        totalAmount[0] = totalAmount[0].add(new BigDecimal(0.0));
                        totalPremium29[0] = totalPremium29[0].add(new BigDecimal(0.0));
                        refundAmount29[0] = refundAmount29[0].add(new BigDecimal(0.0));
                        totalAmount29[0] =  totalAmount29[0].add(new BigDecimal(0.0));
                        totalPremium33[0] = totalPremium33[0].add(new BigDecimal(0.0));
                        refundAmount33[0] = refundAmount33[0].add(new BigDecimal(0.0));
                        totalAmount33[0] =  totalAmount33[0].add(new BigDecimal(0.0));
                        insuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0], 3));
                    }else {
                        coverageBoList.stream().filter(coverageBo -> addInsured.getInsuredId().equals(coverageBo.getInsuredId())).forEach(coverageBo -> {
                            BigDecimal coverageActualPremium = AssertUtils.isNotNull(coverageBo.getActualPremium()) ? coverageBo.getActualPremium() : new BigDecimal(0.0);
                            BigDecimal coverageRefundAmount = AssertUtils.isNotNull(coverageBo.getRefundAmount()) ? coverageBo.getRefundAmount() : new BigDecimal(0.0);
                            BigDecimal amount = AssertUtils.isNotNull(coverageBo.getAmount()) ? coverageBo.getAmount() : new BigDecimal(0.0);
                            totalPremium[0] = totalPremium[0].add(coverageActualPremium);
                            refundAmount[0] = refundAmount[0].add(coverageRefundAmount);
                            totalAmount[0] = totalAmount[0].add(amount);

                            String productId1 = coverageBo.getProductId();
                            // 29号产品
                            if ("PRO880000000000029".equals(productId1)) {
                                //保额
                                totalPremium29[0] = totalPremium29[0].add(coverageActualPremium);
                                refundAmount29[0] = refundAmount29[0].add(coverageRefundAmount);
                                totalAmount29[0] = totalAmount29[0].add(amount);
                            }
                            // 33号产品
                            if ("PRO880000000000033".equals(productId1)) {
                                //保额
                                totalPremium33[0] = totalPremium33[0].add(coverageActualPremium);
                                refundAmount33[0] = refundAmount33[0].add(coverageRefundAmount);
                                totalAmount33[0] = totalAmount33[0].add(amount);
                            }
                        });
                        insuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                    }

                    if (AssertUtils.isNotEmpty(addInsured.getAddInsuredId()) || AssertUtils.isNotEmpty(addInsured.getSubtractInsuredId())) {
                        totalPremium[0] = new BigDecimal(0);
                        refundAmount[0] = new BigDecimal(0);
                        totalAmount[0] = new BigDecimal(0);
                        totalPremium29[0] = new BigDecimal(0);
                        refundAmount29[0] = new BigDecimal(0);
                        totalAmount29[0] =  new BigDecimal(0);
                        totalPremium33[0] = new BigDecimal(0);
                        refundAmount33[0] = new BigDecimal(0);
                        totalAmount33[0] =  new BigDecimal(0);
                    }
                    insuredMap.put("totalPremium", PrintCommon.getPrintString(totalPremium[0], 3));
                    insuredMap.put("totalAmount", PrintCommon.getPrintString(totalAmount[0], 3));
                    insuredMap.put("totalPremium29", PrintCommon.getPrintString(totalPremium29[0], 3));
                    insuredMap.put("totalAmount29", PrintCommon.getPrintString(totalAmount29[0], 3));
                    insuredMap.put("totalPremium33", PrintCommon.getPrintString(totalPremium33[0], 3));
                    insuredMap.put("totalAmount33", PrintCommon.getPrintString(totalAmount33[0], 3));
                    insuredMap.put("beneficiaryName",PrintCommon.getPrintString(endorsePrintBeneficiaryBo.getName(),3));
                    insuredMap.put("beneficiarySexName",PrintCommon.getPrintString(endorsePrintBeneficiaryBo.getSexName(),3));
                    insuredMap.put("relationshipName",PrintCommon.getPrintString(endorsePrintBeneficiaryBo.getRelationshipName(),3));
                    insuredMap.put("beneficiaryProportion",PrintCommon.getPrintString(endorsePrintBeneficiaryBo.getBeneficiaryProportion(),3));
                    i++;
                    insuredMapList.add(insuredMap);
                }

            }

        }
    }
}
