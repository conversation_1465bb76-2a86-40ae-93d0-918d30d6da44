package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.ApplyAgentBo;
import com.gclife.attachment.model.policy.apply.ApplyApplicantBo;
import com.gclife.attachment.model.policy.apply.ApplyBeneficiaryBo;
import com.gclife.attachment.model.policy.apply.ApplyBeneficiaryInfoBo;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.attachment.model.policy.apply.ApplyCoverageBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.policy.apply.ElectronicSignatureAttachmentBo;
import com.gclife.attachment.model.policy.apply.ProductHealthNoticeBo;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.PolicyAddPremiumBo;
import com.gclife.attachment.model.policy.policy.PolicyAgentBo;
import com.gclife.attachment.model.policy.policy.PolicyApplicantBo;
import com.gclife.attachment.model.policy.policy.PolicyBo;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;
import com.gclife.attachment.model.policy.policy.PolicyInsuredBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN;
import static com.gclife.attachment.model.config.AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

@Component
public class GCSokSanData extends BaseBusinessServiceImpl {
    @Autowired
    private AttachmentExtDao attachmentExtDao;

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    /**
     * <p>
     *    34号产品计划书
     * <p>
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate =  planPrintBo.getCreatedDate();
        // 计划书编号
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        // 投保人信息
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        Integer applicantAgeYear = null;
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        // 被保人信息
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        // 获取保险信息
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            BigDecimal additionalAccAmount = coverageBo.getAdditionalAccAmount();
            coverageMap.put("additionalAccAmount", PrintCommon.getPrintString(AssertUtils.isNotNull(additionalAccAmount) ? PrintCommon.decimalFormat1.format(additionalAccAmount) : null, 2));
            // 保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            // 缴费年限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            // 交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            //34号产品目前只有年缴
            coverageMap.put("premiumFrequency", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            coverageListMap.add(coverageMap);
        }
        map.put("coverageListMap", coverageListMap);
        // 期缴保费
        double totalPremium = coverageListMap.stream().mapToDouble(mapper -> Double.valueOf((mapper.get("totalPremium") + "").replace(",", ""))).sum();
        map.put("allTotalPremium", PrintCommon.getPrintString(new BigDecimal(totalPremium), 2));
        map.put("allYearTotalPremium", PrintCommon.getPrintString(new BigDecimal(totalPremium), 2));
        map.put("totalAmount",coverageListMap.get(0).get("totalAmount"));
        map.put("additionalAccAmount", coverageListMap.get(0).get("additionalAccAmount"));
        //代理人信息
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        this.getLogger().info("计划书map数据----------------" + map);
        return map;
    }

    /**
     * 34号产品保险证
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream()
                .filter(policyCoverage -> MAIN.name().equals(policyCoverage.getPrimaryFlag()))
                .findFirst().get();
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        // 风险开始日期
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        // 生效日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 缴费周期
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        /**投保人信息**/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        // 投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        // 身份证号
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        // 年龄
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        // 投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        // 地址
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**被保人信息**/
        // 被保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        // 被保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        // 被保人年龄
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        // 被保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        // 被保人地址
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        // 保险利益
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageMap.put("productId", coverageBo.getProductId());
            // 产品名称
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            // 保险金额
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            BigDecimal additionalAccAmount = coverageBo.getAdditionalAccAmount();
            coverageMap.put("additionalAccAmount", PrintCommon.getPrintString(AssertUtils.isNotNull(additionalAccAmount) ? PrintCommon.decimalFormat1.format(additionalAccAmount) : null, 2));
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            // 额外保费
            BigDecimal extraPremium = null;
            // 期缴保费
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            // 缴费终止日期
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            //到期日
            Long maturityDate = policyBo.getMaturityDate();
            PrintCommon.setPrintDateTime(coverageMap, "contractEndDate", maturityDate, 3);
            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        // 总保费
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));
        // 业务员信息
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        this.getLogger().info("保险证map数据----------------" + map);
        return map;
    }

    /**
     * 34号产品投保单
     * @param electronicPolicyGeneratorRequest
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        this.getLogger().info("投保人" + JSON.toJSONString(applicant));
        /********************************************代理人信息*********************************************************/
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        map.put("agentName", applyAgentBo.getAgentName());
        map.put("agentCode", applyAgentBo.getAgentCode());
        /********************************************投保人信息*********************************************************/
        // 投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        // 投保人出生日期
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //体重和身高
        map.put("weight", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("height", PrintCommon.getPrintString(applicant.getStature(), 3));
        //身份证
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        ApplyInsuredBo insured = new ApplyInsuredBo();
        this.getLogger().info("被保人:" + JSON.toJSONString(listInsured));
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(listCoverage.get(0).getAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(listCoverage.get(0).getAmount())) : null, 2);
            map.put("totalAmount", totalAmount);
            BigDecimal additionalAccAmount = listCoverage.get(0).getAdditionalAccAmount();
            map.put("additionalAccAmount", PrintCommon.getPrintString(AssertUtils.isNotNull(additionalAccAmount) ? PrintCommon.decimalFormat1.format(additionalAccAmount) : null, 2));
        }
        // 销售计划
        String planCode = listCoverage.get(0).getPlanCode();
        String packageCode = listCoverage.get(0).getPackageCode();
        StringBuilder stringBuilder = new StringBuilder();
        if (AssertUtils.isNotEmpty(packageCode)) {
            ResultObject<SyscodeResponse> productSalesPackages = platformInternationalBaseApi.queryOneInternational("PRODUCT_SALES_PACKAGES", packageCode, language);
            if (AssertUtils.isNotNull(productSalesPackages)) {
                stringBuilder.append(productSalesPackages.getData().getCodeName());
            }
        }
        if (AssertUtils.isNotEmpty(planCode)) {
            ResultObject<SyscodeResponse> salesPlan = platformInternationalBaseApi.queryOneInternational("PRODUCT_SALES_PLAN", planCode, language);
            if (AssertUtils.isNotNull(salesPlan)) {
                stringBuilder.append(" ").append("(").append(salesPlan.getData().getCodeName()).append(")");
            }
        }
        map.put("planName", PrintCommon.getPrintString(stringBuilder, 2));


        //与投保人关系
        map.put("relationshipInstructions", PrintCommon.getPrintString(insured.getRelationshipName(), 2));
        //家庭住址
        map.put("ApplicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 2));
        //联系电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        /********************************************被保人信息*********************************************************/
        //被保人姓名
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件类型以及号码
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //家庭地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        /********************************************受益人信息*********************************************************/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            for (int i = 0; i < listBeneficiary.size(); i++) {
                ApplyBeneficiaryInfoBo applyBeneficiaryInfoBo = listBeneficiary.get(i);
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                int indexNo = i + 1;
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //序号
                beneficiaryMap.put("indexNo", indexNo);
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                //受益人名称
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //与被保险人关系
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //联系电话
                beneficiaryMap.put("beneficiaryMobile", PrintCommon.getPrintString(applyBeneficiaryBo.getMobile(), 2));
                beneficiaryListMap.add(beneficiaryMap);
            }
            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        /********************************************电子签名*********************************************************/
        ElectronicSignatureAttachmentBo electronicSignatureAttachmentBo = applyPrintBo.getElectronicSignatureAttachmentBo();
        if (AssertUtils.isNotNull(electronicSignatureAttachmentBo)) {
            String agentSignatureAttachmentId = electronicSignatureAttachmentBo.getAgentSignatureAttachmentId();
            String applicantSignatureAttachmentId = electronicSignatureAttachmentBo.getApplicantSignatureAttachmentId();
            String insuredSignatureAttachmentId = electronicSignatureAttachmentBo.getInsuredSignatureAttachmentId();
            List<String> signatureAttachmentIds = Arrays.asList(agentSignatureAttachmentId, applicantSignatureAttachmentId, insuredSignatureAttachmentId);
            List<AttachmentPo> attachmentPos = attachmentExtDao.listAttachmentPoByPk(signatureAttachmentIds);
            if (AssertUtils.isNotEmpty(attachmentPos)) {
                for (AttachmentPo attachmentPo : attachmentPos) {
                    // 动态域名 根据不同环境获取不同的OSS域名
                    String dynamicDomain = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.");
                    // 签名附件
                    String attachmentId = attachmentPo.getAttachmentId();
                    // 签名日期
                    Long createdDate = attachmentPo.getCreatedDate();
                    // 业务员签名
                    if (AssertUtils.isNotEmpty(agentSignatureAttachmentId) && agentSignatureAttachmentId.equals(attachmentId)) {
                        map.put("agentSignature", dynamicDomain + attachmentPo.getUrl());
                        PrintCommon.setPrintDateTime(map, "agentSignatureDate", createdDate, 3);
                    }
                    // 投保人签名
                    if (AssertUtils.isNotEmpty(applicantSignatureAttachmentId) && applicantSignatureAttachmentId.equals(attachmentId)) {
                        map.put("applicantSignature", dynamicDomain + attachmentPo.getUrl());
                        PrintCommon.setPrintDateTime(map, "applicantSignatureDate", createdDate, 3);
                    }
                }
            }
        }
        /********************************************健康告知*********************************************************/
        List<ProductHealthNoticeBo> listHealthNotice = applyPrintBo.getListHealthNotice();
        //判断总金额
        boolean amountMark = false;
        boolean isHealthNotice = false;
        if(listCoverage.get(0).getAmount().compareTo("3000") > 0){
            amountMark = true;
        }
        map.put("amountMark", amountMark);
        //表行
        String table_01 = "";
        String table_02 = "";
        String table_03 = "";
        //答案
        String answer_001 = "";
        String answer_002 = "";
        String answer_003 = "";
        this.getLogger().info("健康告知：" + JSON.toJSONString(listHealthNotice));
        if (AssertUtils.isNotEmpty(listHealthNotice)) {
            for (ProductHealthNoticeBo productHealthNoticeBo : listHealthNotice) {
                if ("34_QUESTION_001".equals(productHealthNoticeBo.getQuestionCode())) {
                    answer_001 = productHealthNoticeBo.getAnswer();
                    table_01 = "Y";
                }
                if ("34_QUESTION_002".equals(productHealthNoticeBo.getQuestionCode())) {
                    answer_002 = productHealthNoticeBo.getAnswer();
                    table_02 = "Y";
                }
                if ("34_QUESTION_003".equals(productHealthNoticeBo.getQuestionCode())) {
                    answer_003 = productHealthNoticeBo.getAnswer();
                    table_03 = "Y";
                }
            }
            isHealthNotice = true;
        }
        map.put("table_01", table_01);
        map.put("table_02", table_02);
        map.put("table_03", table_03);
        map.put("answer_001", answer_001);
        map.put("answer_002", answer_002);
        map.put("answer_003", answer_003);
        map.put("isHealthNotice", isHealthNotice);
        this.getLogger().info("投保单map数据----------------" + map);
        return map;
    }
}
