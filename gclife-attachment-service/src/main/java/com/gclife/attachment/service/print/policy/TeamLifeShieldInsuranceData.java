package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.group.*;
import com.gclife.attachment.model.policy.policy.group.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.PolicyConfigEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;


@Component
public class TeamLifeShieldInsuranceData extends BaseBusinessServiceImpl {
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private ITextPdfService iTextPdfService;

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();

        String content = electronicPolicyGeneratorRequest.getContent();
        GroupAttachApplyBo applyPrintBo = JSON.parseObject(content, GroupAttachApplyBo.class);
        GroupAttachApplyApplicantBo applicant = applyPrintBo.getGroupApplicant();

        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("planNo", PrintCommon.getPrintString(null, 3));
        /*****************************************团险公司********************************************************************/
        map.put("companyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        String companyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String companyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("companyAddressWhole", PrintCommon.getPrintString(companyAreaName + " " + companyAddress, 3));
        map.put("companyZipCode", PrintCommon.getPrintString(applicant.getCompanyZipCode(), 3));
        map.put("industryName", PrintCommon.getPrintString(applicant.getCompanyIndustryName(), 3));
        if (AssertUtils.isNotEmpty(applicant.getCompanyType())
                && !(
                PolicyConfigEnum.COMPANY_TYPE.ENTERPRISE.name().equals(applicant.getCompanyType()) || PolicyConfigEnum.COMPANY_TYPE.INSTITUTION.name().equals(applicant.getCompanyType()) ||
                        PolicyConfigEnum.COMPANY_TYPE.GOVERNMENT_INSTITUTION.name().equals(applicant.getCompanyType()) || PolicyConfigEnum.COMPANY_TYPE.BUSINESS_ORGANIZATION.name().equals(applicant.getCompanyType()) ||
                        PolicyConfigEnum.COMPANY_TYPE.SOCIAL_GROUP.name().equals(applicant.getCompanyType())
        )) {
            applicant.setCompanyType(PolicyConfigEnum.COMPANY_TYPE.OTHER.name());
        }
        PrintCommon.setSelectionBox(map, "company" + applicant.getCompanyType(), applicant.getCompanyType());
        GroupAttachApplyInsuredCollectBo applyInsuredCollect = applyPrintBo.getApplyInsuredCollect();
        if (!AssertUtils.isNotNull(applyInsuredCollect)) {
            applyInsuredCollect = new GroupAttachApplyInsuredCollectBo();
        }
        map.put("totalQuantity", PrintCommon.getPrintString(applyInsuredCollect.getTotalQuantity(), 3));
        map.put("effectiveQuantity", PrintCommon.getPrintString(applyInsuredCollect.getEffectiveQuantity(), 3));
        /*****************************************团险公司法人********************************************************************/
        map.put("companyLegalPersonName", PrintCommon.getPrintString(applicant.getCompanyLegalPersonName(), 3));
        map.put("companyLegalPersonIdType", PrintCommon.getPrintString(applicant.getCompanyLegalPersonIdType(), 3));
        //证件类型
        if (AssertUtils.isNotEmpty(applicant.getCompanyLegalPersonIdType()) &&
                !AttachmentTermEnum.ID_TYPE.ID.name().equals(applicant.getCompanyLegalPersonIdType()) &&
                !AttachmentTermEnum.ID_TYPE.PASSPORT.name().equals(applicant.getCompanyLegalPersonIdType())) {
            applicant.setCompanyLegalPersonIdType(AttachmentTermEnum.ID_TYPE.OTHER.name());
        }
        PrintCommon.setSelectionBox(map, "clpit" + applicant.getCompanyLegalPersonIdType(), applicant.getCompanyLegalPersonIdType());
        map.put("companyLegalPersonNationalityName", PrintCommon.getPrintString(applicant.getCompanyLegalPersonNationalityName(), 3));
        //TODO 是否长期
        PrintCommon.setPrintDateTime(map, "companyLegalPersonIdExpDate", applicant.getCompanyLegalPersonIdExpDate(), 3);
        map.put("companyLegalPersonIdNo", PrintCommon.getPrintString(applicant.getCompanyLegalPersonIdNo(), 3));
        /*****************************************团险公司联系人********************************************************************/
        map.put("companyContractDept", PrintCommon.getPrintString(applicant.getCompanyContractDept(), 3));
        map.put("companyContractPosition", PrintCommon.getPrintString(applicant.getCompanyContractPosition(), 3));
        map.put("companyContractNationalityName", PrintCommon.getPrintString(applicant.getCompanyContractNationalityName(), 3));
        map.put("companyContractName", PrintCommon.getPrintString(applicant.getCompanyContractName(), 3));
        map.put("companyContractIdNo", PrintCommon.getPrintString(applicant.getCompanyContractIdNo(), 3));
        map.put("companyContractPhone", PrintCommon.getPrintString(applicant.getCompanyContractMobile(), 3));
        map.put("companyContractEmail", PrintCommon.getPrintString(applicant.getCompanyContractEmail(), 3));
        PrintCommon.setPrintDateTime(map, "companyContractIdExpDate", applicant.getCompanyContractIdExpDate(), 3);

        //证件类型
        if (AssertUtils.isNotEmpty(applicant.getCompanyContractIdType()) &&
                !AttachmentTermEnum.ID_TYPE.ID.name().equals(applicant.getCompanyContractIdType()) &&
                !AttachmentTermEnum.ID_TYPE.PASSPORT.name().equals(applicant.getCompanyContractIdType())) {
            applicant.setCompanyContractIdType(AttachmentTermEnum.ID_TYPE.OTHER.name());
        }
        PrintCommon.setSelectionBox(map, "ccit" + applicant.getCompanyContractIdType(), applicant.getCompanyContractIdType());
        //是否长期
        /*****************************************险种信息********************************************************************/
        List<GroupAttachApplyCoverageBo> listGroupCoverage = applyPrintBo.getListGroupCoverage();
        String premiumFrequency = null;
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        for (GroupAttachApplyCoverageBo applyCoverageBo : listGroupCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            //险种名称
            PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId());
            //保险金额
            if (AssertUtils.isNotNull(applyCoverageBo.getTotalAmount()) && applyCoverageBo.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                applyCoverageBo.setTotalAmount(null);
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(applyCoverageBo.getTotalAmount(), 2));
            //保险期限
            //设置保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) ? applyCoverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName()) ? applyCoverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));

            //保险费金额
            coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
            //缴费期限
            String premiumPeriod = AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) ? applyCoverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName()) ? applyCoverageBo.getPremiumPeriodUnitName() : "";
            String payTerm = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                payTerm = premiumPeriodUnitName + premiumPeriod;
            }
            if (SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "Single Payment";
                }
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(payTerm, 2));
            if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                premiumFrequency = applyCoverageBo.getPremiumFrequency();
            }
            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("totalPremiumSum", PrintCommon.getPrintString(applyPrintBo.getTotalPremium(), 3));

        /******************************************************支付信息*****************************************************************/
        if (AssertUtils.isNotEmpty(premiumFrequency)
                && !AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)
                && !AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)
                && !AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)
                && !AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)
                && !AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            premiumFrequency = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "pf" + premiumFrequency, premiumFrequency);

        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AssertUtils.isNotEmpty(paymentMode) &&
                !AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode) &&
                !AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode) &&
                !AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "pm" + paymentMode, paymentMode);
        this.getLogger().debug("打印日志-------------001");
        List<GroupAttachApplyAccountBo> applyAccountList = applyPrintBo.getApplyAccountList();
        GroupAttachApplyAccountBo groupAttachApplyAccountBo = new GroupAttachApplyAccountBo();
        if (AssertUtils.isNotEmpty(applyAccountList)) {
            groupAttachApplyAccountBo = applyAccountList.get(0);
        }

        map.put("bankName", PrintCommon.getPrintString(groupAttachApplyAccountBo.getBankName(), 3));
        map.put("accountOwner", PrintCommon.getPrintString(groupAttachApplyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(groupAttachApplyAccountBo.getAccountNo(), 3));

        PrintCommon.setSelectionBox(map, "autoRenewalInsurance" + applyPrintBo.getAutoRenewalInsurance(), applyPrintBo.getAutoRenewalInsurance());
        /******************************************************支付信息*****************************************************************/
        GroupAttachApplyAgentBo groupAgent = applyPrintBo.getGroupAgent();


        String agentName = AssertUtils.isNotNull(groupAgent.getAgentName()) ? groupAgent.getAgentName() : "";
        String bracketsAgentCode = AssertUtils.isNotNull(groupAgent.getAgentCode()) ? "(" + groupAgent.getAgentCode() + ")" : "";
        map.put("agentNameAndAgentCode", PrintCommon.getPrintString(agentName + bracketsAgentCode, 3));

        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(groupAgent.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(groupAgent.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(groupAgent.getMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachPolicyBo policyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);
        Map<String, Object> map = new HashMap<>();
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /************************************************单位信息***************************************************/
        GroupAttachApplicantBo applicant = policyBo.getGroupApplicant();
        map.put("companyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        String companyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : " ";
        String companyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("companyAddress", PrintCommon.getPrintString(companyAreaName + " " + companyAddress, 3));
        map.put("companyZipCode", PrintCommon.getPrintString(applicant.getCompanyZipCode(), 3));
        map.put("companyPhone", PrintCommon.getPrintString(applicant.getCompanyPhone(), 3));
        map.put("industryName", PrintCommon.getPrintString(applicant.getCompanyIndustryName(), 3));
        map.put("companyContractName", PrintCommon.getPrintString(applicant.getCompanyContractName(), 3));
        map.put("companyContractMobile", PrintCommon.getPrintString(applicant.getCompanyContractMobile(), 3));
        /************************************************险种信息***************************************************/
        List<GroupAttachCoverageBo> coverageList = policyBo.getListGroupCoverage();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal allTotalPremium = new BigDecimal(0);
        boolean isProduct = false;
        List<String> productIds = coverageList.stream().map(GroupAttachCoverageBo::getProductId).collect(Collectors.toList());
        if(productIds.contains("PRO8800000000000G7")) {
            isProduct = true;
        }
        map.put("isProduct", isProduct);
        for (GroupAttachCoverageBo coverageBo : coverageList) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId());
            //保险金额
            if (AssertUtils.isNotNull(coverageBo.getTotalAmount()) && coverageBo.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                coverageBo.setTotalAmount(null);
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(coverageBo.getTotalAmount(), 2));
            //设置保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            map.put(coverageBo.getProductId() + "coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            if (AssertUtils.isNotNull(coverageBo.getTotalPremium())) {
                allTotalPremium = allTotalPremium.add(coverageBo.getTotalPremium());
            }
            String totalPremiumPrintString = PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2);
            coverageMap.put("totalPremium", totalPremiumPrintString);
            map.put(coverageBo.getProductId() + "totalPremium", totalPremiumPrintString);
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String payTerm = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                payTerm = premiumPeriodUnitName + premiumPeriod;
            }
            if (SINGLE.name().equals(coverageBo.getPremiumPeriodUnit())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    payTerm = "Single Payment";
                }
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(payTerm, 2));


            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        GroupAttachPolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new GroupAttachPolicyPremiumBo();
        }
        if (AssertUtils.isNotNull(policyPremium.getActualPremium())) {
            allTotalPremium = policyPremium.getActualPremium();
        }
        map.put("allTotalPremium", PrintCommon.getPrintString(allTotalPremium, 3));
        /************************************************特别约定***************************************************/
        List<GroupAttachSpecialContractBo> listSpecialContract = policyBo.getListSpecialContract();
        GroupAttachSpecialContractBo groupAttachSpecialContractBo = new GroupAttachSpecialContractBo();
        if (AssertUtils.isNotEmpty(listSpecialContract)) {
            groupAttachSpecialContractBo = listSpecialContract.get(0);
        }
        PrintCommon.getPolicySpecialContractContent(map, groupAttachSpecialContractBo.getSpecialContractContent());
        /************************************************代理人***************************************************/
        GroupAttachAgentBo agent = policyBo.getGroupAgent();
        String agentName = AssertUtils.isNotNull(agent.getAgentName()) ? agent.getAgentName() : "";
        String bracketsAgentCode = AssertUtils.isNotNull(agent.getAgentCode()) ? "(" + agent.getAgentCode() + ")" : "";
        map.put("agentNameAndAgentCode", PrintCommon.getPrintString(agentName + bracketsAgentCode, 3));
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(agent.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(agent.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(agent.getMobile(), 3));
        PrintCommon.setPrintDateTime(map, "effectiveDate", policyBo.getEffectiveDate(), 3);
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getEffectiveDate(), 3);
        /************************************************产品档次展示***************************************************/
        List<GroupAttachInsuredCoverageBo> listCoverage = new ArrayList<>();
        Map<String, Object> dutyLevelMap = new HashMap<>();

        List<GroupAttachInsuredBo> listGroupInsured = policyBo.getListGroupInsured();
        List<GroupAttachCoverageBo> listGroupCoverage = policyBo.getListGroupCoverage();
        if (AssertUtils.isNotEmpty(listGroupInsured)) {
            List<GroupAttachInsuredCoverageBo> finalListCoverage = listCoverage;
            listGroupInsured.stream().filter(bo -> AssertUtils.isNotEmpty(bo.getListCoverage())).forEach(bo -> finalListCoverage.addAll(bo.getListCoverage()));
        } else {
            listCoverage = JSON.parseArray(JSON.toJSONString(listGroupCoverage), GroupAttachInsuredCoverageBo.class);
        }
        listCoverage.forEach(coverage -> {
            putProductOrDutyLevel(dutyLevelMap, coverage, coverage.getProductId());
        });
        map.putAll(dutyLevelMap);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    private void putProductOrDutyLevel(Map<String, Object> dutyLevelMap, GroupAttachInsuredCoverageBo coverage, String productId) {
        List<GroupAttachInsuredCoverageDutyBo> listCoverageDuty = coverage.getListCoverageDuty();
        List<GroupAttachInsuredCoverageLevelBo> listCoverageLevel = coverage.getListCoverageLevel();
        listCoverageLevel.forEach(leveBo -> {
            String dutyId = "";
            if (AssertUtils.isNotEmpty(listCoverageDuty)) {
                Optional<GroupAttachInsuredCoverageDutyBo> first = listCoverageDuty.stream().filter(groupAttachInsuredCoverageDutyBo -> groupAttachInsuredCoverageDutyBo.getCoverageDutyId().equals(leveBo.getCoverageDutyId())).findFirst();
                if (first.isPresent()) {
                    dutyId = first.get().getDutyId();
                    dutyLevelMap.put(dutyId, dutyId);
                }
            }
            String id = AssertUtils.isNotEmpty(dutyId) ? dutyId : productId;
            dutyLevelMap.put(id + leveBo.getProductLevel(), leveBo.getProductLevel());
        });
    }

    /**
     * 获取保单
     *
     * @param electronicPolicyGeneratorRequest
     * @param policyData
     * @return
     * @throws Exception
     */
    public byte[] getPolicyPdfBytes(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, Map<String, Object> policyData) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        //保险证 1
        List<byte[]> policyBookPdfBytesList = new ArrayList<>();
        if (AssertUtils.isNotNull(policyData.get("PRO8800000000000G3")) && (!AssertUtils.isNotNull(policyData.get("PRO8800000000000G7")))) {
            PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
            byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
            policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);
            //设置 签名 和 公司盖章 start
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                PdfStamper pdfStamper = new PdfStamper(new PdfReader(policyBookBytes), byteArrayOutputStream);
                PdfContentByte canvas = pdfStamper.getOverContent(1);
                //添加 CEO 签名
                canvas.addImage(PrintCommon.addImage(PrintCommon.CEO_Signature, 62.93f, 41.67f, 366.06f, 649.09f));
                //添加公司盖章
                canvas.addImage(PrintCommon.addImage(PrintCommon.Company_Stamp, 138.09f, 87.33f, 461.44f, 615.72f));
                pdfStamper.close();
                policyBookBytes = byteArrayOutputStream.toByteArray();
            }
            policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
            policyBookPdfBytesList.add(policyBookBytes);
        }
        //保险证 7
        if (AssertUtils.isNotNull(policyData.get("PRO8800000000000G7"))) {
            String endorse = "GROUP_ADD_ADDITIONAL".equals(electronicPolicyGeneratorRequest.getPdfType()) || "ADD_SUBTRACT_INSURED".equals(electronicPolicyGeneratorRequest.getPdfType()) ? "ENDORSE_" : "";
            byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId(endorse + "PRO8800000000000G7_POLICY_" + language);
            policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);
            //设置 签名 和 公司盖章 start
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                PdfStamper pdfStamper = new PdfStamper(new PdfReader(policyBookBytes), byteArrayOutputStream);
                PdfContentByte canvas = pdfStamper.getOverContent(1);
                //添加 CEO 签名
                canvas.addImage(PrintCommon.addImage(PrintCommon.CEO_Signature, 62.93f, 41.67f, 366.06f, 619.09f));
                //添加公司盖章
                canvas.addImage(PrintCommon.addImage(PrintCommon.Company_Stamp, 138.09f, 87.33f, 461.44f, 585.72f));
                pdfStamper.close();
                policyBookBytes = byteArrayOutputStream.toByteArray();
            }
            policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
            policyBookPdfBytesList.add(policyBookBytes);
        }

        //保险证 11
        if (AssertUtils.isNotNull(policyData.get("PRO8800000000000G11"))) {
            byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId("PRO8800000000000G11_POLICY_" + language);
            policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);

            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                PdfStamper pdfStamper = new PdfStamper(new PdfReader(policyBookBytes), byteArrayOutputStream);
                PdfContentByte canvas = pdfStamper.getOverContent(1);
                //添加 CEO 签名
                canvas.addImage(PrintCommon.addImage(PrintCommon.CEO_Signature, 62.93f, 41.67f, 366.06f - 27f, 626.09f));
                //添加公司盖章
                canvas.addImage(PrintCommon.addImage(PrintCommon.Company_Stamp, 138.09f, 87.33f, 461.44f - 27f, 592.72f));
                pdfStamper.close();
                policyBookBytes = byteArrayOutputStream.toByteArray();
            }
            policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
            policyBookPdfBytesList.add(policyBookBytes);

        }

        //保险证 12
        if (AssertUtils.isNotNull(policyData.get("PRO8800000000000G12"))) {
            byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId("PRO8800000000000G12_POLICY_" + language);
            policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);

            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                PdfStamper pdfStamper = new PdfStamper(new PdfReader(policyBookBytes), byteArrayOutputStream);
                PdfContentByte canvas = pdfStamper.getOverContent(1);
                //添加 CEO 签名
                canvas.addImage(PrintCommon.addImage(PrintCommon.CEO_Signature, 62.93f, 41.67f, 366.06f - 27f, 626.09f));
                //添加公司盖章
                canvas.addImage(PrintCommon.addImage(PrintCommon.Company_Stamp, 138.09f, 87.33f, 461.44f - 27f, 592.72f));
                pdfStamper.close();
                policyBookBytes = byteArrayOutputStream.toByteArray();
            }
            policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
            policyBookPdfBytesList.add(policyBookBytes);

        }
        return PrintCommon.mergePdfFiles(policyBookPdfBytesList);
    }

}
