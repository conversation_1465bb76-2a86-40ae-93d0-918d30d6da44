package com.gclife.attachment.service.business.impl;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.OSSObject;
import com.gclife.attachment.aliyun.OssOptionService;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.config.AttachmentTypeConfigEnum;
import com.gclife.attachment.model.request.AttachmentBatchRequest;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.business.ImageZoomBusinessService;
import com.gclife.attachment.service.common.AttachmentCommonBusinessService;
import com.gclife.attachment.service.data.AttachmentService;
import com.gclife.attachment.validate.parameter.AttachmentParameterValidate;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 图片缩放操作实现类
 */
@Service
public class ImageZoomBusinessServiceImpl extends BaseBusinessServiceImpl implements ImageZoomBusinessService {


    @Autowired
    AttachmentExtDao attachmentResponse;

    @Autowired
    OssConfigExtDao ossConfigExtDao;

    @Autowired
    AttachmentCommonBusinessService attachmentCommonBusinessService;


    /**
     * 下载媒体文件 (缩放) 返回图片地址
     *
     * @param users 当前登录用户
     * @param mediaId 媒体ID
     * @param width 图片宽度
     * @param heigth 图片高度
     * @return ResultObject<AttachmentResponse>
     */
    @Override
    public ResultObject<AttachmentResponse> loadMediaRouteFixedFrame(Users users, String mediaId, long width, long heigth) {
        ResultObject<AttachmentResponse> resultObject=new ResultObject<AttachmentResponse>();
        try {
            /**
             * TODO:
             * 1.根据传入的媒体ID获取资源路径
             */
            /**
             *获取配置文件
             */
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(this.getLogger(),ossConfigBo,AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(),mediaId,AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_MEDIA_ID_IS_NOT_NULL);
            //查询资源
            AttachmentPo attachmentPo = attachmentResponse.loadAttachmentPo(mediaId);
            //资源验证
            AssertUtils.isNotNull(this.getLogger(),attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //查看文件后缀
            List<String> suffixList =  Arrays.stream(AttachmentTermEnum.MEDIA_TYPE_SUFFIX.values()).filter(e->e.group().equals(AttachmentTermEnum.MEDIA_TYPE.IMAGE.name())).map(AttachmentTermEnum.MEDIA_TYPE_SUFFIX::name).collect(Collectors.toList());
            //验证后缀
            if(suffixList.stream().noneMatch(e->e.equals(attachmentPo.getFileSuffix().toUpperCase()))){
                throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_ZOOM);
            }
            //设置返回数据
            AttachmentResponse attachmentResponse = new AttachmentResponse();
            attachmentResponse.setMediaId(attachmentPo.getAttachmentId());
            //获取缩放后缀
            String suffix = attachmentCommonBusinessService.getImageZoomSuffix(AttachmentTermEnum.IMAGE_ZOOM_TYPE.ZOOM_FIXED_FRAME,heigth+"",width+"",null);
            //从配置文件中读取配置
            attachmentResponse.setUrl(getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.")+attachmentPo.getUrl()+suffix);
            resultObject.setData(attachmentResponse);
        }catch (Exception e){
            if(e instanceof RequestException){
                RequestException error  =(RequestException)e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            }else {
                resultObject.setIenum(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
            }

        }
        return  resultObject;
    }


}
