package com.gclife.attachment.service.print.renewal;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.policy.group.GroupAttachApplicantBo;
import com.gclife.attachment.model.policy.renewal.AgentBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceConfirmPrintBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceCoveragePrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:56 2019/3/18
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class GroupRenewalData {

    public Map<String, Object> getGroupRenewalData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> stringObjectMap = new HashMap<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        RenewalInsuranceConfirmPrintBo renewalInsuranceConfirmPrintBo = JSON.parseObject(content, RenewalInsuranceConfirmPrintBo.class);
        PrintCommon.setPrintDateTime(stringObjectMap, "systemPrintDate", DateUtils.getCurrentTime(), 3);
        stringObjectMap.put("policyNo", PrintCommon.getPrintString(renewalInsuranceConfirmPrintBo.getPolicyNo(), 3));
        AgentBo agent = renewalInsuranceConfirmPrintBo.getAgent();
        if(!AssertUtils.isNotNull(agent)){
            agent = new AgentBo();
        }
        stringObjectMap.put("agentCode", PrintCommon.getPrintString(agent.getAgentCode(), 3));
        GroupAttachApplicantBo groupApplicant = renewalInsuranceConfirmPrintBo.getGroupApplicant();
        stringObjectMap.put("companyName", PrintCommon.getPrintString(groupApplicant.getCompanyName(), 3));

        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<RenewalInsuranceCoveragePrintBo> coverageBoList = renewalInsuranceConfirmPrintBo.getListGroupCoverage();
        final AtomicReference<BigDecimal>[] totalPremium = new AtomicReference[]{new AtomicReference<>(new BigDecimal(0))};
        coverageBoList.forEach(coverageBo -> {
            Map<String, Object> map = new HashMap<>();
            PrintCommon.setProductName(map, coverageBo.getProductId());
            PrintCommon.setPrintDateTime(map, "coveragePeriodStartDate", coverageBo.getCoveragePeriodStartDate(), 3);
            PrintCommon.setPrintDateTime(map, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            map.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 3));
            if (AssertUtils.isNotNull(coverageBo.getTotalPremium())) {
                totalPremium[0].set(totalPremium[0].get().add(coverageBo.getTotalPremium()));
            }
            String premiumPeriodUnitName = PrintCommon.getPremiumPeriodUnitName(language,coverageBo.getPremiumPeriod(),coverageBo.getPremiumPeriodUnit(),coverageBo.getPremiumPeriodUnitName(),coverageBo.getPremiumFrequency());
            map.put("premiumPeriodUnitName", PrintCommon.getPrintString(premiumPeriodUnitName, 3));
            coverageListMap.add(map);
        });
        PrintCommon.coverageSort(coverageListMap);
        stringObjectMap.put("coverageListMap", coverageListMap);

        stringObjectMap.put("totalPremiumAll", PrintCommon.getPrintString(totalPremium[0], 3));
        PrintCommon.setPrintDateTime(stringObjectMap, "effectiveDate", renewalInsuranceConfirmPrintBo.getEffectiveDate(), 3);
        // 公司基础信息
        stringObjectMap.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return stringObjectMap;
    }
}
