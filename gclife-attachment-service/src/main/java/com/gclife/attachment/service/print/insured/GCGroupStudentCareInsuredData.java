package com.gclife.attachment.service.print.insured;


import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.apply.ApplyBeneficiaryBo;
import com.gclife.attachment.model.policy.apply.ApplyCoverageBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.policy.apply.group.ApplyGroupHealthQuestionnaireAnswerBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyApplicantBo;
import com.gclife.attachment.model.policy.apply.group.PrintApplyInsuredBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.common.PrintCommon.sdfEN_US29;

/**
 * <AUTHOR>
 * @date 2023/08/08
 */
@Component
public class GCGroupStudentCareInsuredData {
    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String renewalFlag = electronicPolicyGeneratorRequest.getRenewalFlag();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PrintApplyInsuredBo applyPrintBo = JSON.parseObject(content, PrintApplyInsuredBo.class);
        GroupAttachApplyApplicantBo groupApplicant = applyPrintBo.getGroupApplicant();
        List<ApplyGroupHealthQuestionnaireAnswerBo> aghqaList = applyPrintBo.getAghqaList();
        //区分续保和投保单被保人清单
        if (AssertUtils.isNotEmpty(renewalFlag)) {
            map.put("applyNo", applyPrintBo.getPolicyNo());
            map.put("applyNameKM_KH","លេខបណ្ណសន្យារ៉ាប់រង");
            map.put("applyNameEN_US","Policy No.");
            map.put("applyNameZH_CN","保单编号");
        }else {
            map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
            map.put("applyNameKM_KH","លេខពាក្យស្នើសុំ");
            map.put("applyNameEN_US","Application No.");
            map.put("applyNameZH_CN","投保单编号");
        }
        //业务员信息
        map.put("companyName", PrintCommon.getPrintString(applyPrintBo.getCompanyName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyPrintBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyPrintBo.getAgentMobile(), 3));
        map.put("agentName", PrintCommon.getPrintString(applyPrintBo.getAgentName(), 3));
        //代表信息
        map.put("delegateName", PrintCommon.getPrintString(groupApplicant.getDelegateName(), 3));
        map.put("delegateMobile", PrintCommon.getPrintString(groupApplicant.getDelegateMobile(), 3));

        Map<String, Object> productMap = new HashMap<>();
        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        BigDecimal totalPremium = BigDecimal.ZERO;
        int insuredNo = 1;
        // 被保人集合
        List<ApplyInsuredBo> applyInsuredBos = applyPrintBo.getApplyInsuredList();
        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            // 被保人ID
            String insuredId = applyInsuredBo.getInsuredId();
            // 设置受益人信息
            List<ApplyBeneficiaryBo> applyBeneficiaryBos = applyPrintBo.getApplyBeneficiaryBoList();
            int i = 1;
            for (ApplyBeneficiaryBo applyBeneficiaryBo : applyBeneficiaryBos) {
                if (insuredId.equals(applyBeneficiaryBo.getInsuredId())) {
                    HashMap<String, Object> insuredMap = new HashMap<>();
                    // 被保人 总保费
                    BigDecimal insuredPremium = BigDecimal.ZERO;
                    BigDecimal insuredAmount = BigDecimal.ZERO;
                    BigDecimal insuredPremium29 = BigDecimal.ZERO;
                    BigDecimal insuredAmount29 = BigDecimal.ZERO;
                    BigDecimal insuredPremium33 = BigDecimal.ZERO;
                    BigDecimal insuredAmount33 = BigDecimal.ZERO;
                    insuredMap.put("name", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                    insuredMap.put("idNo", PrintCommon.getPrintString(applyInsuredBo.getIdNo(), 3));
                    String dateEN_US = sdfEN_US29.format(applyInsuredBo.getBirthday());
                    insuredMap.put("birthday" + "EN_US", PrintCommon.getPrintString(dateEN_US, 3));
                    insuredMap.put("birthday" + "ZH_CN", PrintCommon.getPrintString(dateEN_US, 3));
                    Integer ageYear = null;
                    if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                        try {
                            ageYear = DateUtils.getAgeYear(new Date(applyInsuredBo.getBirthday()), new Date(applyPrintBo.getApplyDate()));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new RequestException();
                        }
                    }
                    insuredMap.put("ageYear", PrintCommon.getPrintString(ageYear, 3));
                    insuredMap.put("sexName", PrintCommon.getPrintString(applyInsuredBo.getSexName(), 3));
                    //健康告知
                    final String[] answerCodeJoin = {null};
                    if (AssertUtils.isNotEmpty(aghqaList)) {
                        aghqaList.stream().filter(aghqaBo -> applyInsuredBo.getInsuredId().equals(aghqaBo.getInsuredId()) && AssertUtils.isNotEmpty(aghqaBo.getAnswer())).forEach(aghqaBo -> {
                            answerCodeJoin[0] = (AssertUtils.isNotEmpty(answerCodeJoin[0]) ? answerCodeJoin[0] + "," : "") + aghqaBo.getAnswer();
                        });
                    }
                    insuredMap.put("answerCodeJoin", PrintCommon.getPrintString(answerCodeJoin[0], 3));
                    // 被保人的险种
                    List<ApplyCoverageBo> applyCoverageBos = applyPrintBo.getApplyCoverageList();
                    for (ApplyCoverageBo applyCoverageBo : applyCoverageBos) {
                        if (insuredId.equals(applyCoverageBo.getInsuredId())) {
                            BigDecimal coverageBoTotalPremium = applyCoverageBo.getTotalPremium();
                            String amount = applyCoverageBo.getAmount();
                            // 如果 [amount 保额]为null，设置为"0"
                            if (amount == null) {
                                amount = "0";
                            }
                            // 被保人 总保费
                            insuredPremium = insuredPremium.add(coverageBoTotalPremium);
                            insuredAmount = insuredAmount.add(new BigDecimal(amount));

                            String productId = applyCoverageBo.getProductId();
                            // 29号产品
                            if ("PRO880000000000029".equals(productId)) {
                                //保额
                                insuredAmount29 = insuredAmount29.add(new BigDecimal(amount));
                                insuredPremium29 = insuredPremium29.add(coverageBoTotalPremium);
                            }
                            // 33号产品
                            if ("PRO880000000000033".equals(productId)) {
                                //保额
                                insuredAmount33 = insuredAmount33.add(new BigDecimal(amount));
                                insuredPremium33 = insuredPremium33.add(coverageBoTotalPremium);
                            }
                        }
                    }
                    if (i == 2) {
                        insuredMap.put("insuredPremium", PrintCommon.getPrintString(null, 3));
                        insuredMap.put("insuredAmount", PrintCommon.getPrintString(null, 3));
                        insuredPremium = new BigDecimal(0);
                        insuredAmount = new BigDecimal(0);
                        insuredPremium29 = new BigDecimal(0);
                        insuredAmount29 = new BigDecimal(0);
                        insuredPremium33 = new BigDecimal(0);
                        insuredAmount33 = new BigDecimal(0);
                        //29号产品特殊处理，同属一个人的数据序号一样，不自增
                        insuredMap.put("insuredNo", insuredNo);
                    }else {
                        insuredMap.put("insuredPremium", PrintCommon.getPrintString(insuredPremium, 3));
                        insuredMap.put("insuredAmount", PrintCommon.getPrintString(insuredAmount, 3));
                        insuredMap.put("insuredPremium29", PrintCommon.getPrintString(insuredPremium29, 3));
                        insuredMap.put("insuredAmount29", PrintCommon.getPrintString(insuredAmount29, 3));
                        insuredMap.put("insuredPremium33", PrintCommon.getPrintString(insuredPremium33, 3));
                        insuredMap.put("insuredAmount33", PrintCommon.getPrintString(insuredAmount33, 3));
                        //29号产品特殊处理，同属一个人的数据序号一样，不自增
                        insuredMap.put("insuredNo", insuredNo);
                    }

                    // 保费总额
                    totalPremium = totalPremium.add(insuredPremium);
                    // 一个被保人可能有多个受益人
                    insuredMap.put("beneficiaryName", PrintCommon.getPrintString(applyBeneficiaryBo.getName(), 3));
                    insuredMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 3));
                    insuredMap.put("relationshipName", PrintCommon.getPrintString(applyBeneficiaryBo.getRelationshipName(), 3));
                    insuredMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryBo.getBeneficiaryProportion() + "%", 3));
                    insuredMapList.add(insuredMap);
                    i++;

                }
            }
            insuredNo++;

        }
        // 被保人
        productMap.put("insuredMapList", insuredMapList);
        map.put("insuredSum", PrintCommon.getPrintString(applyInsuredBos.size(), 3));
        map.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));
        productMap.putAll(map);
        return productMap;
    }
}
