package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @date 2021/12/29
 */
@Component
public class GCNearyCareData {

    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        String mainProductId = electronicPolicyGeneratorRequest.getProductId();

        PrintCommon.setProductName(map, mainProductId, "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            if ("PRO880000000000021".equals(coverageBo.getProductId())) {
                maturityAmount = new BigDecimal("0.5").multiply(new BigDecimal(coverageBo.getTotalAmount()));
            }
            coverageMap.put("maturityAmount", PrintCommon.getPrintAmountString(maturityAmount, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Payment";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        /***********************************现金价值******************************************/
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                Map<String, Object> productCashValue = new HashMap<>();
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                policyCashValuesListMap.add(productCashValue);
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  可选其他缴费周期start***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal mainTotalAmount = BigDecimal.ZERO;
        String mainPremiumPeriod = null;
        String mainPremiumFrequency = null;
        String mainCoveragePeriod = null;
        String mainMult = null;
        String mult14 = null;
        String mult15 = null;

        BigDecimal totalAmount14 = BigDecimal.ZERO;
        String productLevel14 = null;
        BigDecimal totalAmount15 = BigDecimal.ZERO;
        BigDecimal totalPremium15 = BigDecimal.ZERO;
        BigDecimal termProtectionRiderAmountA = BigDecimal.ZERO;// #23A号附加险
        BigDecimal termProtectionRiderAmountB = BigDecimal.ZERO;// #23B号附加险
        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintAmountString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = null;
            if ("PRO880000000000021".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mainTotalAmount = amount;
                mainPremiumPeriod = premiumPeriod;
                mainCoveragePeriod = coveragePeriod;
                mainPremiumFrequency = coverageBo.getPremiumFrequency();
                mainMult = coverageBo.getMult();
            } else if ("PRO880000000000014".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                totalAmount14 = amount;
                productLevel14 = productLevel;
                mult14 = coverageBo.getMult();
                if ("ACCELERATION_CI".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(提前给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                    coverageMap.put("productLevelEN_US", "(Acceleration)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(额外给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                    coverageMap.put("productLevelEN_US", "(Additional)");
                }
            } else if ("PRO880000000000015".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mult15 = coverageBo.getMult();
                totalAmount15 = amount;
            }
            if ("PRO880000000000016A".equals(coverageBo.getProductId()) || "PRO880000000000016B".equals(coverageBo.getProductId())) {
                map.put("PRO880000000000016", "PRO880000000000016");
            }
            if ("PRO880000000000023A".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountA = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023AOPTION_TWO", "PRO880000000000023AOPTION_TWO");
                }
            }
            if ("PRO880000000000023B".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountB = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023BOPTION_TWO", "PRO880000000000023BOPTION_TWO");
                }
            }
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            //每期保费
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int i = YEAR.name().equals(premiumFrequency) ? 1 : SEMIANNUAL.name().equals(premiumFrequency) ? 2 : SEASON.name().equals(premiumFrequency) ? 3 : MONTH.name().equals(premiumFrequency) ? 4 : 0;
            coverageMap.put("totalPremium" + i, PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            //可选缴费周期保险费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 1, coverageMap, cpfList, YEAR.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 2, coverageMap, cpfList, SEMIANNUAL.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 3, coverageMap, cpfList, SEASON.name(), premiumFrequency);
            // ProductCalculation.getPremiumFrequencyTotalPremium(map, 4, coverageMap, cpfList, MONTH.name(), premiumFrequency);
            // 21号产品 没有月缴
            coverageMap.put("totalPremium4", PrintCommon.getPrintString(null, 2));
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            BigDecimal maturityAmount = yearTotalPremium.multiply(new BigDecimal(coverageBo.getPremiumPeriod())).setScale(2, RoundingMode.HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", mainPremiumFrequency);
        double totalPremium1 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium1") + "").replace(",", ""))).sum();
        double totalPremium2 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium2") + "").replace(",", ""))).sum();
        double totalPremium3 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium3") + "").replace(",", ""))).sum();
        // double totalPremium4 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium4") + "").replace(",", ""))).sum();
        map.put("allTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2), 2));
        map.put("allTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3), 2));
        map.put("allTotalPremium4", PrintCommon.getPrintString(null, 2)); // 21号产品 没有月缴
        map.put("allYearTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allYearTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2).multiply(new BigDecimal(2)), 2));
        map.put("allYearTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3).multiply(new BigDecimal(4)), 2));
        map.put("allYearTotalPremium4", PrintCommon.getPrintString(null, 2)); // 21号产品 没有月缴
        /****************************************************************************保险利益***********************************************************************************/
        BigDecimal tpd_benefit_due_to_accident = mainTotalAmount.add(totalAmount15).add(termProtectionRiderAmountA);
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 2));
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintAmountString(mainTotalAmount.add(termProtectionRiderAmountA), 2));

        // 主险 + 附加险的 死亡或高度残疾保险金
        BigDecimal death_benefit_due_to_accident = mainTotalAmount.add(totalAmount15.multiply(new BigDecimal(3))).add(termProtectionRiderAmountA);
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintAmountString(death_benefit_due_to_accident, 2));
        // 保单付款人意外高残保险金
        BigDecimal payorTPDAccidentAmount = termProtectionRiderAmountB;
        map.put("payorTPDAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAccidentAmount)
                , 2));
        // 保单付款人高残保险金
        BigDecimal payorTPDAmount = termProtectionRiderAmountB;
        map.put("payorTPDAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAccidentAmount = termProtectionRiderAmountB;
        map.put("payorDeathAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAccidentAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAmount = termProtectionRiderAmountB;
        map.put("payorDeathAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAmount)
                , 2));
        // 保单付款人晚期重大疾病保险金
        BigDecimal payorLateStageCriticalIllnessAmount = termProtectionRiderAmountB;
        map.put("payorLateStageCriticalIllnessAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorLateStageCriticalIllnessAmount)
                , 2));
        BigDecimal benefitsAmount1 = mainTotalAmount;
        BigDecimal benefitsAmount2 = new BigDecimal("0.02").multiply(mainTotalAmount);
        BigDecimal maxBenefitsAmount2 = new BigDecimal("2").multiply(benefitsAmount2);
        BigDecimal benefitsAmount3 = new BigDecimal("0.1").multiply(mainTotalAmount);
        BigDecimal benefitsAmount4 = new BigDecimal("0.01").multiply(mainTotalAmount);
        BigDecimal benefitsAmount5 = new BigDecimal("0.02").multiply(mainTotalAmount);
        BigDecimal totalHealthyAmount = BigDecimal.ZERO;
        BigDecimal benefitsAmount6 = new BigDecimal("0.25").multiply(mainTotalAmount);
        BigDecimal benefitsAmount7 = mainTotalAmount;
        BigDecimal benefitsAmount8 = new BigDecimal("0.5").multiply(mainTotalAmount);
        List<Map<String, Object>> individualizationDatas = planProductDetail.getIndividualizationDatas();
        if (AssertUtils.isNotEmpty(individualizationDatas)) {
            Optional<Map<String, Object>> product21MapOptional = individualizationDatas.stream()
                    .filter(individualizationData -> "PRO880000000000021".equals(individualizationData.get("productId")))
                    .findFirst();

            if (product21MapOptional.isPresent()) {
                Map<String, Object> product21Map = product21MapOptional.get();
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount1"))) {
                    benefitsAmount1 = new BigDecimal((product21Map.get("benefitsAmount1") + "").replace(",", ""));// 主险 死亡或高度残疾保险金
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount2"))) {
                    benefitsAmount2 = new BigDecimal((product21Map.get("benefitsAmount2") + "").replace(",", ""));// 主险 满月保险金
                    maxBenefitsAmount2 = benefitsAmount2.multiply(new BigDecimal("2"));
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount3"))) {
                    benefitsAmount3 = new BigDecimal((product21Map.get("benefitsAmount3") + "").replace(",", ""));// 主险 祝贺金
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount4"))) {
                    benefitsAmount4 = new BigDecimal((product21Map.get("benefitsAmount4") + "").replace(",", ""));// 主险 健康保险金 6 ~ 14
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount5"))) {
                    benefitsAmount5 = new BigDecimal((product21Map.get("benefitsAmount5") + "").replace(",", ""));// 主险 健康保险金 16 ~ 24
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount6"))) {
                    benefitsAmount6 = new BigDecimal((product21Map.get("benefitsAmount6") + "").replace(",", ""));// 主险 女性早期重大疾病保险金
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount7"))) {
                    benefitsAmount7 = new BigDecimal((product21Map.get("benefitsAmount7") + "").replace(",", ""));// 主险 女性晚期重大疾病保险金
                }
                if (AssertUtils.isNotNull(product21Map.get("benefitsAmount8"))) {
                    benefitsAmount8 = new BigDecimal((product21Map.get("benefitsAmount8") + "").replace(",", ""));// 主险 满期保险金
                }
            }
        }
        map.put("benefitsAmount1", PrintCommon.getPrintAmountString(benefitsAmount1, 2));
        map.put("benefitsAmount2", PrintCommon.getPrintAmountString(benefitsAmount2, 2));
        map.put("maxBenefitsAmount2", PrintCommon.getPrintAmountString(maxBenefitsAmount2, 2));
        map.put("benefitsAmount3", PrintCommon.getPrintAmountString(benefitsAmount3, 2));
        map.put("benefitsAmount4", PrintCommon.getPrintAmountString(benefitsAmount4, 2));
        map.put("benefitsAmount5", PrintCommon.getPrintAmountString(benefitsAmount5, 2));
        map.put("benefitsAmount6", PrintCommon.getPrintAmountString(benefitsAmount6, 2));
        map.put("benefitsAmount7", PrintCommon.getPrintAmountString(benefitsAmount7, 2));
        // 早期重大疾病保险金 附加险
        BigDecimal totalAmount14Multiply25 = totalAmount14.multiply(new BigDecimal("0.25"));
        // 女性早期重大疾病保险金 主险 + 附加险
        map.put("benefitsAmount6AddTotalAmount14Multiply25", PrintCommon.getPrintAmountString(benefitsAmount6.add(totalAmount14Multiply25), 2));
        map.put("benefitsAmount8", PrintCommon.getPrintAmountString(benefitsAmount8, 2));

        map.put("death_benefit_non_accident", PrintCommon.getPrintAmountString(mainTotalAmount, 2));
        map.put("early_stage_critical_illness_benefit", PrintCommon.getPrintAmountString(totalAmount14Multiply25, 2));
        map.put("late_stage_critical_illness_benefit", PrintCommon.getPrintAmountString(totalAmount14, 2));
        BigDecimal womenLateStageCriticalIllness = benefitsAmount7.add(totalAmount14);
        // 被保险人晚期重大疾病保险金
        BigDecimal insuredLateStageCriticalIllness = totalAmount14;
        if ("OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            insuredLateStageCriticalIllness = insuredLateStageCriticalIllness.add(termProtectionRiderAmountA);
            womenLateStageCriticalIllness = womenLateStageCriticalIllness.add(termProtectionRiderAmountA);
        }
        map.put("insuredLateStageCriticalIllness", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        insuredLateStageCriticalIllness)
                , 2));
        // 女性晚期重大疾病保险金 主险 + 附加险
        map.put("benefitsAmount7AddTotalAmount14", PrintCommon.getPrintAmountString(womenLateStageCriticalIllness, 2));

        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int mainCoveragePeriodi = Integer.parseInt(mainCoveragePeriod);
        BigDecimal accumulatedPremiumsPaid = BigDecimal.ZERO;
        for (int i = 1; i <= mainCoveragePeriodi; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            interestMap.put("interestMapMainTotalAmount", PrintCommon.getPrintAmountDefaultStr(mainTotalAmount));
            BigDecimal survivalAmount = BigDecimal.ZERO;
            if (5 == i) {
                survivalAmount = survivalAmount.add(benefitsAmount3);
            }
            // 最后一年不给健康保险金
            if (mainCoveragePeriodi != i) {
                if (6 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (8 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (10 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (12 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (14 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (16 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (18 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (20 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (22 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (24 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
            }
            if (mainCoveragePeriodi == i) {
                survivalAmount = survivalAmount.add(benefitsAmount8);
            }
            // 意外死亡保险金 意外高度残疾保险金
            interestMap.put("mainTotalAmountAddTotalAmount15", PrintCommon.getPrintAmountDefaultStr(tpd_benefit_due_to_accident));
            // 生存保险金
            interestMap.put("survivalAmount", PrintCommon.getPrintAmountDefaultStr(survivalAmount));
            // 女性重大疾病保险金
            interestMap.put("interestMapBenefitsAmount7AddTotalAmount14", PrintCommon.getPrintAmountDefaultStr(womenLateStageCriticalIllness));
            interestMap.put("totalAmount14", PrintCommon.getPrintAmountDefaultStr(totalAmount14));
            interestMap.put("totalAmount15", PrintCommon.getPrintAmountDefaultStr(totalAmount15));
            // 意外死亡保险金
            interestMap.put("mainTotalAmountAddTotalAmount15Multiply3", PrintCommon.getPrintAmountDefaultStr(death_benefit_due_to_accident));
            interestMap.put("totalAmount15multiply3", PrintCommon.getPrintString("$", totalAmount15.multiply(new BigDecimal(3)), 1));
            interestMap.put("mainTotalAmount_15multiply3", PrintCommon.getPrintString("$", mainTotalAmount.add(totalAmount15.multiply(new BigDecimal(3))), 1));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            interestMap.put("interestMapPRO880000000000014", map.get("PRO880000000000014"));
            accumulatedPremiumsPaid = policyYearTotalPremium;// policyYearTotalPremium 就是累计已交保费
            // 累计已交保费
            interestMap.put("accumulatedPremiumsPaid", PrintCommon.getPrintString("$", accumulatedPremiumsPaid, 1));
            // 死亡或高残保险金
            interestMap.put("deathOrTPDBenefitsForInsuredAmount", PrintCommon.getPrintAmountDefaultStr(mainTotalAmount.add(termProtectionRiderAmountA)));
            // 保单付款人死亡或高残或重疾保险金
            interestMap.put("deathOrTPDOrCIBenefitsForPayorAmount", PrintCommon.getPrintAmountDefaultStr(termProtectionRiderAmountB));
            // 被保险人意外高残保险金
            interestMap.put("insuredAccidentalTPD", PrintCommon.getPrintAmountDefaultStr(tpd_benefit_due_to_accident));
            // 被保险人最高意外死亡保险金
            interestMap.put("insuredAccidentalDeathUpTo", PrintCommon.getPrintAmountDefaultStr(death_benefit_due_to_accident));
            interestMap.put("insuredMapInsuredCriticalIllness", PrintCommon.getPrintAmountDefaultStr(insuredLateStageCriticalIllness));
            for (ApplyCoveragePlanBo applyCoveragePlanBo : listCoverage) {
                String productId = applyCoveragePlanBo.getProductId();
                interestMap.put("interestMap" + productId, productId);
            }
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO880000000000021".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                BigDecimal mainCashValue = ProductCalculation.getCashValue(listCashValue, "PRO880000000000021", i);
                BigDecimal cashValue14 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000014", i);
                BigDecimal cashValue15 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000015", i);
                interestMap.put("mainCashValue", PrintCommon.getPrintString("$", mainCashValue, 1));
                interestMap.put("cashValue14", PrintCommon.getPrintString("$", cashValue14, 1));
                interestMap.put("cashValue15", PrintCommon.getPrintString("$", cashValue15, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", mainCashValue.add(cashValue14).add(cashValue15), 1));
            }
            interestListMap.add(interestMap);
        }
        // 总的健康保险金
        map.put("totalHealthyAmount", PrintCommon.getPrintAmountString(totalHealthyAmount, 2));

        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("mainTotalAmount", PrintCommon.getPrintAmountString(mainTotalAmount, 2));
        map.put("totalAmount14", PrintCommon.getPrintAmountString(totalAmount14, 2));
        map.put("totalAmount14multiply_25", PrintCommon.getPrintAmountString(totalAmount14Multiply25, 2));
        map.put("totalAmount14multiply_75", PrintCommon.getPrintAmountString(totalAmount14.multiply(new BigDecimal("0.75")), 2));
        map.put("totalAmount15", PrintCommon.getPrintAmountString(totalAmount15, 2));
        map.put("totalAmount15multiply_2", PrintCommon.getPrintAmountString(totalAmount15.multiply(new BigDecimal(2)), 2));
        map.put("totalAmount15multiply_3", PrintCommon.getPrintAmountString(totalAmount15.multiply(new BigDecimal(3)), 2));
        map.put("termProtectionRiderAmountA", PrintCommon.getPrintAmountString(termProtectionRiderAmountA, 2));
        map.put("termProtectionRiderAmountB", PrintCommon.getPrintAmountString(termProtectionRiderAmountB, 2));
        if (AssertUtils.isNotNull(map.get("PRO880000000000014"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023BProductLevel"))) {
            map.put("isCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        // 是否有被保人重大疾病利益
        if (AssertUtils.isNotNull(map.get("PRO880000000000014"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            map.put("isInsuredLateStageCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }
}
