package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.endorse.ModeOfPaymentModifyPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 10:03 2019/1/14
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class EndorseModeOfPaymentModifyData {

    public List<PrintObject> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = new ArrayList<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        ModeOfPaymentModifyPrintBo modeOfPaymentModifyPrintBo = JSON.parseObject(content, ModeOfPaymentModifyPrintBo.class);

        PrintCommon.setPrintData(printObjectList, "acceptNo", modeOfPaymentModifyPrintBo.getAcceptNo(), 3);
        PrintCommon.setPrintData(printObjectList, "policyNo", modeOfPaymentModifyPrintBo.getPolicyNo(), 3);
        PrintCommon.setPrintData(printObjectList, "applicantName", modeOfPaymentModifyPrintBo.getApplicantName(), 3);
        PrintCommon.setPrintData(printObjectList, "insuredName", modeOfPaymentModifyPrintBo.getInsuredName(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "applyDate", modeOfPaymentModifyPrintBo.getApplyDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "acceptDate", modeOfPaymentModifyPrintBo.getAcceptDate(), 3);
        PrintCommon.setPrintData(printObjectList, "afterPremiumFrequency", modeOfPaymentModifyPrintBo.getAfterPremiumFrequency(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "endorseFinishDate", modeOfPaymentModifyPrintBo.getEndorseFinishDate(), 3);

        return printObjectList;
    }

}
