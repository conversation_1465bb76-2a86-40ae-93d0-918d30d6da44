package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.attachment.model.config.AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER;
import static com.gclife.common.InternationalTypeEnum.BANK;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @date 2021/12/22
 */
@Component
public class GCMultiProtectData {

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    /**
     * 获取计划书打印数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  可选其他缴费周期start***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount20 = BigDecimal.ZERO;
        String premiumFrequency20 = null;
        String coveragePeriod20 = null;

        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getAmount()));
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int premiumPeriodNum = Integer.parseInt(AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "0");
            String isSinglePayment = PaymentTermEnum.YES_NO.NO.name();
            if (SINGLE.name().equals(premiumFrequency) || (YEAR.name().equals(premiumFrequency) && 1 == premiumPeriodNum)) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Premium";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
                isSinglePayment = PaymentTermEnum.YES_NO.YES.name();
            }
            map.put("isSinglePayment", isSinglePayment);
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
            totalAmount20 = amount;
            coveragePeriod20 = coveragePeriod;
            premiumFrequency20 = premiumFrequency;
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            //每期保费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            // 添加选中的缴费周期和保费
            CoveragePremiumFrequencyResponse coveragePremiumFrequencyResponse = new CoveragePremiumFrequencyResponse();
            coveragePremiumFrequencyResponse.setPremiumFrequency(premiumFrequency);
            coveragePremiumFrequencyResponse.setTotalPremium(coverageBo.getTotalPremium());
            cpfList.add(coveragePremiumFrequencyResponse);

            // 循环所有缴费期限
            for (CoveragePremiumFrequencyResponse premiumFrequencyResponse : cpfList) {
                String coveragePremiumFrequency = premiumFrequencyResponse.getPremiumFrequency();
                BigDecimal coverageTotalPremium = premiumFrequencyResponse.getTotalPremium();

                // 计算期缴保费
                Object totalPremiumSumObject = map.get("totalPremium" + coveragePremiumFrequency + "Sum");
                BigDecimal totalPremiumSum = BigDecimal.ZERO;
                if (totalPremiumSumObject instanceof BigDecimal) {
                    totalPremiumSum = (BigDecimal) totalPremiumSumObject;
                }
                totalPremiumSum = totalPremiumSum.add(coverageTotalPremium);

                // 趸缴不展示其它缴费周期保费
                if (PaymentTermEnum.YES_NO.YES.name().equals(isSinglePayment) && !coveragePremiumFrequency.equals(premiumFrequency)) {
                    coverageTotalPremium = null;
                    totalPremiumSum = null;
                }

                // 设置当前险种的所有缴费周期和对应保费
                coverageMap.put("totalPremium" + coveragePremiumFrequency, PrintCommon.getPrintString("$", coverageTotalPremium, "--"));

                // 期缴保费
                map.put("totalPremium" + coveragePremiumFrequency + "Sum", totalPremiumSum);
            }

            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(premiumFrequency).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", premiumFrequency20);
        // 期缴保费
        Object totalPremiumYEARSum = map.get("totalPremiumYEARSum");
        Object totalPremiumSEMIANNUALSum = map.get("totalPremiumSEMIANNUALSum");
        Object totalPremiumSEASONSum = map.get("totalPremiumSEASONSum");
        Object totalPremiumMONTHSum = map.get("totalPremiumMONTHSum");
        map.put("totalPremiumYearSum", PrintCommon.getPrintString("$", totalPremiumYEARSum, "--"));
        map.put("totalPremiumSemiannualSum", PrintCommon.getPrintString("$", totalPremiumSEMIANNUALSum, "--"));
        map.put("totalPremiumSeasonSum", PrintCommon.getPrintString("$", totalPremiumSEASONSum, "--"));
        map.put("totalPremiumMonthSum", PrintCommon.getPrintString("$", totalPremiumMONTHSum, "--"));
        // 年缴保费
        map.put("totalPremiumYearOfYearSum", PrintCommon.getPrintString("$", totalPremiumYEARSum, "--"));
        map.put("totalPremiumSemiannualOfYearSum", PrintCommon.getPrintString("$", AssertUtils.isNotNull(totalPremiumSEMIANNUALSum) ? ((BigDecimal) totalPremiumSEMIANNUALSum).multiply(new BigDecimal("2")) : null, "--"));
        map.put("totalPremiumSeasonOfYearSum", PrintCommon.getPrintString("$", AssertUtils.isNotNull(totalPremiumSEASONSum) ? ((BigDecimal) totalPremiumSEASONSum).multiply(new BigDecimal("4")) : null, "--"));
        map.put("totalPremiumMonthOfYearSum", PrintCommon.getPrintString("$", AssertUtils.isNotNull(totalPremiumMONTHSum) ? ((BigDecimal) totalPremiumMONTHSum).multiply(new BigDecimal("12")) : null, "--"));
        /****************************************************************************保险利益***********************************************************************************/
        BigDecimal tpd_benefit_due_to_accident = totalAmount20.multiply(new BigDecimal("2"));
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        tpd_benefit_due_to_accident)
                , 2));
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        totalAmount20)
                , 2));
        BigDecimal death_benefit_due_to_accident = totalAmount20.multiply(new BigDecimal("4"));
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        death_benefit_due_to_accident)
                , 2));
        map.put("death_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        totalAmount20)
                , 2));
        BigDecimal busAccidentalDeathAmount = totalAmount20.multiply(new BigDecimal("3"));
        map.put("busAccidentalDeathAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        busAccidentalDeathAmount)
                , 2));
        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int coveragePeriod20i = Integer.parseInt(coveragePeriod20);
        for (int i = 1; i <= coveragePeriod20i; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            interestMap.put("totalAmount20", PrintCommon.getPrintString("$", PrintCommon.decimalFormat1.format(totalAmount20), 1));
            interestMap.put("maxTotalAmount20", PrintCommon.getPrintString("$", PrintCommon.decimalFormat1.format(death_benefit_due_to_accident), 1));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                String productId = electronicPolicyGeneratorRequest.getProductId();
                BigDecimal cashValue20 = ProductCalculation.getCashValue(listCashValue, productId, i);
                interestMap.put("cashValue20", PrintCommon.getPrintString("$", cashValue20, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue20, 1));
            }
            interestListMap.add(interestMap);
        }
        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("totalAmount20", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount20), 2));
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取投保单打印数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Long backTrackDate = applyPrintBo.getApplyDate();
        if (AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())) {
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("prohibitedString2", PrintCommon.getPrintString(null, 2));
        map.put("prohibitedString3", PrintCommon.getPrintString(null, 3));
        map.put("prohibitedString4", PrintCommon.getPrintString(null, 4));
        map.put("prohibitedString5", PrintCommon.getPrintString(null, 5));
        map.put("prohibitedString6", PrintCommon.getPrintString(null, 6));
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));
        /*****************************************投保人********************************************************************/
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        long applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        String applicantExpectedPremiumSources = applicant.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "applicantEPS", applicantExpectedPremiumSources);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        map.put("applicantIncome", PrintCommon.getPrintString(applicant.getIncome(), 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //移动电话
        map.put("applicantMobile_2", PrintCommon.getPrintString(applicant.getMobile_2(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //通讯地址
        String applicantCompanyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String applicantCompanyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("applicantCompanyAddressWhole", PrintCommon.getPrintString(applicantCompanyAreaName + applicantCompanyAddress, 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        map.put("applicantFacebookNo", PrintCommon.getPrintString(applicant.getFacebookNo(), 3));
        map.put("applicantStature", PrintCommon.getPrintString(applicant.getStature(), 3));
        map.put("applicantAvoirdupois", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("applicantExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(applicant.getExpectedPremiumSourcesSpecific(), 3));
        map.put("applicantDoctorName", PrintCommon.getPrintString(applicant.getDoctorName(), 3));
        String applicantDoctorAreaCodeName = AssertUtils.isNotEmpty(applicant.getDoctorAreaCodeName()) ? applicant.getDoctorAreaCodeName() : "";
        String applicantDoctorAddress = AssertUtils.isNotEmpty(applicant.getDoctorAddress()) ? applicant.getDoctorAddress() : "";
        map.put("applicantDoctorAreaCodeName", PrintCommon.getPrintString(applicantDoctorAreaCodeName + applicantDoctorAddress, 3));
        PrintCommon.setSelectionBox(map, "applicantAddressType" + applicant.getAddressType(), applicant.getAddressType());
        /******************************************************被保人********************************************************************/
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }
        map.put("relationshipName", insured.getRelationshipName());

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        String insuredExpectedPremiumSources = insured.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "insuredEPS", insuredExpectedPremiumSources);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(insured.getIncome(), 2));
        long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //移动电话
        map.put("insuredMobile_2", PrintCommon.getPrintString(insured.getMobile_2(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //通讯地址
        String insuredCompanyAreaName = AssertUtils.isNotEmpty(insured.getCompanyAreaName()) ? insured.getCompanyAreaName() : "";
        String insuredCompanyAddress = AssertUtils.isNotEmpty(insured.getCompanyAddress()) ? insured.getCompanyAddress() : "";
        map.put("insuredCompanyAddressWhole", PrintCommon.getPrintString(insuredCompanyAreaName + insuredCompanyAddress, 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        map.put("insuredFacebookNo", PrintCommon.getPrintString(insured.getFacebookNo(), 3));
        map.put("insuredStature", PrintCommon.getPrintString(insured.getStature(), 3));
        map.put("insuredAvoirdupois", PrintCommon.getPrintString(insured.getAvoirdupois(), 3));
        map.put("insuredExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(insured.getExpectedPremiumSourcesSpecific(), 3));
        map.put("insuredDoctorName", PrintCommon.getPrintString(insured.getDoctorName(), 3));
        String insuredDoctorAreaCodeName = AssertUtils.isNotEmpty(insured.getDoctorAreaCodeName()) ? insured.getDoctorAreaCodeName() : "";
        String insuredDoctorAddress = AssertUtils.isNotEmpty(insured.getDoctorAddress()) ? insured.getDoctorAddress() : "";
        map.put("insuredDoctorAreaCodeName", PrintCommon.getPrintString(insuredDoctorAreaCodeName + insuredDoctorAddress, 3));
        map.put("taxpayerNo", PrintCommon.getPrintString(insured.getTaxpayerNo(), 3));
        PrintCommon.setSelectionBox(map, "insuredAddressType" + insured.getAddressType(), insured.getAddressType());
        /******************************************************受益人信息***************************************************************/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //证件类型
                beneficiaryMap.put("homeAddress", PrintCommon.getPrintString(applyBeneficiaryBo.getHomeAddress(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });
            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        /**********************************************************险种信息**************************************************************/
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel(), language);
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000009".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                if ("PRO880000000000014".equals(applyCoverageBo.getProductId())) {
                    if ("ACCELERATION_CI".equals(applyCoverageBo.getProductLevel())) {
                        coverageMap.put("productLevelZH_CN", "(提前给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                        coverageMap.put("productLevelEN_US", "(Acceleration)");
                    } else {
                        coverageMap.put("productLevelZH_CN", "(额外给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                        coverageMap.put("productLevelEN_US", "(Additional)");
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }
        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));
        /*****************************************交费*******************************************************************/
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOtherInsuranceBo> listApplyOtherInsurancePo = applyPrintBo.getOtherInsurance();
        List<Map<String, Object>> applyOtherInsuranceListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyOtherInsurancePo)) {
            for (ApplyOtherInsuranceBo applyOtherInsuranceBo : listApplyOtherInsurancePo) {
                Map<String, Object> applyOtherInsuranceMap = new HashMap<>();
                applyOtherInsuranceMap.put("otherInsuringInsuredName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuredName(), 3));
                applyOtherInsuranceMap.put("otherInsuringCompany", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuringCompany(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceTypeName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceTypeName(), 3));
                applyOtherInsuranceMap.put("otherInsuringAmount", PrintCommon.getPrintString(applyOtherInsuranceBo.getAmount(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceYear", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceYear(), 3));
                applyOtherInsuranceListMap.add(applyOtherInsuranceMap);
            }
        }
        map.put("applyOtherInsuranceListMap", applyOtherInsuranceListMap);
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOccupationNatureBo> occupationNatureList = applyPrintBo.getOccupationNature();
        if (!AssertUtils.isNotEmpty(occupationNatureList)) {
            occupationNatureList = new ArrayList<>();
        }
        ApplyOccupationNatureBo applicantOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> applicantOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "APPLICANT".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (applicantOptionalOccupationNatureBo.isPresent()) {
            applicantOccupationNatureBo = applicantOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "applicantON" + applicantOccupationNatureBo.getOccupationNature(), applicantOccupationNatureBo.getOccupationNature());
        map.put("applicantOccupationNatureSpecific", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("applicantEmployerName", PrintCommon.getPrintString(applicantOccupationNatureBo.getEmployerName(), 3));
        map.put("applicantBusinessNature", PrintCommon.getPrintString(applicantOccupationNatureBo.getBusinessNature(), 3));
        map.put("applicantOccupationExactDuties", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupation(), 3));
        map.put("applicantOccupationClass", PrintCommon.getPrintString(applicantOccupationNatureBo.getExactDuties(), 3));

        ApplyOccupationNatureBo insuredOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> insuredOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "INSURED".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (insuredOptionalOccupationNatureBo.isPresent()) {
            insuredOccupationNatureBo = insuredOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "insuredON" + insuredOccupationNatureBo.getOccupationNature(), insuredOccupationNatureBo.getOccupationNature());
        map.put("insuredOccupationNatureSpecific", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("insuredEmployerName", PrintCommon.getPrintString(insuredOccupationNatureBo.getEmployerName(), 3));
        map.put("insuredBusinessNature", PrintCommon.getPrintString(insuredOccupationNatureBo.getBusinessNature(), 3));
        map.put("insuredOccupationExactDuties", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupation(), 3));
        map.put("insuredOccupationClass", PrintCommon.getPrintString(insuredOccupationNatureBo.getExactDuties(), 3));
        /********************************************账户*********************************************************/
        List<ApplyAccountBo> listApplyAccount = applyPrintBo.getListApplyAccount();
        ApplyAccountBo applyAccountBo = new ApplyAccountBo();
        String kmKmBankName = null;
        String bankName = null;
        if (AssertUtils.isNotEmpty(listApplyAccount)) {
            applyAccountBo = listApplyAccount.get(0);
            if (AssertUtils.isNotEmpty(applyAccountBo.getBankCode())) {
                SyscodeResponse kmKmBankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name()).getData();
                SyscodeResponse bankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name().equals(language) ? EN_US.name() : language).getData();
                kmKmBankName = AssertUtils.isNotNull(kmKmBankSyscode) && AssertUtils.isNotEmpty(kmKmBankSyscode.getCodeName()) ? kmKmBankSyscode.getCodeName() : null;
                bankName = AssertUtils.isNotNull(bankSyscode) && AssertUtils.isNotEmpty(bankSyscode.getCodeName()) ? bankSyscode.getCodeName() : null;
            }
        }
        map.put("kmKmBankName", PrintCommon.getPrintString(kmKmBankName, 3));
        map.put("bankName", PrintCommon.getPrintString(bankName, 3));
        map.put("accountOwner", PrintCommon.getPrintString(applyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(applyAccountBo.getAccountNo(), 3));
        /********************************************健康告知书*********************************************************/
        ProductCalculation.setHealthRemark1(map, applyPrintBo);
        List<ApplyStatementBo> statements = applyPrintBo.getStatements();
        if (AssertUtils.isNotEmpty(statements)) {
            statements.forEach(applyStatementBo -> {
                map.put(applyStatementBo.getStatementCode(), applyStatementBo.getStatementValue());
            });
        }
        /********************************************投保申请日期*********************************************************/
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyAgentBo.getAgentMobile(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取保险证打印数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        String mainProductId = electronicPolicyGeneratorRequest.getProductId();
        PrintCommon.setProductName(map, mainProductId, "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            coverageMap.put("maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        String premiumFrequency = mainCoverageBo.getPremiumFrequency();
        int premiumPeriodNum = Integer.parseInt(AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "0");
        if (SINGLE.name().equals(premiumFrequency) || (YEAR.name().equals(premiumFrequency) && 1 == premiumPeriodNum)) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Premium";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        // 判断20号产品是否趸缴
        String mainPremiumFrequency = mainCoverageBo.getPremiumFrequency();
        int mainPremiumPeriod = Integer.parseInt(AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "0");
        String isSinglePayment = PaymentTermEnum.YES_NO.NO.name();
        // 年缴且一年期
        if (SINGLE.name().equals(mainPremiumFrequency) || (YEAR.name().equals(mainPremiumFrequency) && 1 == mainPremiumPeriod)) {
            isSinglePayment = PaymentTermEnum.YES_NO.YES.name();
        }
        map.put("isSinglePayment", isSinglePayment);
        /***********************************现金价值******************************************/
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                long policyYear = productCashValueBo.getPolicyYear();
                // 不展示保单年度是0的现价
                if (policyYear != 0) {
                    Map<String, Object> productCashValue = new HashMap<>();
                    productCashValue.put("pcvPolicyYear", policyYear);
                    productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                    productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                    policyCashValuesListMap.add(productCashValue);
                }
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }
}
