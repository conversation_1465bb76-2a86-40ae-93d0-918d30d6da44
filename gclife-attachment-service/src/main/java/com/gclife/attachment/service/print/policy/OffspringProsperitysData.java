package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.BENEFICIARY_NO.ORDER_ONE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;

@Component
public class OffspringProsperitysData {


    /**
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        Long backTrackDate =  planPrintBo.getCreatedDate();
        if(AssertUtils.isNotNull(planPrintBo.getBackTrackDate())){
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            map.put("backTrackDateYearZH_CN", PrintCommon.getPrintString("年 ", 3));
            map.put("backTrackDateMonthZH_CN", PrintCommon.getPrintString("月 ", 3));
            map.put("backTrackDateDayZH_CN", PrintCommon.getPrintString("日 ", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday()) && AssertUtils.isNotNull(backTrackDate)) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(planPrintBo.getCreatedDate()));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        map.put("relationshipName", PrintCommon.getPrintString(insured.getRelationshipName(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount4 = new BigDecimal(0);
        BigDecimal totalAmount1 = new BigDecimal(0);
        String productLevel4 = null;
        String mult1 = null;
        String productLevel1 = null;
        String premiumFrequency1 = null;
        String coveragePeriod1 = null;
        String premiumPeriod1 = null;
        BigDecimal totalAmount7 = new BigDecimal(0);
        BigDecimal totalPremium7 = new BigDecimal(0);
        long receiveAge = 0;
        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId());
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            if ("PRO88000000000001V2018".equals(coverageBo.getProductId())) {
                coveragePeriod1 = coveragePeriod;
                mult1 = coverageBo.getMult();
                premiumPeriod1 = coverageBo.getPremiumPeriod();
                productLevel1 = coverageBo.getProductLevel();
                premiumFrequency1 = coverageBo.getPremiumFrequency();
                String multLevel = "(" + coverageBo.getMult() + coverageBo.getProductLevel() + ")";
                coverageMap.put("productLevelZH_CN", multLevel);
                coverageMap.put("productLevelKM_KH", multLevel);
                coverageMap.put("productLevelEN_US", multLevel);
                //教育金算法
                Date policyEffectiveDateCalCul = DateUtils.addYears(new Date(backTrackDate), Integer.valueOf(premiumPeriod1));
                //获取计算岁数
                receiveAge = DateUtils.getAgeYear(new Date(insured.getBirthday()), policyEffectiveDateCalCul);
                receiveAge = ApplyData.getReceiveAge(Integer.valueOf(receiveAge + ""), productLevel1);
                totalAmount1 = getAllAgeAmount(mult1, productLevel1, receiveAge);
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount1, 3));
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ22", 2));
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("至被保险人22岁", 2));
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("Until the Insured is 22", 2));
                }
            } else if ("PRO88000000000004".equals(coverageBo.getProductId())) {
                totalAmount4 = totalAmount;
                productLevel4 = productLevel;
                if ("AMOUNT_UP".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(递增型)");
                    coverageMap.put("productLevelKM_KH", "(ប្រភេទប្រែប្រួល)");
                    coverageMap.put("productLevelEN_US", "(Accelerated)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(固定型)");
                    coverageMap.put("productLevelKM_KH", "(ប្រភេទថេរ)");
                    coverageMap.put("productLevelEN_US", "(Fixed)");
                }
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ22", 2));
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("至被保险人22岁", 2));
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    coverageMap.put("coveragePeriod", PrintCommon.getPrintString("Until the Insured is 22", 2));
                }
            } else if ("PRO88000000000007".equals(coverageBo.getProductId())) {
                ProductCalculation.calculation7(map, coverageBo.getProductLevel(), coverageBo.getMult());
                totalAmount7 = new BigDecimal((map.get("totalAmount7") + "").replace(",", ""));
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount7, 2));
                totalPremium7 = coverageBo.getTotalPremium();
                String multLevel = "(" + coverageBo.getMult() + coverageBo.getProductLevel() + ")";
                coverageMap.put("productLevelZH_CN", multLevel);
                coverageMap.put("productLevelKM_KH", multLevel);
                coverageMap.put("productLevelEN_US", multLevel);
            }
            //每期保费
            coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /****************************************************************************可选其他缴费周期***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        if (AssertUtils.isNotNull(planProductDetail)) {
            Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
            List<Map<String, Object>> coveragePremiumFrequencyListMap = new ArrayList<>();
            for (String productId : coveragePremiumFrequencyMap.keySet()) {
                List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
                Map<String, Object> cpfMap = new HashMap<>();
                PrintCommon.setProductName(cpfMap, productId);
                Integer column = 1;
                column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, YEAR.name(), premiumFrequency1);
                column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEMIANNUAL.name(), premiumFrequency1);
                column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEASON.name(), premiumFrequency1);
                ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, MONTH.name(), premiumFrequency1);
                coveragePremiumFrequencyListMap.add(cpfMap);
            }
            PrintCommon.coverageSort(coveragePremiumFrequencyListMap);
            map.put("coveragePremiumFrequencyListMap", coveragePremiumFrequencyListMap);
        }

        /****************************************************************************保险利益***********************************************************************************/
        map.put("totalAmount1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount1), 2));
        map.put("totalAmount4_1", PrintCommon.getPrintString(totalAmount4, 2));
        map.put("totalAmount4", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount4), 2));
        map.put("productLevel4", PrintCommon.getPrintString(productLevel4, 2));
        if ("AMOUNT_UP".equals(productLevel4)) {
            BigDecimal amountUpAmount = totalAmount4.multiply(new BigDecimal(0.03));
            BigDecimal totalAmount4AmountUpSum = totalAmount4.add(new BigDecimal(new Integer(coveragePeriod1) - 2).multiply(amountUpAmount));
            map.put("totalAmount4AmountUpSum", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount4AmountUpSum), 2));
        }
        map.put("totalAmount7", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount7), 2));
        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();

        Object payAmount7 = map.get("payAmount7");
        BigDecimal policyYearTotalPremium = new BigDecimal(0);
        int year = 1;
        for (int i = insuredAgeYear; i <= 22; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            interestMap.put("year", year);
            interestMap.put("insuredAge", i);
            /****************************************************************************保险费和现金价值*/
            BigDecimal ageAmount1 = new BigDecimal(0);
            if (receiveAge <= i) {
                ageAmount1 = getAgeAmount(mult1, productLevel1, i);
            }
            interestMap.put("totalAmount1", PrintCommon.getPrintString("$", ageAmount1, 1));
            interestMap.put("totalAmount4", PrintCommon.getPrintString("$", (i == 22) ? null : ((year == 1) ? new BigDecimal(0.00) : totalAmount4), 1));
            interestMap.put("payAmount7", PrintCommon.getPrintString("$", (i == 22) ? null : payAmount7, 1));
            /****************************************************************************利益显示*/

            int finalYear = year;
            Optional<ProductCashValueBo> first = listCashValue.stream()
                    .filter(productCashValueBo -> "PRO88000000000001V2018".equals(productCashValueBo.getProductId()) && finalYear == productCashValueBo.getPolicyYear())
                    .findFirst();
            if (first.isPresent()) {
                interestMap.put("year2", year);

                //保险费
                //保单年度
                policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, year, new BigDecimal(0));
                interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));

                //现金价值
                BigDecimal cashValue1 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000001V2018", year);
                BigDecimal cashValue4 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000004", year);
                BigDecimal cashValue7 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000007", year);
                interestMap.put("cashValue1", PrintCommon.getPrintString("$", cashValue1, 1));
                interestMap.put("cashValue4", PrintCommon.getPrintString("$", cashValue4, 1));
                interestMap.put("cashValue7", PrintCommon.getPrintString("$", cashValue7, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue1.add(cashValue4).add(cashValue7), 1));
            }
            interestListMap.add(interestMap);
            year++;
        }
        map.put("interestListMap", interestListMap);


        /************************************保险利益************************************************/
        //保险利益

        BigDecimal totalAmount3_3 = new BigDecimal(0);
        if (receiveAge < 23) {
            totalAmount3_3 = getAgeAmount(mult1, productLevel1, 20);
            map.put("PRO88000000000001V2018Level", "C");
        }
        map.put("totalAmount3_3", PrintCommon.getPrintString(totalAmount3_3, 2));


        BigDecimal totalAmount3_2 = new BigDecimal(0);
        if (("A".equals(productLevel1) || "B".equals(productLevel1)) && receiveAge < 19) {
            totalAmount3_2 = getAgeAmount(mult1, productLevel1, 14);
            map.put("PRO88000000000001V2018Level", "B");
        }
        map.put("totalAmount3_2", PrintCommon.getPrintString(totalAmount3_2, 2));

        BigDecimal totalAmount3_1 = new BigDecimal(0);
        if ("A".equals(productLevel1) && receiveAge < 13) {
            totalAmount3_1 = getAgeAmount(mult1, productLevel1, 8);
            map.put("PRO88000000000001V2018Level", "A");
        }
        map.put("totalAmount3_1", PrintCommon.getPrintString(totalAmount3_1, 2));


        map.put("receiveAge", receiveAge);
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    public BigDecimal getAgeAmount(String mult, String productLevel, long age) {
        // 3号：每份可以领取教育金总额乘以份数
        BigDecimal totalXxInsuranceAmount = new BigDecimal(300)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal("A".equals(productLevel) && age > 6 && age < 13 ? 1 : 0));

        BigDecimal totalZxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 500 : 0)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age > 12 && age < 19 ? 1 : 0));

        BigDecimal totalDxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 500 : "B".equals(productLevel) ? 700 : "C".equals(productLevel) ? 1200 : 0)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age > 18 && age < 23 ? 1 : 0));

        return totalXxInsuranceAmount.add(totalZxInsuranceAmount).add(totalDxInsuranceAmount);
    }

    public BigDecimal getAllAgeAmount(String mult, String productLevel, long age) {
        // 3号：每份可以领取教育金总额乘以份数
        BigDecimal totalXxInsuranceAmount = new BigDecimal(300)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal("A".equals(productLevel) && age < 13 ? 13 - (age > 7 ? age : 7) : 0));
        BigDecimal totalZxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 500 : 0)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 19 ? 19 - (age > 13 ? age : 13) : 0));
        BigDecimal totalDxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 500 : "B".equals(productLevel) ? 700 : "C".equals(productLevel) ? 1200 : 0)
                .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 23 ? 23 - (age > 19 ? age : 19) : 0));
        return totalXxInsuranceAmount.add(totalZxInsuranceAmount).add(totalDxInsuranceAmount);
    }


    /**
     * 获取保单打印数据
     *
     * @return
     */
    public List<PrintObject> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(printObjectList, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        PrintCommon.setPrintData(printObjectList, "policyNo", policyBo.getPolicyNo(), 3);
        //投保人信息
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", policyApplicant.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "applicantSexName", policyApplicant.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", policyApplicant.getIdNo(), 3);
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "applicantIdNoAndIdTypeName", applicantIdNoAndIdTypeName, 3);
        // TODO 与被保人什么关系 没有
        //手机号
        PrintCommon.setPrintData(printObjectList, "applicantMobile", policyApplicant.getMobile(), 3);

        //被保人信息
        Long insuredBirthday = null;
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", policyInsuredBo.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", policyInsuredBo.getSexName(), 3);
        //投保人 出生年月日
        insuredBirthday = policyInsuredBo.getBirthday();
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "insuredIdNoAndIdTypeName", insuredIdNoAndIdTypeName, 3);
        //与投保人什么关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", policyInsuredBo.getRelationshipName(), 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "insuredMobile", policyInsuredBo.getMobile(), 3);

        //受益人
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        if (AssertUtils.isNotEmpty(listPolicyBeneficiary)) {
            listPolicyBeneficiary.removeIf(policyBeneficiaryInfoBo -> !ORDER_ONE.name().equals(policyBeneficiaryInfoBo.getBeneficiaryNoOrder()));
        }
        for (int i = 0; i < 3; i++) {
            PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = null;
            PolicyBeneficiaryBo policyBeneficiary = null;
            if (AssertUtils.isNotEmpty(listPolicyBeneficiary) && i < listPolicyBeneficiary.size() && AssertUtils.isNotNull(listPolicyBeneficiary.get(i))) {
                policyBeneficiaryInfoBo = listPolicyBeneficiary.get(i);
                if (AssertUtils.isNotNull(policyBeneficiaryInfoBo) && AssertUtils.isNotNull(policyBeneficiaryInfoBo.getPolicyBeneficiary())) {
                    policyBeneficiary = policyBeneficiaryInfoBo.getPolicyBeneficiary();
                }
            }
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            if (!AssertUtils.isNotNull(policyBeneficiaryInfoBo)) {
                policyBeneficiaryInfoBo = new PolicyBeneficiaryInfoBo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryName" + i, policyBeneficiary.getName(), 3);
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdNo" + i, beneficiaryIdNo, 3);
            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                beneficiaryProportion = policyBeneficiaryInfoBo.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryProportion" + i, beneficiaryProportion, 2);
        }
        /****************************************************************************获取保险期限***********************************************************************************/
        //主险
        String mult = null;
        String mult7 = null;
        String premiumPeriod = null;
        String productLevelMain = null;
        String premiumFrequencyName = null;
        String premiumPeriodUnitName = null;
        BigDecimal totalPremiumMain = null;
        List<ProductCashValueBo> policyCashValuesMain = null;
        //附加险 7号
        String productLevelAdditional = null;
        String totalAmountAdditional = null;
        BigDecimal totalPremiumAdditional = null;
        Long effectiveDate7 = null;
        Long maturityDate7 = null;
        //附加险 4号
        BigDecimal totalPremium4 = null;
        BigDecimal premium4 = null;
        String productLevel4 = null;
        String premiumPeriod4 = null;
        String premiumPeriodUnit4 = null;
        String premiumPeriodUnitName4 = null;
        String premiumFrequencyName4 = null;
        String coveragePeriod4 = null;
        BigDecimal totalAmountAdditional4 = null;

        List<ProductCashValueBo> policyCashValues4 = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyCoverage())) {
            List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
            Optional<PolicyCoverageBo> policyCoverageBoMain = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst();
            if (policyCoverageBoMain.isPresent()) {
                PolicyCoverageBo policyCoverageBo = policyCoverageBoMain.get();
                premiumPeriod = policyCoverageBo.getPremiumPeriod();
                productLevelMain = policyCoverageBo.getProductLevel();
                premiumFrequencyName = policyCoverageBo.getPremiumFrequencyName();
                premiumPeriodUnitName = policyCoverageBo.getPremiumPeriodUnitName();
                totalPremiumMain = policyCoverageBo.getTotalPremium();
                mult = policyCoverageBo.getMult();
                String productId = policyCoverageBo.getProductId();
                electronicPolicyGeneratorRequest.setProductId(productId + "_" + productLevelMain);
                List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
                PrintCommon.setProductName(printObjectList, productId, "Main", productLevelMain);
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValuesMain = policyCashValues.stream().filter(productCashValueBo -> productId.equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
            }

            List<PolicyCoverageBo> policyCoverageAdditionalBoList = listPolicyCoverage.stream().filter(coveragePlanBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(coveragePlanBo.getPrimaryFlag())).collect(Collectors.toList());
            Optional<PolicyCoverageBo> policyCoverageBo7 = policyCoverageAdditionalBoList.stream().filter(policyCoverageBo -> "PRO88000000000007".equals(policyCoverageBo.getProductId())).findFirst();
            if (policyCoverageBo7.isPresent()) {
                PolicyCoverageBo applyCoverageAdditionalBo = policyCoverageBo7.get();
                productLevelAdditional = applyCoverageAdditionalBo.getProductLevel();
                totalPremiumAdditional = applyCoverageAdditionalBo.getTotalPremium();
                totalAmountAdditional = applyCoverageAdditionalBo.getTotalAmount();
                mult7 = applyCoverageAdditionalBo.getMult();
                effectiveDate7 = applyCoverageAdditionalBo.getEffectiveDate();
                maturityDate7 = applyCoverageAdditionalBo.getMaturityDate();
                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), applyCoverageAdditionalBo.getProductId() + "_V"));
            }
            Optional<PolicyCoverageBo> policyCoverageBo4 = policyCoverageAdditionalBoList.stream().filter(policyCoverageBo -> "PRO88000000000004".equals(policyCoverageBo.getProductId())).findFirst();
            if (policyCoverageBo4.isPresent()) {
                PolicyCoverageBo applyCoverageAdditionalBo = policyCoverageBo4.get();
                productLevel4 = applyCoverageAdditionalBo.getProductLevel();
                premium4 = applyCoverageAdditionalBo.getPremium();
                totalPremium4 = applyCoverageAdditionalBo.getTotalPremium();
                totalAmountAdditional4 = AssertUtils.isNotEmpty(applyCoverageAdditionalBo.getTotalAmount()) ? new BigDecimal(applyCoverageAdditionalBo.getTotalAmount()) : null;
                premiumPeriod4 = applyCoverageAdditionalBo.getPremiumPeriod();
                premiumPeriodUnit4 = applyCoverageAdditionalBo.getPremiumPeriodUnit();
                premiumPeriodUnitName4 = applyCoverageAdditionalBo.getPremiumPeriodUnitName();
                premiumFrequencyName4 = applyCoverageAdditionalBo.getPremiumFrequencyName();
                coveragePeriod4 = applyCoverageAdditionalBo.getCoveragePeriod();
                List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), applyCoverageAdditionalBo.getProductId() + "_22"));
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValues4 = policyCashValues.stream().filter(productCashValueBo -> applyCoverageAdditionalBo.getProductId().equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
            }
        }
        /******************主险*/
        PrintCommon.setPrintData(printObjectList, "mult", mult, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelMain", productLevelMain, 3);
        PrintCommon.setPrintData(printObjectList, "premiumPeriod", premiumPeriod, 3);
        //缴费年期
        String premiumPeriodName = null;
        if (AssertUtils.isNotEmpty(premiumPeriod) && AssertUtils.isNotEmpty(premiumPeriodUnitName)) {
            premiumPeriodName = premiumPeriod + " " + premiumPeriodUnitName;
        }
        PrintCommon.setPrintData(printObjectList, "premiumPeriodName", premiumPeriodName, 3);
        PrintCommon.setPrintData(printObjectList, "totalPremiumMain", totalPremiumMain, 3);
        //缴费周期
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", premiumFrequencyName, 3);
        //获取现金价值
        ProductCalculation.policyCashValues(printObjectList, policyCashValuesMain);
        /******************附加险7号*/
        ProductCalculation.calculation7(printObjectList, productLevelAdditional, mult7);
        PrintCommon.setPrintData(printObjectList, "totalPremiumAdditional", totalPremiumAdditional, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelAdditional", productLevelAdditional, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom7", effectiveDate7, 3);
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil7", maturityDate7, 3);

        /******************附加险4号*/
        String totalAmountAdditional4EN_US = PrintCommon.getPrintString(totalAmountAdditional4, 3);
        String totalAmountAdditional4ZH_CN = PrintCommon.getPrintString(totalAmountAdditional4, 3);
        String totalAmountAdditional4KM_KH = PrintCommon.getPrintString(totalAmountAdditional4, 3);
        PrintCommon.setPrintData(printObjectList, "productLevel4", productLevel4, 2);
        if (AssertUtils.isNotNull(totalAmountAdditional4) && "AMOUNT_UP".equals(productLevel4)) {
            totalAmountAdditional4EN_US = "(Initial) " + totalAmountAdditional4;
            totalAmountAdditional4ZH_CN = "(初始) " + totalAmountAdditional4;
            totalAmountAdditional4KM_KH = "(ដើមគ្រា) " + totalAmountAdditional4;
        }
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4EN_US", totalAmountAdditional4EN_US, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4ZH_CN", totalAmountAdditional4ZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmountAdditional4KM_KH", totalAmountAdditional4KM_KH, 3);
        PrintCommon.setPrintData(printObjectList, new String[]{"productLevel4AMOUNT_UP", "productLevel4AMOUNT_FIXED"}, "productLevel4" + productLevel4);
        PrintCommon.setPrintData(printObjectList, "premiumPeriodUnit4", premiumPeriod4 == null ? null : premiumPeriod4 + " " + premiumPeriodUnitName4, 3);
        //柬文岁特殊处理
        if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(premiumPeriodUnit4)) {
            PrintCommon.setPrintData(printObjectList, "premiumPeriodUnit4", premiumPeriod4 == null ? null : premiumPeriodUnitName4 + " " + premiumPeriod4, 3);
        }
        PrintCommon.setPrintData(printObjectList, "totalPremium4", totalPremium4, 3);
        PrintCommon.setPrintData(printObjectList, "premium4", premium4, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName4", premiumFrequencyName4, 3);
        PrintCommon.setPrintData(printObjectList, "premiumPeriod4", premiumPeriod4, 3);
        ProductCalculation.policyCashValues(printObjectList, policyCashValues4, "4");
        /****************************************************************************获取保险期限***********************************************************************************/

        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        PrintCommon.setPrintData(printObjectList, "periodTotalPremium", policyPremium.getActualPremium(), 3);
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        //支付方式
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom", policyPayment.getGainedDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "gainedDate", policyPayment.getGainedDate(), 3);
        Long gainedDate = policyPayment.getGainedDate();

        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        //代理人编码
        PrintCommon.setPrintData(printObjectList, "agentCode", policyAgent.getAgentCode(), 3);
        PrintCommon.setPrintData(printObjectList, "agentName", policyAgent.getAgentName(), 3);


        //签发日期
        PrintCommon.setPrintDateTime(printObjectList, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "approveDate", policyBo.getApproveDate(), 3);

        this.setCoverageDate(printObjectList, insuredBirthday, gainedDate, productLevelMain, Long.parseLong(mult), premiumPeriod);

        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getPolicySpecialContractContent(printObjectList, specialContractContent);
        // 公司基础信息
        printObjectList.addAll(CompanyInfo.getCompanyBaseInfoList(language));
        return printObjectList;
    }

    /**
     * 3号+ 产品保险利益
     *
     * @return
     * @throws Exception
     */
    public void setCoverageDate(List<PrintObject> printObjectList, Long insuredBirthday, Long policyEffective, String productLevel, long coverageMult, String coveragePremiumPeriod) throws Exception {
        //计算保单截止日期
        Long policyEffectiveDateEnd = null;
        Long hmStartTime = null;
        Long hmEndTime = null;

        Long zdStartTime = null;
        Long zdEndTime = null;
        Long zdMult = coverageMult;

        Long xxStartTime = null;
        Long xxEndTime = null;
        BigDecimal xxInsuranceAmount = null;
        Long xxStartAge = null;
        BigDecimal totalXxInsuranceAmount = null;
        BigDecimal xxAmount = null;
        Long xxMult = coverageMult;
        Long xxYears = null;

        Long receiveAge = null;

        Long zxStartTime = null;
        Long zxEndTime = null;
        BigDecimal zxInsuranceAmount = null;
        Long zxStartAge = null;
        BigDecimal totalZxInsuranceAmount = null;
        BigDecimal zxAmount = null;
        Long zxMult = coverageMult;
        Long zxYears = null;

        Long dxStartTime = null;
        Long dxEndTime = null;
        BigDecimal dxInsuranceAmount = null;
        Long dxStartAge = null;
        BigDecimal totalDxInsuranceAmount = null;
        BigDecimal dxAmount = null;
        Long dxMult = coverageMult;
        Long dxYears = null;


        BigDecimal zJInsuranceAmount = null;
        BigDecimal totalZJInsuranceAmount = null;

        long mult;
        if (!AssertUtils.isNotNull(coverageMult) || coverageMult <= 0) {
            mult = 1;
        } else {
            mult = coverageMult;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");//时间格式化类
        //被保人出生年月
        Date date = new Date(insuredBirthday);//解析到一个时间
        String dateStr = sdf.format(date);
        Date insuredBirthdayDate = sdf.parse(dateStr);
        //转换保单生效日
        date = StringUtil.getDateAll(policyEffective + "");//解析到一个时间
        dateStr = sdf.format(date);
        Date policyEffectiveDate = sdf.parse(dateStr);//  StringUtil.getDateAll(policyEffective  +  "");


        //获取岁数
        int age = DateUtils.getAgeYear(insuredBirthdayDate, policyEffectiveDate);
        //计算保单截止日期
        policyEffectiveDateEnd = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 22 - age);
        //保多少年
        Integer policyYear = null;
        if (AssertUtils.isNotNull(policyEffective) && AssertUtils.isNotNull(policyEffectiveDateEnd)) {
            policyYear = Integer.valueOf(DateUtils.timeStrToString(policyEffectiveDateEnd, DateUtils.FORMATE1))
                    - Integer.valueOf(DateUtils.timeStrToString(policyEffective, DateUtils.FORMATE1));
        }
        PrintCommon.setPrintData(printObjectList, "policyYear", policyYear, 2);

        //被保人出生日期
        System.out.println("被保人出生日期:" + insuredBirthday);
        //保单生效日期
        System.out.println("保单生效日期:" + policyEffectiveDate.getTime());
        //保单截止日
        System.out.println("保单截止日期:" + policyEffectiveDateEnd);
        //豁免保险费
        //交费3年

        //缴费期限
        int payYear = Integer.valueOf(coveragePremiumPeriod);
        hmStartTime = policyEffectiveDate.getTime();
        hmEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), payYear);

        System.out.println("交费豁免开始时间:" + hmStartTime);
        System.out.println("交费豁免结束时间:" + hmEndTime);

        //重大疾病保险金
        zdStartTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 3) - (1 * 24 * 60 * 60 * 1000);
        zdEndTime = policyEffectiveDateEnd;

        System.out.println("重大疾病开始时间:" + hmStartTime);
        System.out.println("重大疾病结束时间:" + hmEndTime);
        //重大疾病保险金
        totalZJInsuranceAmount = new BigDecimal(mult * 7000).setScale(2, BigDecimal.ROUND_HALF_UP);
        zJInsuranceAmount = new BigDecimal(7000).setScale(2, BigDecimal.ROUND_HALF_UP);
        System.out.println("重大疾病保险金计算公式:" + totalZJInsuranceAmount + "美元×" + mult + "份");
        System.out.println("重大疾病保险金:" + totalZJInsuranceAmount);

        //教育金算法
        Date policyEffectiveDateCalCul = DateUtils.addYears(policyEffectiveDate, payYear);
        //获取计算岁数
        age = DateUtils.getAgeYear(insuredBirthdayDate, policyEffectiveDateCalCul);
        System.out.println("领取时间戳:" + policyEffectiveDateCalCul.getTime());
        System.out.println("生日时间戳:" + insuredBirthdayDate.getTime());
        System.out.println("领取时年龄:" + age);

        //教育金算法　领取方式　A
        //小学教育金
        if (age < 13 && "A".equals(productLevel)) {
            //计算开始时间
            xxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 7 ? age : 7) - age);
            //计算结束时间
            xxEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 13 - age - 1);
            //开始领取岁数
            xxStartAge = (age > 7l ? age : 7l);
            //总计领取年限
            xxYears = (13l - (age > 7 ? age : 7));
            //每年领取金额
            xxInsuranceAmount = new BigDecimal(300 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);
            xxAmount = new BigDecimal(300).setScale(2, BigDecimal.ROUND_HALF_UP);
            //总领取金额
            totalXxInsuranceAmount = xxAmount.multiply(new BigDecimal(xxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);

            receiveAge = xxStartAge;

            System.out.println("小学教育金开始时间:" + xxStartTime);
            System.out.println("小学教育金结束时间:" + xxEndTime);
            System.out.println("开始领取岁数:" + xxStartAge);
            System.out.println("小学教育金(美元):" + xxInsuranceAmount + "/年,总领取:" + xxYears + "年");
            System.out.println("小学教育金计算公式:" + 300 + "美元/每年×" + mult + "份×" + xxYears + "年");
            System.out.println("总领取教育金(美元):" + totalXxInsuranceAmount);

        }
        //中学教育金
        if (age < 19 && "A".equals(productLevel)) {
            //计算开始时间
            zxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 13 ? age : 13) - age);
            //计算结束时间
            zxEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 19 - age - 1);
            //开始领取岁数
            zxStartAge = (age > 13l ? age : 13l);
            //总计领取年限
            zxYears = (19l - (age > 13 ? age : 13));
            //每年领取金额
            zxAmount = new BigDecimal(400).setScale(2, BigDecimal.ROUND_HALF_UP);
            zxInsuranceAmount = new BigDecimal(400 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);
            //总领取金额
            totalZxInsuranceAmount = zxAmount.multiply(new BigDecimal(zxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);

            if (!AssertUtils.isNotNull(receiveAge)) {
                receiveAge = zxStartAge;
            }
            System.out.println("中学教育金开始时间:" + zxStartTime);
            System.out.println("中学教育金结束时间:" + zxEndTime);
            System.out.println("开始领取岁数:" + zxStartAge);
            System.out.println("中学教育金(美元):" + zxAmount + "/年,总领取:" + zxYears + "年");
            System.out.println("中学教育金计算公式:" + 400 + "美元/每年×" + mult + "份×" + zxYears + "年");
            System.out.println("总领取教育金(美元):" + totalZxInsuranceAmount);
        }

        //大學教育金
        if (age <= 22 && "A".equals(productLevel)) {
            //计算开始时间
            dxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 19 ? age : 19) - age);
            //计算结束时间
            dxEndTime = zxEndTime;
            //开始领取岁数
            dxStartAge = (age > 19l ? age : 19l);
            //总计领取年限
            dxYears = (23l - (age > 19 ? age : 19));
            //每年领取金额
            dxAmount = new BigDecimal(500).setScale(2, BigDecimal.ROUND_HALF_UP);
            dxInsuranceAmount = new BigDecimal(500 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);
            //总领取金额
            totalDxInsuranceAmount = dxAmount.multiply(new BigDecimal(dxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);

            if (!AssertUtils.isNotNull(receiveAge)) {
                receiveAge = dxStartAge;
            }
            System.out.println("大学教育金开始时间:" + dxStartTime);
            System.out.println("大学教育金结束时间:" + dxEndTime);
            System.out.println("开始领取岁数:" + dxStartAge);
            System.out.println("大学教育金(美元):" + dxAmount + "/年,总领取:" + dxYears + "年");
            System.out.println("大学中学教育金计算公式:" + 500 + "美元/每年×" + mult + "份×" + dxYears + "年");
            System.out.println("总领取教育金(美元):" + totalDxInsuranceAmount);
        }


        //中学教育金　领取方式B
        if (age < 19 && "B".equals(productLevel)) {
            //计算开始时间
            zxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 13 ? age : 13) - age);
            //计算结束时间
            zxEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 19 - age - 1);
            //开始领取岁数
            zxStartAge = (age > 13l ? age : 13l);
            //总计领取年限
            zxYears = (19l - (age > 13 ? age : 13));
            //每年领取金额
            zxAmount = new BigDecimal(500).setScale(2, BigDecimal.ROUND_HALF_UP);
            zxInsuranceAmount = new BigDecimal(500 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);
            //总领取金额
            totalZxInsuranceAmount = zxAmount.multiply(new BigDecimal(zxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (!AssertUtils.isNotNull(receiveAge)) {
                receiveAge = zxStartAge;
            }
            System.out.println("中学教育金开始时间:" + zxStartTime);
            System.out.println("中学教育金结束时间:" + zxEndTime);
            System.out.println("开始领取岁数:" + zxStartAge);
            System.out.println("中学教育金(美元):" + zxAmount + "/年,总领取:" + zxYears + "年");
            System.out.println("中学教育金计算公式:" + 500 + "美元/每年×" + mult + "份×" + zxYears + "年");
            System.out.println("总领取教育金(美元):" + totalZxInsuranceAmount);
        }

        //大中教育金
        if (age <= 22 && "B".equals(productLevel)) {
            //计算开始时间
            dxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 19 ? age : 19) - age);
            //计算结束时间
            dxEndTime = policyEffectiveDateEnd;
            //开始领取岁数
            dxStartAge = (age > 19l ? age : 19l);
            //总计领取年限
            dxYears = (23l - (age > 19 ? age : 19));
            //每年领取金额
            dxAmount = new BigDecimal(700).setScale(2, BigDecimal.ROUND_HALF_UP);
            dxInsuranceAmount = new BigDecimal(700 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);
            //总领取金额
            totalDxInsuranceAmount = dxAmount.multiply(new BigDecimal(dxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (!AssertUtils.isNotNull(receiveAge)) {
                receiveAge = dxStartAge;
            }
            System.out.println("大学教育金开始时间:" + dxStartTime);
            System.out.println("大学教育金结束时间:" + dxEndTime);
            System.out.println("开始领取岁数:" + dxStartAge);
            System.out.println("大学教育金(美元):" + dxAmount + "/年,总领取:" + dxYears + "年");
            System.out.println("大学中学教育金计算公式:" + 700 + "美元/每年×" + mult + "份×" + dxYears + "年");
            System.out.println("总领取教育金(美元):" + totalDxInsuranceAmount);
        }


        //大學教育金
        if (age <= 22 && "C".equals(productLevel)) {
            //计算开始时间
            dxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 19 ? age : 19) - age);
            //计算结束时间
            dxEndTime = policyEffectiveDateEnd;
            //开始领取岁数
            dxStartAge = (age > 19l ? age : 19l);
            //总计领取年限
            dxYears = (23l - (age > 19 ? age : 19));
            //每年领取金额
            dxAmount = new BigDecimal(1200).setScale(2, BigDecimal.ROUND_HALF_UP);
            dxInsuranceAmount = new BigDecimal(1200 * mult).setScale(2, BigDecimal.ROUND_HALF_UP);

            //总领取金额
            totalDxInsuranceAmount = dxAmount.multiply(new BigDecimal(dxYears)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (!AssertUtils.isNotNull(receiveAge)) {
                receiveAge = dxStartAge;
            }
            System.out.println("大学教育金开始时间:" + dxStartTime);
            System.out.println("大学教育金结束时间:" + dxEndTime);
            System.out.println("开始领取岁数:" + dxYears);
            System.out.println("大学教育金(美元):" + dxAmount + "/年,总领取:" + dxYears + "年");
            System.out.println("大学中学教育金计算公式:" + 1200 + "美元/每年×" + mult + "份×" + dxYears + "年");
            System.out.println("总领取教育金(美元):" + totalDxInsuranceAmount);

        }

        String policyOrPolicies = null;
        if (coverageMult == 1) {
            policyOrPolicies = "Policy";
        } else {
            policyOrPolicies = "Policies";
        }

        String year = "Year";
        String years = "Years";

        PrintCommon.setPrintData(printObjectList, "totalZJInsuranceAmount", totalZJInsuranceAmount, 1);
        PrintCommon.setPrintData(printObjectList, "zJInsuranceAmount", zJInsuranceAmount, 1);

        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil", policyEffectiveDateEnd, 3);

        PrintCommon.setPrintDateTime(printObjectList, "hmStartTime", hmStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "hmEndTime", hmEndTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zdStartTime", zdStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zdEndTime", zdEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "zdMult", zdMult, 3);
        String zdDescriptionZH_CN = null;
        String zdDescriptionEN_US = null;
        String zdDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(zJInsuranceAmount) && AssertUtils.isNotNull(zdMult)) {
            zdDescriptionZH_CN = zJInsuranceAmount + "美元 × " + zdMult + "份";
            zdDescriptionEN_US = zJInsuranceAmount + "USD × " + zdMult + policyOrPolicies;
            zdDescriptionKM_KH = zJInsuranceAmount + "ដុល្លារសហរដ្ឋអាមេរិក × " + zdMult + "ច្បាប់";
        }
        PrintCommon.setPrintData(printObjectList, "zdDescriptionZH_CN", zdDescriptionZH_CN, 1);
        PrintCommon.setPrintData(printObjectList, "zdDescriptionEN_US", zdDescriptionEN_US, 1);
        PrintCommon.setPrintData(printObjectList, "zdDescriptionKM_KH", zdDescriptionKM_KH, 1);


        PrintCommon.setPrintDateTime(printObjectList, "xxStartTime", xxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "xxEndTime", xxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "xxInsuranceAmount", xxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxStartAge", xxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalXxInsuranceAmount", totalXxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxAmount", xxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxMult", xxMult, 3);
        PrintCommon.setPrintData(printObjectList, "xxYears", xxYears, 3);
        String xxDescriptionZH_CN = null;
        String xxDescriptionEN_US = null;
        String xxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(xxAmount) && AssertUtils.isNotNull(xxMult) && AssertUtils.isNotNull(xxYears)) {
            xxDescriptionZH_CN = xxAmount + "美元/每年 × " + xxMult + "份 × " + xxYears + "年";
            xxDescriptionEN_US = xxAmount + " USD/Every Year × " + xxMult + policyOrPolicies + " × " + xxYears + (xxYears == 1l ? year : years);
            xxDescriptionKM_KH = xxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + xxMult + "ច្បាប់ × " + xxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "xxDescriptionZH_CN", xxDescriptionZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "xxDescriptionEN_US", xxDescriptionEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "xxDescriptionKM_KH", xxDescriptionKM_KH, 3);


        PrintCommon.setPrintDateTime(printObjectList, "zxStartTime", zxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zxEndTime", zxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "zxInsuranceAmount", zxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxStartAge", zxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalZxInsuranceAmount", totalZxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxAmount", zxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxMult", zxMult, 3);
        PrintCommon.setPrintData(printObjectList, "zxYears", zxYears, 3);
        String zxDescriptionZH_CN = null;
        String zxDescriptionEN_US = null;
        String zxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(zxAmount) && AssertUtils.isNotNull(zxMult) && AssertUtils.isNotNull(zxYears)) {
            zxDescriptionZH_CN = zxAmount + "美元/每年 × " + zxMult + "份 × " + zxYears + "年";
            zxDescriptionEN_US = zxAmount + " USD/Every Year × " + zxMult + policyOrPolicies + " × " + zxYears + (zxYears == 1l ? year : years);
            zxDescriptionKM_KH = zxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + zxMult + "ច្បាប់ × " + zxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "zxDescriptionZH_CN", zxDescriptionZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "zxDescriptionEN_US", zxDescriptionEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "zxDescriptionKM_KH", zxDescriptionKM_KH, 3);

        PrintCommon.setPrintDateTime(printObjectList, "dxStartTime", dxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "dxEndTime", dxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "dxInsuranceAmount", dxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxStartAge", dxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalDxInsuranceAmount", totalDxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxAmount", dxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxMult", dxMult, 3);
        PrintCommon.setPrintData(printObjectList, "dxYears", dxYears, 3);
        String dxDescriptionZH_CN = null;
        String dxDescriptionEN_US = null;
        String dxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(dxAmount) && AssertUtils.isNotNull(dxMult) && AssertUtils.isNotNull(dxYears)) {
            dxDescriptionZH_CN = dxAmount + "美元/每年 × " + dxMult + "份 × " + dxYears + "年";
            dxDescriptionEN_US = dxAmount + " USD/Every Year × " + dxMult + policyOrPolicies + " × " + dxYears + (dxYears == 1l ? year : years);
            dxDescriptionKM_KH = dxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + dxMult + "ច្បាប់ × " + dxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "dxDescriptionZH_CN", dxDescriptionZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "dxDescriptionEN_US", dxDescriptionEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "dxDescriptionKM_KH", dxDescriptionKM_KH, 3);


        PrintCommon.setPrintData(printObjectList, "receiveAge", receiveAge, 3);
    }
}
