package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.attachment.model.config.AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER;
import static com.gclife.common.InternationalTypeEnum.BANK;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @description
 * @date 2020/5/12 4:53 下午
 */
@Component
public class GCEnrichLifeData {


    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    /**
     * <p>
     * <p>
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate =  planPrintBo.getCreatedDate();
        if(AssertUtils.isNotNull(planPrintBo.getBackTrackDate())){
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  可选其他缴费周期start***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount13 = new BigDecimal(0);
        BigDecimal juvenileCIAmount = BigDecimal.ZERO;// #22号附加险
        BigDecimal termProtectionRiderAmountA = BigDecimal.ZERO;// #23A号附加险
        BigDecimal termProtectionRiderAmountB = BigDecimal.ZERO;// #23B号附加险
        String premiumPeriod13 = null;
        String premiumFrequency13 = null;
        String coveragePeriod13 = null;
        String mult13 = null;
        String mult14 = null;
        String mult15 = null;

        BigDecimal totalAmount14 = new BigDecimal(0);
        String productLevel14 = null;
        BigDecimal totalAmount15 = new BigDecimal(0);
        BigDecimal totalPremium15 = new BigDecimal(0);
        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = null;
            if ("PRO880000000000013".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                totalAmount13 = amount;
                premiumPeriod13 = premiumPeriod;
                coveragePeriod13 = coveragePeriod;
                premiumFrequency13 = coverageBo.getPremiumFrequency();
                mult13 = coverageBo.getMult();
            } else if ("PRO880000000000014".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                totalAmount14 = amount;
                productLevel14 = productLevel;
                mult14 = coverageBo.getMult();
                if ("ACCELERATION_CI".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(提前给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                    coverageMap.put("productLevelEN_US", "(Acceleration)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(额外给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                    coverageMap.put("productLevelEN_US", "(Additional)");
                }
            } else if ("PRO880000000000015".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mult15 = coverageBo.getMult();
                totalAmount15 = amount;
            }
            if ("PRO880000000000022".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                juvenileCIAmount = amount;
            }
            if ("PRO880000000000023A".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountA = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023AOPTION_TWO", "PRO880000000000023AOPTION_TWO");
                }
            }
            if ("PRO880000000000023B".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountB = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023BOPTION_TWO", "PRO880000000000023BOPTION_TWO");
                }
            }
            if ("PRO880000000000016A".equals(coverageBo.getProductId()) || "PRO880000000000016B".equals(coverageBo.getProductId())) {
                map.put("PRO880000000000016", "PRO880000000000016");
            }
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            //每期保费
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int i = YEAR.name().equals(premiumFrequency) ? 1 : SEMIANNUAL.name().equals(premiumFrequency) ? 2 : SEASON.name().equals(premiumFrequency) ? 3 : MONTH.name().equals(premiumFrequency) ? 4 : 0;
            coverageMap.put("totalPremium" + i, PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            //可选缴费周期保险费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 1, coverageMap, cpfList, YEAR.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 2, coverageMap, cpfList, SEMIANNUAL.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 3, coverageMap, cpfList, SEASON.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 4, coverageMap, cpfList, MONTH.name(), premiumFrequency);

            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maturityAmount = yearTotalPremium.multiply(new BigDecimal(coverageBo.getPremiumPeriod())).setScale(2, BigDecimal.ROUND_HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", premiumFrequency13);
        double totalPremium1 = coverageListMap.stream().mapToDouble(mapper -> Double.valueOf((mapper.get("totalPremium1") + "").replace(",", ""))).sum();
        double totalPremium2 = coverageListMap.stream().mapToDouble(mapper -> Double.valueOf((mapper.get("totalPremium2") + "").replace(",", ""))).sum();
        double totalPremium3 = coverageListMap.stream().mapToDouble(mapper -> Double.valueOf((mapper.get("totalPremium3") + "").replace(",", ""))).sum();
        double totalPremium4 = coverageListMap.stream().mapToDouble(mapper -> Double.valueOf((mapper.get("totalPremium4") + "").replace(",", ""))).sum();
        map.put("allTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2), 2));
        map.put("allTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3), 2));
        map.put("allTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4), 2));
        map.put("allYearTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allYearTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2).multiply(new BigDecimal(2)), 2));
        map.put("allYearTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3).multiply(new BigDecimal(4)), 2));
        map.put("allYearTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4).multiply(new BigDecimal(12)), 2));
        /****************************************************************************保险利益***********************************************************************************/
        BigDecimal tpd_benefit_due_to_accident = totalAmount13.add(totalAmount15).add(termProtectionRiderAmountA);
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                tpd_benefit_due_to_accident)
                , 2));
        BigDecimal tpd_benefit_non_accident = totalAmount13.add(termProtectionRiderAmountA);
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        tpd_benefit_non_accident)
                , 2));
        BigDecimal death_benefit_due_to_accident = totalAmount13.add(totalAmount15.multiply(new BigDecimal(3))).add(termProtectionRiderAmountA);
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                death_benefit_due_to_accident)
                , 2));
        BigDecimal death_benefit_non_accident = totalAmount13.add(termProtectionRiderAmountA);
        map.put("death_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        death_benefit_non_accident)
                , 2));
        map.put("early_stage_critical_illness_benefit", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        totalAmount14.multiply(new BigDecimal("0.25")))
                , 2));
        // 被保险人晚期重大疾病保险金
        BigDecimal late_stage_critical_illness_benefit = totalAmount14;
        if ("OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            late_stage_critical_illness_benefit = late_stage_critical_illness_benefit.add(termProtectionRiderAmountA);
        }
        map.put("late_stage_critical_illness_benefit", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        late_stage_critical_illness_benefit)
                , 2));
        map.put("juvenileCIAmount", PrintCommon.getPrintAmountString(juvenileCIAmount,2));
        // 保单付款人意外高残保险金
        BigDecimal payorTPDAccidentAmount = termProtectionRiderAmountB;
        map.put("payorTPDAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAccidentAmount)
                , 2));
        // 保单付款人高残保险金
        BigDecimal payorTPDAmount = termProtectionRiderAmountB;
        map.put("payorTPDAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAccidentAmount = termProtectionRiderAmountB;
        map.put("payorDeathAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAccidentAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAmount = termProtectionRiderAmountB;
        map.put("payorDeathAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAmount)
                , 2));
        // 保单付款人晚期重大疾病保险金
        BigDecimal payorLateStageCriticalIllnessAmount = termProtectionRiderAmountB;
        map.put("payorLateStageCriticalIllnessAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorLateStageCriticalIllnessAmount)
                , 2));
        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int coveragePeriod13i = Integer.parseInt(coveragePeriod13);
        BigDecimal accumulatedPremiumsPaid = BigDecimal.ZERO;
        for (int i = 1; i <= coveragePeriod13i; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            interestMap.put("totalAmount13", PrintCommon.getPrintString("$", totalAmount13, 1));
            interestMap.put("totalAmount14", PrintCommon.getPrintString("$", totalAmount14, 1));
            interestMap.put("totalAmount15", PrintCommon.getPrintString("$", totalAmount15, 1));
            interestMap.put("interestMapJuvenileCIAmount", PrintCommon.getPrintString("$", juvenileCIAmount, 1));
            interestMap.put("totalAmount13_add_totalAmount15", PrintCommon.getPrintString("$", tpd_benefit_due_to_accident, 2));
            interestMap.put("totalAmount13_add_totalAmount15multiply_3", PrintCommon.getPrintString("$", death_benefit_due_to_accident, 2));
            interestMap.put("totalAmount15multiply3", PrintCommon.getPrintString("$", totalAmount15.multiply(new BigDecimal(3)), 1));
            interestMap.put("totalAmount13_15multiply3", PrintCommon.getPrintString("$", totalAmount13.add(totalAmount15.multiply(new BigDecimal(3))), 1));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            accumulatedPremiumsPaid = policyYearTotalPremium;// policyYearTotalPremium 就是累计已交保费
            // 累计已交保费
            interestMap.put("accumulatedPremiumsPaid", PrintCommon.getPrintString("$", accumulatedPremiumsPaid, 1));
            // 死亡或高残保险金
            interestMap.put("deathOrTPDBenefitsForInsuredAmount", PrintCommon.getPrintString("$", totalAmount13.add(termProtectionRiderAmountA), 1));
            // 保单付款人死亡或高残或重疾保险金
            interestMap.put("deathOrTPDOrCIBenefitsForPayorAmount", PrintCommon.getPrintString("$", termProtectionRiderAmountB, 1));
            // 被保险人意外高残保险金
            interestMap.put("insuredAccidentalTPD", PrintCommon.getPrintString("$", tpd_benefit_due_to_accident, 1));
            // 被保险人最高意外死亡保险金
            interestMap.put("insuredAccidentalDeathUpTo", PrintCommon.getPrintString("$", death_benefit_due_to_accident, 1));
            // 被保险人重大疾病保险金
            interestMap.put("insuredMapInsuredCriticalIllness", PrintCommon.getPrintString("$", late_stage_critical_illness_benefit, 1));
            interestMap.put("interestMapPRO880000000000023B", map.get("PRO880000000000023B"));
            interestMap.put("interestMapPRO880000000000023A", map.get("PRO880000000000023A"));
            interestMap.put("interestMapPRO880000000000022", map.get("PRO880000000000022"));
            interestMap.put("interestMapPRO880000000000014", map.get("PRO880000000000014"));
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO880000000000013".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                BigDecimal cashValue13 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000013", i);
                BigDecimal cashValue14 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000014", i);
                BigDecimal cashValue15 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000015", i);
                interestMap.put("cashValue13", PrintCommon.getPrintString("$", cashValue13, 1));
                map.put("PRO880000000000013maturityAmount", PrintCommon.getPrintString(cashValue13, 2));
                map.put("PRO880000000000013maturityAmount_1", PrintCommon.getPrintString(cashValue13, 2));
                interestMap.put("cashValue14", PrintCommon.getPrintString("$", cashValue14, 1));
                interestMap.put("cashValue15", PrintCommon.getPrintString("$", cashValue15, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue13.add(cashValue14).add(cashValue15), 1));
            }
            interestListMap.add(interestMap);
        }
        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("totalAmount13", PrintCommon.getPrintString(totalAmount13, 2));
        map.put("totalAmount14", PrintCommon.getPrintString(totalAmount14, 2));
        map.put("totalAmount14multiply_25", PrintCommon.getPrintString(totalAmount14.multiply(new BigDecimal("0.25")), 2));
        map.put("totalAmount14multiply_75", PrintCommon.getPrintString(totalAmount14.multiply(new BigDecimal("0.75")), 2));
        map.put("totalAmount15", PrintCommon.getPrintString(totalAmount15, 2));
        map.put("totalAmount15multiply_2", PrintCommon.getPrintString(totalAmount15.multiply(new BigDecimal(2)), 2));
        map.put("totalAmount15multiply_3", PrintCommon.getPrintString(totalAmount15.multiply(new BigDecimal(3)), 2));
        map.put("termProtectionRiderAmountA", PrintCommon.getPrintString(termProtectionRiderAmountA, 2));
        map.put("termProtectionRiderAmountB", PrintCommon.getPrintString(termProtectionRiderAmountB, 2));
        // 是否有重大疾病利益
        if (AssertUtils.isNotNull(map.get("PRO880000000000014"))
                || AssertUtils.isNotNull(map.get("PRO880000000000022"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023BProductLevel"))) {
            map.put("isCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        // 是否有晚期重大疾病利益
        if (AssertUtils.isNotNull(map.get("PRO880000000000014"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023BProductLevel"))) {
            map.put("isLateStageCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        // 是否有被保人重大疾病利益
        if (AssertUtils.isNotNull(map.get("PRO880000000000014"))
                || AssertUtils.isNotNull(map.get("PRO880000000000022"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            map.put("isInsuredLateStageCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Long backTrackDate = applyPrintBo.getApplyDate();
        if(AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())){
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("prohibitedString2", PrintCommon.getPrintString(null, 2));
        map.put("prohibitedString3", PrintCommon.getPrintString(null, 3));
        map.put("prohibitedString4", PrintCommon.getPrintString(null, 4));
        map.put("prohibitedString5", PrintCommon.getPrintString(null, 5));
        map.put("prohibitedString6", PrintCommon.getPrintString(null, 6));
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));
        /*****************************************投保人********************************************************************/
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        long applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        String applicantExpectedPremiumSources = applicant.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "applicantEPS", applicantExpectedPremiumSources);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        map.put("applicantIncome", PrintCommon.getPrintString(applicant.getIncome(), 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //移动电话
        map.put("applicantMobile_2", PrintCommon.getPrintString(applicant.getMobile_2(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //通讯地址
        String applicantCompanyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String applicantCompanyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("applicantCompanyAddressWhole", PrintCommon.getPrintString(applicantCompanyAreaName + applicantCompanyAddress, 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        map.put("applicantFacebookNo", PrintCommon.getPrintString(applicant.getFacebookNo(), 3));
        map.put("applicantStature", PrintCommon.getPrintString(applicant.getStature(), 3));
        map.put("applicantAvoirdupois", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("applicantExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(applicant.getExpectedPremiumSourcesSpecific(), 3));
        map.put("applicantDoctorName", PrintCommon.getPrintString(applicant.getDoctorName(), 3));
        String applicantDoctorAreaCodeName = AssertUtils.isNotEmpty(applicant.getDoctorAreaCodeName()) ? applicant.getDoctorAreaCodeName() : "";
        String applicantDoctorAddress = AssertUtils.isNotEmpty(applicant.getDoctorAddress()) ? applicant.getDoctorAddress() : "";
        map.put("applicantDoctorAreaCodeName", PrintCommon.getPrintString(applicantDoctorAreaCodeName + applicantDoctorAddress, 3));
        PrintCommon.setSelectionBox(map, "applicantAddressType" + applicant.getAddressType(), applicant.getAddressType());
        /******************************************************被保人********************************************************************/
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }
        map.put("relationshipName", insured.getRelationshipName());

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        String insuredExpectedPremiumSources = insured.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "insuredEPS", insuredExpectedPremiumSources);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(insured.getIncome(), 2));
        long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //移动电话
        map.put("insuredMobile_2", PrintCommon.getPrintString(insured.getMobile_2(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //通讯地址
        String insuredCompanyAreaName = AssertUtils.isNotEmpty(insured.getCompanyAreaName()) ? insured.getCompanyAreaName() : "";
        String insuredCompanyAddress = AssertUtils.isNotEmpty(insured.getCompanyAddress()) ? insured.getCompanyAddress() : "";
        map.put("insuredCompanyAddressWhole", PrintCommon.getPrintString(insuredCompanyAreaName + insuredCompanyAddress, 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        map.put("insuredFacebookNo", PrintCommon.getPrintString(insured.getFacebookNo(), 3));
        map.put("insuredStature", PrintCommon.getPrintString(insured.getStature(), 3));
        map.put("insuredAvoirdupois", PrintCommon.getPrintString(insured.getAvoirdupois(), 3));
        map.put("insuredExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(insured.getExpectedPremiumSourcesSpecific(), 3));
        map.put("insuredDoctorName", PrintCommon.getPrintString(insured.getDoctorName(), 3));
        String insuredDoctorAreaCodeName = AssertUtils.isNotEmpty(insured.getDoctorAreaCodeName()) ? insured.getDoctorAreaCodeName() : "";
        String insuredDoctorAddress = AssertUtils.isNotEmpty(insured.getDoctorAddress()) ? insured.getDoctorAddress() : "";
        map.put("insuredDoctorAreaCodeName", PrintCommon.getPrintString(insuredDoctorAreaCodeName + insuredDoctorAddress, 3));
        map.put("taxpayerNo", PrintCommon.getPrintString(insured.getTaxpayerNo(), 3));
        PrintCommon.setSelectionBox(map, "insuredAddressType" + insured.getAddressType(), insured.getAddressType());
        /******************************************************受益人信息***************************************************************/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //证件类型
                beneficiaryMap.put("homeAddress", PrintCommon.getPrintString(applyBeneficiaryBo.getHomeAddress(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });
            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        /**********************************************************险种信息**************************************************************/
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel(), language);
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000009".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                if ("PRO880000000000014".equals(applyCoverageBo.getProductId())) {
                    if ("ACCELERATION_CI".equals(applyCoverageBo.getProductLevel())) {
                        coverageMap.put("productLevelZH_CN", "(提前给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                        coverageMap.put("productLevelEN_US", "(Acceleration)");
                    } else {
                        coverageMap.put("productLevelZH_CN", "(额外给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                        coverageMap.put("productLevelEN_US", "(Additional)");
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }
        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));
        /*****************************************交费*******************************************************************/
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOtherInsuranceBo> listApplyOtherInsurancePo = applyPrintBo.getOtherInsurance();
        List<Map<String, Object>> applyOtherInsuranceListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyOtherInsurancePo)) {
            for (ApplyOtherInsuranceBo applyOtherInsuranceBo : listApplyOtherInsurancePo) {
                Map<String, Object> applyOtherInsuranceMap = new HashMap<>();
                applyOtherInsuranceMap.put("otherInsuringInsuredName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuredName(), 3));
                applyOtherInsuranceMap.put("otherInsuringCompany", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuringCompany(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceTypeName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceTypeName(), 3));
                applyOtherInsuranceMap.put("otherInsuringAmount", PrintCommon.getPrintString(applyOtherInsuranceBo.getAmount(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceYear", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceYear(), 3));
                applyOtherInsuranceListMap.add(applyOtherInsuranceMap);
            }
        }
        map.put("applyOtherInsuranceListMap", applyOtherInsuranceListMap);
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOccupationNatureBo> occupationNatureList = applyPrintBo.getOccupationNature();
        if (!AssertUtils.isNotEmpty(occupationNatureList)) {
            occupationNatureList = new ArrayList<>();
        }
        ApplyOccupationNatureBo applicantOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> applicantOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "APPLICANT".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (applicantOptionalOccupationNatureBo.isPresent()) {
            applicantOccupationNatureBo = applicantOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "applicantON" + applicantOccupationNatureBo.getOccupationNature(), applicantOccupationNatureBo.getOccupationNature());
        map.put("applicantOccupationNatureSpecific", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("applicantEmployerName", PrintCommon.getPrintString(applicantOccupationNatureBo.getEmployerName(), 3));
        map.put("applicantBusinessNature", PrintCommon.getPrintString(applicantOccupationNatureBo.getBusinessNature(), 3));
        map.put("applicantOccupationExactDuties", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupation(), 3));
        map.put("applicantOccupationClass", PrintCommon.getPrintString(applicantOccupationNatureBo.getExactDuties(), 3));

        ApplyOccupationNatureBo insuredOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> insuredOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "INSURED".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (insuredOptionalOccupationNatureBo.isPresent()) {
            insuredOccupationNatureBo = insuredOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "insuredON" + insuredOccupationNatureBo.getOccupationNature(), insuredOccupationNatureBo.getOccupationNature());
        map.put("insuredOccupationNatureSpecific", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("insuredEmployerName", PrintCommon.getPrintString(insuredOccupationNatureBo.getEmployerName(), 3));
        map.put("insuredBusinessNature", PrintCommon.getPrintString(insuredOccupationNatureBo.getBusinessNature(), 3));
        map.put("insuredOccupationExactDuties", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupation(), 3));
        map.put("insuredOccupationClass", PrintCommon.getPrintString(insuredOccupationNatureBo.getExactDuties(), 3));
        /********************************************账户*********************************************************/
        List<ApplyAccountBo> listApplyAccount = applyPrintBo.getListApplyAccount();
        ApplyAccountBo applyAccountBo = new ApplyAccountBo();
        String kmKmBankName = null;
        String bankName = null;
        if (AssertUtils.isNotEmpty(listApplyAccount)) {
            applyAccountBo = listApplyAccount.get(0);
            if (AssertUtils.isNotEmpty(applyAccountBo.getBankCode())) {
                SyscodeResponse kmKmBankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name()).getData();
                SyscodeResponse bankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name().equals(language) ? EN_US.name() : language).getData();
                kmKmBankName = AssertUtils.isNotNull(kmKmBankSyscode) && AssertUtils.isNotEmpty(kmKmBankSyscode.getCodeName()) ? kmKmBankSyscode.getCodeName() : null;
                bankName = AssertUtils.isNotNull(bankSyscode) && AssertUtils.isNotEmpty(bankSyscode.getCodeName()) ? bankSyscode.getCodeName() : null;
            }
        }
        map.put("kmKmBankName", PrintCommon.getPrintString(kmKmBankName, 3));
        map.put("bankName", PrintCommon.getPrintString(bankName, 3));
        map.put("accountOwner", PrintCommon.getPrintString(applyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(applyAccountBo.getAccountNo(), 3));
        /********************************************健康告知书*********************************************************/
        ProductCalculation.setHealthRemark1(map, applyPrintBo);
        List<ApplyStatementBo> statements = applyPrintBo.getStatements();
        if (AssertUtils.isNotEmpty(statements)) {
            statements.forEach(applyStatementBo -> {
                map.put(applyStatementBo.getStatementCode(), applyStatementBo.getStatementValue());
            });
        }
        /********************************************投保申请日期*********************************************************/
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyAgentBo.getAgentMobile(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        PrintCommon.setProductName(map, electronicPolicyGeneratorRequest.getProductId(), "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? new BigDecimal(coverageBo.getTotalAmount()) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            if ("PRO880000000000014".equals(productId)) {
                if ("ACCELERATION_CI".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(提前给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                    coverageMap.put("productLevelEN_US", "(Acceleration)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(额外给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                    coverageMap.put("productLevelEN_US", "(Additional)");
                }
            }
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos) && "PRO880000000000013".equals(productId)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            coverageMap.put("maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "Single Payment";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        /***********************************现金价值******************************************/
        ProductCalculation.policyCashValues13(map, policyCashValueListMap);
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }



}
