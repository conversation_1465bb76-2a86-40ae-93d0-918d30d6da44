package com.gclife.attachment.service.print.policy.confirm;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Component
public class PolicyConfirmPersonalData {

    /**
     * 获取保单打印数据 只针对个险
     *
     * @return
     */
    public Map<String, Object> getPolicyConfirmData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        PrintCommon.setProductName(map, electronicPolicyGeneratorRequest.getProductId(), "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            // 24号产品不需要展示主险标识
            if (!"PRO880000000000024".equals(productId)) {
                coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            }
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? new BigDecimal(coverageBo.getTotalAmount()) : null, 2);
            String totalAmountString = null;
            if (AssertUtils.isNotEmpty(coverageBo.getTotalAmount())) {
                totalAmountString = PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount()));
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmountString, 2));
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            if ("PRO880000000000014".equals(productId)) {
                if ("ACCELERATION_CI".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(提前给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                    coverageMap.put("productLevelEN_US", "(Acceleration)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(额外给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                    coverageMap.put("productLevelEN_US", "(Additional)");
                }
            }
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maturityAmount = null;
            String maturityAmountString = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos) && "PRO880000000000013".equals(productId)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            if (AssertUtils.isNotNull(maturityAmount)) {
                maturityAmountString = PrintCommon.decimalFormat1.format(maturityAmount);
            }
            coverageMap.put("maturityAmount", PrintCommon.getPrintString(maturityAmountString, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            if ("PRO880000000000034".equals(productId)) {
                totalPremium = coverageBo.getAdditionalAccAmount();
                extraPremium = coverageBo.getTotalPremium();
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        String premiumFrequency = mainCoverageBo.getPremiumFrequency();
        int premiumPeriodNum = Integer.parseInt(AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "0");
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency()) || (YEAR.name().equals(premiumFrequency) && 1 == premiumPeriodNum)) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "一次性缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "Single Premium";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        // 34号产品只显示年缴
        if ("PRO880000000000034".equals(electronicPolicyGeneratorRequest.getProductId())) {
            premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        // 判断是否趸缴
        String mainPremiumFrequency = mainCoverageBo.getPremiumFrequency();
        int mainPremiumPeriod = Integer.parseInt(AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "0");
        String isSinglePayment = PaymentTermEnum.YES_NO.NO.name();
        // 趸缴 或 年缴且一年期
        if (SINGLE.name().equals(mainPremiumFrequency) || (YEAR.name().equals(mainPremiumFrequency) && 1 == mainPremiumPeriod)) {
            isSinglePayment = PaymentTermEnum.YES_NO.YES.name();
        }
        map.put("isSinglePayment", isSinglePayment);
        /***********************************现金价值******************************************/
        ProductCalculation.policyCashValues13(map, policyCashValueListMap);
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }


    public List<PrintObject> getGcSokSanPolicyConfirmData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();
        List<PrintObject> printObjectList = new ArrayList<>();
        //保单号
        PrintCommon.setPrintData(printObjectList, "policyNo", policyBo.getPolicyNo(), 3);
        //承保日期
        PrintCommon.setPrintDateTime(printObjectList, "approveDate", policyBo.getApproveDate(), 3);
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        //风险开始日期
        PrintCommon.setPrintDateTime(printObjectList, "backTrackDate", riskCommencementDate, 3);
        PolicyInsuredBo policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        //缴费周期
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", premiumFrequencyName, 2);
        /********************************************被保人信息***************************************/
        //被保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", policyInsuredBo.getName(), 3);
        //被保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", policyInsuredBo.getSexName(), 3);
        //被保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        //被保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "insuredIdNoAndIdTypeName", insuredIdNoAndIdTypeName, 3);
        //与投保人什么关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", policyInsuredBo.getRelationshipName(), 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "insuredMobile", policyInsuredBo.getMobile(), 3);
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        }
        PrintCommon.setPrintData(printObjectList, "insuredAgeYear", insuredAgeYear, 3);
        /********************************************投保人信息***************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", policyApplicant.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "applicantSexName", policyApplicant.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", policyApplicant.getIdNo(), 3);
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "applicantIdNoAndIdTypeName", applicantIdNoAndIdTypeName, 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "applicantMobile", policyApplicant.getMobile(), 3);
        Integer applicantAgeYear = null;
        if (AssertUtils.isNotNull(policyApplicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        }
        PrintCommon.setPrintData(printObjectList, "applicantAgeYear", applicantAgeYear, 3);
        PrintCommon.setPrintData(printObjectList, "applicantFullAddress", policyApplicant.getFullAddress(), 3);
        /********************************************保险利益信息***************************************/
        // 34号产品只有主险
        // 险种名称
        PrintCommon.setProductName(printObjectList, mainCoverageBo.getProductId(), mainCoverageBo.getProductLevel(), language);
        // 非意外保险金额
        BigDecimal totalAmount = new BigDecimal(mainCoverageBo.getTotalAmount());
        PrintCommon.setPrintData(printObjectList, "totalAmount", totalAmount, 2);
        // 意外保险金额
        BigDecimal additionalAccAmount = mainCoverageBo.getAdditionalAccAmount();
        PrintCommon.setPrintData(printObjectList, "additionalAccAmount", additionalAccAmount, 2);
        // 期缴保费
        BigDecimal totalPremium = mainCoverageBo.getTotalPremium();
        PrintCommon.setPrintData(printObjectList, "totalPremium", totalPremium, 2);
        //
        int premiumPeriodInteger = Integer.parseInt(mainCoverageBo.getPremiumPeriod());
        long premiumCessationDate = mainCoverageBo.getCoveragePeriodStartDate();
        String premiumFrequency = mainCoverageBo.getPremiumFrequency();
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            premiumCessationDate = DateUtils.addStringMonthRT(mainCoverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
        } else if (SEASON.name().equals(premiumFrequency)) {
            premiumCessationDate = DateUtils.addStringMonthRT(mainCoverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
        } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
            premiumCessationDate = DateUtils.addStringMonthRT(mainCoverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
        } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            premiumCessationDate = DateUtils.addStringMonthRT(mainCoverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
        }
        PrintCommon.setPrintDateTime(printObjectList, "premiumCessationDate", premiumCessationDate, 3);
        PrintCommon.setPrintDateTime(printObjectList, "coveragePeriodEndDate", mainCoverageBo.getCoveragePeriodEndDate(), 3);
        // 公司基础信息
        printObjectList.addAll(CompanyInfo.getCompanyBaseInfoList(language));
        Logger logger = LoggerFactory.getLogger(this.getClass());
        logger.info("保险证确认书" + JSON.toJSONString(printObjectList));
        return printObjectList;
    }
}
