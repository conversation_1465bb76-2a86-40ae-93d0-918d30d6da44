package com.gclife.attachment.service.print.insured;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyCoverageLevelBo;
import com.gclife.attachment.model.policy.endorse.*;
import com.gclife.attachment.model.policy.renewal.AgentBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Component
public class EndorseTeamLifeOfInsuredData {

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public List<Map<String, Object>> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        EndorsePrintAttachmentData applyPrintBo = JSON.parseObject(content, EndorsePrintAttachmentData.class);
        map.put("policyNo", PrintCommon.getPrintString(applyPrintBo.getPolicyNo(), 3));
        EndorsePrintApplicantBo applicantBo = applyPrintBo.getApplicantBo();
        if (!AssertUtils.isNotNull(applicantBo)) {
            applicantBo = new EndorsePrintApplicantBo();
        }
        map.put("applicantName", PrintCommon.getPrintString(applicantBo.getName(), 3));
        AgentBo agentBo = applyPrintBo.getAgentBo();
        if (!AssertUtils.isNotNull(applicantBo)) {
            agentBo = new AgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(agentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(agentBo.getMobile(), 3));
        EndorsePrintHealthQuestionnaireRemarkBo healthQuestionnaireRemarkBo = applyPrintBo.getHealthQuestionnaireRemarkBo();
        if (!AssertUtils.isNotNull(healthQuestionnaireRemarkBo)) {
            healthQuestionnaireRemarkBo = new EndorsePrintHealthQuestionnaireRemarkBo();
        }
        map.put("remark", PrintCommon.getPrintString(healthQuestionnaireRemarkBo.getRemark(), 3));
        PrintCommon.setPrintDateTime(map, "printDate", DateUtils.getCurrentTime(), 3);

        List<EndorsePrintBeneficiaryBo> beneficiaryBoList = applyPrintBo.getBeneficiaryBoList();
        List<EndorsePrintGroupHealthQuestionnaireAnswerBo> ghqaBoList = applyPrintBo.getGhqaBoList();
        List<EndorsePrintInsuredBo> insuredBoList = applyPrintBo.getInsuredBoList();
        if (!AssertUtils.isNotEmpty(insuredBoList)) {
            mapList.add(map);
            return mapList;
        }
        List<EndorsePrintCoverageBo> coverageBoList = applyPrintBo.getCoverageBoList();
        List<EndorsePrintCoverageLevelBo> coverageLevelBoList = applyPrintBo.getCoverageLevelBoList();
        List<EndorsePrintCoverageDutyBo> coverageDutyBoList = applyPrintBo.getCoverageDutyBoList();
        if (!AssertUtils.isNotEmpty(coverageBoList) || !AssertUtils.isNotEmpty(coverageLevelBoList)) {
            mapList.add(map);
            return mapList;
        }
        Map<String, List<EndorsePrintCoverageBo>> productMapListCoverageBo = coverageBoList.stream().collect(Collectors.groupingBy(EndorsePrintCoverageBo::getProductId));
        productMapListCoverageBo.forEach((productId, coverageList) -> {
            Map<String, Object> productMap = new HashMap<>();
            PrintCommon.setProductName(productMap, productId);
            List<String> coverageInsuredIdList = coverageList.stream().map(EndorsePrintCoverageBo::getInsuredId).collect(Collectors.toList());
            //增加被保人
            List<Map<String, Object>> addInsuredMapList = new ArrayList<>();
            List<String> addInsuredId = coverageList.stream().filter(coverageBo -> "ADD".equals(coverageBo.getChangeFlag())).map(EndorsePrintCoverageBo::getInsuredId).collect(Collectors.toList());
            final AtomicInteger[] insuredNo = {new AtomicInteger(0)};
            List<EndorsePrintInsuredBo> addInsuredList = insuredBoList.stream().filter(insuredBo -> {
                return ("ADD".equals(insuredBo.getChangeFlag()) && coverageInsuredIdList.contains(insuredBo.getInsuredId()))
                        || (AssertUtils.isNotEmpty(addInsuredId) && addInsuredId.contains(insuredBo.getInsuredId()));
            }).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(addInsuredList)) {
                addInsuredList.forEach(addInsuredBo -> {
                    Map<String, Object> addInsuredMap = new HashMap<>();
                    this.setInsuredMap(productId, language, addInsuredMap, addInsuredBo, beneficiaryBoList, ghqaBoList, coverageList, coverageLevelBoList, coverageDutyBoList,applyPrintBo.getEffectiveDate());
                    addInsuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                    addInsuredMapList.add(addInsuredMap);
                });

                //只展示主险的受益人
                coverageList.forEach(endorsePrintCoverageBo -> {
                    if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(endorsePrintCoverageBo.getPrimaryFlag())) {
                        this.setAddInsuredBeneficiary(productMap, addInsuredList, beneficiaryBoList);
                    }
                });
            }
            //减少被保人
            insuredNo[0] = new AtomicInteger(0);
            List<Map<String, Object>> subtractInsuredMapList = new ArrayList<>();
            insuredBoList.stream().filter(insuredBo -> {
                return "SUBTRACT".equals(insuredBo.getChangeFlag()) && coverageInsuredIdList.contains(insuredBo.getInsuredId());
            }).forEach(subtractInsuredBo -> {
                Map<String, Object> subtractInsuredMap = new HashMap<>();
                this.setInsuredMap(productId, language, subtractInsuredMap, subtractInsuredBo, beneficiaryBoList, ghqaBoList, coverageList, coverageLevelBoList, coverageDutyBoList,applyPrintBo.getEffectiveDate());
                subtractInsuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                subtractInsuredMapList.add(subtractInsuredMap);
            });
            if (AssertUtils.isNotEmpty(addInsuredMapList) || AssertUtils.isNotEmpty(subtractInsuredMapList)) {
                productMap.put("addInsuredMapList", addInsuredMapList);
                productMap.put("subtractInsuredMapList", subtractInsuredMapList);
                productMap.putAll(map);
                mapList.add(productMap);
            }
        });
        PrintCommon.coverageSort(mapList);
        if (!AssertUtils.isNotEmpty(mapList)) {
            mapList.add(map);
        }
        return mapList;
    }

    private void setAddInsuredBeneficiary(Map<String, Object> productMap, List<EndorsePrintInsuredBo> addInsuredList, List<EndorsePrintBeneficiaryBo> beneficiaryBoList) {
        if (!AssertUtils.isNotEmpty(beneficiaryBoList)) {
            return;
        }
        List<String> insuredIdList = addInsuredList.stream().map(EndorsePrintInsuredBo::getInsuredId).collect(Collectors.toList());
        List<EndorsePrintBeneficiaryBo> beneficiaryList = beneficiaryBoList.stream().filter(applyBeneficiaryBo -> insuredIdList.contains(applyBeneficiaryBo.getInsuredId())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(beneficiaryList)) {
            List<Map<String, Object>> beneficiaryMapList = new ArrayList<>();
            AtomicInteger insuredNo = new AtomicInteger(1);
            Map<String, List<EndorsePrintBeneficiaryBo>> insuredIdBeneficiaryListMap = beneficiaryList.stream().collect(Collectors.groupingBy(EndorsePrintBeneficiaryBo::getInsuredId));
            insuredIdBeneficiaryListMap.forEach((s, applyBeneficiaryBos) -> {
                applyBeneficiaryBos.forEach(beneficiaryBo -> {
                    Map<String, Object> beneficiaryMap = new HashMap<>();
                    Optional<EndorsePrintInsuredBo> applyInsuredBoOptional = addInsuredList.stream().filter(applyInsuredBo -> applyInsuredBo.getInsuredId().equals(beneficiaryBo.getInsuredId())).findFirst();
                    EndorsePrintInsuredBo applyInsuredBo = applyInsuredBoOptional.get();
                    beneficiaryMap.put("insuredNo", PrintCommon.getPrintString(insuredNo.getAndIncrement(), 3));
                    beneficiaryMap.put("insuredName", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                    beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryNoOrderName(), 3));
                    beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryBo.getName(), 3));
                    beneficiaryMap.put("sexName", PrintCommon.getPrintString(beneficiaryBo.getSexName(), 3));
                    if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(beneficiaryBo.getRelationship()) && AssertUtils.isNotEmpty(beneficiaryBo.getRelationshipInstructions())) {
                        beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipInstructions(), 3));
                    } else {
                        beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipName(), 3));
                    }
                    beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryProportion(), 3));
                    beneficiaryMap.put("idTypeName", PrintCommon.getPrintString(beneficiaryBo.getIdTypeName(), 3));
                    beneficiaryMap.put("idNo", PrintCommon.getPrintString(beneficiaryBo.getIdNo(), 3));
                    beneficiaryMapList.add(beneficiaryMap);
                });
            });
            productMap.put("beneficiaryMapList", beneficiaryMapList);
        }
    }

    private void setInsuredMap(String productId, String language,
                               Map<String, Object> insuredMap,
                               EndorsePrintInsuredBo addInsured,
                               List<EndorsePrintBeneficiaryBo> beneficiaryBoList,
                               List<EndorsePrintGroupHealthQuestionnaireAnswerBo> ghqaBoList,
                               List<EndorsePrintCoverageBo> coverageBoList,
                               List<EndorsePrintCoverageLevelBo> coverageLevelBoList,
                               List<EndorsePrintCoverageDutyBo> coverageDutyBoList,
                               long effectiveDate) {

        insuredMap.put("name", PrintCommon.getPrintString(addInsured.getName(), 3));
        insuredMap.put("idTypeName", PrintCommon.getPrintString(addInsured.getIdTypeName(), 3));
        insuredMap.put("idNo", PrintCommon.getPrintString(addInsured.getIdNo(), 3));
        Integer age = null;
        if(AssertUtils.isNotNull(addInsured.getBirthday())){
            try{
                age =  DateUtils.getAgeYear(new Date(addInsured.getBirthday()),new Date(effectiveDate));
            }catch (Exception e){
                e.printStackTrace();
                throw new RequestException();
            }
        }
        insuredMap.put("age", PrintCommon.getPrintString(age, 3));
        insuredMap.put("sexName", PrintCommon.getPrintString(addInsured.getSexName(), 3));
        final String[] answerCodeJoin = {null};
        if (AssertUtils.isNotEmpty(ghqaBoList)) {
            ghqaBoList.stream().filter(ghqaBo -> addInsured.getInsuredId().equals(ghqaBo.getInsuredId()) && AssertUtils.isNotEmpty(ghqaBo.getAnswer())).forEach(ghqaBo -> {
                answerCodeJoin[0] = (AssertUtils.isNotEmpty(answerCodeJoin[0]) ? answerCodeJoin[0] + "," : "") + ghqaBo.getAnswer();
            });
        }
        insuredMap.put("answerCodeJoin", PrintCommon.getPrintString(answerCodeJoin[0], 3));
        insuredMap.put("occupationCode", PrintCommon.getPrintString(addInsured.getOccupationCode(), 3));
        final BigDecimal[] totalPremium = {new BigDecimal(0)};
        final BigDecimal[] refundAmount = {new BigDecimal(0)};
        final String[] totalAmount = {null};
        coverageBoList.stream().filter(coverageBo -> addInsured.getInsuredId().equals(coverageBo.getInsuredId())).forEach(coverageBo -> {
            BigDecimal coverageActualPremium = AssertUtils.isNotNull(coverageBo.getActualPremium()) ? coverageBo.getActualPremium() : new BigDecimal(0.0);
            BigDecimal coverageRefundAmount = AssertUtils.isNotNull(coverageBo.getRefundAmount()) ? coverageBo.getRefundAmount() : new BigDecimal(0.0);
            totalPremium[0] = totalPremium[0].add(coverageActualPremium);
            refundAmount[0] = refundAmount[0].add(coverageRefundAmount);
            List<EndorsePrintCoverageLevelBo> cLBoList = coverageLevelBoList.stream().filter(coverageLevelBo -> coverageBo.getCoverageId().equals(coverageLevelBo.getCoverageId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(coverageDutyBoList)) {
                cLBoList.forEach(groupAttachApplyCoverageLevelBo -> {
                    String coverageDutyId = groupAttachApplyCoverageLevelBo.getCoverageDutyId();
                    if (AssertUtils.isNotEmpty(coverageDutyId)) {
                        coverageDutyBoList.stream().filter(acdBo -> coverageDutyId.equals(acdBo.getCoverageDutyId())).findFirst().ifPresent(acdBo -> {
                            groupAttachApplyCoverageLevelBo.setCoverageDutyId(acdBo.getDutyId());
                        });
                    }
                });
                cLBoList.sort(Comparator.comparing(EndorsePrintCoverageLevelBo::getCoverageDutyId,Comparator.nullsLast(String::compareTo)));
            }
            cLBoList.forEach(coverageLevelBo -> {
                String mult = AssertUtils.isNotEmpty(coverageLevelBo.getMult()) ? coverageLevelBo.getMult() : "1";
                String productLevel = coverageLevelBo.getProductLevel();
                String dutyId = coverageLevelBo.getCoverageDutyId();
                ProductCalculation.setAmount(productId, totalAmount, mult, productLevel, dutyId);
            });
        });
        if (AssertUtils.isNotEmpty(addInsured.getAddInsuredId()) || AssertUtils.isNotEmpty(addInsured.getSubtractInsuredId())) {
            totalPremium[0] = new BigDecimal(0);
            refundAmount[0] = new BigDecimal(0);
        }
        insuredMap.put("totalPremium", PrintCommon.getPrintString(totalPremium[0], 3));
        insuredMap.put("refundAmount", PrintCommon.getPrintString(refundAmount[0], 3));
        insuredMap.put("totalAmount", PrintCommon.getPrintString(totalAmount[0], 3));
        boolean beneficiaryPresent = false;
        if (AssertUtils.isNotEmpty(beneficiaryBoList)) {
            Optional<EndorsePrintBeneficiaryBo> beneficiaryBoOptional = beneficiaryBoList.stream().filter(beneficiaryBo -> addInsured.getInsuredId().equals(beneficiaryBo.getInsuredId())).findFirst();
            beneficiaryPresent = beneficiaryBoOptional.isPresent();
        }
        String beneficiaryInformation = PrintCommon.getBeneficiaryInformation(language, beneficiaryPresent);
        insuredMap.put("beneficiaryInformation", PrintCommon.getPrintString(beneficiaryInformation, 3));
    }

}
