package com.gclife.attachment.service.business;

import com.gclife.attachment.model.request.*;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.model.response.CoiBatchResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件上传service处理
 */
public interface AttachmentBusinessService extends BaseBusinessService {


    /**
     * 上传多媒体文件
     *
     * @param users        当前登录用户
     * @param fileName
     * @param type         文件类型
     * @param files        文件集合
     * @param attachmentId
     * @return ResultObject<AttachmentResponse>
     */
    ResultObject<AttachmentResponse> uploadMedia(Users users, String fileName, String type, MultipartFile files, String attachmentId);


    /**
     * 下载媒体文件 返回url
     *
     * @param users   当前登录用户
     * @param mediaId 媒体ID
     * @return ResultObject<AttachmentResponse>
     */
    ResultObject<AttachmentResponse> loadMedia(Users users, String mediaId);

    /**
     * 下载媒体文件 可预览可下载
     */
    ResultObject<AttachmentResponse> loadAutoMedia(Users users, String mediaId);


    /**
     * 下载媒体文件 返回字节流
     *
     * @param users   当前登录用户
     * @param mediaId 媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    ResultObject<AttachmentByteResponse> loadMediaByte(Users users, String mediaId);


    /**
     * 下载媒体文件 (缩放) 返回url
     *
     * @param users      当前登录用户
     * @param proportion 缩放比例
     * @param mediaId    媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    ResultObject<AttachmentResponse> loadMediaProportion(Users users, long proportion, String mediaId);


    /**
     * 下载媒体文件 (缩放) 返回字节流
     *
     * @param users      当前登录用户
     * @param proportion 缩放比例
     * @param mediaId    媒体ID
     * @return ResultObject<AttachmentByteResponse>
     */
    ResultObject<AttachmentByteResponse> loadMediaByteProportion(Users users, long proportion, String mediaId);


    /**
     * Base64上传多媒体文件
     *
     * @param users             当前登录用户
     * @param type              文件类型
     * @param attachmentRequest 文件信息
     * @return ResultObject<AttachmentResponse>
     */
    ResultObject<AttachmentResponse> uploadMediaBase64(Users users, String type, AttachmentRequest attachmentRequest);


    /**
     * Base64上传多媒体文件
     *
     * @param type              文件类型
     * @param attachmentRequest 文件信息
     * @return ResultObject<AttachmentResponse>
     */
    AttachmentResponse uploadMediaBase64(String type, AttachmentRequest attachmentRequest);


    /**
     * Base64批量上传多媒体文件
     *
     * @param users                  当前登录用户
     * @param type                   文件类型
     * @param attachmentBatchRequest 文件集合信息
     * @return ResultObject<List < AttachmentResponse>>
     */
    ResultObject<List<AttachmentResponse>> uploadMediaBatchBase64(Users users, String type, AttachmentBatchRequest attachmentBatchRequest);

    /**
     * 电子保单生成，加密，并上传到阿里云
     *
     * @param users                            当前登录用户
     * @param electronicPolicyGeneratorRequest 电子保单生成请求
     * @return ResultObject<AttachmentResponse>
     */
    ResultObject<List<AttachmentResponse>> electronicPolicyGenerator(Users users, ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest);

    /**
     * 电子保单下载并加密
     *
     * @param response
     * @param password     文件加锁
     * @param attachmentId
     * @return
     */
    ResultObject<AttachmentByteResponse> electronicPolicyDownload(HttpServletResponse response, String password, String... attachmentId);

    /**
     * 电子保单下载并加密
     *
     * @param response
     * @param password     文件加锁
     * @param attachmentId
     * @return
     */
    ResultObject<AttachmentByteResponse> electronicPolicyDownloadCoi(HttpServletResponse response, String password, String... attachmentId);


    /**
     * 二维码生成并保存到阿里云
     *
     * @param qrCodeAttachmentRequest 附件请求
     * @return ResultObject<AttachmentByteResponse>
     */
    ResultObject<AttachmentResponse> attachmentQrCodeGenerate(Users users, QrCodeAttachmentRequest qrCodeAttachmentRequest);

    /**
     * 查询模版
     *
     * @param users
     * @param templateCode
     * @return
     */
    ResultObject<AttachmentResponse> templateGet(Users users, String templateCode);


    /**
     * 二维码生成并保存到阿里云
     *
     * @param qrCodeRequest 附件请求
     * @return ResultObject<AttachmentByteResponse>
     */
    ResultObject<AttachmentResponse> generateQrcode(QRCodeRequest qrCodeRequest);

    /**
     * 单个附件删除操作
     *
     * @param users
     * @param mediaId
     * @return
     */
    ResultObject deleteOneAttachment(Users users, String mediaId);

    /**
     * 批量附件删除操作
     *
     * @param users
     * @param mediaIds
     * @return
     */
    ResultObject deleteAttachment(Users users, List<String> mediaIds);

    /**
     * 通过attachment表中的ID获取一个OSSObject
     *
     * @param mediaId attachment表中的主键ID
     * @return OSSObject
     */
    byte[] loadOssObjectByAttachmentId(String mediaId);

    /**
     * 附件下载
     *
     * @param response
     * @param attachmentId
     */
    void downloadAttachments(HttpServletResponse response, String attachmentId);

    /**
     * 附件下载
     *
     * @param response
     * @param attachmentId
     */
    void downloadInlineAttachments(HttpServletResponse response, String attachmentId);

    /**
     * 查询附件集合
     *
     * @param currentLoginUsers
     * @param mediaIds
     * @return
     */
    ResultObject<List<AttachmentResponse>> attachmentList(Users currentLoginUsers, List<String> mediaIds);

    /**
     * 查询附件集合
     *
     * @param currentLoginUsers
     * @param batchId
     * @return
     */
    ResultObject<List<CoiBatchResponse>> attachmentCoiList(Users currentLoginUsers, String batchId);

    /**
     * 附件回显
     *
     * @param response
     * @param attachmentId
     * @return
     */
    ResultObject electronicPolicyDisplay(HttpServletResponse response, String... attachmentId) throws Exception;

    /**
     * 合并pdf并保存
     *
     * @param response
     * @param attachmentId
     * @return
     */
    ResultObject<AttachmentByteResponse> mergePdfSave(HttpServletResponse response, String... attachmentId);

    /**
     * 合并pdf并保存
     *
     * @param response
     * @param attachmentId
     * @return
     */
    ResultObject<AttachmentByteResponse> mergePdfSaveCoi(HttpServletResponse response, String... attachmentId);

    /**
     * pdf 转 图片
     *
     * @param attachmentId
     * @return
     */
    ResultObject<String> pdfTransformImage(String attachmentId);
}
