package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.common.ProductName;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.endorse.*;
import com.gclife.attachment.model.policy.policy.PolicyBeneficiaryBo;
import com.gclife.attachment.model.policy.policy.PolicyBeneficiaryInfoBo;
import com.gclife.attachment.model.policy.policy.group.GroupAttachInsuredBo;
import com.gclife.attachment.model.policy.policy.group.GroupAttachInsuredCoverageBo;
import com.gclife.attachment.model.policy.policy.group.GroupAttachPolicyBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.TeamLifeShieldInsuranceData;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/17 8:27 下午
 */
@Component
public class EndorseAddSubtractInsuredData {

    private static final Logger log = LoggerFactory.getLogger(EndorseAddSubtractInsuredData.class);
    @Autowired
    private TeamLifeShieldInsuranceData teamLifeShieldInsuranceData;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;

    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();

        AddSubtractInsuredPrintBo printBo = JSON.parseObject(content, AddSubtractInsuredPrintBo.class);

        List<EndorsePrintCoverageBo> endorseCoverageBoList = printBo.getListGroupCoverage();
        if (AssertUtils.isNotEmpty(endorseCoverageBoList)) {
            endorseCoverageBoList.stream().forEach(endorseCoverageBo -> {
                String productId = endorseCoverageBo.getProductId();
                putProductOrDutyLevel(map, endorseCoverageBo, productId);
                Long coveragePeriodStartDate = endorseCoverageBo.getCoveragePeriodStartDate();
                Long coveragePeriodEndDate = endorseCoverageBo.getCoveragePeriodEndDate();
                PrintCommon.setPrintDateTime(map, productId + "coverageStartDate", coveragePeriodStartDate, 3);
                PrintCommon.setPrintDateTime(map, productId + "coverageEndDate", coveragePeriodEndDate, 3);
            });
            Map<String, Object> policyData = teamLifeShieldInsuranceData.getPolicyData(electronicPolicyGeneratorRequest);
            map.putAll(policyData);
        }
        final String[] productNameListString = {null};
        List<String> productIdAddList = printBo.getProductIdAddList();
        if (AssertUtils.isNotEmpty(productIdAddList)) {
            productIdAddList.forEach(productId -> {
                productNameListString[0] = (AssertUtils.isNotEmpty(productNameListString[0]) ? productNameListString[0] + "," : "") + ProductName.getProductName(productId + language);
            });
        }
        map.put("productNameListString", PrintCommon.getPrintString(productNameListString[0], 3));
        EndorsePrintApplicantBo groupApplicant = AssertUtils.isNotNull(printBo.getGroupApplicant()) ? printBo.getGroupApplicant() : new EndorsePrintApplicantBo();
        map.put("delegateName", PrintCommon.getPrintString(groupApplicant.getDelegateName(), 3));
        map.put("delegateIdNo", PrintCommon.getPrintString(groupApplicant.getDelegateIdNo(), 3));
        map.put("delegateIdTypeName", PrintCommon.getPrintString(groupApplicant.getDelegateIdTypeName(), 3));
        PrintCommon.setPrintDateTime(map,"delegateBirthday", groupApplicant.getDelegateBirthday(), 3);
        map.put("delegateMobile", PrintCommon.getPrintString(groupApplicant.getDelegateMobile(), 3));

        //29号产品取代表人姓名
        if ("PRO880000000000029".equals(printBo.getMainProductId())) {
            map.put("companyContractName", PrintCommon.getPrintString(groupApplicant.getDelegateName(), 3));
        }else {
            map.put("companyContractName", PrintCommon.getPrintString(groupApplicant.getCompanyContractName(), 3));
        }
        map.put("mainProductId",printBo.getMainProductId());
        map.put("companyContractDept", PrintCommon.getPrintString(groupApplicant.getCompanyContractDept(), 3));
        map.put("companyContractPosition", PrintCommon.getPrintString(groupApplicant.getCompanyContractPosition(), 3));
        map.put("companyContractMobile", PrintCommon.getPrintString(groupApplicant.getCompanyContractMobile(), 3));
        map.put("companyContractNationalityName", PrintCommon.getPrintString(groupApplicant.getCompanyContractNationalityName(), 3));
        map.put("companyContractIdTypeName", PrintCommon.getPrintString(groupApplicant.getCompanyContractIdTypeName(), 3));
        map.put("companyContractIdNo", PrintCommon.getPrintString(groupApplicant.getCompanyContractIdNo(), 3));
        PrintCommon.setPrintDateTime(map,"companyContractIdExpDate", groupApplicant.getCompanyContractIdExpDate(),3);
        map.put("companyContractEmail", PrintCommon.getPrintString(groupApplicant.getCompanyContractEmail(), 3));


        map.put("acceptNo", PrintCommon.getPrintString(printBo.getAcceptNo(), 3));
        map.put("companyName", PrintCommon.getPrintString(printBo.getCompanyName(), 3));
        map.put("applyNo", PrintCommon.getPrintString(printBo.getApplyNo(), 3));
        map.put("policyNo", PrintCommon.getPrintString(printBo.getApplyNo(), 3));
        //map.put("companyContractName", PrintCommon.getPrintString(printBo.getCompanyContractName(), 3));
        PrintCommon.setPrintDateTime(map, "applyDate", printBo.getApplyDate(), 3);
        PrintCommon.setPrintDateTime(map, "effectiveStartDate", printBo.getEffectiveStartDate(), 3);
        PrintCommon.setPrintDateTime(map, "effectiveDate", printBo.getEffectiveStartDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }


    private void putProductOrDutyLevel(Map<String, Object> dutyLevelMap, EndorsePrintCoverageBo coverage, String productId) {
        List<EndorsePrintCoverageLevelBo> listCoverageLevel = coverage.getListCoverageLevel();
        listCoverageLevel.forEach(leveBo -> {
            dutyLevelMap.put(productId + leveBo.getProductLevel(), leveBo.getProductLevel());
        });
    }

    /**
     * 获取COI附件
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public byte[] getPolicyCOIPdfBytes(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        AddSubtractInsuredPrintBo addSubtractInsuredPrintBo = JSON.parseObject(content, AddSubtractInsuredPrintBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId("GROUP_INSURED_29_COI");

        String fullAddress = addSubtractInsuredPrintBo.getGroupApplicant().getCompanyAddressWhole();
        String schoolName = addSubtractInsuredPrintBo.getGroupApplicant().getName();
        //保险证 1
        List<byte[]> policyBookPdfBytesList = new ArrayList<>();

        List<EndorseInsuredPrintBo> addInsuredList = addSubtractInsuredPrintBo.getAddInsuredList();
        if (AssertUtils.isNotEmpty(addInsuredList)) {
            for (EndorseInsuredPrintBo endorseInsuredPrintBo : addInsuredList) {
                Map<String, Object> map = new HashMap<>();
                //合同号  保单号
                PrintCommon.setPrintData(printObjectList,"policyNo", addSubtractInsuredPrintBo.getApplyNo(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "effectiveDate", addSubtractInsuredPrintBo.getPolicyEffectiveDate(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "maturityDate", addSubtractInsuredPrintBo.getMaturityDate(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "riskCommencementDate", addSubtractInsuredPrintBo.getRiskCommencementDate(), 3);

                //被保人信息组装
                PrintCommon.setPrintData(printObjectList,"insuredName",endorseInsuredPrintBo.getName(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "birthdayDate", endorseInsuredPrintBo.getBirthday(), 3);
                PrintCommon.setPrintData(printObjectList,"sexName",endorseInsuredPrintBo.getSexName(), 3);
                PrintCommon.setPrintData(printObjectList,"nationalityName",endorseInsuredPrintBo.getNationalityName(), 3);
                PrintCommon.setPrintData(printObjectList,"schoolName",schoolName, 3);
                PrintCommon.setPrintData(printObjectList,"location",fullAddress, 3);
                PrintCommon.setPrintData(printObjectList,"studentCode",endorseInsuredPrintBo.getIdNo(), 3);
                final BigDecimal[] insuredSum33 = {new BigDecimal(0.00)};
                EndorseCoveragePrintBo endorseCoveragePrintBo = endorseInsuredPrintBo.getListCoverage().stream()
                        .filter(endorseCoveragePrintBo1 -> "PRO880000000000029".equals(endorseCoveragePrintBo1.getProductId()))
                        .findFirst().get();
                PrintCommon.setPrintData(printObjectList,"insuredSum",endorseCoveragePrintBo.getAmount(), 3);
                endorseInsuredPrintBo.getListCoverage().stream()
                        .filter(endorseCoveragePrintBo2 -> "PRO880000000000033".equals(endorseCoveragePrintBo2.getProductId()))
                        .findFirst().ifPresent(endorseCoveragePrintBo1 -> {
                            insuredSum33[0] = endorseCoveragePrintBo1.getAmount();
                        });
                PrintCommon.setPrintData(printObjectList,"insuredSum33", insuredSum33[0], 3);

                //受益人信息组装
                List<EndorseBeneficiaryInfoPrintBo> listBeneficiaryInfo = endorseInsuredPrintBo.getListBeneficiaryInfo();
                if (AssertUtils.isNotEmpty(listBeneficiaryInfo)) {
                    int i = 1;
                    for (EndorseBeneficiaryInfoPrintBo endorseBeneficiaryInfoPrintBo : listBeneficiaryInfo) {
                        PrintCommon.setPrintData(printObjectList,"beneficiaryName" + i, endorseBeneficiaryInfoPrintBo.getEndorseBeneficiaryPo().getName(), 3);
                        PrintCommon.setPrintData(printObjectList,"relationshipName" + i, endorseBeneficiaryInfoPrintBo.getRelationshipName(), 3);
                        PrintCommon.setPrintData(printObjectList,"beneficiaryNameAndRelation" + i,
                                endorseBeneficiaryInfoPrintBo.getEndorseBeneficiaryPo().getName()+"/"+endorseBeneficiaryInfoPrintBo.getRelationshipName(), 3);
                        i++;
                    }
                }

                EndorseCoveragePrintBo endorseCoveragePrintBo1 = endorseInsuredPrintBo.getListCoverage().get(0);


                String schoolLevel = "";
                schoolLevel = transSchoolType(endorseCoveragePrintBo1.getProductLevel29(), language,schoolLevel);
                PrintCommon.setPrintData(printObjectList,"schoolLevel",schoolLevel, 3);

                byte[] bytes = PrintCommon.fillData(policyBookBytes, printObjectList);
                policyBookPdfBytesList.add(bytes);
            }
        }
        return PrintCommon.mergePdfFiles(policyBookPdfBytesList);
    }

    public String transSchoolType(String schoolType,String language,String schoolLevel) {
        String preSchool = "";
        String primary = "";
        String secondary = "";
        String university = "";
        //保单状态国际化
        List<SyscodeRespFc> school_type = platformBaseInternationServiceApi.queryInternational("SCHOOL_TYPE", language).getData();
        if (schoolType.equals("PRE_SCHOOL")) {
            preSchool = getCodeName(school_type, "PRE_SCHOOL");
        }
        if (schoolType.equals("PRIMARY_SCHOOL")) {
            primary = getCodeName(school_type, "PRIMARY");
        }
        if (schoolType.equals("SECONDARY_SCHOOL")) {
            secondary = getCodeName(school_type, "SECONDARY");
        }
        if (schoolType.equals("UNIVERSITY")) {
            university = getCodeName(school_type, "UNIVERSITY");
        }

        schoolLevel = preSchool + primary + secondary + university;
        return schoolLevel;
    }

    /**
     * 根据key 获取国际化编码值
     *
     * @param syscodeRespFcList
     * @param codeKey
     * @return
     */
    public static String getCodeName(List<SyscodeRespFc> syscodeRespFcList, String codeKey) {
        if (AssertUtils.isNotEmpty(syscodeRespFcList) && AssertUtils.isNotEmpty(codeKey)) {
            for (SyscodeRespFc syscodeRespFc : syscodeRespFcList) {
                if (syscodeRespFc.getCodeKey().equals(codeKey)) {
                    return syscodeRespFc.getCodeName();
                }
            }
        }
        return null;
    }
}
