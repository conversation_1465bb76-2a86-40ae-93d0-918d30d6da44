package com.gclife.attachment.service.print.payment;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintBo;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintCoverageBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:19 2018/12/14
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class GroupPaymentReceiptData {
    public Map<String, Object> getPaymentReceiptData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        AttachmentPaymentPrintBo paymentPrintBo = JSON.parseObject(content, AttachmentPaymentPrintBo.class);

        Map<String, Object> stringObjectMap = new HashMap<>();

        if ("APPLY".equals(paymentPrintBo.getBusinessType())) {
            stringObjectMap.put("applyOrPolicyName", "លេខពាក្យស្នើសុំ (APPLICATION NO.)");
        } else {
            stringObjectMap.put("applyOrPolicyName", "លេខបណ្ណសន្យារ៉ាប់រង (POLICY NO.)");
        }
        stringObjectMap.put("businessNo", PrintCommon.getPrintString(paymentPrintBo.getBusinessNo(), 3));
        stringObjectMap.put("receiptNo", PrintCommon.getPrintString(paymentPrintBo.getReceiptNo(), 3));
        String printDate = null;
        if (AssertUtils.isNotNull(paymentPrintBo.getPrintDate())) {
            String[] k = new Date(paymentPrintBo.getPrintDate()).toString().split(" ");
            printDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        }
        stringObjectMap.put("printDate", PrintCommon.getPrintString(printDate, 3));

        stringObjectMap.put("applicantName", PrintCommon.getPrintString(paymentPrintBo.getApplicantName(), 3));
        stringObjectMap.put("paymentMethodName", PrintCommon.getPrintString(paymentPrintBo.getPaymentMethodName(), 3));
        stringObjectMap.put("installmentNo", PrintCommon.getPrintString(paymentPrintBo.getInstallmentNo(), 3));
        stringObjectMap.put("premiumFrequencyName", PrintCommon.getPrintString(paymentPrintBo.getPremiumFrequencyName(), 3));
        stringObjectMap.put("endorseProjectName", PrintCommon.getPrintString(paymentPrintBo.getEndorseProjectName(), 3));
        stringObjectMap.put("endorseProjectCode", PrintCommon.getPrintString(paymentPrintBo.getEndorseProjectCode(), 3));
        stringObjectMap.put("businessType", PrintCommon.getPrintString(paymentPrintBo.getBusinessType(), 3));

        BigDecimal premiumBeforeDiscount = paymentPrintBo.getPremiumBeforeDiscount();

        String premiumBeforeDiscountString = PrintCommon.getPrintString(premiumBeforeDiscount, 3);
        BigDecimal specialDiscount = paymentPrintBo.getSpecialDiscount();
        if (AssertUtils.isNotNull(specialDiscount) && AssertUtils.isNotNull(premiumBeforeDiscount)) {
            stringObjectMap.put("specialDiscountPremium", PrintCommon.getPrintString(premiumBeforeDiscount.multiply(specialDiscount), 3));
        } else {
            premiumBeforeDiscountString = PrintCommon.getPrintString(paymentPrintBo.getTotalPremium(), 3);
            if ("GET".equals(paymentPrintBo.getFeeType())) {
                premiumBeforeDiscountString = "(" + premiumBeforeDiscountString + ")";
            }
        }
        stringObjectMap.put("premiumBeforeDiscount", PrintCommon.getPrintString(premiumBeforeDiscountString, 3));

        String totalPremium = PrintCommon.getPrintString(paymentPrintBo.getTotalPremium(), 3);
        if ("GET".equals(paymentPrintBo.getFeeType())) {
            totalPremium = "(" + totalPremium + ")";
        }
        stringObjectMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));

        stringObjectMap.put("agentCode", PrintCommon.getPrintString(paymentPrintBo.getAgentCode(), 3));
        stringObjectMap.put("receivedBy", PrintCommon.getPrintString(paymentPrintBo.getReceivedBy(), 3));

        return stringObjectMap;
    }

}
