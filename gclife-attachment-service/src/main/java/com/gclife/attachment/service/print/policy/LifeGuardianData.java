package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.BENEFICIARY_NO.ORDER_ONE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @description
 * @date 2020/5/12 4:53 下午
 */
@Component
public class LifeGuardianData {


    /**
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        Long backTrackDate =  planPrintBo.getCreatedDate();
        if(AssertUtils.isNotNull(planPrintBo.getBackTrackDate())){
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            map.put("backTrackDateYearZH_CN", PrintCommon.getPrintString("年 ", 3));
            map.put("backTrackDateMonthZH_CN", PrintCommon.getPrintString("月 ", 3));
            map.put("backTrackDateDayZH_CN", PrintCommon.getPrintString("日 ", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()),new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()),new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount9 = new BigDecimal(0);
        String premiumPeriod9 = null;
        String premiumFrequency9 = null;
        if (AssertUtils.isNotEmpty(listCoverage)) {
            for (ApplyCoveragePlanBo coverageBo : listCoverage) {
                Map<String, Object> coverageMap = new HashMap<>();
                map.put(coverageBo.getProductId(), coverageBo.getProductId());
                coverageMap.put("productId", coverageBo.getProductId());
                PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
                coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
                coverageMap.put("productLevel", coverageBo.getProductLevel());
                map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                    totalAmount = new BigDecimal(coverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                //保险期间
                String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
                String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
                String insurancePeriod = coveragePeriod + " " + coveragePeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                    insurancePeriod = coveragePeriodUnitName + " " + coveragePeriod;
                }
                coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
                //交费期限
                String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
                String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
                String premiumPeriodAndUnitName = premiumPeriod + " " + premiumPeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                    premiumPeriodAndUnitName = premiumPeriodUnitName + " " + premiumPeriod;
                }
                //交费类型
                String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
                if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "一次性全额缴清";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "Single Payment";
                    }
                    premiumPeriodAndUnitName = premiumFrequencyName;
                }
                coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
                coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
                if ("PRO88000000000009".equals(coverageBo.getProductId())) {
                    totalAmount9 = totalAmount;
                    premiumPeriod9 = premiumPeriod;
                    premiumFrequency9 = coverageBo.getPremiumFrequency();
                }
                //每期保费
                coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
                coverageListMap.add(coverageMap);
            }
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /****************************************************************************可选其他缴费周期***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<Map<String, Object>> coveragePremiumFrequencyListMap = new ArrayList<>();
        for (String productId : coveragePremiumFrequencyMap.keySet()) {
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            Map<String, Object> cpfMap = new HashMap<>();
            PrintCommon.setProductName(cpfMap, productId);
            Integer column = 1;
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, YEAR.name(), premiumFrequency9);
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEMIANNUAL.name(), premiumFrequency9);
            column = ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, SEASON.name(), premiumFrequency9);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, column, cpfMap, cpfList, MONTH.name(), premiumFrequency9);
            coveragePremiumFrequencyListMap.add(cpfMap);
        }
        PrintCommon.coverageSort(coveragePremiumFrequencyListMap);
        map.put("coveragePremiumFrequencyListMap", coveragePremiumFrequencyListMap);
        map.put("premiumFrequency9", PrintCommon.getPrintString(premiumFrequency9, 2));

        /****************************************************************************保险利益***********************************************************************************/
        map.put("totalAmount9", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount9), 2));
        BigDecimal totalAmount9multiply3 = totalAmount9.multiply(new BigDecimal(3));
        BigDecimal totalAmount9multiply1_5 = totalAmount9.multiply(new BigDecimal(1.5));
        map.put("totalAmount9multiply3", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount9multiply3), 2));
        map.put("totalAmount9multiply1_5", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount9multiply1_5), 2));
        /****************************************************************************利益显示***********************************************************************************/
        Integer premiumPeriodYearSum = Integer.valueOf(premiumPeriod9);
        int ageYear_80 = 80 - insuredAgeYear;
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        BigDecimal policyYearTotalPremium = new BigDecimal(0);
        int maxYesarSum = premiumPeriodYearSum > ageYear_80 ? premiumPeriodYearSum : ageYear_80;
        int policyYear = 1;
        BigDecimal pYTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, policyYear, new BigDecimal(0));
        for (int i = 1; i <= maxYesarSum || policyYear <= premiumPeriodYearSum; i++) {
            /****************************************************************************保险费和现金价值*/
            int ageYear = insuredAgeYear + i;

            if (!(i <= 10 || (i % 5 == 0 && ageYear < 60) || (Arrays.asList(59, 60, 65, 70, 75, 80).contains(ageYear)) || i > 80)) {
                continue;
            }
            Map<String, Object> interestMap = new HashMap<>();
            if (i <= ageYear_80) {
                //保单年度
                interestMap.put("year1", i);
                //年龄
                interestMap.put("insuredAge", ageYear);
                //生存金
                BigDecimal hcf = ageYear == 60 ? totalAmount9multiply1_5 : new BigDecimal(0);
                interestMap.put("hcf", PrintCommon.getPrintString("$", hcf, 1));
                //共计保健金
                BigDecimal expiredHcf = ageYear == 80 ? totalAmount9 : new BigDecimal(0);
                interestMap.put("expiredHcf", PrintCommon.getPrintString("$", expiredHcf, 1));
                //意外死亡或高残
                BigDecimal adOrHd = ageYear < 60 ? totalAmount9multiply3 : totalAmount9;
                interestMap.put("adOrHd", PrintCommon.getPrintString("$", adOrHd, 1));
                //疾病死亡或高残
                BigDecimal dDOrHD = i <= 2 ? totalAmount9 : ageYear < 60 ? totalAmount9multiply1_5 : totalAmount9;
                interestMap.put("dDOrHD", PrintCommon.getPrintString("$", dDOrHD, 1));
                //重大疾病
                BigDecimal majorDiseases = i > 2 && ageYear < 60 ? totalAmount9 : new BigDecimal(0);
                interestMap.put("majorDiseases", PrintCommon.getPrintString("$", majorDiseases, 1));
                //重大疾病的豁免保费
                BigDecimal heavyDisease = i <= 2 || i >= premiumPeriodYearSum ? new BigDecimal(0) : pYTotalPremium.multiply(new BigDecimal(premiumPeriodYearSum - i));
                interestMap.put("heavyDisease", PrintCommon.getPrintString("$", heavyDisease, 1));
            }
            /****************************************************************************利益显示*/
            //保险费
            //保单年度
            if (policyYear <= premiumPeriodYearSum) {
                policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, policyYear, new BigDecimal(0));
                interestMap.put("year2", policyYear);
                //总保费
                interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
                policyYear = policyYear >= 25 && policyYear < 30 ? 30 : (policyYear + 1);
            }
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO88000000000009".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                interestMap.put("year3", i);
                BigDecimal cashValue9 = ProductCalculation.getCashValue(listCashValue, "PRO88000000000009", i);
                interestMap.put("cashValue9", PrintCommon.getPrintString("$", cashValue9, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue9, 1));
            }
            interestListMap.add(interestMap);
        }
        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("totalAmount9_1", PrintCommon.getPrintString(totalAmount9, 2));
        map.put("totalAmount9multiply3_1", PrintCommon.getPrintString(totalAmount9multiply3, 2));
        map.put("totalAmount9multiply1_5_1", PrintCommon.getPrintString(totalAmount9multiply1_5, 2));
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Map<String, Object> map = new HashMap<String, Object>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if(AssertUtils.isNotNull(policyBo.getRiskCommencementDate())){
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        Integer applicantAgeYear = null;
        if (AssertUtils.isNotNull(policyApplicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(policyBo.getApplyDate()));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(policyApplicant.getSex(), 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(policyInsuredBo.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(policyBo.getApplyDate()));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************受益人***************************************/
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listPolicyBeneficiary)) {
            listPolicyBeneficiary.removeIf(policyBeneficiaryInfoBo -> !ORDER_ONE.name().equals(policyBeneficiaryInfoBo.getBeneficiaryNoOrder()));
        }
        if (!AssertUtils.isNotNull(listPolicyBeneficiary)) {
            listPolicyBeneficiary = new ArrayList<>();
        }
        listPolicyBeneficiary.forEach(beneficiary -> {
            Map<String, Object> beneficiaryMap = new HashMap<>();
            PolicyBeneficiaryBo policyBeneficiary = beneficiary.getPolicyBeneficiary();
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(policyBeneficiary.getName(), 3));
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(beneficiaryIdNo, 3));
            beneficiaryMap.put("beneficiaryId-No", PrintCommon.getPrintString(policyBeneficiary.getIdNo(), 3));
            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(beneficiary.getBeneficiaryProportion())) {
                beneficiaryProportion = beneficiary.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(beneficiaryProportion, 2));
            beneficiaryListMap.add(beneficiaryMap);
        });
        map.put("beneficiaryListMap", beneficiaryListMap);
        /**********************************保险***************************************/
        Long contractStartDate = policyBo.getApproveDate();
        Long contractEndDate = policyBo.getMaturityDate();
        PrintCommon.setPrintDateTime(map, "contractStartDate", contractStartDate, 3);
        PrintCommon.setPrintDateTime(map, "contractEndDate", contractEndDate, 3);
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();

        PrintCommon.setProductName(map, mainCoverageBo.getProductId(), "Main", null, null);

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<PolicyCoverageBo> policyCoverageBo16List = listPolicyCoverage.stream().filter(policyCoverageBo -> policyCoverageBo.getProductId().indexOf("PRO880000000000016") >= 0).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(policyCoverageBo16List)) {
            final BigDecimal[] totalPremium16 = {new BigDecimal(0)};
            policyCoverageBo16List.forEach(policyCoverageBo -> {
                totalPremium16[0] = totalPremium16[0].add(policyCoverageBo.getTotalPremium());
                String premiumMonthFrequency = null;
                if (YEAR.name().equals(policyCoverageBo.getPremiumFrequency())) {
                    premiumMonthFrequency = "12";
                } else if (SEMIANNUAL.name().equals(policyCoverageBo.getPremiumFrequency())) {
                    premiumMonthFrequency = "06";
                } else if (SEASON.name().equals(policyCoverageBo.getPremiumFrequency())) {
                    premiumMonthFrequency = "03";
                } else if (MONTH.name().equals(policyCoverageBo.getPremiumFrequency())) {
                    premiumMonthFrequency = "01";
                }
                map.put("premiumMonthFrequency16", premiumMonthFrequency);
                long premiumCessationDate = policyCoverageBo.getCoveragePeriodStartDate();
                String premiumFrequency = policyCoverageBo.getPremiumFrequency();
                int premiumPeriodInteger = Integer.parseInt(policyCoverageBo.getPremiumPeriod());

                if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 1);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 4);
                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 6);
                } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                    premiumCessationDate = DateUtils.addStringMonthRT(premiumCessationDate, (premiumPeriodInteger * 12) - 12);
                }
                //额外加费
                BigDecimal extraPremium = null;
                BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(policyCoverageBo.getPremiumFrequency()).value());
                if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                    List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                            .filter(policyAddPremiumBo -> policyCoverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                    AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                    EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                        double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                        extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    }
                }
                map.put("extraPremium16" + policyCoverageBo.getProductLevel(), PrintCommon.getPrintString(extraPremium, 3));

                PrintCommon.setPrintDateTime(map, "premiumCessationDate", premiumCessationDate, 3);
                map.put(policyCoverageBo.getProductId() + "ProductLevel", policyCoverageBo.getProductLevel());
                map.put("totalPremium16" + policyCoverageBo.getProductLevel(), policyCoverageBo.getTotalPremium());
                PrintCommon.setPrintDateTime(map, "coveragePeriodStartDate", policyCoverageBo.getCoveragePeriodStartDate(), 3);
                PrintCommon.setPrintDateTime(map, "coveragePeriodEndDate", policyCoverageBo.getCoveragePeriodEndDate(), 3);
            });
            map.put("totalPremium16", totalPremium16[0]);
            map.put("PRO880000000000016", "PRO880000000000016");
        }
        //交费期限
        BigDecimal totalAmount = new BigDecimal(mainCoverageBo.getTotalAmount());
        map.put("totalAmount", PrintCommon.getPrintString(totalAmount, 3));
        map.put("totalPremium", PrintCommon.getPrintString(mainCoverageBo.getTotalPremium(), 3));
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        map.put("firstTotalPremium", PrintCommon.getPrintString(policyPremium.getActualPremium(), 3));
        String premiumPeriod = AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriod()) ? mainCoverageBo.getPremiumPeriod() : "";
        String premiumPeriodUnitName = AssertUtils.isNotEmpty(mainCoverageBo.getPremiumPeriodUnitName()) ? mainCoverageBo.getPremiumPeriodUnitName() : "";
        String ppun = premiumPeriod + premiumPeriodUnitName;
        if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(mainCoverageBo.getPremiumPeriodUnit())) {
            ppun = premiumPeriodUnitName + premiumPeriod;
        }
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                premiumFrequencyName = "Single Payment";
            }
            ppun = premiumFrequencyName;
        }
        map.put("ppun", PrintCommon.getPrintString(ppun, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        Long gainedDate = policyPayment.getGainedDate();
        PrintCommon.setPrintDateTime(map, "gainedDate", gainedDate, 3);
        /****************************************保险利益********************************/
        long insured60Age = DateUtils.addStringYearsRT(contractEndDate, -20);
        PrintCommon.setPrintDateTime(map, "insured60Age", insured60Age, 3);
        long insured60A1 = DateUtils.addDays(new Date(insured60Age), 1).getTime();
        PrintCommon.setPrintDateTime(map, "insured60A1", insured60A1, 3);
        long csdA91Day = DateUtils.addDays(new Date(contractStartDate), 91).getTime();
        PrintCommon.setPrintDateTime(map, "csdA91Day", csdA91Day, 3);
        long csdA2Year = DateUtils.addStringYearsRT(contractStartDate, 2);
        PrintCommon.setPrintDateTime(map, "csdA2Year", csdA2Year, 3);
        long csdA2YearD1Day = DateUtils.addDays(new Date(csdA2Year), -1).getTime();
        PrintCommon.setPrintDateTime(map, "csdA2YearD1Day", csdA2YearD1Day, 3);
        long insured60D1 = DateUtils.addDays(new Date(insured60Age), -1).getTime();
        PrintCommon.setPrintDateTime(map, "insured60D1", insured60D1, 3);
        map.put("totalAmount3", PrintCommon.getPrintString(totalAmount.multiply(new BigDecimal(3)), 3));
        map.put("totalAmount1_5", PrintCommon.getPrintString(totalAmount.multiply(new BigDecimal(1.5)), 3));
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        /***********************************现金价值******************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
        ProductCalculation.policyCashValues(map, policyCashValues);
        /***********************************特别约定******************************************/
        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getPolicySpecialContractContent(map, specialContractContent);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

}
