package com.gclife.attachment.service.business;

import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;


/**
 * <AUTHOR>
 * create 17-10-16
 * description:　图片缩放处理
 */
public interface ImageZoomBusinessService extends BaseBusinessService {


    /**
     * 下载媒体文件 (缩放) 返回图片地址
     *
     * @param users 当前登录用户
     * @param mediaId 媒体ID
     * @param width 图片宽度
     * @param height 图片高度
     * @return ResultObject<AttachmentResponse>
     */
    ResultObject<AttachmentResponse> loadMediaRouteFixedFrame(Users users,String mediaId,long width,long height);






}