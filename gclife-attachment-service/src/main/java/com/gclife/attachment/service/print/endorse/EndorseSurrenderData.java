package com.gclife.attachment.service.print.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.endorse.SurrenderPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 2018/9/26
 * description:退保
 */
@Component
public class EndorseSurrenderData {

    public List<PrintObject> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = new ArrayList<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        SurrenderPrintBo surrenderPrintBo = JSON.parseObject(content,SurrenderPrintBo.class);

        PrintCommon.setPrintData(printObjectList, "acceptNo", surrenderPrintBo.getAcceptNo(), 3);
        PrintCommon.setPrintData(printObjectList, "policyNo", surrenderPrintBo.getPolicyNo(), 3);
        PrintCommon.setPrintData(printObjectList, "applicantName", surrenderPrintBo.getApplicantName(), 3);
        PrintCommon.setPrintData(printObjectList, "insuredName", surrenderPrintBo.getInsuredName(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "applyDate", surrenderPrintBo.getApplyDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "acceptDate", surrenderPrintBo.getAcceptDate(), 3);
        PrintCommon.setPrintData(printObjectList, "totalFee", surrenderPrintBo.getTotalFee(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "endorseFinishDate", surrenderPrintBo.getEndorseFinishDate(), 3);

        return printObjectList;
    }

}
