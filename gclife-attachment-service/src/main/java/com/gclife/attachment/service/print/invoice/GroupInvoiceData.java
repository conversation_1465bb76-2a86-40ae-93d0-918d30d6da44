package com.gclife.attachment.service.print.invoice;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyApplicantBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachInvoiceDetailBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.api.PaymentConversionRateApi;
import com.gclife.payment.model.response.ConversionRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.gclife.common.TerminologyConfigEnum.CURRENCY.KHR;
import static com.gclife.common.TerminologyConfigEnum.CURRENCY.USD;

/**
 * <AUTHOR>
 * @description
 * @date 2019/8/26 10:13 上午
 */
@Slf4j
@Component
public class GroupInvoiceData {

    @Autowired
    private PaymentConversionRateApi paymentConversionRateApi;

    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        GroupAttachApplyBo applyPrintBo = JSON.parseObject(content, GroupAttachApplyBo.class);
        /**************************************详情*********************************************/
        GroupAttachInvoiceDetailBo invoiceDetail = applyPrintBo.getInvoiceDetail();
        map.put("companyName", PrintCommon.getPrintString(invoiceDetail.getCompanyName(), 3));
        GroupAttachApplyApplicantBo groupApplicant = applyPrintBo.getGroupApplicant();
        map.put("companyPhone", PrintCommon.getPrintString(groupApplicant.getCompanyPhone(), 3));
        map.put("companyLegalPersonName", PrintCommon.getPrintString(groupApplicant.getCompanyLegalPersonName(), 3));
        map.put("invoiceNo", PrintCommon.getPrintString(invoiceDetail.getInvoiceNo(), 3));
        map.put("taxRegistrationNo", PrintCommon.getPrintString(invoiceDetail.getTaxRegistrationNo(), 3));
        map.put("premiumFrequency", PrintCommon.getPrintString(invoiceDetail.getPremiumFrequency(), 3));
        Long currentTime = DateUtils.getCurrentTime();
        String[] k = new Date(currentTime).toString().split(" ");
        String printDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        map.put("printDate", PrintCommon.getPrintString(printDate, 3));
        String companyContractEmail = AssertUtils.isNotEmpty(groupApplicant.getCompanyContractEmail()) ? groupApplicant.getCompanyContractEmail() : groupApplicant.getDelegateEmail();
        map.put("companyContractEmail", PrintCommon.getPrintString(companyContractEmail, 3));
        map.put("acceptNo", PrintCommon.getPrintString(invoiceDetail.getAcceptNo(), 3));
        //计算将到期付款日期
        Date paymentDue = null;
        try {
            paymentDue = DateUtils.addDays(new Date(currentTime), 14);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String[] k1 = paymentDue.toString().split(" ");
        String paymentDueDate = k1[2] + " " + k1[1].toUpperCase() + " " + k1[5];
        map.put("paymentDueDate", PrintCommon.getPrintString(paymentDueDate, 3));
        String companyAreaName = AssertUtils.isNotEmpty(groupApplicant.getCompanyAreaName()) ? groupApplicant.getCompanyAreaName() : "";
        String companyAddress = AssertUtils.isNotEmpty(groupApplicant.getCompanyAddress()) ? groupApplicant.getCompanyAddress() : "";
        if (AssertUtils.isNotEmpty(companyAreaName)) {
            map.put("companyAddress", PrintCommon.getPrintString(companyAddress + " " + companyAreaName, 3));
        } else {
            map.put("companyAddress", PrintCommon.getPrintString(groupApplicant.getCompanyAddressWhole(), 3));
        }
        String applyPolicyNo = AssertUtils.isNotEmpty(applyPrintBo.getPolicyNo()) ? applyPrintBo.getPolicyNo() : applyPrintBo.getApplyNo();
        map.put("applyPolicyNo", PrintCommon.getPrintString(applyPolicyNo, 3));

        ResultObject<ConversionRateResponse> conversionRateResponseResultObject = paymentConversionRateApi.queryUseOneConversionRate(USD.name(), KHR.name());
        AssertUtils.isResultObjectError(log, conversionRateResponseResultObject);
        ConversionRateResponse conversionRateResponse = conversionRateResponseResultObject.getData();

        BigDecimal premiumBeforeDiscount = applyPrintBo.getPremiumBeforeDiscount();
        map.put("premiumBeforeDiscount", PrintCommon.getPrintString(premiumBeforeDiscount, 3));
        BigDecimal specialDiscount = applyPrintBo.getSpecialDiscount();
        if (AssertUtils.isNotNull(specialDiscount) && AssertUtils.isNotNull(premiumBeforeDiscount)) {
            map.put("specialDiscountPremium", PrintCommon.getPrintString(premiumBeforeDiscount.multiply(specialDiscount), 3));
        }else {
            map.put("premiumBeforeDiscount", PrintCommon.getPrintString(applyPrintBo.getTotalPremium(), 3));
        }

        BigDecimal rate = conversionRateResponse.getRate();
        BigDecimal duePayAmount = applyPrintBo.getTotalPremium();
        map.put("allTotalPremiumEN_US", PrintCommon.getPrintString(duePayAmount.setScale(2, BigDecimal.ROUND_HALF_UP), 3));
        map.put("allTotalPremiumKM_KH", PrintCommon.getPrintString(duePayAmount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP), 3));
        map.put("rate",PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(rate), 3));
        // CFO签名 + 公司印章
        map.put("signSealCFOPicture", PrintCommon.SIGN_SEAL_CFO);
        return map;
    }
}
