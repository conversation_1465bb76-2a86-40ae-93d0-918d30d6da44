package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyAgentBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyApplicantBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyInsuredCollectBo;
import com.gclife.attachment.model.policy.policy.group.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/10/28
 */
@Component
public class GroupInsuranceBenefitsSummaryData extends BaseBusinessServiceImpl {
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();

        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachApplyBo applyPrintBo = JSON.parseObject(content, GroupAttachApplyBo.class);
        GroupAttachApplyApplicantBo applicant = applyPrintBo.getGroupApplicant();

        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));

        // 代理人
        GroupAttachApplyAgentBo groupAgent = applyPrintBo.getGroupAgent();
        // 代理人姓名
        map.put("agentName", PrintCommon.getPrintString(groupAgent.getAgentName(), 3));
        // 代理人代码
        map.put("agentCode", PrintCommon.getPrintString(groupAgent.getAgentCode(), 3));
        // 代理人手机号
        map.put("agentMobil", PrintCommon.getPrintString(groupAgent.getMobile(), 3));

        // insuredCompany
        map.put("insuredCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        map.put("insuredTaxRegistrationNo", PrintCommon.getPrintString(applicant.getCompanyIdNo(), 3));
        map.put("insuredCompanyType", PrintCommon.getPrintString(applicant.getCompanyTypeName(), 3));
        map.put("insuredCompanyAddress", PrintCommon.getPrintString(applicant.getCompanyAddressWhole(), 3));
        map.put("insuredCompanyPhone", PrintCommon.getPrintString(applicant.getCompanyPhone(), 3));
        map.put("insuredCompanyMobil", PrintCommon.getPrintString(applicant.getCompanyPhone(), 3));
        map.put("insuredCompanyEmail", PrintCommon.getPrintString(applicant.getCompanyEmail(), 3));
        // insuredCompanyContract
        map.put("insuredCompanyContractName", PrintCommon.getPrintString(applicant.getCompanyContractName(), 3));
        map.put("insuredCompanyContractPosition", PrintCommon.getPrintString(applicant.getCompanyContractPosition(), 3));
        map.put("insuredCompanyContractOfficeNumber", PrintCommon.getPrintString(applicant.getCompanyContractOfficeNumber(), 3));
        map.put("insuredCompanyContractMobile", PrintCommon.getPrintString(applicant.getCompanyContractMobile(), 3));
        map.put("insuredCompanyContractEmail", PrintCommon.getPrintString(applicant.getCompanyContractEmail(), 3));
        // mainInsuredSum
        GroupAttachApplyInsuredCollectBo applyInsuredCollect = applyPrintBo.getApplyInsuredCollect();
        map.put("mainInsuredSum", PrintCommon.getPrintString(applyInsuredCollect.getMainQuantity(), 3));
        map.put("additionalInsuredSum", PrintCommon.getPrintString(applyInsuredCollect.getAdditionQuantity(), 3));
        map.put("additionalInsuredSum18", PrintCommon.getPrintString(applyInsuredCollect.getAdditionQuantity18(), 3));
        map.put("additionalInsuredSum26", PrintCommon.getPrintString(applyInsuredCollect.getAdditionQuantity26(), 3));
        map.put("additionalInsuredSum27", PrintCommon.getPrintString(applyInsuredCollect.getAdditionQuantity27(), 3));
        map.put("additionalInsuredSum33", PrintCommon.getPrintString(applyInsuredCollect.getAdditionQuantity33(), 3));
        map.put("totalEmployeeNum", PrintCommon.getPrintString(applicant.getTotalEmployeeNum(), 3));
        // SALARY POSITION FIXED YEARS OTHER
        map.put("basisOfSumInsured", PrintCommon.getPrintString(applicant.getBasisOfSumInsured(), 3));
        map.put("otherCategorySpecify", PrintCommon.getPrintString(applicant.getOtherCategorySpecify(), 3));
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        map.put("companyBaseNameEN_US", CompanyInfo.COMPANY_NAME_EN_US);
        map.put("companyBaseNameKM_KH", CompanyInfo.COMPANY_NAME_KM_KH);
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachPolicyBo policyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);
        Map<String, Object> map = new HashMap<>();
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));

        PrintCommon.setPrintDateTime(map, "effectiveDate", policyBo.getEffectiveDate(), 3);
        PrintCommon.setPrintDateTime(map, "maturityDate", policyBo.getMaturityDate(), 3);
        /************************************************单位信息***************************************************/
        GroupAttachApplicantBo applicant = policyBo.getGroupApplicant();
        map.put("companyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        this.getLogger().info("basicFreeCoverLimit字段+===========================" + JSON.toJSON(applicant));
        String format = null;
        if (AssertUtils.isNotNull(applicant.getBasicFreeCoverLimit())) {
            format = PrintCommon.decimalFormat1.format(new BigDecimal(applicant.getBasicFreeCoverLimit()));
        }
        map.put("basicFreeCoverLimit", PrintCommon.getPrintString(format, 3));
        /************************************************险种信息***************************************************/
        List<GroupAttachCoverageBo> coverageList = policyBo.getListGroupCoverage();
        //根据产品险种和id进行排序，便于条款按顺序打印
        coverageList.sort(Comparator.comparing(GroupAttachCoverageBo::getProductId));
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        for (GroupAttachCoverageBo coverageBo : coverageList) {
            Map<String, Object> coverageMap = new HashMap<>();
            coverageMap.put("productId", coverageBo.getProductId());
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageListMap.add(coverageMap);
        }
        map.put("coverageListMap", coverageListMap);
        /************************************************代理人***************************************************/
        GroupAttachAgentBo agent = policyBo.getGroupAgent();
        String agentName = AssertUtils.isNotNull(agent.getAgentName()) ? agent.getAgentName() : "";
        String bracketsAgentCode = AssertUtils.isNotNull(agent.getAgentCode()) ? "(" + agent.getAgentCode() + ")" : "";
        map.put("agentNameAndAgentCode", PrintCommon.getPrintString(agentName + bracketsAgentCode, 3));
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(agent.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(agent.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(agent.getMobile(), 3));
        PrintCommon.setPrintDateTime(map, "effectiveDate", policyBo.getEffectiveDate(), 3);
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getEffectiveDate(), 3);
        /************************************************被保人展示***************************************************/
        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        List<GroupAttachInsuredCoverageBo> listCoverage = new ArrayList<>();
        Map<String, Object> dutyLevelMap = new HashMap<>();
        BigDecimal illnessOrAccidentTotalAmount = BigDecimal.ZERO;
        BigDecimal onlyAccidentTotalAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal illnessOrAccidentTotalPremium = BigDecimal.ZERO;
        BigDecimal onlyAccidentTotalPremium = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalPremium = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalPremium26 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalPremium27 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalPremium33 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalAmount18 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalAmount26 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalAmount27 = BigDecimal.ZERO;
        BigDecimal additionalPlanTotalAmount33 = BigDecimal.ZERO;
        BigDecimal totalPremium = BigDecimal.ZERO;
        int insuredNo = 1;
        // 被保人集合
        List<GroupAttachInsuredBo> groupAttachInsuredBos = policyBo.getListGroupInsured();
        if (AssertUtils.isNotEmpty(groupAttachInsuredBos)) {
            for (GroupAttachInsuredBo groupAttachInsuredBo : groupAttachInsuredBos) {
                HashMap<String, Object> insuredMap = new HashMap<>();
                // 因疾病或意外 保额
                BigDecimal illnessOrAccidentAmount = BigDecimal.ZERO;
                // 因意外 保额
                BigDecimal onlyAccidentAmount = BigDecimal.ZERO;
                //18附加项 保额
                BigDecimal additionalPlanAmount18 = BigDecimal.ZERO;
                //26附加项 保额
                BigDecimal additionalPlanAmount26 = BigDecimal.ZERO;
                //27附加项 保额
                BigDecimal additionalPlanAmount27 = BigDecimal.ZERO;
                //33附加项 保额
                BigDecimal additionalPlanAmount33 = BigDecimal.ZERO;
                // 附加团体医疗保险 档次
                String additionalPlan = null;
                insuredMap.put("insuredNo", insuredNo++);
                insuredMap.put("name", PrintCommon.getPrintString(groupAttachInsuredBo.getName(), 3));
                PrintCommon.setPrintDateTime(insuredMap, "birthday", groupAttachInsuredBo.getBirthday(), 3);
                insuredMap.put("sexName", PrintCommon.getPrintString(groupAttachInsuredBo.getSexName(), 3));
                insuredMap.put("ageYear", PrintCommon.getPrintString(groupAttachInsuredBo.getAge(), 3));
                // 被保人险种集合
                List<GroupAttachInsuredCoverageBo> groupAttachInsuredCoverageBos = groupAttachInsuredBo.getListCoverage();
                for (GroupAttachInsuredCoverageBo groupAttachInsuredCoverageBo : groupAttachInsuredCoverageBos) {
                    // 被保人险种档次集合
                    List<GroupAttachInsuredCoverageLevelBo> groupAttachInsuredCoverageLevelBos = groupAttachInsuredCoverageBo.getListCoverageLevel();
                    String coverageBoProductId = groupAttachInsuredCoverageBo.getProductId();

                    for (GroupAttachInsuredCoverageLevelBo groupAttachInsuredCoverageLevelBo : groupAttachInsuredCoverageLevelBos) {
                        String levelBoProductLevel = groupAttachInsuredCoverageLevelBo.getProductLevel();
                        BigDecimal coverageLevelBoTotalPremium = groupAttachInsuredCoverageLevelBo.getTotalPremium();

                        // 17号产品 GC全优团保
                        if ("PRO880000000000017".equals(coverageBoProductId)) {
                            // 因疾病或意外 的保额
                            BigDecimal coverageLevelBoAmount = groupAttachInsuredCoverageLevelBo.getAmount();
                            if (AssertUtils.isNotNull(coverageLevelBoAmount)) {
                                if ("TL".equals(levelBoProductLevel)
                                        || "TC".equals(levelBoProductLevel)
                                        || "TS".equals(levelBoProductLevel)) {
                                    illnessOrAccidentAmount = illnessOrAccidentAmount.add(coverageLevelBoAmount.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                                    // 因疾病或意外 总保费
                                    illnessOrAccidentTotalPremium = illnessOrAccidentTotalPremium.add(coverageLevelBoTotalPremium);
                                }
                                // 1因意外 的保额
                                if ("TA".equals(levelBoProductLevel)) {
                                    // onlyAccidentAmount
                                    onlyAccidentAmount = onlyAccidentAmount.add(coverageLevelBoAmount.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                                    // 因意外 总保费
                                    onlyAccidentTotalPremium = onlyAccidentTotalPremium.add(coverageLevelBoTotalPremium);
                                }
                            }
                        }
                        // 18号产品 附加团体医疗保险 险种档次国际化
                        if ("PRO880000000000018".equals(coverageBoProductId)) {
                            BigDecimal coverageLevelBoAmount18 = groupAttachInsuredCoverageLevelBo.getAmount();
                            // additionalPlan
                            additionalPlan = groupAttachInsuredCoverageLevelBo.getProductLevelI18n();

                            //18 保额
                            additionalPlanAmount18 = additionalPlanAmount18.add(coverageLevelBoAmount18.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP));

                            // 附加险 总保费
                            additionalPlanTotalPremium = additionalPlanTotalPremium.add(coverageLevelBoTotalPremium);
                        }

                        // 26号产品 附加团体意外死亡和残疾保险 险种档次国际化
                        if ("PRO880000000000026".equals(coverageBoProductId)) {
                            BigDecimal coverageLevelBoAmount26 = groupAttachInsuredCoverageLevelBo.getAmount();
                            // additionalPlan
                            additionalPlan = groupAttachInsuredCoverageLevelBo.getProductLevelI18n();
                            //26 保额
                            additionalPlanAmount26 = additionalPlanAmount26.add(coverageLevelBoAmount26.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP));
                            // 26附加险 总保费
                            additionalPlanTotalPremium26 = additionalPlanTotalPremium26.add(coverageLevelBoTotalPremium);
                        }

                        // 27号产品 附加新团体意外医疗保险 险种档次国际化
                        if ("PRO880000000000027".equals(coverageBoProductId)) {
                            BigDecimal coverageLevelBoAmount27 = groupAttachInsuredCoverageLevelBo.getAmount();
                            // additionalPlan
                            additionalPlan = groupAttachInsuredCoverageLevelBo.getProductLevelI18n();
                            //27 保额
                            additionalPlanAmount27 = additionalPlanAmount27.add(coverageLevelBoAmount27.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP));
                            // 27附加险 总保费
                            additionalPlanTotalPremium27 = additionalPlanTotalPremium27.add(coverageLevelBoTotalPremium);
                        }

                        //33号产品 附加团体意外无忧医疗保险
                        if ("PRO880000000000033".equals(coverageBoProductId)) {
                            BigDecimal coverageLevelBoAmount33 = groupAttachInsuredCoverageLevelBo.getAmount();
                            // additionalPlan
                            additionalPlan = groupAttachInsuredCoverageLevelBo.getProductLevelI18n();
                            //33 保额
                            additionalPlanAmount33 = additionalPlanAmount33.add(coverageLevelBoAmount33.multiply(new BigDecimal(groupAttachInsuredCoverageLevelBo.getMult()))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP));
                            // 33附加险 总保费
                            additionalPlanTotalPremium33 = additionalPlanTotalPremium33.add(coverageLevelBoTotalPremium);
                        }
                    }
                }
                insuredMap.put("illnessOrAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(illnessOrAccidentAmount), 3));
                insuredMap.put("onlyAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(onlyAccidentAmount), 3));
                insuredMap.put("additionalPlan", PrintCommon.getPrintString(additionalPlan, 3));
                // 统计每个被保人 因疾病或意外 总保额
                illnessOrAccidentTotalAmount = illnessOrAccidentAmount.add(illnessOrAccidentTotalAmount);
                // 因意外 总保额
                onlyAccidentTotalAmount = onlyAccidentAmount.add(onlyAccidentTotalAmount);
                //18附加项 保额
                additionalPlanTotalAmount18 = additionalPlanAmount18.add(additionalPlanTotalAmount18);
                //26附加项 保额
                additionalPlanTotalAmount26 = additionalPlanAmount26.add(additionalPlanTotalAmount26);
                //27附加项 保额
                additionalPlanTotalAmount27 = additionalPlanAmount27.add(additionalPlanTotalAmount27);
                //33附加项 保额
                additionalPlanTotalAmount33 = additionalPlanAmount33.add(additionalPlanTotalAmount33);
                insuredMapList.add(insuredMap);
            }
            List<GroupAttachInsuredCoverageBo> finalListCoverage = listCoverage;
            groupAttachInsuredBos.stream().filter(bo -> AssertUtils.isNotEmpty(bo.getListCoverage())).forEach(bo -> finalListCoverage.addAll(bo.getListCoverage()));
        } else {
            listCoverage = JSON.parseArray(JSON.toJSONString(groupAttachInsuredBos), GroupAttachInsuredCoverageBo.class);
        }
        map.put("insuredMapList", insuredMapList);
        if (AssertUtils.isNotNull(illnessOrAccidentTotalAmount)) {
            totalAmount = illnessOrAccidentTotalAmount.add(onlyAccidentTotalAmount).add(additionalPlanTotalAmount18).add(additionalPlanTotalAmount26).add(additionalPlanTotalAmount27).add(additionalPlanTotalAmount33);
        }
        if (AssertUtils.isNotNull(illnessOrAccidentTotalPremium)) {
            totalPremium = illnessOrAccidentTotalPremium.add(additionalPlanTotalPremium
                    .add(additionalPlanTotalPremium26).add(additionalPlanTotalPremium27).add(additionalPlanTotalPremium33).add(onlyAccidentTotalPremium));
            illnessOrAccidentTotalPremium = illnessOrAccidentTotalPremium.add(onlyAccidentTotalPremium);
        }
        map.put("totalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount), 3));
        map.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));
        map.put("illnessOrAccidentTotalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(illnessOrAccidentTotalAmount), 3));
        map.put("onlyAccidentTotalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(onlyAccidentTotalAmount), 3));
        map.put("illnessOrAccidentTotalPremium", PrintCommon.getPrintString(illnessOrAccidentTotalPremium, 3));
        map.put("onlyAccidentTotalPremium", PrintCommon.getPrintString(onlyAccidentTotalPremium, 3));
        map.put("additionalPlanTotalPremium", PrintCommon.getPrintString(additionalPlanTotalPremium, 3));
        map.put("additionalPlanTotalPremium26", PrintCommon.getPrintString(additionalPlanTotalPremium26, 3));
        map.put("additionalPlanTotalPremium27", PrintCommon.getPrintString(additionalPlanTotalPremium27, 3));
        map.put("additionalPlanTotalPremium33", PrintCommon.getPrintString(additionalPlanTotalPremium33, 3));
        map.put("additionalPlanTotalAmount18", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(additionalPlanTotalAmount18), 3));
        map.put("additionalPlanTotalAmount26", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(additionalPlanTotalAmount26), 3));
        map.put("additionalPlanTotalAmount27", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(additionalPlanTotalAmount27), 3));
        map.put("additionalPlanTotalAmount33", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(additionalPlanTotalAmount33), 3));

        listCoverage.forEach(coverage -> {
            putProductOrDutyLevel(dutyLevelMap, coverage, coverage.getProductId());
        });
        map.putAll(dutyLevelMap);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    private void putProductOrDutyLevel(Map<String, Object> dutyLevelMap, GroupAttachInsuredCoverageBo coverage, String productId) {
        List<GroupAttachInsuredCoverageDutyBo> listCoverageDuty = coverage.getListCoverageDuty();
        List<GroupAttachInsuredCoverageLevelBo> listCoverageLevel = coverage.getListCoverageLevel();
        listCoverageLevel.forEach(leveBo -> {
            String dutyId = "";
            if (AssertUtils.isNotEmpty(listCoverageDuty)) {
                Optional<GroupAttachInsuredCoverageDutyBo> first = listCoverageDuty.stream().filter(groupAttachInsuredCoverageDutyBo -> groupAttachInsuredCoverageDutyBo.getCoverageDutyId().equals(leveBo.getCoverageDutyId())).findFirst();
                if (first.isPresent()) {
                    dutyId = first.get().getDutyId();
                    dutyLevelMap.put(dutyId, dutyId);
                }
            }
            String id = AssertUtils.isNotEmpty(dutyId) ? dutyId : productId;
            dutyLevelMap.put(id + leveBo.getProductLevel(), leveBo.getProductLevel());
        });
    }

    /**
     * 获取保单
     *
     * @param electronicPolicyGeneratorRequest
     * @param policyData
     * @return
     * @throws Exception
     */
    public byte[] getPolicyPdfBytes(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, Map<String, Object> policyData) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        //保险证 1
        List<byte[]> policyBookPdfBytesList = new ArrayList<>();

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);
        policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
        policyBookPdfBytesList.add(policyBookBytes);
        return PrintCommon.mergePdfFiles(policyBookPdfBytesList);
    }

}
