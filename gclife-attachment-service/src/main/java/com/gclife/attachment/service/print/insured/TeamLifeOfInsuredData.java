package com.gclife.attachment.service.print.insured;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.ApplyBeneficiaryBo;
import com.gclife.attachment.model.policy.apply.ApplyCoverageBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.policy.apply.group.ApplyGroupHealthQuestionnaireAnswerBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyCoverageDutyBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyCoverageLevelBo;
import com.gclife.attachment.model.policy.apply.group.PrintApplyInsuredBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Component
public class TeamLifeOfInsuredData {

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public List<Map<String, Object>> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PrintApplyInsuredBo applyPrintBo = JSON.parseObject(content, PrintApplyInsuredBo.class);
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        if (AssertUtils.isNotEmpty(applyPrintBo.getPolicyNo())) {
            map.put("policyNo", applyPrintBo.getPolicyNo());
        }
        map.put("companyName", PrintCommon.getPrintString(applyPrintBo.getCompanyName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyPrintBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyPrintBo.getAgentMobile(), 3));
        map.put("remark", PrintCommon.getPrintString(applyPrintBo.getRemark(), 3));
        PrintCommon.setPrintDateTime(map, "printDate", applyPrintBo.getPrintDate(), 3);

        List<ApplyCoverageBo> applyCoverageList = applyPrintBo.getApplyCoverageList();
        List<ApplyInsuredBo> applyInsuredList = applyPrintBo.getApplyInsuredList();
        List<ApplyBeneficiaryBo> applyBeneficiaryBoList = applyPrintBo.getApplyBeneficiaryBoList();
        List<GroupAttachApplyCoverageLevelBo> applyCoverageLevelList = applyPrintBo.getApplyCoverageLevelList();
        List<GroupAttachApplyCoverageDutyBo> applyCoverageDutyList = applyPrintBo.getApplyCoverageDutyList();
        List<ApplyGroupHealthQuestionnaireAnswerBo> aghqaList = applyPrintBo.getAghqaList();
        Map<String, List<ApplyCoverageBo>> productMapListCoverageBo = applyCoverageList.stream().collect(Collectors.groupingBy(ApplyCoverageBo::getProductId));
        productMapListCoverageBo.forEach((productId, applyCoverageBos) -> {
            PrintCommon.setProductName(map, productId);
            final AtomicInteger[] insuredNo = {new AtomicInteger(0)};
            Map<String, Object> productMap = new HashMap<>();
            List<String> insuredIdList = applyCoverageBos.stream().map(ApplyCoverageBo::getInsuredId).collect(Collectors.toList());
            List<ApplyInsuredBo> insuredList = applyInsuredList.stream().filter(applyInsuredBo -> insuredIdList.contains(applyInsuredBo.getInsuredId())).collect(Collectors.toList());
            if (!AssertUtils.isNotEmpty(insuredList)) {
                return;
            }
            List<Map<String, Object>> insuredMapList = new ArrayList<>();
            final BigDecimal[] totalPremiumSum = {new BigDecimal(0)};
            insuredList.forEach(applyInsuredBo -> {
                Map<String, Object> insuredMap = new HashMap<>();
                insuredMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].incrementAndGet(), 3));
                insuredMap.put("name", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                insuredMap.put("idTypeName", PrintCommon.getPrintString(applyInsuredBo.getIdTypeName(), 3));
                insuredMap.put("idNo", PrintCommon.getPrintString(applyInsuredBo.getIdNo(), 3));
                Integer ageYear = null;
                if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                    try {
                        ageYear = DateUtils.getAgeYear(new Date(applyInsuredBo.getBirthday()), new Date(applyPrintBo.getApplyDate()));
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RequestException();
                    }
                }
                insuredMap.put("ageYear", PrintCommon.getPrintString(ageYear, 3));
                insuredMap.put("sexName", PrintCommon.getPrintString(applyInsuredBo.getSexName(), 3));
                //健康告知
                final String[] answerCodeJoin = {null};
                if (AssertUtils.isNotEmpty(aghqaList)) {
                    aghqaList.stream().filter(aghqaBo -> applyInsuredBo.getInsuredId().equals(aghqaBo.getInsuredId()) && AssertUtils.isNotEmpty(aghqaBo.getAnswer())).forEach(aghqaBo -> {
                        answerCodeJoin[0] = (AssertUtils.isNotEmpty(answerCodeJoin[0]) ? answerCodeJoin[0] + "," : "") + aghqaBo.getAnswer();
                    });
                }
                insuredMap.put("answerCodeJoin", PrintCommon.getPrintString(answerCodeJoin[0], 3));
                insuredMap.put("occupationCode", PrintCommon.getPrintString(applyInsuredBo.getOccupationCode(), 3));
                final BigDecimal[] totalPremium = {new BigDecimal(0)};
                final String[] totalAmount = {""};
                applyCoverageBos.stream().filter(applyCoverageBo -> applyInsuredBo.getInsuredId().equals(applyCoverageBo.getInsuredId())).forEach(applyCoverageBo -> {
                    totalPremium[0] = totalPremium[0].add(applyCoverageBo.getTotalPremium());
                    List<GroupAttachApplyCoverageLevelBo> clBos = applyCoverageLevelList.stream().filter(groupAttachApplyCoverageLevelBo -> applyCoverageBo.getCoverageId().equals(groupAttachApplyCoverageLevelBo.getCoverageId())).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(applyCoverageDutyList)) {
                        clBos.forEach(groupAttachApplyCoverageLevelBo -> {
                            String coverageDutyId = groupAttachApplyCoverageLevelBo.getCoverageDutyId();
                            if (AssertUtils.isNotEmpty(coverageDutyId)) {
                                applyCoverageDutyList.stream().filter(acdBo -> coverageDutyId.equals(acdBo.getCoverageDutyId())).findFirst().ifPresent(acdBo -> {
                                    groupAttachApplyCoverageLevelBo.setCoverageDutyId(acdBo.getDutyId());
                                });
                            }
                        });
                        clBos.sort(Comparator.comparing(GroupAttachApplyCoverageLevelBo::getCoverageDutyId, Comparator.nullsFirst(String::compareTo)));
                    }
                    clBos.forEach(groupAttachApplyCoverageLevelBo -> {
                        String mult = AssertUtils.isNotEmpty(groupAttachApplyCoverageLevelBo.getMult()) ? groupAttachApplyCoverageLevelBo.getMult() : "1";
                        String dutyId = groupAttachApplyCoverageLevelBo.getCoverageDutyId();
                        String productLevel = groupAttachApplyCoverageLevelBo.getProductLevel();
                        BigDecimal amount = groupAttachApplyCoverageLevelBo.getAmount();
                        ProductCalculation.setAmount(productId, totalAmount, mult, productLevel, dutyId, amount);
                    });
                });
                insuredMap.put("totalPremium", PrintCommon.getPrintString(totalPremium[0], 3));
                totalPremiumSum[0] = totalPremiumSum[0].add(totalPremium[0]);
                insuredMap.put("totalAmount", PrintCommon.getPrintString(totalAmount[0], 3));
                boolean beneficiaryPresent = false;
                if (AssertUtils.isNotEmpty(applyBeneficiaryBoList)) {
                    Optional<ApplyBeneficiaryBo> applyBeneficiaryBoOptional = applyBeneficiaryBoList.stream().filter(applyBeneficiaryBo -> applyInsuredBo.getInsuredId().equals(applyBeneficiaryBo.getInsuredId())).findFirst();
                    beneficiaryPresent = applyBeneficiaryBoOptional.isPresent();
                }
                String beneficiaryInformation = PrintCommon.getBeneficiaryInformation(language, beneficiaryPresent);
                insuredMap.put("beneficiaryInformation", PrintCommon.getPrintString(beneficiaryInformation, 3));
                insuredMapList.add(insuredMap);
            });
            map.put("totalPremiumSum", PrintCommon.getPrintString(totalPremiumSum[0], 3));
            map.put("insuredSum", PrintCommon.getPrintString(insuredNo[0].get(), 3));
            productMap.put("insuredMapList", insuredMapList);
            if (AssertUtils.isNotEmpty(applyBeneficiaryBoList)) {
                List<ApplyBeneficiaryBo> beneficiaryList = applyBeneficiaryBoList.stream().filter(applyBeneficiaryBo -> insuredIdList.contains(applyBeneficiaryBo.getInsuredId())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(beneficiaryList)) {
                    List<Map<String, Object>> beneficiaryMapList = new ArrayList<>();
                    insuredNo[0].set(1);
                    Map<String, List<ApplyBeneficiaryBo>> insuredIdBeneficiaryListMap = beneficiaryList.stream().collect(Collectors.groupingBy(ApplyBeneficiaryBo::getInsuredId));
                    insuredIdBeneficiaryListMap.forEach((s, applyBeneficiaryBos) -> {
                        applyBeneficiaryBos.forEach(beneficiaryBo -> {
                            Map<String, Object> beneficiaryMap = new HashMap<>();
                            Optional<ApplyInsuredBo> applyInsuredBoOptional = applyInsuredList.stream().filter(applyInsuredBo -> applyInsuredBo.getInsuredId().equals(beneficiaryBo.getInsuredId())).findFirst();
                            ApplyInsuredBo applyInsuredBo = applyInsuredBoOptional.get();
                            beneficiaryMap.put("insuredNo", PrintCommon.getPrintString(insuredNo[0].getAndIncrement(), 3));
                            beneficiaryMap.put("insuredName", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                            beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryNoOrderName(), 3));
                            beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryBo.getName(), 3));
                            beneficiaryMap.put("sexName", PrintCommon.getPrintString(beneficiaryBo.getSexName(), 3));
                            if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(beneficiaryBo.getRelationship()) && AssertUtils.isNotEmpty(beneficiaryBo.getRelationshipInstructions())) {
                                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipInstructions(), 3));
                            } else {
                                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(beneficiaryBo.getRelationshipName(), 3));
                            }
                            beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(beneficiaryBo.getBeneficiaryProportion(), 3));
                            beneficiaryMap.put("idTypeName", PrintCommon.getPrintString(beneficiaryBo.getIdTypeName(), 3));
                            beneficiaryMap.put("idNo", PrintCommon.getPrintString(beneficiaryBo.getIdNo(), 3));
                            beneficiaryMapList.add(beneficiaryMap);
                        });
                    });
                    productMap.put("beneficiaryMapList", beneficiaryMapList);
                }
            }
            productMap.putAll(map);
            Object primaryFlagZHCn = productMap.get("primaryFlagZH_CN");
            if (!"(主险)".equals(primaryFlagZHCn)) {
                productMap.remove("beneficiaryMapList");
            }
            mapList.add(productMap);
        });
        PrintCommon.coverageSort(mapList);
        return mapList;
    }

}
