package com.gclife.attachment.service.print.insured;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.apply.ApplyBeneficiaryBo;
import com.gclife.attachment.model.policy.apply.ApplyCoverageBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.policy.apply.group.GroupAttachApplyCoverageLevelBo;
import com.gclife.attachment.model.policy.apply.group.PrintApplyInsuredBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/11/15
 */
@Component
public class GroupInsuranceBenefitsSummaryInsuredData {

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        PrintApplyInsuredBo applyPrintBo = JSON.parseObject(content, PrintApplyInsuredBo.class);
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        if (AssertUtils.isNotEmpty(applyPrintBo.getPolicyNo())) {
            map.put("policyNo", applyPrintBo.getPolicyNo());
        }
        map.put("companyName", PrintCommon.getPrintString(applyPrintBo.getCompanyName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyPrintBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyPrintBo.getAgentMobile(), 3));
        map.put("agentName", PrintCommon.getPrintString(applyPrintBo.getAgentName(), 3));
        map.put("remark", PrintCommon.getPrintString(applyPrintBo.getRemark(), 3));
        PrintCommon.setPrintDateTime(map, "printDate", applyPrintBo.getPrintDate(), 3);

        Map<String, Object> productMap = new HashMap<>();
        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        List<Map<String, Object>> beneficiaryMapList = new ArrayList<>();
        BigDecimal totalPremium = BigDecimal.ZERO;
        int insuredNo = 1;
        int beneficiaryNo = 1;
        // 被保人集合
        List<ApplyInsuredBo> applyInsuredBos = applyPrintBo.getApplyInsuredList();
        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            HashMap<String, Object> insuredMap = new HashMap<>();
            // 因疾病或意外 保额
            BigDecimal illnessOrAccidentAmount = BigDecimal.ZERO;
            // 因意外 保额
            BigDecimal onlyAccidentAmount = BigDecimal.ZERO;
            //26保额
            BigDecimal additionalPlanAmount26 = BigDecimal.ZERO;
            //27保额
            BigDecimal additionalPlanAmount27 = BigDecimal.ZERO;
            //33保额
            BigDecimal additionalPlanAmount33 = BigDecimal.ZERO;
            // 被保人 总保费
            BigDecimal insuredPremium = BigDecimal.ZERO;
            // 附加团体医疗保险 档次
            String additionalPlan = null;
            String additionalPlan26 = null;
            String additionalPlan27 = null;
            String additionalPlan33 = null;
            insuredMap.put("insuredNo", insuredNo++);
            insuredMap.put("name", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
            insuredMap.put("idNo", PrintCommon.getPrintString(applyInsuredBo.getIdNo(), 3));
            PrintCommon.setPrintDateTime(insuredMap, "birthday", applyInsuredBo.getBirthday(), 3);
            Integer ageYear = null;
            if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                try {
                    ageYear = DateUtils.getAgeYear(new Date(applyInsuredBo.getBirthday()), new Date(applyPrintBo.getApplyDate()));
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RequestException();
                }
            }
            insuredMap.put("ageYear", PrintCommon.getPrintString(ageYear, 3));
            insuredMap.put("sexName", PrintCommon.getPrintString(applyInsuredBo.getSexName(), 3));
            // 被保人ID
            String insuredId = applyInsuredBo.getInsuredId();
            // 被保人的险种
            List<ApplyCoverageBo> applyCoverageBos = applyPrintBo.getApplyCoverageList();
            for (ApplyCoverageBo applyCoverageBo : applyCoverageBos) {
                if (insuredId.equals(applyCoverageBo.getInsuredId())) {
                    // 险种ID
                    String coverageId = applyCoverageBo.getCoverageId();
                    String coverageBoProductId = applyCoverageBo.getProductId();
                    // 被保人的险种档次
                    List<GroupAttachApplyCoverageLevelBo> applyCoverageLevelBos = applyPrintBo.getApplyCoverageLevelList();
                    for (GroupAttachApplyCoverageLevelBo applyCoverageLevelBo : applyCoverageLevelBos) {
                        if (coverageId.equals(applyCoverageLevelBo.getCoverageId())) {
                            String levelBoProductLevel = applyCoverageLevelBo.getProductLevel();
                            // 17号产品 GC全优团保
                            if ("PRO880000000000017".equals(coverageBoProductId)) {
                                // 因疾病或意外 的保额
                                BigDecimal coverageLevelBoAmount = applyCoverageLevelBo.getAmount();
                                if (AssertUtils.isNotNull(coverageLevelBoAmount)) {
                                    if ("TL".equals(levelBoProductLevel)
                                            || "TC".equals(levelBoProductLevel)
                                            || "TS".equals(levelBoProductLevel)) {
                                        illnessOrAccidentAmount = illnessOrAccidentAmount.add(coverageLevelBoAmount.multiply(new BigDecimal(applyCoverageLevelBo.getMult()))
                                                .setScale(2, BigDecimal.ROUND_HALF_UP));
                                    }
                                    // 因意外 的保额
                                    if ("TA".equals(levelBoProductLevel)) {
                                        // onlyAccidentAmount
                                        onlyAccidentAmount = onlyAccidentAmount.add(coverageLevelBoAmount.multiply(new BigDecimal(applyCoverageLevelBo.getMult()))
                                                .setScale(2, BigDecimal.ROUND_HALF_UP));
                                    }
                                }
                            }
                            // 18号产品 附加团体医疗保险 险种档次国际化
                            if ("PRO880000000000018".equals(coverageBoProductId)) {
                                // additionalPlan
                                additionalPlan = applyCoverageLevelBo.getProductLevelI18n();
                            }

                            // 26号产品 附加团体意外死亡和残疾保险 险种档次国际化
                            if ("PRO880000000000026".equals(coverageBoProductId)) {
                                BigDecimal coverageLevelBoAmount26 = applyCoverageLevelBo.getAmount();
                                // additionalPlan
                                additionalPlan26 = applyCoverageLevelBo.getProductLevelI18n();
                                //26 保额
                                additionalPlanAmount26 = additionalPlanAmount26.add(coverageLevelBoAmount26.multiply(new BigDecimal(applyCoverageLevelBo.getMult()))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP));
                            }

                            // 27号产品 附加新团体意外医疗保险 险种档次国际化
                            if ("PRO880000000000027".equals(coverageBoProductId)) {
                                BigDecimal coverageLevelBoAmount27 = applyCoverageLevelBo.getAmount();
                                // additionalPlan
                                additionalPlan27 = applyCoverageLevelBo.getProductLevelI18n();
                                //27 保额
                                additionalPlanAmount27 = additionalPlanAmount27.add(coverageLevelBoAmount27.multiply(new BigDecimal(applyCoverageLevelBo.getMult()))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP));
                            }

                            // 33号产品 附加新团体意外医疗保险 险种档次国际化
                            if ("PRO880000000000033".equals(coverageBoProductId)) {
                                BigDecimal coverageLevelBoAmount33 = applyCoverageLevelBo.getAmount();
                                // additionalPlan
                                additionalPlan33 = applyCoverageLevelBo.getProductLevelI18n();
                                //33 保额
                                additionalPlanAmount33 = additionalPlanAmount33.add(coverageLevelBoAmount33.multiply(new BigDecimal(applyCoverageLevelBo.getMult()))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP));
                            }
                        }
                    }
                    BigDecimal coverageBoTotalPremium = applyCoverageBo.getTotalPremium();
                    // 被保人 总保费
                    insuredPremium = insuredPremium.add(coverageBoTotalPremium);
                }
            }
            insuredMap.put("illnessOrAccidentAmount", PrintCommon.getPrintString(illnessOrAccidentAmount, 3));
            insuredMap.put("onlyAccidentAmount", PrintCommon.getPrintString(onlyAccidentAmount, 3));
            insuredMap.put("additionalPlan", PrintCommon.getPrintString(additionalPlan, 3));
            insuredMap.put("additionalPlan26", PrintCommon.getPrintString(additionalPlan26, 3));
            insuredMap.put("additionalPlan27", PrintCommon.getPrintString(additionalPlan27, 3));
            insuredMap.put("additionalPlan33", PrintCommon.getPrintString(additionalPlan33, 3));
            insuredMap.put("additionalPlanAmount26", PrintCommon.getPrintString(additionalPlanAmount26, 3));
            insuredMap.put("additionalPlanAmount27", PrintCommon.getPrintString(additionalPlanAmount27, 3));
            insuredMap.put("additionalPlanAmount33", PrintCommon.getPrintString(additionalPlanAmount33, 3));
            insuredMap.put("insuredPremium", PrintCommon.getPrintString(insuredPremium, 3));
            // 保费总额
            totalPremium = totalPremium.add(insuredPremium);
            insuredMapList.add(insuredMap);

            // 设置受益人信息
            List<ApplyBeneficiaryBo> applyBeneficiaryBos = applyPrintBo.getApplyBeneficiaryBoList();
            for (ApplyBeneficiaryBo applyBeneficiaryBo : applyBeneficiaryBos) {
                if (insuredId.equals(applyBeneficiaryBo.getInsuredId())) {
                    // 一个被保人可能有多个受益人
                    Map<String, Object> beneficiaryMap = new HashMap<>();
                    beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(beneficiaryNo++, 3));
                    beneficiaryMap.put("insuredName", PrintCommon.getPrintString(applyInsuredBo.getName(), 3));
                    beneficiaryMap.put("insuredIdNo", PrintCommon.getPrintString(applyInsuredBo.getIdNo(), 3));
                    beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(applyBeneficiaryBo.getName(), 3));
                    beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(applyBeneficiaryBo.getIdNo(), 3));
                    PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);

                    Integer beneficiaryAgeYear = null;
                    if (AssertUtils.isNotNull(applyBeneficiaryBo.getBirthday())) {
                        try {
                            beneficiaryAgeYear = DateUtils.getAgeYear(new Date(applyBeneficiaryBo.getBirthday()), new Date(applyPrintBo.getApplyDate()));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new RequestException();
                        }
                    }
                    beneficiaryMap.put("ageYear", PrintCommon.getPrintString(beneficiaryAgeYear, 3));
                    beneficiaryMap.put("sexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 3));
                    beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(applyBeneficiaryBo.getRelationshipName(), 3));
                    beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryBo.getBeneficiaryProportion() + "%", 3));
                    beneficiaryMapList.add(beneficiaryMap);
                }
            }
        }
        // 被保人
        productMap.put("insuredMapList", insuredMapList);
        // 受益人
        productMap.put("beneficiaryMapList", beneficiaryMapList);
        map.put("insuredSum", PrintCommon.getPrintString(insuredMapList.size(), 3));
        map.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));
        productMap.putAll(map);
        return productMap;
    }

}
