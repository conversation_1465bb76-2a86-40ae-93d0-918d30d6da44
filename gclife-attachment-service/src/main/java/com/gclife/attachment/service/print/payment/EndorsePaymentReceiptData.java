package com.gclife.attachment.service.print.payment;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintBo;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintCoverageBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:56 2019/3/18
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class EndorsePaymentReceiptData {

    public Map<String, Object> getPaymentReceiptData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> stringObjectMap = new HashMap<>();

        String content = electronicPolicyGeneratorRequest.getContent();
        AttachmentPaymentPrintBo paymentPrintBo = JSON.parseObject(content, AttachmentPaymentPrintBo.class);

        stringObjectMap.put("businessNo", PrintCommon.getPrintString(paymentPrintBo.getBusinessNo(), 3));
        stringObjectMap.put("receiptNo", PrintCommon.getPrintString(paymentPrintBo.getReceiptNo(), 3));
        String printDate = null;
        if (AssertUtils.isNotNull(paymentPrintBo.getPrintDate())) {
            String[] k = new Date(paymentPrintBo.getPrintDate()).toString().split(" ");
            printDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        }
        stringObjectMap.put("printDate", PrintCommon.getPrintString(printDate, 3));

        stringObjectMap.put("applicantName", PrintCommon.getPrintString(paymentPrintBo.getApplicantName(), 3));
        stringObjectMap.put("paymentMethodName", PrintCommon.getPrintString(paymentPrintBo.getPaymentMethodName(), 3));
        stringObjectMap.put("endorseProjectName", PrintCommon.getPrintString(paymentPrintBo.getEndorseProjectName(), 3));

        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<AttachmentPaymentPrintCoverageBo> coverageBoList = paymentPrintBo.getPrintCoverages();
        coverageBoList.forEach(coverageBo -> {
            Map<String, Object> map = new HashMap<>();
            String productId = AssertUtils.isNotEmpty(coverageBo.getProductId()) ? coverageBo.getProductId() : "";
            String productLevel = AssertUtils.isNotEmpty(coverageBo.getDutyId()) ? coverageBo.getDutyId() : coverageBo.getProductLevel();
            map.put("dutyId",coverageBo.getDutyId());
            PrintCommon.setProductName(map, productId, productLevel);
            map.put("paymentItem", PrintCommon.getPrintString(coverageBo.getPaymentItem(), 3));
            map.put("installmentNo", PrintCommon.getPrintString(coverageBo.getInstallmentNo(), 3));
            String calculatePaymentAmount = coverageBo.getCalculatePaymentAmount() == null ? null : "$" + coverageBo.getCalculatePaymentAmount();
            map.put("calculatePaymentAmount", PrintCommon.getPrintString(calculatePaymentAmount, 3));
            coverageListMap.add(map);
        });
        PrintCommon.coverageSort(coverageListMap);
        stringObjectMap.put("coverageListMap", coverageListMap);

        String totalPremium = paymentPrintBo.getTotalPremium() == null ? null : "$" + PrintCommon.getPrintString(paymentPrintBo.getTotalPremium(), 3);

        stringObjectMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 3));

        stringObjectMap.put("agentCode", PrintCommon.getPrintString(paymentPrintBo.getAgentCode(), 3));
        stringObjectMap.put("receivedBy", PrintCommon.getPrintString(paymentPrintBo.getReceivedBy(), 3));

        return stringObjectMap;
    }
}
