package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.BENEFICIARY_NO.ORDER_ONE;

/**
 * 废弃类  原3号产品
 */
@Component
public class OffspringProsperityData {


    /**
     * 获取计划书打印数据
     *
     * @return
     */
    public List<PrintObject> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        //投保人信息
        PrintCommon.setPrintData(printObjectList, "applyPlanNo", planPrintBo.getApplyPlanNo(), 3);

        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        String applicantName = null;
        Integer applicantAgeYear = null;
        String applicantSex = null;
        String applicantSexName = null;
        String applicantIdNo = null;
        if (AssertUtils.isNotNull(applicant)) {
            // 投保人姓名
            applicantName = applicant.getName();
            // 投保人年龄
            if (AssertUtils.isNotNull(applicant.getBirthday())) {
                int ageYear = StringUtil.getAgeByBirthday(applicant.getBirthday());
                applicantAgeYear = ageYear;
            }
            // 投保人性别
            applicantSex = applicant.getSex();
            applicantSexName = applicant.getSexName();
            //投保人证件号码
            if (AssertUtils.isNotEmpty(applicant.getIdTypeName()) && AssertUtils.isNotEmpty(applicant.getIdNo())) {
                applicantIdNo = applicant.getIdTypeName() + "/" + applicant.getIdNo();
            }
        }

        String applicantSexRespectZH_CN = null;
        String applicantSexRespectEN_US = null;
        String applicantSexRespectKM_KH = null;
        if (AttachmentTermEnum.GENDER.MALE.name().equals(applicantSex)) {
            applicantSexRespectZH_CN = "先生";
            applicantSexRespectEN_US = "Mr";
            applicantSexRespectKM_KH = "លោក";
        }
        if (AttachmentTermEnum.GENDER.FEMALE.name().equals(applicantSex)) {
            applicantSexRespectZH_CN = "女士";
            applicantSexRespectEN_US = "Ms";
            applicantSexRespectKM_KH = "លោកស្រី";
        }
        PrintCommon.setPrintData(printObjectList, "applicantSexRespectZH_CN", applicantSexRespectZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "applicantSexRespectEN_US", applicantSexRespectEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "applicantSexRespectKM_KH", applicantSexRespectKM_KH, 3);
        PrintCommon.setPrintData(printObjectList, "applicantName", applicantName, 3);
        PrintCommon.setPrintData(printObjectList, "applicantAgeYear", applicantAgeYear, 3);
        PrintCommon.setPrintData(printObjectList, "applicantSex", applicantSex, 0);
        PrintCommon.setPrintData(printObjectList, "applicantSexName", applicantSexName, 3);
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", applicantIdNo, 3);


        //被保人信息
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        // 被保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", insured.getName(), 3);
        // 被保年龄
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = StringUtil.getAgeByBirthday(insured.getBirthday());
        }
        PrintCommon.setPrintData(printObjectList, "insuredAgeYear", insuredAgeYear, 3);
        // 投保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", insured.getSexName(), 3);
        // 与投保人关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", insured.getRelationshipName(), 3);


        // 购买 份数
        /********************************************险种信息*******************************************************/
        String premiumFrequency = null;
        // 购买 份数
        String mult = null;
        List<ProductCashValueBo> policyCashValuesMain = null;
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            Optional<ApplyCoveragePlanBo> optionalApplyCoverageMainBo = listCoverage.stream().filter(coveragePlanBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coveragePlanBo.getPrimaryFlag())).findFirst();
            if (optionalApplyCoverageMainBo.isPresent()) {
                ApplyCoveragePlanBo applyCoverageMainBo = optionalApplyCoverageMainBo.get();
                mult = applyCoverageMainBo.getMult();
                // 缴费类型
                premiumFrequency = applyCoverageMainBo.getPremiumFrequency();
                String productId = applyCoverageMainBo.getProductId();
                List<ProductCashValueBo> policyCashValues = planPrintBo.getListCashValue();
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValuesMain = policyCashValues.stream().filter(productCashValueBo -> productId.equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
            }

            Optional<ApplyCoveragePlanBo> optionalApplyCoverageAdditionalBo = listCoverage.stream().filter(coveragePlanBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(coveragePlanBo.getPrimaryFlag())).findFirst();
            if (optionalApplyCoverageAdditionalBo.isPresent()) {
                ApplyCoveragePlanBo applyCoverageAdditionalBo = optionalApplyCoverageAdditionalBo.get();
                electronicPolicyGeneratorRequest.setProductId(electronicPolicyGeneratorRequest.getProductId() + "_" + applyCoverageAdditionalBo.getProductId());
            }
        }
        //添加缴费类型数据
        PrintCommon.setPremiumFrequencyName(premiumFrequency, printObjectList);

        PrintCommon.setPrintData(printObjectList, "mult", mult, 3);
        // 首期保险费
        PrintCommon.setPrintData(printObjectList, "receivablePremium", planPrintBo.getReceivablePremium(), 3);
        //现金价值
        this.setCoverageDate(printObjectList, insured.getBirthday(), planPrintBo.getCreatedDate(), mult);
        ProductCalculation.policyCashValues(printObjectList, policyCashValuesMain);
        //代理人姓名
        PrintCommon.setPrintData(printObjectList, "agentName", planPrintBo.getAgentName(), 3);
        //代理人代码
        PrintCommon.setPrintData(printObjectList, "agentCode", planPrintBo.getAgentCode(), 3);
        //代理人手机号
        PrintCommon.setPrintData(printObjectList, "agentMobile", planPrintBo.getAgentMobile(), 3);
        //制作日期
        PrintCommon.setPrintDateTime(printObjectList, "createdDate", planPrintBo.getCreatedDate(), 3);
        return printObjectList;
    }

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public List<PrintObject> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();

        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        PrintCommon.setPrintData(printObjectList, "applyNo", applyPrintBo.getApplyNo(), 3);
        PrintCommon.setPrintData(printObjectList, "applyPlanNo", applyPrintBo.getApplyPlanNo(), 3);
        //姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", applicant.getName(), 3);
        //性别
        PrintCommon.setPrintData(printObjectList, new String[]{"applicantSexMALE", "applicantSexFEMALE"},
                "applicantSex" + applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", applicant.getBirthday(), 3);
        //证件类型
        PrintCommon.setPrintData(printObjectList,
                new String[]{"applicantIdTypeID", "applicantIdTypeHOUSEHOLD_REGISTER", "applicantIdTypePASSPORT", "applicantIdTypeOTHER"}
                , "applicantIdType" + applicant.getIdType());
        //国籍
        PrintCommon.setPrintData(printObjectList, "applicantNationalityName", applicant.getNationalityName(), 2);
        //证件有效期
        PrintCommon.setPrintDateTime(printObjectList, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //TODO 未有长期数据
        //证件号
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", applicant.getIdNo(), 3);
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setPrintData(printObjectList,
                new String[]{"applicantMarriageMARRIED", "applicantMarriageUNMARRIED"},
                "applicantMarriage" + applicantMarriage);
        //工作单位
        PrintCommon.setPrintData(printObjectList, "applicantCompanyName", applicant.getCompanyName(), 3);
        //收入
        BigDecimal applicantIncome = null;
        if (AssertUtils.isNotEmpty(applicant.getIncome())) {
            applicantIncome = new BigDecimal(applicant.getIncome()).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        PrintCommon.setPrintData(printObjectList, "applicantIncome", applicantIncome, 3);
        //固定电话
        PrintCommon.setPrintData(printObjectList, "applicantPhone", applicant.getHomePhone(), 3);
        //移动电话
        PrintCommon.setPrintData(printObjectList, "applicantMobile", applicant.getMobile(), 3);
        //邮箱
        PrintCommon.setPrintData(printObjectList, "applicantEmail", applicant.getEmail(), 2);
        //通讯地址
        PrintCommon.setPrintData(printObjectList, "applicantHomeAddress", applicant.getFullAddress(), 3);
        //邮政编码
        PrintCommon.setPrintData(printObjectList, "applicantZipCode", applicant.getHomeZipCode(), 3);
        //职业
        PrintCommon.setPrintData(printObjectList, "applicantOccupationName", applicant.getOccupationName(), 3);
        //兼职
        PrintCommon.setPrintData(printObjectList, "applicantPluralityName", applicant.getPluralityName(), 3);
        //职业代码
        PrintCommon.setPrintData(printObjectList, "applicantOccupationCode", applicant.getOccupationCode(), 3);

        String mult = null;
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = null;
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        } else {
            insured = new ApplyInsuredBo();
        }
        //与投保人关系
        String insuredRelationship = insured.getRelationship();
        if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.PARENTS.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.SPOUSE.name().equals(insuredRelationship)) {
        } else if (AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.CHILD.name().equals(insuredRelationship)) {
        } else {
            insuredRelationship = AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name();
        }
        PrintCommon.setPrintData(printObjectList,
                new String[]{"insuredRelationshipONESELF", "insuredRelationshipPARENTS", "insuredRelationshipCHILD", "insuredRelationshipSPOUSE", "insuredRelationshipOTHER"},
                "insuredRelationship" + insuredRelationship);
        //姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", insured.getName(), 3);
        //性别
        PrintCommon.setPrintData(printObjectList, new String[]{"insuredSexMALE", "insuredSexFEMALE"},
                "insuredSex" + insured.getSex());
        //出生年月日
        PrintCommon.setPrintData(printObjectList, "insuredBirthday", insured.getBirthday(), 0);
        Long insuredBirthday = insured.getBirthday();
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", insuredBirthday, 0);
        //证件类型
        PrintCommon.setPrintData(printObjectList,
                new String[]{"insuredIdTypeID", "insuredIdTypeHOUSEHOLD_REGISTER", "insuredIdTypePASSPORT", "insuredIdTypeOTHER"},
                "insuredIdType" + insured.getIdType());
        //国籍
        PrintCommon.setPrintData(printObjectList, "insuredNationalityName", insured.getNationalityName(), 0);
        //证件有效期
        PrintCommon.setPrintDateTime(printObjectList, "insuredIdExpDate", insured.getIdExpDate(), 0);
        //TODO 没有 是否长期数据
        PrintCommon.setPrintData(printObjectList, "insuredIdNo", insured.getIdNo(), 3);
        //婚姻状况
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setPrintData(printObjectList, new String[]{"insuredMarriageMARRIED", "insuredMarriageUNMARRIED"}, "insuredMarriage" + insuredMarriage);
        //工作单位
        PrintCommon.setPrintData(printObjectList, "insuredCompanyName", insured.getCompanyName(), 3);
        //收入
        PrintCommon.setPrintData(printObjectList, "insuredIncome", null, 3);
        //固定电话
        PrintCommon.setPrintData(printObjectList, "insuredPhone", insured.getHomePhone(), 3);
        //移动电话
        PrintCommon.setPrintData(printObjectList, "insuredMobile", insured.getMobile(), 3);
        //邮箱
        PrintCommon.setPrintData(printObjectList, "insuredEmail", insured.getEmail(), 3);
        //通讯地址
        PrintCommon.setPrintData(printObjectList, "insuredHomeAddress", insured.getFullAddress(), 3);
        //邮政编码
        PrintCommon.setPrintData(printObjectList, "insuredZipCode", insured.getHomeZipCode(), 3);
        //职业
        PrintCommon.setPrintData(printObjectList, "insuredOccupationName", insured.getOccupationName(), 3);
        //兼职
        PrintCommon.setPrintData(printObjectList, "insuredPluralityName", insured.getPluralityName(), 3);
        //职业代码
        PrintCommon.setPrintData(printObjectList, "insuredOccupationCode", insured.getOccupationCode(), 3);

        /**受益人信息*/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();

        for (int i = 0; i < 3; i++) {
            //受益配置信息
            ApplyBeneficiaryInfoBo applyBeneficiaryInfoBo = null;
            ApplyBeneficiaryBo applyBeneficiaryBo = null;
            if (AssertUtils.isNotEmpty(listBeneficiary) && i < listBeneficiary.size() && AssertUtils.isNotNull(listBeneficiary.get(i))) {
                applyBeneficiaryInfoBo = listBeneficiary.get(i);
                if (AssertUtils.isNotNull(applyBeneficiaryInfoBo)) {
                    applyBeneficiaryBo = listBeneficiary.get(i).getApplyBeneficiaryBo();
                }
            } else {
                applyBeneficiaryInfoBo = new ApplyBeneficiaryInfoBo();
                if (!AssertUtils.isNotNull(applyBeneficiaryBo)) {
                    applyBeneficiaryBo = new ApplyBeneficiaryBo();
                }
            }
            //收益人信息
            //收益顺序
            PrintCommon.setPrintData(printObjectList, "beneficiaryNo" + i, applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3);
            //姓名
            PrintCommon.setPrintData(printObjectList, "beneficiaryName" + i, applyBeneficiaryBo.getName(), 3);
            //性别
            PrintCommon.setPrintData(printObjectList, "beneficiarySexName" + i, applyBeneficiaryBo.getSexName(), 1);
            //是被保险人的
            PrintCommon.setPrintData(printObjectList, "relationshipName" + i, applyBeneficiaryInfoBo.getRelationshipName(), 2);
            //收益份额
            PrintCommon.setPrintData(printObjectList, "beneficiaryProportion" + i, AssertUtils.isNotNull(applyBeneficiaryInfoBo.getBeneficiaryProportion()) ? applyBeneficiaryInfoBo.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "" : null, 2);
            //证件类型
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdTypeName" + i, applyBeneficiaryBo.getIdTypeName(), 3);
            //出生年月日
            PrintCommon.setPrintDateTime(printObjectList, "beneficiaryBirthday" + i, applyBeneficiaryBo.getBirthday(), 3);
            //证件号码
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdNo" + i, applyBeneficiaryBo.getIdNo(), 3);
        }

        /**险种信息*/
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        BigDecimal totalPremium = new BigDecimal(0);
        String pensionReceiveFrequency = null;
        for (int i = 0; i < 4; i++) {
            ApplyCoverageBo applyCoverageBo = null;
            if (AssertUtils.isNotEmpty(listCoverage) && i < listCoverage.size() && AssertUtils.isNotNull(listCoverage.get(i))) {
                applyCoverageBo = listCoverage.get(i);
            } else {
                applyCoverageBo = new ApplyCoverageBo();
            }

            mult = listCoverage.get(0).getMult();
            //代码
            //险种名称
            PrintCommon.setProductName(printObjectList, applyCoverageBo.getProductId(), i + "");
            //保险金额
            PrintCommon.setPrintData(printObjectList, "amount" + i, null, 2);
            //领取年龄及方式　　
            if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                pensionReceiveFrequency = applyCoverageBo.getPensionReceiveFrequency();
            }

            //保险期限
            String coveragePeriod = "";
            if (AssertUtils.isNotNull(insuredBirthday)) {
                coveragePeriod = 22 - DateUtils.getAgeYear(new Date(insuredBirthday)) + "";
            }
            PrintCommon.setPrintData(printObjectList, "coveragePeriodKM_KH" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) && AssertUtils.isNotNull(coveragePeriod) ? coveragePeriod + " ឆ្នាំ" : null, 2);
            PrintCommon.setPrintData(printObjectList, "coveragePeriodZH_CN" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) && AssertUtils.isNotNull(coveragePeriod) ? coveragePeriod + " 年" : null, 2);
            PrintCommon.setPrintData(printObjectList, "coveragePeriodEN_US" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) && AssertUtils.isNotNull(coveragePeriod) ? coveragePeriod + " years" : null, 2);
            //保险费金额
            PrintCommon.setPrintData(printObjectList, "totalPremium" + i, AssertUtils.isNotNull(applyCoverageBo.getTotalPremium()) ? applyCoverageBo.getTotalPremium() + "" : null, 2);
            //缴费期限
            PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) ? "3ឆ្នាំ" : null, 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodZH_CN" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) ? "3年" : null, 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodEN_US" + i, AssertUtils.isNotEmpty(applyCoverageBo.getProductCode()) ? "3years" : null, 2);

            //TODO 没有 追加保费金额
            totalPremium = totalPremium.add(AssertUtils.isNotNull(applyCoverageBo.getTotalPremium()) ? applyCoverageBo.getTotalPremium() : new BigDecimal(0));
        }
        //领取年龄及方式
        PrintCommon.setPrintData(printObjectList, new String[]{"pensionReceiveFrequencyYEAR", "pensionReceiveFrequencyMONTH", "pensionReceiveFrequencySINGLE"}, "pensionReceiveFrequency" + pensionReceiveFrequency);
        //保费合计总额 美元
        ApplyPremiumBo applyPremiumBo = applyPrintBo.getApplyPremiumBo();
        if (!AssertUtils.isNotNull(applyPremiumBo)) {
            applyPremiumBo = new ApplyPremiumBo();
        }
        BigDecimal receivablePremium = applyPremiumBo.getReceivablePremium();
        PrintCommon.setPrintData(printObjectList, "totalPremium", totalPremium, 2);

        //交费方式
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
        } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
        } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
        } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
        } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
        } else {
            premiumFrequency = "OTHER";
        }
        AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY[] product_premium_frequencies = AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.values();
        String[] premiumFrequencys = new String[product_premium_frequencies.length + 1];
        for (int i = 0; i < product_premium_frequencies.length; i++) {
            premiumFrequencys[i] = "premiumFrequency" + product_premium_frequencies[i].name();
        }
        premiumFrequencys[product_premium_frequencies.length] = "premiumFrequencyOTHER";
        PrintCommon.setPrintData(printObjectList, premiumFrequencys, "premiumFrequency" + premiumFrequency);
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        AttachmentTermEnum.PAYMENT_METHODS[] payment_methods = AttachmentTermEnum.PAYMENT_METHODS.values();
        String[] paymentModes = new String[payment_methods.length + 1];
        for (int i = 0; i < payment_methods.length; i++) {
            paymentModes[i] = "paymentMode" + payment_methods[i].name();
        }
        paymentModes[payment_methods.length] = "paymentModeOTHER";
        PrintCommon.setPrintData(printObjectList, paymentModes, "paymentMode" + paymentMode);
        //投保申请日期
        PrintCommon.setPrintDateTime(printObjectList, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        PrintCommon.setPrintData(printObjectList, "acceptBranchName", applyPrintBo.getAcceptBranchName(), 3);
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        PrintCommon.setPrintData(printObjectList, "agentName", applyAgentBo.getAgentName(), 3);
        PrintCommon.setPrintData(printObjectList, "agentCode", applyAgentBo.getAgentCode(), 3);
        //受理时间
        PrintCommon.setPrintDateTime(printObjectList, "acceptDate", applyPrintBo.getApplyDate(), 3);

        List<ProductHealthNoticeBo> listHealthNotice = applyPrintBo.getListHealthNotice();
        for (int i = 0; i <= 15; i++) {
            String answer = null;
            String questionCode = "ZNCC_QUESTION_" + (i + 1);
            if (AssertUtils.isNotEmpty(listHealthNotice)) {
                List<ProductHealthNoticeBo> filterHealthNotice = listHealthNotice.stream().filter(productHealthNoticeBo -> questionCode.equals(productHealthNoticeBo.getQuestionCode())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(filterHealthNotice) && AssertUtils.isNotNull(filterHealthNotice.get(0))) {
                    ProductHealthNoticeBo productHealthNoticeBo = filterHealthNotice.get(0);
                    answer = productHealthNoticeBo.getAnswer();
                }
            }
            PrintCommon.setPrintData(printObjectList, new String[]{"ZNCC_QUESTION_Y_" + i, "ZNCC_QUESTION_N_" + i}, "ZNCC_QUESTION_" + answer + "_" + i);
        }

        this.setCoverageDate(printObjectList, insuredBirthday, applyPrintBo.getCreatedDate(), mult);

        PrintCommon.setInsuredHealthRemark(printObjectList, applyPrintBo.getInsuredHealthRemark());

        List<ApplySpecialContractBo> applySpecialContractList = applyPrintBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(applySpecialContractList)) {
            Optional<ApplySpecialContractBo> first = applySpecialContractList.stream().filter(applySpecialContractBo -> "OTHER".equals(applySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                ApplySpecialContractBo applySpecialContractBo = first.get();
                specialContractContent = applySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getApplySpecialContractContent(printObjectList, specialContractContent);
        return printObjectList;
    }


    /**
     * 获取保单打印数据
     *
     * @return
     */
    public List<PrintObject> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        //合同号  保单号
        PrintCommon.setPrintData(printObjectList, "policyNo", policyBo.getPolicyNo(), 3);
        //投保人信息
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", policyApplicant.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "applicantSexName", policyApplicant.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", policyApplicant.getIdNo(), 3);
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "applicantIdNoAndIdTypeName", applicantIdNoAndIdTypeName, 3);
        // TODO 与被保人什么关系 没有
        //手机号
        PrintCommon.setPrintData(printObjectList, "applicantMobile", policyApplicant.getMobile(), 3);

        //被保人信息
        Long insuredBirthday = null;
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", policyInsuredBo.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", policyInsuredBo.getSexName(), 3);
        //投保人 出生年月日
        insuredBirthday = policyInsuredBo.getBirthday();
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "insuredIdNoAndIdTypeName", insuredIdNoAndIdTypeName, 3);
        //与投保人什么关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", policyInsuredBo.getRelationshipName(), 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "insuredMobile", policyInsuredBo.getMobile(), 3);

        //受益人
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        if(AssertUtils.isNotEmpty(listPolicyBeneficiary)){
            listPolicyBeneficiary.removeIf(policyBeneficiaryInfoBo -> !ORDER_ONE.name().equals(policyBeneficiaryInfoBo.getBeneficiaryNoOrder()));
        }
        for (int i = 0; i < 3; i++) {
            PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = null;
            PolicyBeneficiaryBo policyBeneficiary = null;
            if (AssertUtils.isNotEmpty(listPolicyBeneficiary) && i < listPolicyBeneficiary.size() && AssertUtils.isNotNull(listPolicyBeneficiary.get(i))) {
                policyBeneficiaryInfoBo = listPolicyBeneficiary.get(i);
                if (AssertUtils.isNotNull(policyBeneficiaryInfoBo) && AssertUtils.isNotNull(policyBeneficiaryInfoBo.getPolicyBeneficiary())) {
                    policyBeneficiary = policyBeneficiaryInfoBo.getPolicyBeneficiary();
                }
            }
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            if (!AssertUtils.isNotNull(policyBeneficiaryInfoBo)) {
                policyBeneficiaryInfoBo = new PolicyBeneficiaryInfoBo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryName" + i, policyBeneficiary.getName(), 3);
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdNo" + i, beneficiaryIdNo, 3);
            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                beneficiaryProportion = policyBeneficiaryInfoBo.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryProportion" + i, beneficiaryProportion, 2);
        }
        /**********************************保险***************************************/
        String mult = null;
        List<ProductCashValueBo> policyCashValuesMain = null;
        PolicyCoveragePremiumBo policyCoveragePremium = null;
        //获取保险期限
        PolicyCoverageBo policyCoverageMainBo = new PolicyCoverageBo();
        if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyCoverage())) {
            List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
            Optional<PolicyCoverageBo> optionalCoverageMainBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst();
            if (optionalCoverageMainBo.isPresent()) {
                policyCoverageMainBo = optionalCoverageMainBo.get();
                mult = policyCoverageMainBo.getMult();
                String productId = policyCoverageMainBo.getProductId();
                List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();
                if (AssertUtils.isNotEmpty(policyCashValues)) {
                    policyCashValuesMain = policyCashValues.stream().filter(productCashValueBo -> productId.equals(productCashValueBo.getProductId())).collect(Collectors.toList());
                }
                policyCoveragePremium = policyCoverageMainBo.getPolicyCoveragePremium();
                if (!AssertUtils.isNotNull(policyCoveragePremium)) {
                    policyCoveragePremium = new PolicyCoveragePremiumBo();
                }

            }
        }
        PrintCommon.setPrintData(printObjectList, "mult", mult, 3);

        //缴费年期
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", policyCoveragePremium.getPremiumFrequencyName(), 3);
        String premiumFrequency = policyCoveragePremium.getPremiumFrequency();
        PrintCommon.setPremiumFrequencyName(premiumFrequency, printObjectList);

        Long gainedDate = null;
        //TODO 重大基本保险金额,小学教育金,中学教育金,大学教育金
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        PrintCommon.setPrintData(printObjectList, "periodTotalPremium", policyPremium.getActualPremium(), 3);
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        //支付方式
        PrintCommon.setPrintData(printObjectList, "payModeCodeName", policyPayment.getPayModeCodeName(), 3);
        //收费时间 就是生效时间 TODO 1010
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom", policyPayment.getGainedDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "gainedDate", policyPayment.getGainedDate(), 3);
        gainedDate = policyPayment.getGainedDate();

        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        //代理人编码
        PrintCommon.setPrintData(printObjectList, "agentCode", policyAgent.getAgentCode(), 3);
        PrintCommon.setPrintData(printObjectList, "agentName", policyAgent.getAgentName(), 3);
        //获取 保险利益数据
        ProductCalculation.policyCashValues(printObjectList, policyCashValuesMain);

        //签发日期
        PrintCommon.setPrintDateTime(printObjectList, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "approveDate", policyBo.getApproveDate(), 3);

        this.setCoverageDate(printObjectList, insuredBirthday, gainedDate, mult);

        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }
        PrintCommon.getPolicySpecialContractContent(printObjectList, specialContractContent);

        return printObjectList;
    }

    /**
     * 3号产品 现金价值计算
     *
     * @param insuredBirthday 被保人出生日期
     * @param policyEffective 保单生效日期
     * @throws Exception
     */
    public void setCoverageDate(List<PrintObject> printObjectList, Long insuredBirthday, Long policyEffective, String multString) throws Exception {
        //计算保单截止日期
        Long policyEffectiveDateEnd = null;
        Long hmStartTime = null;
        Long hmEndTime = null;

        Long zdStartTime = null;
        Long zdEndTime = null;
        Long zdMult = null;

        Long xxStartTime = null;
        Long xxEndTime = null;
        BigDecimal xxInsuranceAmount = null;
        Long xxStartAge = null;
        BigDecimal totalXxInsuranceAmount = null;
        BigDecimal xxAmount = null;
        Long xxMult = null;
        Long xxYears = null;

        Long receiveAge = null;

        Long zxStartTime = null;
        Long zxEndTime = null;
        BigDecimal zxInsuranceAmount = null;
        Long zxStartAge = null;
        BigDecimal totalZxInsuranceAmount = null;
        BigDecimal zxAmount = null;
        Long zxMult = null;
        Long zxYears = null;

        Long dxStartTime = null;
        Long dxEndTime = null;
        BigDecimal dxInsuranceAmount = null;
        Long dxStartAge = null;
        BigDecimal totalDxInsuranceAmount = null;
        BigDecimal dxAmount = null;
        Long dxMult = null;
        Long dxYears = null;

        BigDecimal zJInsuranceAmount = null;
        BigDecimal totalZJInsuranceAmount = null;

        if (AssertUtils.isNotNull(insuredBirthday) && AssertUtils.isNotNull(policyEffective) && AssertUtils.isNotNull(multString)) {
            long mult = Long.parseLong(multString);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");//时间格式化类

            //被保人出生年月
            Date date = StringUtil.getDateAll(insuredBirthday + "");//解析到一个时间
            String dateStr = sdf.format(date);
            Date insuredBirthdayDate = sdf.parse(dateStr);
            //转换保单生效日
            date = StringUtil.getDateAll(policyEffective + "");//解析到一个时间
            dateStr = sdf.format(date);
            Date policyEffectiveDate = sdf.parse(dateStr);// StringUtil.getDateAll(policyEffective + "");


            int age = DateUtils.getAgeYear(insuredBirthdayDate, policyEffectiveDate);
            //计算保单截止日期
            policyEffectiveDateEnd = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 22 - age);
            //被保人出生日期
            System.out.println("被保人出生日期:" + insuredBirthday);
            //保单生效日期
            System.out.println("保单生效日期:" + policyEffectiveDate.getTime());
            //保单截止日
            System.out.println("保单截止日期:" + policyEffectiveDateEnd);

            //缴费期限
            int payYear = 3;
            hmStartTime = policyEffectiveDate.getTime();
            hmEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), payYear);
            System.out.println("交费豁免开始时间:" + hmStartTime);
            System.out.println("交费豁免结束时间:" + hmEndTime);

            //重大疾病保险金
            zdStartTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 3) - (1 * 24 * 60 * 60 * 1000);
            zdEndTime = policyEffectiveDateEnd;

            System.out.println("重大疾病开始时间:" + zdStartTime);
            System.out.println("重大疾病结束时间:" + zdEndTime);

            //重大疾病保险金
            totalZJInsuranceAmount = new BigDecimal(mult * 7000).setScale(2, BigDecimal.ROUND_HALF_UP);
            System.out.println("重大疾病保险金计算公式:" + totalZJInsuranceAmount + "美元×" + mult + "份");
            System.out.println("重大疾病保险金:" + totalZJInsuranceAmount);
            zJInsuranceAmount = new BigDecimal(7000).setScale(2, BigDecimal.ROUND_HALF_UP);
            zdMult = mult;
            //教育金算法
            Date policyEffectiveDateCalCul = DateUtils.addYears(policyEffectiveDate, 1);
            //获取计算岁数
            age = DateUtils.getAgeYear(insuredBirthdayDate, policyEffectiveDateCalCul);
            System.out.println("领取时间戳:" + policyEffectiveDateCalCul.getTime());
            System.out.println("生日时间戳:" + insuredBirthdayDate.getTime());
            System.out.println("领取时年龄:" + age);
            //小学教育金
            if (age < 13) {
                //计算开始时间
                xxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 7 ? age : 7) - age);
                //计算结束时间
                xxEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 13 - age - 1);
                //开始领取岁数
                long startAge = (age > 7 ? age : 7);
                //总计领取年限
                long years = (13 - (age > 7 ? age : 7));
                //每年领取金额
                long insuranceAmount = 300 * mult;
                //总领取金额
                long totalInsuranceAmount = insuranceAmount * years;


                System.out.println("小学教育金开始时间:" + xxStartTime);
                System.out.println("小学教育金结束时间:" + xxEndTime);
                System.out.println("开始领取岁数:" + startAge);
                System.out.println("小学教育金(美元):" + insuranceAmount + "/年,总领取:" + years + "年");
                System.out.println("小学教育金计算公式:" + 300 + "美元/每年×" + mult + "份×" + years + "年");
                System.out.println("总领取教育金(美元):" + totalInsuranceAmount);
                xxInsuranceAmount = new BigDecimal(insuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                xxStartAge = startAge;
                totalXxInsuranceAmount = new BigDecimal(totalInsuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                xxAmount = new BigDecimal(300).setScale(2, BigDecimal.ROUND_HALF_UP);
                xxMult = mult;
                xxYears = years;
                //添加领取年龄
                receiveAge = startAge;
            }
            //中学教育金
            if (age < 19) {
                //计算开始时间
                zxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 13 ? age : 13) - age);
                //计算结束时间
                zxEndTime = DateUtils.addStringYearsRT(policyEffectiveDate.getTime(), 19 - age - 1);
                //开始领取岁数
                long startAge = (age > 13 ? age : 13);
                //总计领取年限
                long years = (19 - (age > 13 ? age : 13));
                //每年领取金额
                long insuranceAmount = 400 * mult;
                //总领取金额
                long totalInsuranceAmount = insuranceAmount * years;

                System.out.println("中学教育金开始时间:" + zxStartTime);
                System.out.println("中学教育金结束时间:" + zxEndTime);
                System.out.println("开始领取岁数:" + startAge);
                System.out.println("中学教育金(美元):" + insuranceAmount + "/年,总领取:" + years + "年");
                System.out.println("中学教育金计算公式:" + 400 + "美元/每年×" + mult + "份×" + years + "年");
                System.out.println("总领取教育金(美元):" + totalInsuranceAmount);
                zxStartTime = zxStartTime;
                zxEndTime = zxEndTime;
                zxInsuranceAmount = new BigDecimal(insuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                zxStartAge = startAge;
                totalZxInsuranceAmount = new BigDecimal(totalInsuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                zxAmount = new BigDecimal(400).setScale(2, BigDecimal.ROUND_HALF_UP);
                zxMult = mult;
                zxYears = years;
                if (!AssertUtils.isNotNull(receiveAge)) {
                    receiveAge = startAge;
                }
            }

            //高中教育金
            if (age <= 22) {
                //计算开始时间
                dxStartTime = DateUtils.addStringYearsRT(policyEffectiveDateCalCul.getTime(), (age > 19 ? age : 19) - age);
                //计算结束时间
                dxEndTime = policyEffectiveDateEnd;
                //开始领取岁数
                long startAge = (age > 19 ? age : 19);
                //总计领取年限
                long years = (23 - (age > 19 ? age : 19));
                //每年领取金额
                long insuranceAmount = 500 * mult;
                //总领取金额
                long totalInsuranceAmount = insuranceAmount * years;

                System.out.println("大学教育金开始时间:" + dxStartTime);
                System.out.println("大学教育金结束时间:" + dxEndTime);
                System.out.println("开始领取岁数:" + startAge);
                System.out.println("大学教育金(美元):" + insuranceAmount + "/年,总领取:" + years + "年");
                System.out.println("大学中学教育金计算公式:" + 500 + "美元/每年×" + mult + "份×" + years + "年");
                System.out.println("总领取教育金(美元):" + totalInsuranceAmount);
                dxStartTime = dxStartTime;
                dxEndTime = dxEndTime;
                dxInsuranceAmount = new BigDecimal(insuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                dxStartAge = startAge;
                totalDxInsuranceAmount = new BigDecimal(totalInsuranceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);

                dxAmount = new BigDecimal(500).setScale(2, BigDecimal.ROUND_HALF_UP);
                dxMult = mult;
                dxYears = years;
                if (!AssertUtils.isNotNull(receiveAge)) {
                    receiveAge = startAge;
                }
            }
        }

        PrintCommon.setPrintData(printObjectList, "totalZJInsuranceAmount", totalZJInsuranceAmount, 1);
        PrintCommon.setPrintData(printObjectList, "zJInsuranceAmount", zJInsuranceAmount, 1);

        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil", policyEffectiveDateEnd, 3);

        PrintCommon.setPrintDateTime(printObjectList, "hmStartTime", hmStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "hmEndTime", hmEndTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zdStartTime", zdStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zdEndTime", zdEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "zdMult", zdMult, 3);
        String zdDescriptionZH_CN = null;
        String zdDescriptionEN_US = null;
        String zdDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(zJInsuranceAmount) && AssertUtils.isNotNull(zdMult)) {
            zdDescriptionZH_CN = zJInsuranceAmount + "美元×" + zdMult + "份";
            zdDescriptionEN_US = zJInsuranceAmount + "USD×" + zdMult + "Policies";
            zdDescriptionKM_KH = zJInsuranceAmount + "ដុល្លារសហរដ្ឋអាមេរិក × " + zdMult + "ច្បាប់";
        }
        PrintCommon.setPrintData(printObjectList, "zdDescriptionZH_CN", zdDescriptionZH_CN, 1);
        PrintCommon.setPrintData(printObjectList, "zdDescriptionEN_US", zdDescriptionEN_US, 1);
        PrintCommon.setPrintData(printObjectList, "zdDescriptionKM_KH", zdDescriptionKM_KH, 1);


        PrintCommon.setPrintDateTime(printObjectList, "xxStartTime", xxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "xxEndTime", xxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "xxInsuranceAmount", xxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxStartAge", xxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalXxInsuranceAmount", totalXxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxAmount", xxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "xxMult", xxMult, 3);
        PrintCommon.setPrintData(printObjectList, "xxYears", xxYears, 3);
        String xxDescriptionZH_CN = null;
        String xxDescriptionEN_US = null;
        String xxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(xxAmount) && AssertUtils.isNotNull(xxMult) && AssertUtils.isNotNull(xxYears)) {
            xxDescriptionZH_CN = xxAmount + "美元/每年×" + xxMult + "份×" + xxYears + "年";
            xxDescriptionEN_US = xxAmount + "美元/每年×" + xxMult + "份×" + xxYears + "年";
            xxDescriptionKM_KH = xxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + xxMult + "ច្បាប់×" + xxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "xxDescriptionZH_CN", xxDescriptionZH_CN, 1);
        PrintCommon.setPrintData(printObjectList, "xxDescriptionEN_US", xxDescriptionEN_US, 1);
        PrintCommon.setPrintData(printObjectList, "xxDescriptionKM_KH", xxDescriptionKM_KH, 1);


        PrintCommon.setPrintDateTime(printObjectList, "zxStartTime", zxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "zxEndTime", zxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "zxInsuranceAmount", zxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxStartAge", zxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalZxInsuranceAmount", totalZxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxAmount", zxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "zxMult", zxMult, 3);
        PrintCommon.setPrintData(printObjectList, "zxYears", zxYears, 3);
        String zxDescriptionZH_CN = null;
        String zxDescriptionEN_US = null;
        String zxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(zxAmount) && AssertUtils.isNotNull(zxMult) && AssertUtils.isNotNull(zxYears)) {
            zxDescriptionZH_CN = zxAmount + "美元/每年×" + zxMult + "份×" + zxYears + "年";
            zxDescriptionEN_US = zxAmount + "美元/每年×" + zxMult + "份×" + zxYears + "年";
            zxDescriptionKM_KH = zxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + zxMult + "ច្បាប់×" + zxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "zxDescriptionZH_CN", zxDescriptionZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "zxDescriptionEN_US", zxDescriptionEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "zxDescriptionKM_KH", zxDescriptionKM_KH, 3);

        PrintCommon.setPrintDateTime(printObjectList, "dxStartTime", dxStartTime, 3);
        PrintCommon.setPrintDateTime(printObjectList, "dxEndTime", dxEndTime, 3);
        PrintCommon.setPrintData(printObjectList, "dxInsuranceAmount", dxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxStartAge", dxStartAge, 1);
        PrintCommon.setPrintData(printObjectList, "totalDxInsuranceAmount", totalDxInsuranceAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxAmount", dxAmount, 3);
        PrintCommon.setPrintData(printObjectList, "dxMult", dxMult, 3);
        PrintCommon.setPrintData(printObjectList, "dxYears", dxYears, 3);
        String dxDescriptionZH_CN = null;
        String dxDescriptionEN_US = null;
        String dxDescriptionKM_KH = null;
        if (AssertUtils.isNotNull(dxAmount) && AssertUtils.isNotNull(dxMult) && AssertUtils.isNotNull(dxYears)) {
            dxDescriptionZH_CN = dxAmount + "美元/每年×" + dxMult + "份×" + dxYears + "年";
            dxDescriptionEN_US = dxAmount + "美元/每年×" + dxMult + "份×" + dxYears + "年";
            dxDescriptionKM_KH = dxAmount + "ដុល្លារសហរដ្ឋអាមេរិក/ឆ្នាំ × " + dxMult + "ច្បាប់×" + dxYears + "ឆ្នាំ";
        }
        PrintCommon.setPrintData(printObjectList, "dxDescriptionZH_CN", dxDescriptionZH_CN, 1);
        PrintCommon.setPrintData(printObjectList, "dxDescriptionEN_US", dxDescriptionEN_US, 1);
        PrintCommon.setPrintData(printObjectList, "dxDescriptionKM_KH", dxDescriptionKM_KH, 1);


        PrintCommon.setPrintData(printObjectList, "receiveAge", receiveAge, 3);
    }

}
