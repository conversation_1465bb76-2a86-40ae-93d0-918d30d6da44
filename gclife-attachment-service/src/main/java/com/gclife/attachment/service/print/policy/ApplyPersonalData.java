package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR;
import static com.gclife.attachment.model.config.AttachmentTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER;
import static com.gclife.common.InternationalTypeEnum.BANK;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;

/**
 * <AUTHOR>
 * @date 2021/11/26
 */
@Component
public class ApplyPersonalData extends BaseBusinessServiceImpl {
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private AttachmentExtDao attachmentExtDao;

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Long backTrackDate = applyPrintBo.getApplyDate();
        if (AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())) {
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("prohibitedString2", PrintCommon.getPrintString(null, 2));
        map.put("prohibitedString3", PrintCommon.getPrintString(null, 3));
        map.put("prohibitedString4", PrintCommon.getPrintString(null, 4));
        map.put("prohibitedString5", PrintCommon.getPrintString(null, 5));
        map.put("prohibitedString6", PrintCommon.getPrintString(null, 6));
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));
        /*****************************************投保人********************************************************************/
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        long applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        String applicantExpectedPremiumSources = applicant.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "applicantEPS", applicantExpectedPremiumSources);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        map.put("applicantIncome", PrintCommon.getPrintString(applicant.getIncome(), 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //移动电话
        map.put("applicantMobile_2", PrintCommon.getPrintString(applicant.getMobile_2(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //通讯地址
        String applicantCompanyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String applicantCompanyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("applicantCompanyAddressWhole", PrintCommon.getPrintString(applicantCompanyAreaName + applicantCompanyAddress, 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        map.put("applicantFacebookNo", PrintCommon.getPrintString(applicant.getFacebookNo(), 3));
        map.put("applicantStature", PrintCommon.getPrintString(applicant.getStature(), 3));
        map.put("applicantAvoirdupois", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("applicantExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(applicant.getExpectedPremiumSourcesSpecific(), 3));
        map.put("applicantDoctorName", PrintCommon.getPrintString(applicant.getDoctorName(), 3));
        String applicantDoctorAreaCodeName = AssertUtils.isNotEmpty(applicant.getDoctorAreaCodeName()) ? applicant.getDoctorAreaCodeName() : "";
        String applicantDoctorAddress = AssertUtils.isNotEmpty(applicant.getDoctorAddress()) ? applicant.getDoctorAddress() : "";
        map.put("applicantDoctorAreaCodeName", PrintCommon.getPrintString(applicantDoctorAreaCodeName + applicantDoctorAddress, 3));
        PrintCommon.setSelectionBox(map, "applicantAddressType" + applicant.getAddressType(), applicant.getAddressType());
        /******************************************************被保人********************************************************************/
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }
        map.put("relationshipName", insured.getRelationshipName());

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        String insuredExpectedPremiumSources = insured.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "insuredEPS", insuredExpectedPremiumSources);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(insured.getIncome(), 2));
        long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //移动电话
        map.put("insuredMobile_2", PrintCommon.getPrintString(insured.getMobile_2(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //通讯地址
        String insuredCompanyAreaName = AssertUtils.isNotEmpty(insured.getCompanyAreaName()) ? insured.getCompanyAreaName() : "";
        String insuredCompanyAddress = AssertUtils.isNotEmpty(insured.getCompanyAddress()) ? insured.getCompanyAddress() : "";
        map.put("insuredCompanyAddressWhole", PrintCommon.getPrintString(insuredCompanyAreaName + insuredCompanyAddress, 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        map.put("insuredFacebookNo", PrintCommon.getPrintString(insured.getFacebookNo(), 3));
        map.put("insuredStature", PrintCommon.getPrintString(insured.getStature(), 3));
        map.put("insuredAvoirdupois", PrintCommon.getPrintString(insured.getAvoirdupois(), 3));
        map.put("insuredExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(insured.getExpectedPremiumSourcesSpecific(), 3));
        map.put("insuredDoctorName", PrintCommon.getPrintString(insured.getDoctorName(), 3));
        String insuredDoctorAreaCodeName = AssertUtils.isNotEmpty(insured.getDoctorAreaCodeName()) ? insured.getDoctorAreaCodeName() : "";
        String insuredDoctorAddress = AssertUtils.isNotEmpty(insured.getDoctorAddress()) ? insured.getDoctorAddress() : "";
        map.put("insuredDoctorAreaCodeName", PrintCommon.getPrintString(insuredDoctorAreaCodeName + insuredDoctorAddress, 3));
        map.put("taxpayerNo", PrintCommon.getPrintString(insured.getTaxpayerNo(), 3));
        PrintCommon.setSelectionBox(map, "insuredAddressType" + insured.getAddressType(), insured.getAddressType());
        /******************************************************受益人信息***************************************************************/
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //证件类型
                beneficiaryMap.put("homeAddress", PrintCommon.getPrintString(applyBeneficiaryBo.getHomeAddress(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });
            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        /**********************************************************险种信息**************************************************************/
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel(), language);
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000009".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                if ("PRO880000000000014".equals(applyCoverageBo.getProductId())) {
                    if ("ACCELERATION_CI".equals(applyCoverageBo.getProductLevel())) {
                        coverageMap.put("productLevelZH_CN", "(提前给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                        coverageMap.put("productLevelEN_US", "(Acceleration)");
                    } else {
                        coverageMap.put("productLevelZH_CN", "(额外给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                        coverageMap.put("productLevelEN_US", "(Additional)");
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }
        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));
        /*****************************************交费*******************************************************************/
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        // 20号产品趸缴的特殊处理。20号产品是一年期的年缴则视为趸缴
        ApplyCoverageBo mainApplyCoverageBo = listCoverage.stream().filter(applyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverage.getPrimaryFlag())).findFirst().get();
        String mainPremiumFrequency = mainApplyCoverageBo.getPremiumFrequency();
        int premiumPeriodNum = Integer.parseInt(AssertUtils.isNotEmpty(mainApplyCoverageBo.getPremiumPeriod()) ? mainApplyCoverageBo.getPremiumPeriod() : "0");
        if (SINGLE.name().equals(mainPremiumFrequency) || (YEAR.name().equals(mainPremiumFrequency) && 1 == premiumPeriodNum)) {
            premiumFrequency = SINGLE.name();
        }
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        // 缴费周期
        String premiumFrequencyName = applyPrintBo.getPremiumFrequencyName();
        if (AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性缴清";
            }
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Premium";
            }
        }
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);
        // 支付方式
        String paymentModeName = applyPrintBo.getPaymentModeName();
        map.put("paymentModeName", PrintCommon.getPrintString(paymentModeName, 2));
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOtherInsuranceBo> listApplyOtherInsurancePo = applyPrintBo.getOtherInsurance();
        List<Map<String, Object>> applyOtherInsuranceListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyOtherInsurancePo)) {
            for (ApplyOtherInsuranceBo applyOtherInsuranceBo : listApplyOtherInsurancePo) {
                Map<String, Object> applyOtherInsuranceMap = new HashMap<>();
                applyOtherInsuranceMap.put("otherInsuringInsuredName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuredName(), 3));
                applyOtherInsuranceMap.put("otherInsuringCompany", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuringCompany(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceTypeName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceTypeName(), 3));
                applyOtherInsuranceMap.put("otherInsuringAmount", PrintCommon.getPrintString(applyOtherInsuranceBo.getAmount(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceYear", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceYear(), 3));
                applyOtherInsuranceListMap.add(applyOtherInsuranceMap);
            }
        }
        map.put("applyOtherInsuranceListMap", applyOtherInsuranceListMap);
        /********************************************其他投保的保险*********************************************************/
        List<ApplyOccupationNatureBo> occupationNatureList = applyPrintBo.getOccupationNature();
        if (!AssertUtils.isNotEmpty(occupationNatureList)) {
            occupationNatureList = new ArrayList<>();
        }
        ApplyOccupationNatureBo applicantOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> applicantOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "APPLICANT".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (applicantOptionalOccupationNatureBo.isPresent()) {
            applicantOccupationNatureBo = applicantOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "applicantON" + applicantOccupationNatureBo.getOccupationNature(), applicantOccupationNatureBo.getOccupationNature());
        map.put("applicantOccupationNatureSpecific", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("applicantEmployerName", PrintCommon.getPrintString(applicantOccupationNatureBo.getEmployerName(), 3));
        map.put("applicantBusinessNature", PrintCommon.getPrintString(applicantOccupationNatureBo.getBusinessNature(), 3));
        map.put("applicantOccupationExactDuties", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupation(), 3));
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        map.put("applicantOccupationClass", PrintCommon.getPrintString(applicantOccupationNatureBo.getExactDuties(), 3));

        ApplyOccupationNatureBo insuredOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> insuredOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "INSURED".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (insuredOptionalOccupationNatureBo.isPresent()) {
            insuredOccupationNatureBo = insuredOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "insuredON" + insuredOccupationNatureBo.getOccupationNature(), insuredOccupationNatureBo.getOccupationNature());
        map.put("insuredOccupationNatureSpecific", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("insuredEmployerName", PrintCommon.getPrintString(insuredOccupationNatureBo.getEmployerName(), 3));
        map.put("insuredBusinessNature", PrintCommon.getPrintString(insuredOccupationNatureBo.getBusinessNature(), 3));
        map.put("insuredOccupationExactDuties", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupation(), 3));
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        map.put("insuredOccupationClass", PrintCommon.getPrintString(insuredOccupationNatureBo.getExactDuties(), 3));
        /********************************************保单持有人*********************************************************/
        ApplyHolderBo applyHolderBo = applyPrintBo.getHolder();
        String applyHolderBoName = null;
        String applyHolderBoIdNo = null;
        String applyHolderBoSexName = null;
        String applyHolderBoRelationshipName = null;
        Long applyHolderBoIdExpDate = null;
        Long applyHolderBoBirthday = null;
        if (AssertUtils.isNotNull(applyHolderBo)) {
            applyHolderBoName = applyHolderBo.getName();
            applyHolderBoIdNo = applyHolderBo.getIdNo();
            applyHolderBoSexName = applyHolderBo.getSexName();
            applyHolderBoRelationshipName = applyHolderBo.getRelationshipName();
            applyHolderBoIdExpDate = applyHolderBo.getIdExpDate();
            applyHolderBoBirthday = applyHolderBo.getBirthday();
        }
        map.put("applyHolderName", PrintCommon.getPrintString(applyHolderBoName, 3));
        map.put("applyHolderIdNo", PrintCommon.getPrintString(applyHolderBoIdNo, 3));
        PrintCommon.setPrintDateTime(map, "applyHolderIdExpDate", applyHolderBoIdExpDate, 3);
        map.put("applyHolderSexName", PrintCommon.getPrintString(applyHolderBoSexName, 3));
        map.put("applyHolderRelationshipName", PrintCommon.getPrintString(applyHolderBoRelationshipName, 3));
        PrintCommon.setPrintDateTime(map, "applyHolderBirthday", applyHolderBoBirthday, 3);
        /********************************************账户*********************************************************/
        List<ApplyAccountBo> listApplyAccount = applyPrintBo.getListApplyAccount();
        ApplyAccountBo applyAccountBo = new ApplyAccountBo();
        String kmKmBankName = null;
        String bankName = null;
        if (AssertUtils.isNotEmpty(listApplyAccount)) {
            applyAccountBo = listApplyAccount.get(0);
            if (AssertUtils.isNotEmpty(applyAccountBo.getBankCode())) {
                SyscodeResponse kmKmBankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name()).getData();
                SyscodeResponse bankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name().equals(language) ? EN_US.name() : language).getData();
                kmKmBankName = AssertUtils.isNotNull(kmKmBankSyscode) && AssertUtils.isNotEmpty(kmKmBankSyscode.getCodeName()) ? kmKmBankSyscode.getCodeName() : null;
                bankName = AssertUtils.isNotNull(bankSyscode) && AssertUtils.isNotEmpty(bankSyscode.getCodeName()) ? bankSyscode.getCodeName() : null;
            }
        }
        map.put("kmKmBankName", PrintCommon.getPrintString(kmKmBankName, 3));
        map.put("bankName", PrintCommon.getPrintString(bankName, 3));
        map.put("accountOwner", PrintCommon.getPrintString(applyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(applyAccountBo.getAccountNo(), 3));
        /********************************************健康告知书*********************************************************/
        ProductCalculation.setHealthRemark1(map, applyPrintBo);
        List<ApplyStatementBo> statements = applyPrintBo.getStatements();
        if (AssertUtils.isNotEmpty(statements)) {
            statements.forEach(applyStatementBo -> {
                map.put(applyStatementBo.getStatementCode(), applyStatementBo.getStatementValue());
            });
        }
        /********************************************投保申请日期*********************************************************/
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyAgentBo.getAgentMobile(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        /********************************************电子签名*********************************************************/
        ElectronicSignatureAttachmentBo electronicSignatureAttachmentBo = applyPrintBo.getElectronicSignatureAttachmentBo();
        if (AssertUtils.isNotNull(electronicSignatureAttachmentBo)) {
            String agentSignatureAttachmentId = electronicSignatureAttachmentBo.getAgentSignatureAttachmentId();
            String applicantSignatureAttachmentId = electronicSignatureAttachmentBo.getApplicantSignatureAttachmentId();
            String insuredSignatureAttachmentId = electronicSignatureAttachmentBo.getInsuredSignatureAttachmentId();
            List<String> signatureAttachmentIds = Arrays.asList(agentSignatureAttachmentId, applicantSignatureAttachmentId, insuredSignatureAttachmentId);
            List<AttachmentPo> attachmentPos = attachmentExtDao.listAttachmentPoByPk(signatureAttachmentIds);
            if (AssertUtils.isNotEmpty(attachmentPos)) {
                for (AttachmentPo attachmentPo : attachmentPos) {
                    // 动态域名 根据不同环境获取不同的OSS域名
                    String dynamicDomain = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.");
                    // 签名附件
                    String attachmentId = attachmentPo.getAttachmentId();
                    // 签名日期
                    Long createdDate = attachmentPo.getCreatedDate();
                    // 业务员签名
                    if (AssertUtils.isNotEmpty(agentSignatureAttachmentId) && agentSignatureAttachmentId.equals(attachmentId)) {
                        map.put("agentSignature", dynamicDomain + attachmentPo.getUrl());
                        PrintCommon.setPrintDateTime(map, "agentSignatureDate", createdDate, 3);
                    }
                    // 投保人签名
                    if (AssertUtils.isNotEmpty(applicantSignatureAttachmentId) && applicantSignatureAttachmentId.equals(attachmentId)) {
                        map.put("applicantSignature", dynamicDomain + attachmentPo.getUrl());
                        PrintCommon.setPrintDateTime(map, "applicantSignatureDate", createdDate, 3);
                    }
                    // 被保人签名
                    if (AssertUtils.isNotEmpty(insuredSignatureAttachmentId) && insuredSignatureAttachmentId.equals(attachmentId)) {
                        map.put("insuredSignature", dynamicDomain + attachmentPo.getUrl());
                        PrintCommon.setPrintDateTime(map, "insuredSignatureDate", createdDate, 3);
                    }
                }
            }
        }
        return map;
    }
}
