package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.apply.ApplyCoverageBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.policy.apply.group.*;
import com.gclife.attachment.model.policy.policy.PolicyBeneficiaryBo;
import com.gclife.attachment.model.policy.policy.PolicyBeneficiaryInfoBo;
import com.gclife.attachment.model.policy.policy.group.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.common.PrintCommon.sdfEN_US29;

/**
 * <AUTHOR>
 * @date 2023/08/08
 */
@Component
public class GCGroupStudentCareData extends BaseBusinessServiceImpl {
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;

    /**
     * 获取投保单打印数据
     *
     * @return
     */
    public Map<String, Object> getApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();

        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachApplyBo applyPrintBo = JSON.parseObject(content, GroupAttachApplyBo.class);
        GroupAttachApplyApplicantBo applicant = applyPrintBo.getGroupApplicant();
        GroupAttachApplyInsuredCollectBo applyInsuredCollect = applyPrintBo.getApplyInsuredCollect();

        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));

        for (GroupAttachApplyCoverageBo coverageBo : applyPrintBo.getListGroupCoverage()) {
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
        }

        // 代理人
        GroupAttachApplyAgentBo groupAgent = applyPrintBo.getGroupAgent();
        // 代理人姓名
        map.put("agentName", PrintCommon.getPrintString(groupAgent.getAgentName(), 3));
        // 代理人代码
        map.put("agentCode", PrintCommon.getPrintString(groupAgent.getAgentCode(), 3));
        // 代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(groupAgent.getMobile(), 3));

        //学校信息
        map.put("companyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        map.put("companyIdNo", PrintCommon.getPrintString(applicant.getCompanyIdNo(), 3));
        map.put("companyAddress", PrintCommon.getPrintString(applicant.getCompanyAddressWhole(), 3));
        if (AssertUtils.isNotEmpty(applicant.getSalesPlan())) {
            if ("COMPULSORY".equals(applicant.getSalesPlan())) {
                map.put("salesPlanCompulsory", PrintCommon.getPrintString(applicant.getSalesPlan(), 0));
            }
            if ("NON_COMPULSORY".equals(applicant.getSalesPlan())) {
                map.put("salesPlanNonCompulsory", PrintCommon.getPrintString(applicant.getSalesPlan(), 0));
            }
        }
        //学校类型
        if (AssertUtils.isNotEmpty(applicant.getSchoolProperties())) {
            if ("PRIVATE".equals(applicant.getSchoolProperties())) {
                map.put("schoolPropertiesPrivate", PrintCommon.getPrintString(applicant.getSchoolProperties(), 0));
            }
            if ("PUBLIC".equals(applicant.getSchoolProperties())) {
                map.put("schoolPropertiesPublic", PrintCommon.getPrintString(applicant.getSchoolProperties(), 0));
            }
        }

        //展示所有的支付方式
        if (AssertUtils.isNotEmpty(applyPrintBo.getPaymentMode())) {
            if ("CASH".equals(applyPrintBo.getPaymentMode())) {
                map.put("cashFlag", PrintCommon.getPrintString("CASH", 0));
            }
            if ("CHEQUE".equals(applyPrintBo.getPaymentMode())) {
                map.put("chequeFlag", PrintCommon.getPrintString("CHEQUE", 0));
            }
            if ("BANK_TRANSFER".equals(applyPrintBo.getPaymentMode())) {
                String bankCode = applyPrintBo.getBankCode();
                if (AssertUtils.isNotEmpty(bankCode)) {
                    if ("ABA BANK".equals(bankCode)) {
                        map.put("bankTransferFlagAba", PrintCommon.getPrintString("ABA BANK", 0));
                    }
                    if ("ACLEDA BANK".equals(bankCode)) {
                        map.put("bankTransferFlagAcleda", PrintCommon.getPrintString("ACLEDA BANK", 0));
                    }
                    if ("CBC".equals(bankCode)) {
                        map.put("bankTransferFlagChief", PrintCommon.getPrintString("CBC", 0));
                    }
                    if ("ICBC".equals(bankCode)) {
                        map.put("bankTransferFlagIcbc", PrintCommon.getPrintString("ICBC", 0));
                    }
                    if ("CANADIA BANK".equals(bankCode)) {
                        map.put("bankTransferFlagCb", PrintCommon.getPrintString("CANADIA BANK", 0));
                    }
                }
            }
            if ("ABA_PAYMENTS".equals(applyPrintBo.getPaymentMode())) {
                map.put("onlineFlagAba", PrintCommon.getPrintString("ABA_PAYMENTS", 0));
                map.put("onlineFlag", PrintCommon.getPrintString("ABA_PAYMENTS", 0));
            }
            if ("ACLEDA_PAYMENT".equals(applyPrintBo.getPaymentMode())) {
                map.put("onlineFlagAcleda", PrintCommon.getPrintString("ACLEDA_PAYMENT", 0));
                map.put("onlineFlag", PrintCommon.getPrintString("ACLEDA_PAYMENT", 0));
            }
            if ("ASIA_WEILUY".equals(applyPrintBo.getPaymentMode())) {
                map.put("onlineFlagAsia", PrintCommon.getPrintString("ASIA_WEILUY", 0));
                map.put("onlineFlag", PrintCommon.getPrintString("ASIA_WEILUY", 0));
            }
            if ("WING_OFFLINE".equals(applyPrintBo.getPaymentMode())) {
                map.put("onlineFlagWing", PrintCommon.getPrintString("WING_OFFLINE", 0));
                map.put("onlineFlag", PrintCommon.getPrintString("WING_OFFLINE", 0));
            }
            if ("E_MONEY".equals(applyPrintBo.getPaymentMode())) {
                map.put("onlineFlagEmoney", PrintCommon.getPrintString("E_MONEY", 0));
                map.put("onlineFlag", PrintCommon.getPrintString("E_MONEY", 0));
            }
        }
        //学校级别,数量，保额
        if (0 == applyInsuredCollect.getQuantityPreSchool29()) {
            map.put("preSchoolNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchool29(), 3));
            map.put("preSchoolAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchoolAmount29(), 0));
        }else {
            map.put("schoolTypePreSchool29", PrintCommon.getPrintString("schoolTypePreSchool29", 0));
            map.put("preSchoolNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchool29(), 3));
            map.put("preSchoolAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchoolAmount29(), 0));
        }
        if (0 == applyInsuredCollect.getQuantityPrimarySchool29()) {
            map.put("primaryNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchool29(), 3));
            map.put("primaryAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchoolAmount29(), 0));
        }else {
            map.put("schoolTypePrimary29", PrintCommon.getPrintString("schoolTypePrimary29", 0));
            map.put("primaryNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchool29(), 3));
            map.put("primaryAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchoolAmount29(), 0));
        }
        if (0 == applyInsuredCollect.getQuantitySecondarySchool29()) {
            map.put("secondaryNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchool29(), 3));
            map.put("secondaryAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchoolAmount29(), 0));
        }else {
            map.put("schoolTypeSecondary29", PrintCommon.getPrintString("schoolTypeSecondary29", 0));
            map.put("secondaryNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchool29(), 3));
            map.put("secondaryAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchoolAmount29(), 0));
        }
        if (0 == applyInsuredCollect.getQuantityUniversity29()) {
            map.put("universityNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversity29(), 3));
            map.put("universityAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversityAmount29(), 0));
        }else {
            map.put("schoolTypeUniversity29", PrintCommon.getPrintString("schoolTypeUniversity29", 0));
            map.put("universityNum29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversity29(), 3));
            map.put("universityAmount29", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversityAmount29(), 0));
        }
        if (0 == applyInsuredCollect.getQuantityPreSchool33()) {
            map.put("preSchoolNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchool33(), 3));
            map.put("preSchoolAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchoolAmount33(), 0));
        }else {
            map.put("schoolTypePreSchool33", PrintCommon.getPrintString("schoolTypePreSchool33", 0));
            map.put("preSchoolNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchool33(), 3));
            map.put("preSchoolAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPreSchoolAmount33(), 0));
        }
        if (0 == applyInsuredCollect.getQuantityPrimarySchool33()) {
            map.put("primaryNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchool33(), 3));
            map.put("primaryAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchoolAmount33(), 0));
        }else {
            map.put("schoolTypePrimary33", PrintCommon.getPrintString("schoolTypePrimary33", 0));
            map.put("primaryNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchool33(), 3));
            map.put("primaryAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityPrimarySchoolAmount33(), 0));
        }
        if (0 == applyInsuredCollect.getQuantitySecondarySchool33()) {
            map.put("secondaryNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchool33(), 3));
            map.put("secondaryAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchoolAmount33(), 0));
        }else {
            map.put("schoolTypeSecondary33", PrintCommon.getPrintString("schoolTypeSecondary33", 0));
            map.put("secondaryNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchool33(), 3));
            map.put("secondaryAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantitySecondarySchoolAmount33(), 0));
        }
        if (0 == applyInsuredCollect.getQuantityUniversity33()) {
            map.put("universityNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversity33(), 3));
            map.put("universityAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversityAmount33(), 0));
        }else {
            map.put("schoolTypeUniversity33", PrintCommon.getPrintString("schoolTypeUniversity33", 0));
            map.put("universityNum33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversity33(), 3));
            map.put("universityAmount33", PrintCommon.getPrintString(applyInsuredCollect.getQuantityUniversityAmount33(), 0));
        }

        BigDecimal totalInsuredNum29 = new BigDecimal(applyInsuredCollect.getQuantityPreSchool29()).add(new BigDecimal(applyInsuredCollect.getQuantityPrimarySchool29()))
                .add(new BigDecimal(applyInsuredCollect.getQuantitySecondarySchool29())).add(new BigDecimal(applyInsuredCollect.getQuantityUniversity29()));
        BigDecimal totalInsuredNum33 = new BigDecimal(applyInsuredCollect.getQuantityPreSchool33()).add(new BigDecimal(applyInsuredCollect.getQuantityPrimarySchool33()))
                .add(new BigDecimal(applyInsuredCollect.getQuantitySecondarySchool33())).add(new BigDecimal(applyInsuredCollect.getQuantityUniversity33()));

        BigDecimal proportion29 = totalInsuredNum29.divide(new BigDecimal(applicant.getStudentsNum()), 3, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));

        map.put("totalInsuredNum29", PrintCommon.getPrintString(totalInsuredNum29, 3));
        map.put("totalInsuredNum33", PrintCommon.getPrintString(totalInsuredNum33, 3));
        map.put("proportion29", PrintCommon.getPrintString(proportion29, 3));
        map.put("studentsNum", PrintCommon.getPrintString(applicant.getStudentsNum(), 3));

        //代表信息
        map.put("delegateName", PrintCommon.getPrintString(applicant.getDelegateName(), 3));
        map.put("delegateIdNo", PrintCommon.getPrintString(applicant.getDelegateIdNo(), 3));
        map.put("delegateNationalityName", PrintCommon.getPrintString(applicant.getDelegateNationalityName(), 3));
        map.put("delegateSexName", PrintCommon.getPrintString(applicant.getDelegateSexName(), 3));
        map.put("delegatePosition", PrintCommon.getPrintString(applicant.getDelegatePosition(), 3));
        map.put("delegateMobile", PrintCommon.getPrintString(applicant.getDelegateMobile(), 3));
        map.put("delegateEmail", PrintCommon.getPrintString(applicant.getDelegateEmail(), 3));
        if (AssertUtils.isNotNull(applicant.getDelegateBirthday())) {
            String dateEN_US = sdfEN_US29.format(applicant.getDelegateBirthday());
            map.put("delegateBirthday" + "EN_US", PrintCommon.getPrintString(dateEN_US, 3));
            map.put("delegateBirthday" + "ZH_CN", PrintCommon.getPrintString(dateEN_US, 3));
        }else {
            map.put("delegateBirthday" + "EN_US", PrintCommon.getPrintString(null, 3));
            map.put("delegateBirthday" + "ZH_CN", PrintCommon.getPrintString(null, 3));
        }
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取保单打印数据
     *
     * @return
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachPolicyBo policyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);
        Map<String, Object> map = new HashMap<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalPremium = BigDecimal.ZERO;
        //29附加项 保额
        BigDecimal totalAmount29 = BigDecimal.ZERO;
        //33附加项 保额
        BigDecimal totalAmount33 = BigDecimal.ZERO;
        //29 保费
        BigDecimal totalPremium29 = BigDecimal.ZERO;
        //33 保费
        BigDecimal totalPremium33 = BigDecimal.ZERO;
        //29 被保人数量
        int insuredNo29 = 0;
        //33 被保人数量
        int insuredNo33 = 0;
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));

        PrintCommon.setPrintDateTime(map, "effectiveDate", policyBo.getEffectiveDate(), 3);
        PrintCommon.setPrintDateTime(map, "maturityDate", policyBo.getMaturityDate(), 3);
        //签发回执日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getEffectiveDate(), 3);
        /************************************************单位信息***************************************************/
        GroupAttachApplicantBo applicant = policyBo.getGroupApplicant();
        map.put("companyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));

        /************************************************代理人***************************************************/
        GroupAttachAgentBo agent = policyBo.getGroupAgent();
        String agentName = AssertUtils.isNotNull(agent.getAgentName()) ? agent.getAgentName() : "";
        String bracketsAgentCode = AssertUtils.isNotNull(agent.getAgentCode()) ? "(" + agent.getAgentCode() + ")" : "";
        map.put("agentNameAndAgentCode", PrintCommon.getPrintString(agentName + bracketsAgentCode, 3));
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(agent.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(agent.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(agent.getMobile(), 3));
        /************************************************险种信息***************************************************/
        List<GroupAttachCoverageBo> coverageList = policyBo.getListGroupCoverage();
        //根据产品险种和id进行排序，便于条款按顺序打印
        coverageList.sort(Comparator.comparing(GroupAttachCoverageBo::getProductId));
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        for (GroupAttachCoverageBo coverageBo : coverageList) {
            Map<String, Object> coverageMap = new HashMap<>();
            coverageMap.put("productId", coverageBo.getProductId());
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            coverageListMap.add(coverageMap);
        }
        map.put("coverageListMap", coverageListMap);
        List<GroupAttachInsuredBo> groupAttachInsuredBos = policyBo.getListGroupInsured();
        if (AssertUtils.isNotEmpty(groupAttachInsuredBos)) {
            for (GroupAttachInsuredBo groupAttachInsuredBo : groupAttachInsuredBos) {
                // 被保人险种集合
                List<GroupAttachInsuredCoverageBo> groupAttachInsuredCoverageBos = groupAttachInsuredBo.getListCoverage();
                for (GroupAttachInsuredCoverageBo groupAttachInsuredCoverageBo : groupAttachInsuredCoverageBos) {
                    String productId = groupAttachInsuredCoverageBo.getProductId();
                    // 29号产品
                    if ("PRO880000000000029".equals(productId)) {
                        //保额
                        BigDecimal coverageBoAmount29 = groupAttachInsuredCoverageBo.getTotalAmount();
                        BigDecimal coverageBoPremium29 = groupAttachInsuredCoverageBo.getTotalPremium();
                        totalAmount29 = totalAmount29.add(coverageBoAmount29);
                        totalPremium29 = totalPremium29.add(coverageBoPremium29);
                        insuredNo29++;
                    }
                    // 33号产品
                    if ("PRO880000000000033".equals(productId)) {
                        //保额
                        BigDecimal coverageBoAmount33 = groupAttachInsuredCoverageBo.getTotalAmount();
                        BigDecimal coverageBoPremium33 = groupAttachInsuredCoverageBo.getTotalPremium();
                        totalAmount33 = totalAmount33.add(coverageBoAmount33);
                        totalPremium33 = totalPremium33.add(coverageBoPremium33);
                        insuredNo33++;
                    }
                }
            }
        }
        //totalAmount = coverageList.stream().map(GroupAttachCoverageBo::getTotalAmount).filter(AssertUtils::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //totalPremium = coverageList.stream().map(GroupAttachCoverageBo::getTotalPremium).filter(AssertUtils::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //map.put("insuredNum", PrintCommon.getPrintString(groupAttachInsuredBos.size(), 3));
        map.put("insuredNum29", PrintCommon.getPrintString(insuredNo29, 3));
        map.put("insuredNum33", PrintCommon.getPrintString(insuredNo33, 3));
        //map.put("totalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount), 3));
        map.put("totalPremium", PrintCommon.getPrintString(totalPremium29.add(totalPremium33), 3));
        map.put("totalAmount29", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount29), 3));
        map.put("totalPremium29", PrintCommon.getPrintString(totalPremium29, 3));
        map.put("totalAmount33", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount33), 3));
        map.put("totalPremium33", PrintCommon.getPrintString(totalPremium33, 3));
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    private void putProductOrDutyLevel(Map<String, Object> dutyLevelMap, GroupAttachInsuredCoverageBo coverage, String productId) {
        List<GroupAttachInsuredCoverageDutyBo> listCoverageDuty = coverage.getListCoverageDuty();
        List<GroupAttachInsuredCoverageLevelBo> listCoverageLevel = coverage.getListCoverageLevel();
        listCoverageLevel.forEach(leveBo -> {
            String dutyId = "";
            if (AssertUtils.isNotEmpty(listCoverageDuty)) {
                Optional<GroupAttachInsuredCoverageDutyBo> first = listCoverageDuty.stream().filter(groupAttachInsuredCoverageDutyBo -> groupAttachInsuredCoverageDutyBo.getCoverageDutyId().equals(leveBo.getCoverageDutyId())).findFirst();
                if (first.isPresent()) {
                    dutyId = first.get().getDutyId();
                    dutyLevelMap.put(dutyId, dutyId);
                }
            }
            String id = AssertUtils.isNotEmpty(dutyId) ? dutyId : productId;
            dutyLevelMap.put(id + leveBo.getProductLevel(), leveBo.getProductLevel());
        });
    }

    /**
     * 获取保单
     *
     * @param electronicPolicyGeneratorRequest
     * @param policyData
     * @return
     * @throws Exception
     */
    public byte[] getPolicyPdfBytes(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest, Map<String, Object> policyData) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        //保险证 1
        List<byte[]> policyBookPdfBytesList = new ArrayList<>();

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        policyBookBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyBookBytes);
        policyBookBytes = PrintCommon.pdfEvenPage(policyBookBytes, language);
        policyBookPdfBytesList.add(policyBookBytes);
        return PrintCommon.mergePdfFiles(policyBookPdfBytesList);
    }

    /**
     * 获取COI附件
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public byte[] getPolicyCOIPdfBytes(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        GroupAttachPolicyBo policyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);

        byte[] policyBookBytes = attachmentBusinessService.loadOssObjectByAttachmentId("GROUP_INSURED_29_COI");

        String fullAddress = policyBo.getGroupApplicant().getCompanyAddressWhole();
        String schoolName = policyBo.getGroupApplicant().getName();

        //保险证 1
        List<byte[]> policyBookPdfBytesList = new ArrayList<>();

        List<GroupAttachInsuredBo> listGroupInsured = policyBo.getListGroupInsured();
        if (AssertUtils.isNotEmpty(listGroupInsured)) {
            for (GroupAttachInsuredBo groupAttachInsuredBo : listGroupInsured) {
                List<PrintObject> printObjectList = new ArrayList<>();
                //合同号  保单号
                PrintCommon.setPrintData(printObjectList,"policyNo", policyBo.getPolicyNo(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "effectiveDate", policyBo.getEffectiveDate(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "maturityDate", policyBo.getMaturityDate(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "riskCommencementDate", policyBo.getRiskCommencementDate(), 3);

                //被保人信息组装
                PrintCommon.setPrintData(printObjectList,"insuredName",groupAttachInsuredBo.getName(), 3);
                PrintCommon.setPrintDateTime(printObjectList, "birthdayDate", groupAttachInsuredBo.getBirthday(), 3);
                PrintCommon.setPrintData(printObjectList,"sexName",groupAttachInsuredBo.getSexName(), 3);
                PrintCommon.setPrintData(printObjectList,"nationalityName",groupAttachInsuredBo.getNationalityName(), 3);
                PrintCommon.setPrintData(printObjectList,"schoolName",schoolName, 3);
                PrintCommon.setPrintData(printObjectList,"location",fullAddress, 3);
                PrintCommon.setPrintData(printObjectList,"studentCode",groupAttachInsuredBo.getIdNo(), 3);
                final BigDecimal[] insuredSum33 = {new BigDecimal(0.00)};
                GroupAttachInsuredCoverageBo groupAttachInsuredCoverageBo1 = groupAttachInsuredBo.getListCoverage().stream()
                        .filter(groupAttachInsuredCoverageBo -> "PRO880000000000029".equals(groupAttachInsuredCoverageBo.getProductId()))
                        .findFirst().get();
                PrintCommon.setPrintData(printObjectList,"insuredSum",groupAttachInsuredCoverageBo1.getTotalAmount(), 3);
                groupAttachInsuredBo.getListCoverage().stream()
                        .filter(groupAttachInsuredCoverageBo -> "PRO880000000000033".equals(groupAttachInsuredCoverageBo.getProductId()))
                        .findFirst().ifPresent(groupAttachInsuredCoverageBo -> {
                            insuredSum33[0] = groupAttachInsuredCoverageBo.getTotalAmount();
                        });
                PrintCommon.setPrintData(printObjectList,"insuredSum33", insuredSum33[0], 3);

                //受益人信息组装
                List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = groupAttachInsuredBo.getListPolicyBeneficiary();
                if (AssertUtils.isNotEmpty(listPolicyBeneficiary)) {
                    int i = 1;
                    for (PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo : listPolicyBeneficiary) {
                        PolicyBeneficiaryBo policyBeneficiary = policyBeneficiaryInfoBo.getPolicyBeneficiary();
                        PrintCommon.setPrintData(printObjectList,"beneficiaryName" + i, policyBeneficiary.getName(), 3);
                        PrintCommon.setPrintData(printObjectList,"relationshipName" + i, policyBeneficiaryInfoBo.getRelationshipName(), 3);
                        PrintCommon.setPrintData(printObjectList,"beneficiaryNameAndRelation" + i, policyBeneficiary.getName()+"/"+policyBeneficiaryInfoBo.getRelationshipName(), 3);
                        i++;
                    }
                }

                //根据险种level设置学校级别
                List<GroupAttachInsuredCoverageBo> listCoverage = groupAttachInsuredBo.getListCoverage();
                this.getLogger().info("listCoverage参数数据：===================" + JSON.toJSON(listCoverage));
                if (AssertUtils.isNotEmpty(listCoverage)) {
                    listCoverage.stream().filter(groupAttachInsuredCoverageBo ->
                            "MAIN".equals(groupAttachInsuredCoverageBo.getPrimaryFlag())).findFirst()
                            .ifPresent(groupAttachInsuredCoverageBo -> {
                                String schoolLevel = "";
                                if (AssertUtils.isNotEmpty(groupAttachInsuredCoverageBo.getListCoverageLevel())) {
                                    GroupAttachInsuredCoverageLevelBo groupAttachInsuredCoverageLevelBo = groupAttachInsuredCoverageBo.getListCoverageLevel().get(0);
                                    schoolLevel = transSchoolType(groupAttachInsuredCoverageLevelBo.getProductLevel(), language,schoolLevel);
                                    PrintCommon.setPrintData(printObjectList,"schoolLevel",schoolLevel, 3);
                                }
                    });
                }

                byte[] bytes = PrintCommon.fillData(policyBookBytes, printObjectList);
                policyBookPdfBytesList.add(bytes);
            }
        }
        return PrintCommon.mergePdfFiles(policyBookPdfBytesList);
    }

    public String transSchoolType(String schoolType,String language,String schoolLevel) {
        //List<String> schoolTypes = JSON.parseArray(schoolType, String.class);

        String preSchool = "";
        String primary = "";
        String secondary = "";
        String university = "";
        //保单状态国际化
        List<SyscodeRespFc> school_type = platformBaseInternationServiceApi.queryInternational("SCHOOL_TYPE", language).getData();
        if (schoolType.equals("PRE_SCHOOL")) {
            preSchool = getCodeName(school_type, "PRE_SCHOOL");
        }
        if (schoolType.equals("PRIMARY_SCHOOL")) {
            primary = getCodeName(school_type, "PRIMARY");
        }
        if (schoolType.equals("SECONDARY_SCHOOL")) {
            secondary = getCodeName(school_type, "SECONDARY");
        }
        if (schoolType.equals("UNIVERSITY")) {
            university = getCodeName(school_type, "UNIVERSITY");
        }

        schoolLevel = preSchool + primary + secondary + university;
        return schoolLevel;
    }

    /**
     * 根据key 获取国际化编码值
     *
     * @param syscodeRespFcList
     * @param codeKey
     * @return
     */
    public static String getCodeName(List<SyscodeRespFc> syscodeRespFcList, String codeKey) {
        if (AssertUtils.isNotEmpty(syscodeRespFcList) && AssertUtils.isNotEmpty(codeKey)) {
            for (SyscodeRespFc syscodeRespFc : syscodeRespFcList) {
                if (syscodeRespFc.getCodeKey().equals(codeKey)) {
                    return syscodeRespFc.getCodeName();
                }
            }
        }
        return null;
    }
}
