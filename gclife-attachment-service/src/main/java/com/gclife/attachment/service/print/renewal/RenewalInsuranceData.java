package com.gclife.attachment.service.print.renewal;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.renewal.AgentBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceConfirmPrintBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceCoveragePrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.print.policy.LifeShieldInsuranceData;
import com.gclife.attachment.service.print.policy.ProductCalculation;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.ZH_CN;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 11:49 2018/12/13
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class RenewalInsuranceData {

    public Map<String, Object> getRenewalInsuranceData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        Map<String, Object> stringObjectMap = new HashMap<>();

        RenewalInsuranceConfirmPrintBo renewalInsurance = JSON.parseObject(content, RenewalInsuranceConfirmPrintBo.class);
        stringObjectMap.put("policyNo", PrintCommon.getPrintString(renewalInsurance.getPolicyNo(), 3));
        stringObjectMap.put("applicantName", PrintCommon.getPrintString(renewalInsurance.getApplicantName(), 3));
        stringObjectMap.put("insuredName", PrintCommon.getPrintString(renewalInsurance.getInsuredName(), 3));

        List<RenewalInsuranceCoveragePrintBo> policyCoverageList = renewalInsurance.getPolicyCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        if (AssertUtils.isNotNull(policyCoverageList)) {
            policyCoverageList.forEach(policyCoverage -> {
                Map<String, Object> map = new HashMap<>();
                map.put("productId", policyCoverage.getProductId());
                PrintCommon.setProductName(map, policyCoverage.getProductId(), policyCoverage.getProductLevel(), language);
                map.put("totalPremium", PrintCommon.getPrintString(policyCoverage.getTotalPremium(), 3));
                String premiumFrequencyName = PrintCommon.getPremiumPeriodUnitName(language, policyCoverage.getPremiumPeriod(), policyCoverage.getPremiumPeriodUnit(), policyCoverage.getPremiumPeriodUnitName(), policyCoverage.getPremiumFrequency());
                map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
                Long coveragePeriodStartDate = policyCoverage.getCoveragePeriodStartDate();
                Long coveragePeriodEndDate = policyCoverage.getCoveragePeriodEndDate();
                String coveragePeriodStartDate_coveragePeriodEndDate = null;
                if (AssertUtils.isNotNull(coveragePeriodStartDate) && AssertUtils.isNotNull(coveragePeriodEndDate)) {
                    String dateFormat = ZH_CN.name().equals(language) ? "yyyy.MM.dd" : "dd.MM.yyyy";
                    coveragePeriodStartDate_coveragePeriodEndDate = new SimpleDateFormat(dateFormat).format(coveragePeriodStartDate) + " - " + new SimpleDateFormat(dateFormat).format(coveragePeriodEndDate);
                }
                map.put("coveragePeriodStartDate_coveragePeriodEndDate", PrintCommon.getPrintString(coveragePeriodStartDate_coveragePeriodEndDate, 3));
                if ("PRO88000000000007".equals(policyCoverage.getProductId())) {
                    ProductCalculation.calculation7(map, policyCoverage.getProductLevel(), policyCoverage.getMult());
                }
                if ("PRO88000000000003".equals(policyCoverage.getProductId())) {
                    LifeShieldInsuranceData.calculation1(map, renewalInsurance.getPolicyPeriod(), policyCoverage.getProductLevel(), policyCoverage.getMult());
                }
                coverageListMap.add(map);
            });
            PrintCommon.coverageSort(coverageListMap);
            stringObjectMap.put("coverageListMap", coverageListMap);
        }
        PrintCommon.setPrintDateTime(stringObjectMap, "effectiveDate", renewalInsurance.getEffectiveDate(), 3);

        AgentBo agent = renewalInsurance.getAgent();
        if (!AssertUtils.isNotNull(agent)) {
            agent = new AgentBo();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("policyNo", PrintCommon.getPrintString(renewalInsurance.getPolicyNo(), 3));
        map.put("agentCode", PrintCommon.getPrintString(agent.getAgentCode(), 3));
        PrintCommon.setPrintDateTime(map, "systemPrintDate", DateUtils.getCurrentTime(), 3);
        // 公司基础信息
        stringObjectMap.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return stringObjectMap;
    }


}
