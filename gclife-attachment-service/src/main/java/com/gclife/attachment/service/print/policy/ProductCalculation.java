package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.attachment.model.policy.apply.ProductHealthNoticeBo;
import com.gclife.attachment.model.policy.policy.ProductCashValueBo;
import com.gclife.common.util.AssertUtils;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.model.config.AttachmentPolicyEnum.INSURED_HEALTH_BOOK;

public class ProductCalculation {

    /**
     * 健康告知
     */
    public static void setHealthRemark(Map<String, Object> map, ApplyBo applyPrintBo) {

        //投保人健康告知
        List<ProductHealthNoticeBo> listHealthNotice = applyPrintBo.getListHealthNotice();
        for (int i = 0; i <= 15; i++) {
            String answer = null;
            String questionCode = "ZNCC_QUESTION_" + (i + 1001);
            if (AssertUtils.isNotEmpty(listHealthNotice)) {
                Optional<ProductHealthNoticeBo> noticeBoOptional = listHealthNotice.stream().filter(productHealthNoticeBo -> questionCode.equals(productHealthNoticeBo.getQuestionCode())).findFirst();
                if (noticeBoOptional.isPresent()) {
                    ProductHealthNoticeBo productHealthNoticeBo = noticeBoOptional.get();
                    answer = productHealthNoticeBo.getAnswer();
                }
            }
            PrintCommon.setSelectionBox(map, "ZNCC_QUESTION_" + answer + "_" + (i + 1001), "ZNCC_QUESTION_" + answer + "_" + (i + 1001));
        }
        PrintCommon.setApplicantHealthRemark(map, applyPrintBo.getApplicantHealthRemark());


        //被保人健康告知
        for (int i = 1; i <= 15; i++) {
            String answer = null;
            String questionCode = "ZNCC_QUESTION_" + i;
            if (AssertUtils.isNotEmpty(listHealthNotice)) {
                Optional<ProductHealthNoticeBo> noticeBoOptional = listHealthNotice.stream().filter(productHealthNoticeBo -> questionCode.equals(productHealthNoticeBo.getQuestionCode())).findFirst();
                if (noticeBoOptional.isPresent()) {
                    ProductHealthNoticeBo productHealthNoticeBo = noticeBoOptional.get();
                    answer = productHealthNoticeBo.getAnswer();
                    if ("N".equals(answer)) {
                        map.put(INSURED_HEALTH_BOOK.name(), answer);
                    }
                }
            }
            PrintCommon.setSelectionBox(map, "ZNCC_QUESTION_" + answer + "_" + i, "ZNCC_QUESTION_" + answer + "_" + i);
        }
        if (AssertUtils.isNotEmpty(applyPrintBo.getInsuredHealthRemark())) {
            map.put(INSURED_HEALTH_BOOK.name(), applyPrintBo.getInsuredHealthRemark());
        }
        PrintCommon.setInsuredHealthRemark(map, applyPrintBo.getInsuredHealthRemark());
    }

    /**
     * 健康告知
     */
    public static void setHealthRemark1(Map<String, Object> map, ApplyBo applyPrintBo) {
        //健康告知
        List<ProductHealthNoticeBo> listHealthNotice = applyPrintBo.getListHealthNotice();

        List<String> questionCodeList = Arrays.asList(
                "QYB_QUESTION_091", "QYB_QUESTION_092", "QYB_QUESTION_093", "QYB_QUESTION_094", "QYB_QUESTION_095", "QYB_QUESTION_096", "QYB_QUESTION_097", "QYB_QUESTION_098",
                "QYB_QUESTION_099", "QYB_QUESTION_100", "QYB_QUESTION_101", "QYB_QUESTION_102", "QYB_QUESTION_103", "QYB_QUESTION_104", "QYB_QUESTION_105", "QYB_QUESTION_106",
                "QYB_QUESTION_009", "QYB_QUESTION_010", "QYB_QUESTION_011", "QYB_QUESTION_012", "QYB_QUESTION_013", "QYB_QUESTION_014", "QYB_QUESTION_015", "QYB_QUESTION_016",
                "QYB_QUESTION_017", "QYB_QUESTION_018", "QYB_QUESTION_019", "QYB_QUESTION_020", "QYB_QUESTION_021", "QYB_QUESTION_022", "QYB_QUESTION_023", "QYB_QUESTION_024",
                "20A_QUESTION_001", "20A_QUESTION_002", "20A_QUESTION_003", "20A_QUESTION_004"
        );
        map.put("APPLICANT", PrintCommon.getPrintString(null, 6));
        map.put("INSURED", PrintCommon.getPrintString(null, 6));
        if (AssertUtils.isNotEmpty(listHealthNotice)) {
            listHealthNotice.forEach(productHealthNoticeBo -> {
                String customerType = productHealthNoticeBo.getCustomerType();
                if (questionCodeList.contains(productHealthNoticeBo.getQuestionCode())&&"Y".equals(productHealthNoticeBo.getAnswer())) {
                    map.put(customerType, "");
                }
                map.put(customerType + productHealthNoticeBo.getQuestionCode(), productHealthNoticeBo.getAnswer());
                map.put(customerType + productHealthNoticeBo.getQuestionCode() + "DESC", productHealthNoticeBo.getAnswerDesc());
            });
        }

    }

    /**
     * 现金价值计算
     *
     * @param printObjectList
     * @param policyCashValues
     */
    public static void policyCashValues(List<PrintObject> printObjectList, List<ProductCashValueBo> policyCashValues) {
        policyCashValues(printObjectList, policyCashValues, null);
    }

    /**
     * 现金价值计算
     *
     * @param productId
     * @param policyCashValues
     */
    public static BigDecimal getCashValue(List<ProductCashValueBo> policyCashValues, String productId,
                                          int policyYear) {
        Optional<ProductCashValueBo> first = policyCashValues.stream().filter(productCashValueBo -> productId.equals(productCashValueBo.getProductId()) && policyYear == productCashValueBo.getPolicyYear()).findFirst();
        if (first.isPresent()) {
            return first.get().getCashValue();
        }
        return new BigDecimal(0);
    }

    /**
     * 现金价值计算
     *
     * @param individualizationDataList
     * @param totalPremium7
     */
    public static BigDecimal policyYearTotalPremium(List<Map<String, Object>> individualizationDataList,
                                                    int policyYear, BigDecimal totalPremium7) {
        final BigDecimal[] totalPremium = {totalPremium7};
        individualizationDataList.forEach(stringObjectMap -> {
            Object returnDemos = stringObjectMap.get("returnDemos");
            if (AssertUtils.isNotNull(returnDemos)) {
                List<Map> listMap = JSONObject.parseArray(returnDemos.toString(), Map.class);
                listMap.forEach(sOMap -> {
                    if (sOMap.get("policyYear").toString().equals(policyYear + "")&&AssertUtils.isNotNull(sOMap.get("totalPremium"))) {
                        totalPremium[0] = totalPremium[0].add(new BigDecimal(sOMap.get("totalPremium") + ""));
                    }
                });
            }
        });
        return totalPremium[0];
    }

    /**
     * 现金价值计算
     *
     * @param printObjectList
     * @param policyCashValues
     */
    public static void policyCashValues(List<PrintObject> printObjectList, List<ProductCashValueBo> policyCashValues, String cashValueCode) {
        if (AssertUtils.isNotEmpty(cashValueCode)) {
            cashValueCode += "_";
        } else {
            cashValueCode = "";
        }
        for (int i = 0; i < 22; i++) {
            BigDecimal cashValue = null;
            if (AssertUtils.isNotEmpty(policyCashValues) && i < policyCashValues.size()) {
                ProductCashValueBo policyCashValue = policyCashValues.get(i);
                cashValue = policyCashValue.getCashValue();
            }
            PrintCommon.setPrintData(printObjectList, "cashValue" + cashValueCode + i, cashValue, 3);
        }
    }

    /**
     *
     * @param map
     * @param policyCashValueListMap
     */
    public static void policyCashValues13(Map<String, Object> map, Map<String, List<ProductCashValueBo>> policyCashValueListMap) {
        if (AssertUtils.isNotEmpty(policyCashValueListMap)) {
            policyCashValueListMap.forEach((s, productCashValueBos) -> {
                List<Map<String, Object>> cashValuesListMap = new ArrayList<>();
                int size = productCashValueBos.size();
                int line = size / 2;
                if ((line * 2) != size) line++;
                for (int i = 0; i < line; i++) {
                    Map<String, Object> cashValuesMap = new HashMap<>();
                    ProductCashValueBo productCashValueBo = productCashValueBos.get(i);
                    cashValuesMap.put(s + "CashValue1", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                    cashValuesMap.put(s + "PolicyYear1", productCashValueBo.getPolicyYear());
                    if (line + i < size) {
                        ProductCashValueBo productCashValueBo1 = productCashValueBos.get(line + i);
                        cashValuesMap.put(s + "CashValue2", PrintCommon.getPrintString(productCashValueBo1.getCashValue(), 3));
                        cashValuesMap.put(s + "PolicyYear2", productCashValueBo1.getPolicyYear());
                    }
                    cashValuesListMap.add(cashValuesMap);
                }
                map.put("cashValuesListMap" + s, cashValuesListMap);
            });
        }
    }

    /**
     * 现金价值计算
     *
     * @param policyCashValues
     */
    public static void policyCashValues(Map<String, Object> map, List<ProductCashValueBo> policyCashValues) {
        policyCashValues(map, policyCashValues, null);
    }

    /**
     * 现金价值计算
     *
     * @param policyCashValues
     */
    public static void policyCashValues(Map<String, Object> map, List<ProductCashValueBo> policyCashValues, String cashValueCode) {
        if (!AssertUtils.isNotEmpty(policyCashValues)) {
            return;
        }
        if (AssertUtils.isNotEmpty(cashValueCode)) {
            cashValueCode += "_";
        } else {
            cashValueCode = "";
        }
        for (int i = 1; i <= policyCashValues.size(); i++) {
            ProductCashValueBo policyCashValue = policyCashValues.get(i - 1);
            BigDecimal cashValue = policyCashValue.getCashValue();
            map.put("cashValue" + cashValueCode + i, PrintCommon.getPrintString(cashValue, 3));
        }
    }

    /**
     * 7号产品现金价值计算
     *
     * @param printObjectList
     * @param productLevel
     * @return
     */
    public static void calculation7(List<PrintObject> printObjectList, String productLevel, String additionalMult) {
        //住院津贴
        BigDecimal payAmount = null;
        String payDay = null;
        String totalPayDay = null;
        String returnPremium = null;
        BigDecimal totalAmount = null;

        if (!AssertUtils.isNotEmpty(additionalMult)) {
            additionalMult = "1";
        }
        //住院津贴
        if ("A".equals(productLevel)) {
            //每天给付额
            payAmount = new BigDecimal("8.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            //每次住院给付天数：
            payDay = "30";
            //每年住院累计给付天数
            totalPayDay = "60";
            returnPremium = "0.00";
            //保险金额
            totalAmount = new BigDecimal("480.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("B".equals(productLevel)) {
            payAmount = new BigDecimal("12.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "50";
            totalPayDay = "100";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("1200.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("C".equals(productLevel)) {
            payAmount = new BigDecimal("16.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "80";
            totalPayDay = "180";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("2880.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("D".equals(productLevel)) {
            payAmount = new BigDecimal("30.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "100";
            totalPayDay = "220";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("6600.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        PrintCommon.setPrintData(printObjectList, "payAmount", payAmount, 3);
        PrintCommon.setPrintData(printObjectList, "payDay", payDay, 3);
        PrintCommon.setPrintData(printObjectList, "totalPayDay", totalPayDay, 3);
        PrintCommon.setPrintData(printObjectList, "returnPremium", returnPremium, 3);
        PrintCommon.setPrintData(printObjectList, "totalAmount", totalAmount, 3);
    }

    public static void calculation7(Map<String, Object> map, String productLevel, String additionalMult) {
        //住院津贴
        BigDecimal payAmount = null;
        String payDay = null;
        String totalPayDay = null;
        String returnPremium = null;
        BigDecimal totalAmount = null;

        if (!AssertUtils.isNotEmpty(additionalMult)) {
            additionalMult = "1";
        }
        //住院津贴
        if ("A".equals(productLevel)) {
            //每天给付额
            payAmount = new BigDecimal("8.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            //每次住院给付天数：
            payDay = "30";
            //每年住院累计给付天数
            totalPayDay = "60";
            returnPremium = "0.00";
            //保险金额
            totalAmount = new BigDecimal("480.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("B".equals(productLevel)) {
            payAmount = new BigDecimal("12.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "50";
            totalPayDay = "100";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("1200.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("C".equals(productLevel)) {
            payAmount = new BigDecimal("16.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "80";
            totalPayDay = "180";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("2880.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if ("D".equals(productLevel)) {
            payAmount = new BigDecimal("30.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
            payDay = "100";
            totalPayDay = "220";
            returnPremium = "0.00";
            totalAmount = new BigDecimal("6600.00").multiply(new BigDecimal(additionalMult)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        map.put("payAmount7", PrintCommon.getPrintString(payAmount, 3));
        map.put("payDay7", PrintCommon.getPrintString(payDay, 3));
        map.put("totalPayDay7", PrintCommon.getPrintString(totalPayDay, 3));
        map.put("returnPremium7", PrintCommon.getPrintString(returnPremium, 3));
        map.put("totalAmount7", PrintCommon.getPrintString(totalAmount, 3));
    }

    /**
     * @param column
     * @param cpfMap
     * @param cpfList
     * @param premiumFrequency
     * @param mainPremiumFrequency
     * @return
     */
    public static Integer getPremiumFrequencyTotalPremium(Map<String, Object> map, Integer column, Map<String, Object> cpfMap, List<CoveragePremiumFrequencyResponse> cpfList, String premiumFrequency, String mainPremiumFrequency) {
        if (premiumFrequency.equals(mainPremiumFrequency)) {
            return column;
        }
        Optional<CoveragePremiumFrequencyResponse> first = cpfList.stream().filter(cpf -> premiumFrequency.equals(cpf.getPremiumFrequency())).findFirst();
        if (first.isPresent()) {
            CoveragePremiumFrequencyResponse cpf = first.get();
            cpfMap.put("totalPremium" + column, PrintCommon.getPrintString(cpf.getTotalPremium(), 3));
            map.put("premiumFrequencyName" + column, cpf.getPremiumFrequencyName());
            column = column + 1;
            return column;
        }
        return column;
    }

    /**
     * 设置团险保额
     *
     * @param productId
     * @param totalAmount
     * @param mult
     * @param productLevel
     * @param dutyId
     */
    public static void setAmount(String productId, String[] totalAmount, String mult, String productLevel, String dutyId) {
        if ("PRO8800000000000G3".equals(productId)) {
            BigDecimal totalAmountBigDecimal = AssertUtils.isNotEmpty(totalAmount[0]) ? new BigDecimal(totalAmount[0].replaceAll(",", "")) : new BigDecimal(0);
            if ("A".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(5000).multiply(new BigDecimal(mult)));
            }
            if ("B".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(10000).multiply(new BigDecimal(mult)));
            }
            if ("C".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(20000).multiply(new BigDecimal(mult)));
            }
            totalAmount[0] = PrintCommon.getPrintString(totalAmountBigDecimal, 0);
        }
        if ("PRO8800000000000G7".equals(productId)) {
            BigDecimal totalAmountBigDecimal = AssertUtils.isNotEmpty(totalAmount[0]) ? new BigDecimal(totalAmount[0].replaceAll(",", "")) : new BigDecimal(0);
            if ("A".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(1440).multiply(new BigDecimal(mult)));
            }
            if ("B".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(2880).multiply(new BigDecimal(mult)));
            }
            if ("C".equals(productLevel)) {
                totalAmountBigDecimal = totalAmountBigDecimal.add(new BigDecimal(6600).multiply(new BigDecimal(mult)));
            }
            totalAmount[0] = PrintCommon.getPrintString(totalAmountBigDecimal, 0);
        }
        if ("PRO8800000000000G11".equals(productId)) {
            totalAmount[0] = (AssertUtils.isNotEmpty(totalAmount[0]) ? totalAmount[0] + "," : "") + mult + productLevel;
        }
        if ("PRO8800000000000G12".equals(productId)) {
            StringBuffer s = new StringBuffer("--/--/--");
            if (AssertUtils.isNotEmpty(totalAmount[0])) {
                s = new StringBuffer(totalAmount[0] + "");
            }
            int startIndex = s.indexOf("/");
            int endIndex = s.lastIndexOf("/");
            String multProductLevel = mult + productLevel;
            if (dutyId.equals("PRO8800000000000G12_DUTY_1")) {
                s.replace(0, startIndex, multProductLevel);
            }
            if (dutyId.equals("PRO8800000000000G12_DUTY_2")) {
                s.replace(startIndex + 1, endIndex, multProductLevel);
            }
            if (dutyId.equals("PRO8800000000000G12_DUTY_3")) {
                s.replace(endIndex + 1, s.length(), multProductLevel);
            }
            totalAmount[0] = s.toString();
        }
    }

    /**
     * setAmount方法的增强
     *
     * @param productId
     * @param totalAmount
     * @param mult
     * @param productLevel
     * @param dutyId
     * @param amount
     */
    public static void setAmount(String productId, String[] totalAmount, String mult, String productLevel, String dutyId, BigDecimal amount) {
        // 先调用旧方法
        setAmount(productId, totalAmount, mult, productLevel, dutyId);
        // 17号产品 GC全优团保 18号产品没保额 不需要处理
        if ("PRO880000000000017".equals(productId)) {
            BigDecimal totalAmountBigDecimal = AssertUtils.isNotEmpty(totalAmount[0]) ? new BigDecimal(totalAmount[0].replaceAll(",", "")) : new BigDecimal(0);
            // 因疾病或意外 的保额
            if (AssertUtils.isNotNull(amount)) {
                if ("TL".equals(productLevel)
                        || "TC".equals(productLevel)
                        || "TS".equals(productLevel)) {
                    totalAmountBigDecimal = totalAmountBigDecimal.add(amount.multiply(new BigDecimal(mult)));
                }
                // 因意外 的保额
                if ("TA".equals(productLevel)) {
                    // onlyAccidentAmount
                    totalAmountBigDecimal = totalAmountBigDecimal.add(amount.multiply(new BigDecimal(mult)));
                }
                totalAmount[0] = PrintCommon.getPrintString(totalAmountBigDecimal, 0);
            }
        }
    }
}
