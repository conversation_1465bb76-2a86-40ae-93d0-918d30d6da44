package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @date 2022/2/16
 */
@Slf4j
@Component
public class GCScholarData {

    /**
     * 获取保险证打印数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Map<String, Object> map = new HashMap<>();

        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream()
                .filter(policyCoverage -> MAIN.name().equals(policyCoverage.getPrimaryFlag()))
                .findFirst().get();

        /*---------------------------------------保单基本信息---------------------------------------*/
        // 保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        // 生效日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        // 风险开始日期
        Long backTrackDate = policyBo.getApproveDate();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        // 缴费周期
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Payment";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        // 缴费期限
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        // 总保费
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        /*---------------------------------------投保人信息---------------------------------------*/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        // 投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        // 身份证号
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        // 年龄
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        // 投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        // 投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        // 手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        // 地址
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));

        /*---------------------------------------被保人信息---------------------------------------*/
        // 被保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        // 被保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        // 被保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        // 被保人年龄
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        // 被保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        // 被保人地址
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        // 与被保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        // 手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));

        /*---------------------------------------保险利益及保费一览表---------------------------------------*/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        String mainProductId = electronicPolicyGeneratorRequest.getProductId();

        PrintCommon.setProductName(map, mainProductId, "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            String productLevel = coverageBo.getProductLevel();
            map.put(coverageBo.getProductId() + "ProductLevel", productLevel);
            coverageMap.put("productId", coverageBo.getProductId());

            // 产品名称以及主附险标识
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), productLevel, language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            // 保险金额
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            // 额外保费
            BigDecimal extraPremium = null;
            // 期缴保费
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));

            // 缴费终止日期
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);

            // 满期日期
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);

            coverageMap.put("productLevel", productLevel);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));

            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = BigDecimal.valueOf(max.getAsDouble());
                }
            }
            // 满期金额
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            if ("PRO880000000000019".equals(coverageBo.getProductId())) {
                maturityAmount = new BigDecimal("0.15").multiply(new BigDecimal(coverageBo.getTotalAmount()));
            }
            coverageMap.put("maturityAmount", PrintCommon.getPrintAmountString(maturityAmount, 2));

            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);

        /*---------------------------------------现金价值表---------------------------------------*/
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        log.info("policyCashValues: {}", JSON.toJSONString(policyCashValues));
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                Map<String, Object> productCashValue = new HashMap<>();
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                policyCashValuesListMap.add(productCashValue);
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }

        /*---------------------------------------代理人信息---------------------------------------*/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }

    /**
     * 获取计划书打印数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     * @throws Exception
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> map = new HashMap<>();
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        /*---------------------------------------计划书信息---------------------------------------*/
        // 计划书编号
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));

        // 客户基本信息
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        // 回溯日期
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        Integer applicantAgeYear = null;
        // 投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        // 投保人年龄
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        // 投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        // 被保人姓名
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        // 被保人年龄
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        log.info("------------GCScholarData 被保人年龄: {}", map.get("insuredAgeYear"));
        // 被保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /*---------------------------------------保险产品信息---------------------------------------*/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal mainTotalAmount = BigDecimal.ZERO;
        BigDecimal juvenileCIAmount = BigDecimal.ZERO;// 22号产品保险金
        BigDecimal termProtectionRiderAmountA = BigDecimal.ZERO;// #23A号附加险
        BigDecimal termProtectionRiderAmountB = BigDecimal.ZERO;// #23B号附加险
        String mainPremiumFrequency = null;

        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            String productLevel = coverageBo.getProductLevel();// 险种档次
            map.put(coverageBo.getProductId() + "ProductLevel", productLevel);
            coverageMap.put("productId", coverageBo.getProductId());
            // 险种代码 险种名称 主险标识
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), productLevel, language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            coverageMap.put("productLevel", productLevel);
            // 保险金额
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintAmountString(totalAmount, 2));
            // 保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            // 交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            // 交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            // 缴费年限
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = null;
            if ("PRO880000000000019".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mainTotalAmount = amount;
                mainPremiumFrequency = coverageBo.getPremiumFrequency();
            }
            if ("PRO880000000000022".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                juvenileCIAmount = amount;
            }
            if ("PRO880000000000023A".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountA = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023AOPTION_TWO", "PRO880000000000023AOPTION_TWO");
                }
            }
            if ("PRO880000000000023B".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                termProtectionRiderAmountB = amount;
                if ("OPTION_TWO".equals(coverageBo.getProductLevel())) {
                    map.put("PRO880000000000023BOPTION_TWO", "PRO880000000000023BOPTION_TWO");
                }
            }
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            // 每期保费
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int i = YEAR.name().equals(premiumFrequency) ? 1 : SEMIANNUAL.name().equals(premiumFrequency) ? 2 : SEASON.name().equals(premiumFrequency) ? 3 : MONTH.name().equals(premiumFrequency) ? 4 : 0;
            coverageMap.put("totalPremium" + i, PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            //可选缴费周期保险费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 1, coverageMap, cpfList, YEAR.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 2, coverageMap, cpfList, SEMIANNUAL.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 3, coverageMap, cpfList, SEASON.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 4, coverageMap, cpfList, MONTH.name(), premiumFrequency);

            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", mainPremiumFrequency);
        // 期缴保费
        double totalPremium1 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium1") + "").replace(",", ""))).sum();
        double totalPremium2 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium2") + "").replace(",", ""))).sum();
        double totalPremium3 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium3") + "").replace(",", ""))).sum();
        double totalPremium4 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium4") + "").replace(",", ""))).sum();
        map.put("allTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2), 2));
        map.put("allTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3), 2));
        map.put("allTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4), 2));
        map.put("allYearTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allYearTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2).multiply(new BigDecimal(2)), 2));
        map.put("allYearTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3).multiply(new BigDecimal(4)), 2));
        map.put("allYearTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4).multiply(new BigDecimal(12)), 2));

        /*---------------------------------------保险利益---------------------------------------*/
        map.put("death_benefit_non_accident", PrintCommon.getPrintAmountString(mainTotalAmount, 2));
        // 主险 死亡或高度残疾保险金
        BigDecimal benefitsAmount1 = mainTotalAmount;

        List<Map<String, Object>> individualizationDatas = planProductDetail.getIndividualizationDatas();
        if (AssertUtils.isNotEmpty(individualizationDatas)) {// 与21号产品一致
            Optional<Map<String, Object>> product19MapOptional = individualizationDatas.stream()
                    .filter(individualizationData -> "PRO880000000000019".equals(individualizationData.get("productId")))
                    .findFirst();

            if (product19MapOptional.isPresent()) {
                Map<String, Object> product19Map = product19MapOptional.get();
                if (AssertUtils.isNotNull(product19Map.get("benefitsAmount1"))) {
                    benefitsAmount1 = new BigDecimal((product19Map.get("benefitsAmount1") + "").replace(",", "")).add(termProtectionRiderAmountA);
                }
            }
        }
        map.put("benefitsAmount1", PrintCommon.getPrintAmountString(benefitsAmount1, 2));
        // 少儿重大疾病保险金
        map.put("juvenileCIAmount", PrintCommon.getPrintAmountString(juvenileCIAmount, 2));
        // 被保险人晚期重大疾病保险金
        BigDecimal insuredLateStageCriticalIllness = BigDecimal.ZERO;
        if ("OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            insuredLateStageCriticalIllness = insuredLateStageCriticalIllness.add(termProtectionRiderAmountA);
        }
        map.put("insuredLateStageCriticalIllness", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        insuredLateStageCriticalIllness)
                , 2));
        // 保单付款人意外高残保险金
        BigDecimal payorTPDAccidentAmount = termProtectionRiderAmountB;
        map.put("payorTPDAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAccidentAmount)
                , 2));
        // 保单付款人高残保险金
        BigDecimal payorTPDAmount = termProtectionRiderAmountB;
        map.put("payorTPDAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorTPDAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAccidentAmount = termProtectionRiderAmountB;
        map.put("payorDeathAccidentAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAccidentAmount)
                , 2));
        // 保单付款人死亡保险金
        BigDecimal payorDeathAmount = termProtectionRiderAmountB;
        map.put("payorDeathAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorDeathAmount)
                , 2));
        // 保单付款人晚期重大疾病保险金
        BigDecimal payorLateStageCriticalIllnessAmount = termProtectionRiderAmountB;
        map.put("payorLateStageCriticalIllnessAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        payorLateStageCriticalIllnessAmount)
                , 2));

        /*---------------------------------------利益显演示---------------------------------------*/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        BigDecimal accumulatedPremiumsPaid = BigDecimal.ZERO;
        // 总生存保险金
        BigDecimal totalSurvivalAmount = null;
        for (int i = 1; i <= listCashValue.size(); i++) {
            Map<String, Object> interestMap = new HashMap<>();
            // 保单年度
            interestMap.put("policyYear", i);
            int policyYearInsuredAge = insuredAgeYear + i;
            // 保单年度末年龄
            interestMap.put("ageYear", policyYearInsuredAge);
            // 死亡或高度残疾保险金
            interestMap.put("interestMapMainTotalAmount", PrintCommon.getPrintAmountDefaultStr(mainTotalAmount));
            BigDecimal survivalAmount = null;
            if (12 == policyYearInsuredAge && (1 <= insuredAgeYear && insuredAgeYear <= 10)) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (15 == policyYearInsuredAge && (1 <= insuredAgeYear && insuredAgeYear <= 13)) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (18 == policyYearInsuredAge) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.2"));
            }
            if (19 == policyYearInsuredAge) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (20 == policyYearInsuredAge) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (21 == policyYearInsuredAge) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (22 == policyYearInsuredAge) {
                survivalAmount = mainTotalAmount.multiply(new BigDecimal("0.15"));
            }
            if (AssertUtils.isNotNull(survivalAmount)) {
                if (!AssertUtils.isNotNull(totalSurvivalAmount)) {
                    totalSurvivalAmount = BigDecimal.ZERO;
                }
                totalSurvivalAmount = totalSurvivalAmount.add(survivalAmount);
            }
            // 生存保险金
            interestMap.put("survivalAmount", PrintCommon.getPrintAmountDefaultStr(survivalAmount));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            // 保费
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintDefaultStr(policyYearTotalPremium));
            // 少儿重大疾病保险金
            interestMap.put("interestMapJuvenileCIAmount", PrintCommon.getPrintAmountDefaultStr(juvenileCIAmount));
            for (ApplyCoveragePlanBo applyCoveragePlanBo : listCoverage) {
                String productId = applyCoveragePlanBo.getProductId();
                interestMap.put("interestMap" + productId, productId);
            }
            accumulatedPremiumsPaid = policyYearTotalPremium;// policyYearTotalPremium 就是累计已交保费
            // 累计已交保费
            interestMap.put("accumulatedPremiumsPaid", PrintCommon.getPrintString("$", accumulatedPremiumsPaid, 1));
            // 死亡或高残保险金
            interestMap.put("deathOrTPDBenefitsForInsuredAmount", PrintCommon.getPrintAmountDefaultStr(benefitsAmount1));
            // 保单付款人死亡或高残或重疾保险金
            interestMap.put("deathOrTPDOrCIBenefitsForPayorAmount", PrintCommon.getPrintAmountDefaultStr(termProtectionRiderAmountB));
            // 被保险人意外高残保险金
            interestMap.put("insuredAccidentalTPD", PrintCommon.getPrintAmountDefaultStr(benefitsAmount1));
            // 被保险人重大疾病保险金
            interestMap.put("insuredMapInsuredCriticalIllness", PrintCommon.getPrintAmountDefaultStr(insuredLateStageCriticalIllness));
            // 现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO880000000000019".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                BigDecimal mainCashValue = ProductCalculation.getCashValue(listCashValue, "PRO880000000000019", i);
                interestMap.put("mainCashValue", PrintCommon.getPrintDefaultStr(mainCashValue));
                interestMap.put("cashValueSum", PrintCommon.getPrintDefaultStr(mainCashValue));
            }
            interestListMap.add(interestMap);
        }
        log.info("这里才是真正的利益演示结果：{} ", JSON.toJSON(interestListMap));
        map.put("interestListMap", interestListMap);
        // 主险 死亡或高度残疾保险金
        map.put("mainTotalAmount", PrintCommon.getPrintAmountString(mainTotalAmount, 2));
        map.put("termProtectionRiderAmountA", PrintCommon.getPrintAmountString(termProtectionRiderAmountA, 2));
        map.put("termProtectionRiderAmountB", PrintCommon.getPrintAmountDefaultStr(termProtectionRiderAmountB));
        // 总生存保险金
        map.put("totalBenefitsAmount", PrintCommon.getPrintAmountString(totalSurvivalAmount, 2));
        // 保险利益是否有重大疾病
        if (AssertUtils.isNotNull(map.get("PRO880000000000022"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023BProductLevel"))) {
            map.put("isCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        // 利益演示是否有被保人重大疾病或少儿重大疾病
        if (AssertUtils.isNotNull(map.get("PRO880000000000022"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))) {
            map.put("isInsuredLateStageCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        // 保险利益是否有晚期重大疾病
        if ("OPTION_TWO".equals(map.get("PRO880000000000023AProductLevel"))
                || "OPTION_TWO".equals(map.get("PRO880000000000023BProductLevel"))) {
            map.put("isLateStageCriticalIllness", PaymentTermEnum.YES_NO.YES.name());
        }
        /*---------------------------------------寿险顾问或银保顾问---------------------------------------*/
        // 代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        // 代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        // 代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        // 制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        log.info("利益演示 gc scholar data -> {}", JSON.toJSON(map));
        return map;
    }

}
