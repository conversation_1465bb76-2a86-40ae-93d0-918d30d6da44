package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.model.config.AttachmentTermEnum.BENEFICIARY_NO.ORDER_ONE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;

@Component
public class LifeShieldInsuranceData {

    /**
     * 获取计划书打印数据
     *
     * @return
     */
    public Map<String, Object> getPlanData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Map<String, Object> map = new HashMap<>();
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(planPrintBo.getCreatedDate()));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(planPrintBo.getCreatedDate()));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  start***********************************************************************************/
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount7 = new BigDecimal(0);
        BigDecimal totalPremium7 = new BigDecimal(0);
        BigDecimal totalAmount1 = new BigDecimal(0);
        if (AssertUtils.isNotEmpty(listCoverage)) {
            for (ApplyCoveragePlanBo coverageBo : listCoverage) {
                Map<String, Object> coverageMap = new HashMap<>();
                map.put(coverageBo.getProductId(), coverageBo.getProductId());
                coverageMap.put("productId", coverageBo.getProductId());
                PrintCommon.setProductName(coverageMap, coverageBo.getProductId());
                coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
                coverageMap.put("productLevel", coverageBo.getProductLevel());
                if ("PRO88000000000003".equals(coverageBo.getProductId())) {
                    calculation1(map, coverageBo.getProductLevel(), coverageBo.getMult());
                    totalAmount1 = new BigDecimal((map.get("accidentPayAmount") + "").replace(",", ""));
                    coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount1, 2));
                } else if ("PRO88000000000007".equals(coverageBo.getProductId())) {
                    ProductCalculation.calculation7(map, coverageBo.getProductLevel(), coverageBo.getMult());
                    totalAmount7 = new BigDecimal((map.get("totalAmount7") + "").replace(",", ""));
                    coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount7, 2));
                }
                String multLevel = "(" + coverageBo.getMult() + coverageBo.getProductLevel() + ")";
                coverageMap.put("productLevelZH_CN", multLevel);
                coverageMap.put("productLevelKM_KH", multLevel);
                coverageMap.put("productLevelEN_US", multLevel);
                coverageMap.put("productLevelName", PrintCommon.getPrintString(coverageBo.getProductLevelName(), 2));
                //保险期间
                String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
                String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
                String insurancePeriod = coveragePeriod + " " + coveragePeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                    insurancePeriod = coveragePeriodUnitName + " " + coveragePeriod;
                }
                coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
                //交费期限
                String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
                String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
                String premiumPeriodAndUnitName = premiumPeriod + " " + premiumPeriodUnitName;
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage()) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                    premiumPeriodAndUnitName = premiumPeriodUnitName + " " + premiumPeriod;
                }
                //交费类型
                String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
                if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "一次性全额缴清";
                    }
                    if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(electronicPolicyGeneratorRequest.getLanguage())) {
                        premiumFrequencyName = "Single Payment";
                    }
                    premiumPeriodAndUnitName = premiumFrequencyName;
                }
                coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
                coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
                //每期保费
                coverageMap.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
                coverageListMap.add(coverageMap);
            }
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("allTotalPremium", PrintCommon.getPrintString(planPrintBo.getReceivablePremium(), 2));
        /************************************利益显示************************************************/

        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }


    /**
     * 获取保单打印数据
     *
     * @return
     */
    public List<PrintObject> getPolicyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        //合同号  保单号
        PrintCommon.setPrintData(printObjectList, "policyNo", policyBo.getPolicyNo(), 3);
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "applicantName", policyApplicant.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "applicantSexName", policyApplicant.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        PrintCommon.setPrintData(printObjectList, "applicantIdNo", policyApplicant.getIdNo(), 3);
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "applicantIdNoAndIdTypeName", applicantIdNoAndIdTypeName, 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "applicantMobile", policyApplicant.getMobile(), 3);

        /**********************************被保人信息**********************************/

        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        PrintCommon.setPrintData(printObjectList, "insuredName", policyInsuredBo.getName(), 3);
        //投保人性别
        PrintCommon.setPrintData(printObjectList, "insuredSexName", policyInsuredBo.getSexName(), 3);
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(printObjectList, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        PrintCommon.setPrintData(printObjectList, "insuredIdNoAndIdTypeName", insuredIdNoAndIdTypeName, 3);
        //与投保人什么关系
        PrintCommon.setPrintData(printObjectList, "insuredRelationshipName", policyInsuredBo.getRelationshipName(), 3);
        //手机号
        PrintCommon.setPrintData(printObjectList, "insuredMobile", policyInsuredBo.getMobile(), 3);

        /**********************************受益人***************************************/
        List<PolicyBeneficiaryInfoBo> listPolicyBeneficiary = policyInsuredBo.getListPolicyBeneficiary();
        if (AssertUtils.isNotEmpty(listPolicyBeneficiary)) {
            listPolicyBeneficiary.removeIf(policyBeneficiaryInfoBo -> !ORDER_ONE.name().equals(policyBeneficiaryInfoBo.getBeneficiaryNoOrder()));
        }
        for (int i = 0; i < 3; i++) {
            PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = null;
            PolicyBeneficiaryBo policyBeneficiary = null;
            if (AssertUtils.isNotEmpty(listPolicyBeneficiary) && i < listPolicyBeneficiary.size() && AssertUtils.isNotNull(listPolicyBeneficiary.get(i))) {
                policyBeneficiaryInfoBo = listPolicyBeneficiary.get(i);
                if (AssertUtils.isNotNull(policyBeneficiaryInfoBo) && AssertUtils.isNotNull(policyBeneficiaryInfoBo.getPolicyBeneficiary())) {
                    policyBeneficiary = policyBeneficiaryInfoBo.getPolicyBeneficiary();
                }
            }
            if (!AssertUtils.isNotNull(policyBeneficiary)) {
                policyBeneficiary = new PolicyBeneficiaryBo();
            }
            if (!AssertUtils.isNotNull(policyBeneficiaryInfoBo)) {
                policyBeneficiaryInfoBo = new PolicyBeneficiaryInfoBo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryName" + i, policyBeneficiary.getName(), 3);
            String beneficiaryIdNo = null;
            if (AssertUtils.isNotEmpty(policyBeneficiary.getIdTypeName()) && AssertUtils.isNotEmpty(policyBeneficiary.getIdNo())) {
                beneficiaryIdNo = policyBeneficiary.getIdTypeName() + " / " + policyBeneficiary.getIdNo();
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryIdNo" + i, beneficiaryIdNo, 3);
            PrintCommon.setPrintData(printObjectList, "beneficiaryId-No" + i, policyBeneficiary.getIdNo(), 3);
            String beneficiaryProportion = null;
            if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                beneficiaryProportion = policyBeneficiaryInfoBo.getBeneficiaryProportion().setScale(0, BigDecimal.ROUND_HALF_UP) + "%";
            }
            PrintCommon.setPrintData(printObjectList, "beneficiaryProportion" + i, beneficiaryProportion, 2);
        }
        /**********************************保险***************************************/
        String productId = null;
        String additional = null;
        String mainMult = null;
        String additionalMult = null;
        String productLevelMain = null;
        String productLevelAdditional = null;
        BigDecimal totalPremiumMain = null;
        BigDecimal totalPremiumAdditional = null;
        //获取保险期限
        PolicyCoverageBo policyCoverageMainBo = new PolicyCoverageBo();
        if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyCoverage())) {
            List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
            Optional<PolicyCoverageBo> optionalCoverageMainBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst();
            if (optionalCoverageMainBo.isPresent()) {
                policyCoverageMainBo = optionalCoverageMainBo.get();
                productId = policyCoverageMainBo.getProductId();
                productLevelMain = policyCoverageMainBo.getProductLevel();
                mainMult = policyCoverageMainBo.getMult();
//                totalPremiumMain = policyCoverageMainBo.getTotalPremium();
                totalPremiumMain = policyCoverageMainBo.getTotalPremium();
            }
            Optional<PolicyCoverageBo> optionalCoverageAdditionalBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoverage.getPrimaryFlag())).findFirst();
            if (optionalCoverageAdditionalBo.isPresent()) {
                PolicyCoverageBo coverageAdditionalBo = optionalCoverageAdditionalBo.get();
                productLevelAdditional = coverageAdditionalBo.getProductLevel();
//                totalPremiumAdditional = coverageAdditionalBo.getTotalPremium();
                totalPremiumAdditional = coverageAdditionalBo.getTotalPremium();
                additionalMult = coverageAdditionalBo.getMult();

                printObjectList.add(new PrintObject(AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name(), coverageAdditionalBo.getProductId()));
            }
        }

        PrintCommon.setPrintData(printObjectList, "mult", policyCoverageMainBo.getMult(), 3);
        PrintCommon.setPrintData(printObjectList, "totalPremiumMain", totalPremiumMain, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelMain", productLevelMain, 3);
        PrintCommon.setPrintData(printObjectList, "totalPremiumAdditional", totalPremiumAdditional, 3);
        PrintCommon.setPrintData(printObjectList, "productLevelAdditional", productLevelAdditional, 3);
        PolicyCoveragePremiumBo policyCoveragePremium = policyCoverageMainBo.getPolicyCoveragePremium();
        if (!AssertUtils.isNotNull(policyCoveragePremium)) {
            policyCoveragePremium = new PolicyCoveragePremiumBo();
        }
        //缴费年期
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyName", policyCoveragePremium.getPremiumFrequencyName(), 3);
        String premiumFrequency = policyCoveragePremium.getPremiumFrequency();
        PrintCommon.setPremiumFrequencyName(premiumFrequency, printObjectList);

        String mult = policyCoverageMainBo.getMult();
        PrintCommon.setPrintData(printObjectList, "mult", mult, 3);

        /************************************************支付***********************************************************/
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (!AssertUtils.isNotNull(policyPremium)) {
            //首期保费合计
            policyPremium = new PolicyPremiumBo();
        }
        PrintCommon.setPrintData(printObjectList, "periodTotalPremium", policyPremium.getActualPremium(), 3);
        PolicyPaymentBo policyPayment = new PolicyPaymentBo();
        if (AssertUtils.isNotNull(policyPremium.getPolicyPayment())) {
            policyPayment = policyPremium.getPolicyPayment();
        }
        //支付方式
        PrintCommon.setPrintData(printObjectList, "payModeCodeName", policyPayment.getPayModeCodeName(), 3);
        // 保单生效日期生效时间
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodFrom", policyBo.getEffectiveDate(), 3);
        // 保单满期日期
        PrintCommon.setPrintDateTime(printObjectList, "insurancePeriodUntil", policyBo.getMaturityDate(), 3);

        PrintCommon.setPrintDateTime(printObjectList, "gainedDate", policyPayment.getGainedDate(), 3);

        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        PrintCommon.setPrintData(printObjectList, "agentCode", policyAgent.getAgentCode(), 3);
        PrintCommon.setPrintData(printObjectList, "agentName", policyAgent.getAgentName(), 3);
        //签发日期
        PrintCommon.setPrintDateTime(printObjectList, "apprvoeDate", policyBo.getApproveDate(), 3);
        PrintCommon.setPrintDateTime(printObjectList, "approveDate", policyBo.getApproveDate(), 3);

        /*****************************************现金价值****************************************/


        /***********************************现金价值******************************************/
        //一号产品
        if ("PRO88000000000003".equals(productId)) {

            this.calculation1(printObjectList, productLevelMain, mainMult);
            ProductCalculation.calculation7(printObjectList, productLevelAdditional, additionalMult);
        }

        List<PolicySpecialContractBo> policySpecialContractList = policyBo.getListPolicySpecialContract();
        String specialContractContent = null;
        if (AssertUtils.isNotEmpty(policySpecialContractList)) {
            Optional<PolicySpecialContractBo> first = policySpecialContractList.stream().filter(policySpecialContractBo -> "OTHER".equals(policySpecialContractBo.getSpecialContractTypeCode())).findFirst();
            if (first.isPresent()) {
                PolicySpecialContractBo policySpecialContractBo = first.get();
                specialContractContent = policySpecialContractBo.getSpecialContractContent();
            }
        }

        PrintCommon.getPolicySpecialContractContent(printObjectList, specialContractContent);
        // 公司基础信息
        printObjectList.addAll(CompanyInfo.getCompanyBaseInfoList(language));
        return printObjectList;
    }

    /**
     * 一号产 现金价值计算
     *
     * @param printObjectList
     * @param productLevel
     * @return
     */
    public void calculation1(List<PrintObject> printObjectList, String productLevel, String mainMult) {
        String diseasePayAmount = null;
        String accidentPayAmount = null;
        if ("A".equals(productLevel)) {
            //疾病死亡保险金额
            //dataMap.put("diseasePayAmount2", 100);
            //dataMap.put("diseasePayAmount4", 500);
            //dataMap.put("diseasePayAmount6", 1000);
            diseasePayAmount = "0/100/500/1000";
            //B,D档返还部分保费
            //dataMap.put("returnPremium", 0);
            //意外死亡保障
            //dataMap.put("accidentPayAmount", 5000);
            accidentPayAmount = "5000";
            //继续投保优惠
            //dataMap.put("nextDiseasePayAmount", 1000);
        } else if ("B".equals(productLevel)) {
            //dataMap.put("diseasePayAmount2", 200);
            //dataMap.put("diseasePayAmount4", 1000);
            //dataMap.put("diseasePayAmount6", 2000);
            diseasePayAmount = "0/200/1000/2000";
            //dataMap.put("returnPremium", 0);
            //dataMap.put("accidentPayAmount", 10000);
            accidentPayAmount = "10000";
            //dataMap.put("nextDiseasePayAmount", 2000);
        } else if ("C".equals(productLevel)) {
            //dataMap.put("diseasePayAmount2", 400);
            //dataMap.put("diseasePayAmount4", 1000);
            //dataMap.put("diseasePayAmount6", 2000);
            diseasePayAmount = "0/400/2000/4000";
            //dataMap.put("returnPremium", 0);
            //dataMap.put("accidentPayAmount", 20000);
            accidentPayAmount = "20000";
            //dataMap.put("nextDiseasePayAmount", 4000);
        }


        if (AssertUtils.isNotEmpty(mainMult)) {
            if (AssertUtils.isNotEmpty(diseasePayAmount)) {
                String[] split = diseasePayAmount.split("/");
                String s1 = "";
                for (int i = 0; split.length > i; i++) {
                    String s = split[i];
                    if (i != 0) {
                        s1 += "/";
                    }
                    s1 += new BigDecimal(Double.parseDouble(s)).multiply(new BigDecimal(Double.parseDouble(mainMult))).toString();
                }
                diseasePayAmount = s1;
            }
            if (AssertUtils.isNotEmpty(accidentPayAmount)) {
                accidentPayAmount = new BigDecimal(Double.parseDouble(accidentPayAmount)).multiply(new BigDecimal(Double.parseDouble(mainMult))).toString();
            }
        }
        PrintCommon.setPrintData(printObjectList, "diseasePayAmount", diseasePayAmount, 3);
        PrintCommon.setPrintData(printObjectList, "accidentPayAmount", accidentPayAmount, 3);
    }

    /**
     * 一号产 保额
     *
     * @param map
     * @param productLevel
     * @return
     */
    public static void calculation1(Map<String, Object> map, String productLevel, String mult) {
        calculation1(map,null,productLevel,mult);
    }

    /**
     * 一号产 续保保额计算
     *
     * @param map
     * @param productLevel
     * @return
     */
    public static void calculation1(Map<String, Object> map, Long policyPeriod, String productLevel, String mult) {
        BigDecimal multBigDecimal = AssertUtils.isNotEmpty(mult) ? new BigDecimal(mult) : new BigDecimal(1);
        BigDecimal diseasePayAmount2 = new BigDecimal(0);
        BigDecimal diseasePayAmount4 = new BigDecimal(0);
        BigDecimal diseasePayAmount6 = new BigDecimal(0);
        BigDecimal accidentPayAmount = new BigDecimal(0);
        if ("A".equals(productLevel)) {
            //疾病死亡保险金额
            diseasePayAmount2 = multBigDecimal.multiply(new BigDecimal(100));
            diseasePayAmount4 = multBigDecimal.multiply(new BigDecimal(500));
            diseasePayAmount6 = multBigDecimal.multiply(new BigDecimal(1000));
            //意外死亡保障
            accidentPayAmount = multBigDecimal.multiply(new BigDecimal(5000));
        } else if ("B".equals(productLevel)) {
            //疾病死亡保险金额
            diseasePayAmount2 = multBigDecimal.multiply(new BigDecimal(200));
            diseasePayAmount4 = multBigDecimal.multiply(new BigDecimal(1000));
            diseasePayAmount6 = multBigDecimal.multiply(new BigDecimal(2000));
            //意外死亡保障
            accidentPayAmount = multBigDecimal.multiply(new BigDecimal(10000));
        } else if ("C".equals(productLevel)) {
            //疾病死亡保险金额
            diseasePayAmount2 = multBigDecimal.multiply(new BigDecimal(400));
            diseasePayAmount4 = multBigDecimal.multiply(new BigDecimal(2000));
            diseasePayAmount6 = multBigDecimal.multiply(new BigDecimal(4000));
            //意外死亡保障
            accidentPayAmount = multBigDecimal.multiply(new BigDecimal(20000));
        }
        BigDecimal renewalAmountRate = AssertUtils.isNotNull(policyPeriod) ?
                policyPeriod.equals(new Long(1)) ? new BigDecimal(0.1) :
                        policyPeriod.equals(new Long(2)) ? new BigDecimal(0.2) : new BigDecimal(0.3) :
                new BigDecimal(0);
        //疾病死亡保险金额
        diseasePayAmount2 = diseasePayAmount2.add(diseasePayAmount2.multiply(renewalAmountRate));
        diseasePayAmount4 = diseasePayAmount4.add(diseasePayAmount4.multiply(renewalAmountRate));
        diseasePayAmount6 = diseasePayAmount6.add(diseasePayAmount6.multiply(renewalAmountRate));
        //意外死亡保障
        accidentPayAmount = accidentPayAmount.add(accidentPayAmount.multiply(renewalAmountRate));
        //疾病死亡保险金额
        map.put("diseasePayAmount2", PrintCommon.getPrintString(diseasePayAmount2, 3));
        map.put("diseasePayAmount4", PrintCommon.getPrintString(diseasePayAmount4, 3));
        map.put("diseasePayAmount6", PrintCommon.getPrintString(diseasePayAmount6, 3));
        //意外死亡保障
        map.put("accidentPayAmount", PrintCommon.getPrintString(accidentPayAmount, 3));
        //疾病死亡保险金额
        map.put("diseasePayAmount2_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(diseasePayAmount2), 3));
        map.put("diseasePayAmount4_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(diseasePayAmount4), 3));
        map.put("diseasePayAmount6_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(diseasePayAmount6), 3));
        //意外死亡保障
        map.put("accidentPayAmount_1", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(accidentPayAmount), 3));
    }

}
