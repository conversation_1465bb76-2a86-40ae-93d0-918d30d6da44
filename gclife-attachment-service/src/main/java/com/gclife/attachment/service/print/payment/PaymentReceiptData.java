package com.gclife.attachment.service.print.payment;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintBo;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintCoverageBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:19 2018/12/14
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Component
public class PaymentReceiptData {
    public Map<String, Object> getPaymentReceiptData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> map = new HashMap<>();
        AttachmentPaymentPrintBo paymentPrintBo = JSON.parseObject(content, AttachmentPaymentPrintBo.class);
        if ("APPLY".equals(paymentPrintBo.getBusinessType())) {
            map.put("applyOrPolicyNoKM_KH", PrintCommon.getPrintString("លេខពាក្យស្នើសុំ", 3));
            map.put("applyOrPolicyNoEN_US", PrintCommon.getPrintString("(APPLICATION NO.)", 3));
        } else {
            map.put("applyOrPolicyNoKM_KH", PrintCommon.getPrintString("លេខបណ្ណសន្យារ៉ាប់រង", 3));
            map.put("applyOrPolicyNoEN_US", PrintCommon.getPrintString("(POLICY NO.)", 3));
        }
        map.put("businessNo", PrintCommon.getPrintString(paymentPrintBo.getBusinessNo(), 3));
        map.put("receiptNo", PrintCommon.getPrintString(paymentPrintBo.getReceiptNo(), 3));
        String printDate = null;
        if (AssertUtils.isNotNull(paymentPrintBo.getPrintDate())) {
            String[] k = new Date(paymentPrintBo.getPrintDate()).toString().split(" ");
            printDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        }
        map.put("printDate", PrintCommon.getPrintString(printDate, 3));

        map.put("applicantName", PrintCommon.getPrintString(paymentPrintBo.getApplicantName(), 3));
        map.put("paymentMethodName", PrintCommon.getPrintString(paymentPrintBo.getPaymentMethodName(), 3));
        map.put("installmentNo", PrintCommon.getPrintString(paymentPrintBo.getInstallmentNo(), 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(paymentPrintBo.getPremiumFrequencyName(), 3));

        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<AttachmentPaymentPrintCoverageBo> coverageBoList = paymentPrintBo.getPrintCoverages();

        // 获取主险数据
        Optional<AttachmentPaymentPrintCoverageBo> mainCoverageBoOptional = coverageBoList.stream()
                .filter(coverageBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                .findFirst();
        if (mainCoverageBoOptional.isPresent()) {
            AttachmentPaymentPrintCoverageBo mainPrintCoverageBo = mainCoverageBoOptional.get();
            String mainPremiumFrequency = mainPrintCoverageBo.getPremiumFrequency();
            int premiumPeriodNum = Integer.parseInt(AssertUtils.isNotEmpty(mainPrintCoverageBo.getPremiumPeriod()) ? mainPrintCoverageBo.getPremiumPeriod() : "0");
            // 20号产品 一年期的年缴视为 趸缴
            if ("PRO880000000000020".equals(mainPrintCoverageBo.getProductId())) {
                if (YEAR.name().equals(mainPremiumFrequency) && 1 == premiumPeriodNum) {
                    mainPremiumFrequency = "Single";
                    // 重新设置缴费周期
                    map.put("premiumFrequencyName", PrintCommon.getPrintString(mainPremiumFrequency, 3));
                }
            }
        }

        for (AttachmentPaymentPrintCoverageBo coverageBo : coverageBoList) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productLevel = AssertUtils.isNotEmpty(coverageBo.getDutyId()) ? coverageBo.getDutyId() : coverageBo.getProductLevel();
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), productLevel);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            coverageMap.put("paymentItem", PrintCommon.getPrintString(coverageBo.getPaymentItem(), 3));
            coverageMap.put("calculatePaymentAmount", PrintCommon.getPrintString(coverageBo.getCalculatePaymentAmount(), 3));
            coverageListMap.add(coverageMap);
        }

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);

        BigDecimal premiumBeforeDiscount = paymentPrintBo.getPremiumBeforeDiscount();

        String premiumBeforeDiscountString = PrintCommon.getPrintString(premiumBeforeDiscount, 3);
        BigDecimal specialDiscount = paymentPrintBo.getSpecialDiscount();
        if (AssertUtils.isNotNull(specialDiscount) && AssertUtils.isNotNull(premiumBeforeDiscount)) {
            map.put("specialDiscountPremium", PrintCommon.getPrintString(premiumBeforeDiscount.subtract(paymentPrintBo.getTotalPremium()), 3));
        } else {
            premiumBeforeDiscountString = PrintCommon.getPrintString(paymentPrintBo.getTotalPremium(), 3);
        }
        map.put("premiumBeforeDiscount", PrintCommon.getPrintString(premiumBeforeDiscountString, 3));

        String totalPremium = PrintCommon.getPrintString(paymentPrintBo.getTotalPremium(), 3);

        map.put("totalPremium", totalPremium);

        map.put("agentCode", PrintCommon.getPrintString(paymentPrintBo.getAgentCode(), 3));
        map.put("receivedBy", PrintCommon.getPrintString(paymentPrintBo.getReceivedBy(), 3));

        return map;
    }

}
