package com.gclife.attachment.certificate.impl;

import com.gclife.attachment.certificate.CertificateKey;
import com.gclife.attachment.certificate.CertificateService;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Date;

import javax.crypto.Cipher;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:数字签名，加密/解密工具包
 */

@Service
public class CertificateServiceImpl implements CertificateService {

    /**
     * Java密钥库(Java 密钥库，JKS)KEY_STORE
     */
    public static final String KEY_STORE = "JKS";

    public static final String X509 = "X.509";

    /**
     * 文件读取缓冲区大小
     */
    private static final int CACHE_SIZE = 2048;

    /**
     * 最大文件加密块
     */
    private static final int MAX_ENCRYPT_BLOCK = 240;

    /**
     * 最大文件解密块
     */
    private static final int MAX_DECRYPT_BLOCK = 256;

    /**
     * 根据密钥库获得私钥
     *
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    @Override
    public PrivateKey getPrivateKey(String keyStorePath, String alias, String password)
            throws Exception {
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        return (PrivateKey) keyStore.getKey(alias, password.toCharArray());
    }

    /**
     * 根据密钥库获得私钥
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    @Override
    public PrivateKey getPrivateKey(InputStream keyStorePath, String alias, String password)
            throws Exception {
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        return (PrivateKey) keyStore.getKey(alias, password.toCharArray());
    }

    /**
     * 根据密钥库获得证书链
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    @Override
    public Certificate[] getCertificateChain(InputStream keyStorePath, String alias, String password)
            throws Exception
    {
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        Certificate[] chain = keyStore.getCertificateChain(alias);
        return chain;
    }

    /**
     * 根据密钥库获得私钥和证书链
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return CertificateKey  私钥和证书链
     * @throws Exception
     */
    @Override
    public CertificateKey getCertificateKey(InputStream keyStorePath, String alias, String password) throws Exception {
        CertificateKey certificateKey = new CertificateKey();

        KeyStore keyStore = getKeyStore(keyStorePath, password);
        certificateKey.setPrivateKey ((PrivateKey) keyStore.getKey(alias, password.toCharArray()));
        certificateKey.setCertificateChain(keyStore.getCertificateChain(alias));
        return certificateKey;
    }

    /**
     * 获得密钥库
     *
     * @param keyStorePath 密钥库存储路径
     * @param password     密钥库密码
     * @return KeyStore 密钥库
     * @throws Exception
     */
    @Override
    public KeyStore getKeyStore(String keyStorePath, String password)
            throws Exception {
        FileInputStream in = new FileInputStream(keyStorePath);
        KeyStore keyStore = KeyStore.getInstance(KEY_STORE);
        keyStore.load(in, password.toCharArray());
        in.close();
        return keyStore;
    }

    /**
     * 获得密钥库
     *
     * @param keyStorePath 密钥库流
     * @param password     密钥库密码
     * @return KeyStore 密钥库
     * @throws Exception
     */
    private KeyStore getKeyStore(InputStream keyStorePath, String password)
            throws Exception {
        KeyStore keyStore = KeyStore.getInstance(KEY_STORE);
        keyStore.load(keyStorePath, password.toCharArray());
        return keyStore;
    }

    /**
     * 根据证书获得公钥
     *
     * @param certificatePath 证书存储路径
     * @return PublicKey 公钥
     * @throws Exception
     */
    private PublicKey getPublicKey(String certificatePath)
            throws Exception {
        Certificate certificate = getCertificate(certificatePath);
        return certificate.getPublicKey();
    }

    /**
     * 根据证书获得公钥
     *
     * @param inputStream 证书文件流
     * @return PublicKey 公钥
     * @throws Exception
     */
    private PublicKey getPublicKey(InputStream inputStream)
            throws Exception {
        Certificate certificate = getCertificate(inputStream);
        return certificate.getPublicKey();
    }

    /**
     * 获得证书
     *
     * @param certificatePath 证书存储路径
     * @return Certificate 证书
     * @throws Exception
     */
    private Certificate getCertificate(String certificatePath)
            throws Exception {
        CertificateFactory certificateFactory = CertificateFactory.getInstance(X509);
        FileInputStream in = new FileInputStream(certificatePath);
        Certificate certificate = certificateFactory.generateCertificate(in);
        in.close();
        return certificate;
    }

    /**
     * 获得证书
     *
     * @param in 文件流
     * @return Certificate 证书
     * @throws Exception
     */
    private Certificate getCertificate(InputStream in)
            throws Exception {
        CertificateFactory certificateFactory = CertificateFactory.getInstance(X509);
        Certificate certificate = certificateFactory.generateCertificate(in);
        return certificate;
    }

    /**
     * 根据密钥库获得证书
     *
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return Certificate 证书
     * @throws Exception
     */
    private Certificate getCertificate(String keyStorePath, String alias, String password)
            throws Exception {
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        return keyStore.getCertificate(alias);
    }

    /**
     * 私钥加密
     *
     * @param data         源数据
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    @Override
    public byte[] encryptByPrivateKey(byte[] data, String keyStorePath, String alias, String password)
            throws Exception {
        // 取得私钥
        PrivateKey privateKey = getPrivateKey(keyStorePath, alias, password);
        Cipher cipher = Cipher.getInstance(privateKey.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    /**
     * 文件私钥加密，过大的文件可能会导致内存溢出
     *
     * @param filePath     文件路径
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    @Override
    public byte[] encryptFileByPrivateKey(String filePath, String keyStorePath, String alias, String password)
            throws Exception {
        byte[] data = fileToByte(filePath);
        return encryptByPrivateKey(data, keyStorePath, alias, password);
    }

    /**
     * 文件加密
     *
     * @param srcFilePath  源文件
     * @param destFilePath 加密后文件
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @throws Exception
     */
    @Override
    public void encryptFileByPrivateKey(String srcFilePath, String destFilePath, String keyStorePath, String alias, String password)
            throws Exception {
        byte[] encryptedData = encryptFileByPrivateKey(srcFilePath, keyStorePath, alias, password);
        Files.write(Paths.get(destFilePath), encryptedData);
    }

    /**
     * 公钥加密
     *
     * @param data            源数据
     * @param certificatePath 证书存储路径
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    @Override
    public byte[] encryptByPublicKey(byte[] data, String certificatePath)
            throws Exception {
        // 取得公钥
        PublicKey publicKey = getPublicKey(certificatePath);
        Cipher cipher = Cipher.getInstance(publicKey.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }


    /**
     * 公钥加密
     *
     * @param data            源数据
     * @param certificateInputStream 证书文件流
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    @Override
    public byte[] encryptByPublicKey(byte[] data, InputStream certificateInputStream)
            throws Exception {
        // 取得公钥
        PublicKey publicKey = getPublicKey(certificateInputStream);
        Cipher cipher = Cipher.getInstance(publicKey.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }


    /**
     * 公钥加密
     *
     * @param filePath        文件路径
     * @param certificatePath 证书路径
     * @return byte[] 加密数据
     */
    @Override
    public byte[] encryptFileByPublicKey(String filePath, String certificatePath)
            throws Exception {
        byte[] data = fileToByte(filePath);
        return encryptByPublicKey(data, certificatePath);
    }

    /**
     * 公钥加密
     *
     * @param srcFilePath     源文件路径
     * @param destFilePath    目标文件路径
     * @param certificatePath 证书路径
     * @return
     */
    @Override
    public void encryptFileByPublicKey(String srcFilePath, String destFilePath, String certificatePath)
            throws Exception {
        byte[] data = encryptFileByPublicKey(srcFilePath, certificatePath);
        Files.write(Paths.get(destFilePath), data);
    }

    /**
     * 私钥解密
     *
     * @param encryptedData 已加密数据
     * @param keyStorePath  密钥库存储路径
     * @param alias         密钥库别名
     * @param password      密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    @Override
    public byte[] decryptByPrivateKey(byte[] encryptedData, String keyStorePath, String alias, String password)
            throws Exception {
        // 取得私钥
        PrivateKey privateKey = getPrivateKey(keyStorePath, alias, password);
        Cipher cipher = Cipher.getInstance(privateKey.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        // 解密byte数组最大长度限制: 256
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * 私钥解密
     *
     * @param encryptedData 已加密数据
     * @param keyStorePath  密钥库流
     * @param alias         密钥库别名
     * @param password      密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    @Override
    public byte[] decryptByPrivateKey(byte[] encryptedData, InputStream keyStorePath, String alias, String password)
            throws Exception {
        // 取得私钥
        PrivateKey privateKey = getPrivateKey(keyStorePath, alias, password);
        Cipher cipher = Cipher.getInstance(privateKey.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        // 解密byte数组最大长度限制: 256
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * 私钥解密
     *
     * @param encryptedFilePath 已加密文件路径
     * @param keyStorePath      密钥库存储路径
     * @param alias             密钥库别名
     * @param password          密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    @Override
    public byte[] decryptFileByPrivateKey(String encryptedFilePath, String keyStorePath, String alias, String password)
            throws Exception {
        byte[] data = fileToByte(encryptedFilePath);

        return decryptByPrivateKey(data, keyStorePath, alias, password);
    }

    /**
     * 私钥解密
     *
     * @param encryptedFilePath 已加密文件路径
     * @param keyStorePath      密钥库存储路径
     * @param alias             密钥库别名
     * @param password          密钥库密码
     * @return
     * @throws Exception
     */
    @Override
    public void decryptFileByPrivateKey(String encryptedFilePath, String decryptedFilePath, String keyStorePath, String alias, String password)
            throws Exception {
        byte[] data = decryptFileByPrivateKey(encryptedFilePath, keyStorePath, alias, password);

        Files.write(Paths.get(decryptedFilePath), data);
    }

    /**
     * 公钥解密
     *
     * @param encryptedData   已加密数据
     * @param certificatePath 证书存储路径
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    @Override
    public byte[] decryptByPublicKey(byte[] encryptedData, String certificatePath)
            throws Exception {
        PublicKey publicKey = getPublicKey(certificatePath);
        Cipher cipher = Cipher.getInstance(publicKey.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * 文件解密
     *
     * @param encryptedFilePath 已加密文件
     * @param certificatePath   证书存储路径
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    @Override
    public byte[] decryptFileByPublicKey(String encryptedFilePath, String certificatePath)
            throws Exception {

        byte[] data = fileToByte(encryptedFilePath);

        return decryptByPublicKey(data, certificatePath);
    }

    /**
     * 文件解密
     *
     * @param encryptedFilePath 源文件
     * @param decryptedFilePath 目标文件
     * @param certificatePath   证书存储路径
     * @throws Exception
     */
    @Override
    public void decryptFileByPublicKey(String encryptedFilePath, String decryptedFilePath, String certificatePath)
            throws Exception {
        byte[] data = decryptFileByPublicKey(encryptedFilePath, certificatePath);

        Files.write(Paths.get(decryptedFilePath), data);
    }

    /**
     * 生成数据签名
     *
     * @param data         源数据
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 数据签名字节数组
     * @throws Exception
     */
    @Override
    public byte[] sign(byte[] data, String keyStorePath, String alias, String password)
            throws Exception {
        // 获得证书
        X509Certificate x509Certificate = (X509Certificate) getCertificate(keyStorePath, alias, password);
        // 获取私钥
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        // 取得私钥
        PrivateKey privateKey = (PrivateKey) keyStore.getKey(alias, password.toCharArray());
        // 构建签名
        Signature signature = Signature.getInstance(x509Certificate.getSigAlgName());
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }

    /**
     * 获取签名算法
     *
     * @param keyStorePath　密钥库路径
     * @param alias　别名
     * @param password　密码
     * @return String 签名算法
     */
    @Override
    public String getSignAlgName(String keyStorePath, String alias, String password)
            throws Exception
    {
        // 获得证书
        X509Certificate x509Certificate = (X509Certificate) getCertificate(keyStorePath, alias, password);
        // 获取私钥

        return x509Certificate.getSigAlgName();
    }

    /**
     * 生成签名时update的byte数组大小和验证签名时的大小应相同，否则验证无法通过
     *
     * @param filePath
     * @param keyStorePath
     * @param alias
     * @param password
     * @return byte[] 签名后的指纹
     */
    @Override
    public byte[] signFile(String filePath, String keyStorePath, String alias, String password)
            throws Exception {
        byte[] sign = new byte[0];
        // 获得证书
        X509Certificate x509Certificate = (X509Certificate) getCertificate(keyStorePath, alias, password);
        // 获取私钥
        KeyStore keyStore = getKeyStore(keyStorePath, password);
        // 取得私钥
        PrivateKey privateKey = (PrivateKey) keyStore.getKey(alias, password.toCharArray());
        // 构建签名
        Signature signature = Signature.getInstance(x509Certificate.getSigAlgName());
        signature.initSign(privateKey);
        File file = new File(filePath);
        if (file.exists()) {
            FileInputStream in = new FileInputStream(file);
            byte[] cache = new byte[CACHE_SIZE];
            int nRead = 0;
            while ((nRead = in.read(cache)) != -1) {
                signature.update(cache, 0, nRead);
            }
            in.close();
            sign = signature.sign();
        }
        return sign;
    }

    /**
     * 校验文件签名
     *
     * @param filePath        文件路径
     * @param sign            　签名字节数组
     * @param certificatePath 证书路径
     * @return boolean 是否有效
     * @throws Exception
     */
    @Override
    public boolean verifyFileSign(String filePath, byte[] sign, String certificatePath)
            throws Exception {
        boolean result = false;
        // 获得证书
        X509Certificate x509Certificate = (X509Certificate) getCertificate(certificatePath);
        // 获得公钥
        PublicKey publicKey = x509Certificate.getPublicKey();
        // 构建签名
        Signature signature = Signature.getInstance(x509Certificate.getSigAlgName());
        signature.initVerify(publicKey);
        File file = new File(filePath);
        if (file.exists()) {
            FileInputStream in = new FileInputStream(file);
            byte[] cache = new byte[CACHE_SIZE];
            int nRead = 0;
            while ((nRead = in.read(cache)) != -1) {
                signature.update(cache, 0, nRead);
            }
            in.close();
            result = signature.verify(sign);
        }
        return result;
    }

    /**
     * 校验证书当前是否有效
     *
     * @param certificate 证书
     * @return boolean 是否有效
     */
    public boolean verifyCertificate(Certificate certificate) {
        return verifyCertificate(new Date(), certificate);
    }

    /**
     * 验证证书是否过期或无效
     *
     * @param date        日期
     * @param certificate 证书
     * @return 是否有效
     */
    public boolean verifyCertificate(Date date, Certificate certificate) {
        boolean isValid = true;
        try {
            X509Certificate x509Certificate = (X509Certificate) certificate;
            x509Certificate.checkValidity(date);
        } catch (Exception e) {
            isValid = false;
        }
        return isValid;
    }

    /**
     * 验证数字证书是在给定的日期是否有效
     *
     * @param date            日期
     * @param certificatePath 证书存储路径
     * @return boolean 是否有效
     */
    public boolean verifyCertificate(Date date, String certificatePath) {
        Certificate certificate;
        try {
            certificate = getCertificate(certificatePath);
            return verifyCertificate(certificate);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 验证数字证书是在给定的日期是否有效
     *
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return boolean 是否有效
     */
    public boolean verifyCertificate(Date date, String keyStorePath, String alias, String password) {
        Certificate certificate;
        try {
            certificate = getCertificate(keyStorePath, alias, password);
            return verifyCertificate(certificate);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 验证数字证书当前是否有效
     *
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return 是否有效
     */
    public boolean verifyCertificate(String keyStorePath, String alias, String password) {
        return verifyCertificate(new Date(), keyStorePath, alias, password);
    }

    /**
     * 验证数字证书当前是否有效
     *
     * @param certificatePath 证书存储路径
     * @return boolean 是否有效
     */
    public boolean verifyCertificate(String certificatePath) {
        return verifyCertificate(new Date(), certificatePath);
    }

    /**
     * 文件转换为byte数组
     *
     * @param filePath 文件路径
     * @return byte[] 二进制字节数组
     * @throws Exception
     */
    public byte[] fileToByte(String filePath) throws Exception {
        byte[] data = new byte[0];
        File file = new File(filePath);
        if (file.exists()) {
            data = Files.readAllBytes(Paths.get(filePath));
        }
        return data;
    }
}