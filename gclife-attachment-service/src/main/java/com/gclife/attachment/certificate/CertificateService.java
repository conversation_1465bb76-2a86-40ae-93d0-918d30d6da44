package com.gclife.attachment.certificate;


import java.io.InputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;

/**
 * <AUTHOR>
 * create 17-10-19
 * description: 数字签名，加密/解密工具包
 */

public interface CertificateService {

    /**
     * 根据密钥库获得私钥
     *
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    public PrivateKey getPrivateKey(String keyStorePath, String alias, String password)
            throws Exception;

    /**
     * 根据密钥库获得所有证书
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    public Certificate[] getCertificateChain(InputStream keyStorePath, String alias, String password)
            throws Exception;

    /**
     * 根据密钥库获得私钥
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return PrivateKey  私钥
     * @throws Exception
     */
    public PrivateKey getPrivateKey(InputStream keyStorePath, String alias, String password)
            throws Exception;

    /**
     * 根据密钥库获得私钥和证书链
     *
     * @param keyStorePath 密钥库流
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return CertificateKey  私钥和证书链
     * @throws Exception
     */
    public CertificateKey getCertificateKey(InputStream keyStorePath, String alias, String password)
        throws Exception;

    /**
     * 获得密钥库
     *
     * @param keyStorePath 密钥库存储路径
     * @param password     密钥库密码
     * @return KeyStore 密钥库
     * @throws Exception
     */
    public KeyStore getKeyStore(String keyStorePath, String password)
            throws Exception;

    /**
     * 获取签名算法
     *
     * @param keyStorePath　密钥库路径
     * @param alias　别名
     * @param password　密码
     * @return String 签名算法
     */
    public String getSignAlgName(String keyStorePath, String alias, String password)
            throws Exception;

    /**
     * 私钥加密
     *
     * @param data         源数据
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    byte[] encryptByPrivateKey(byte[] data, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 文件私钥加密，过大的文件可能会导致内存溢出
     *
     * @param filePath     文件路径
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    byte[] encryptFileByPrivateKey(String filePath, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 文件加密
     *
     * @param srcFilePath  源文件
     * @param destFilePath 加密后文件
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @throws Exception
     */
    void encryptFileByPrivateKey(String srcFilePath, String destFilePath, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 公钥加密
     *
     * @param data            源数据
     * @param certificatePath 证书存储路径
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    byte[] encryptByPublicKey(byte[] data, String certificatePath) throws Exception;

    /**
     * 公钥加密
     *
     * @param data                   源数据
     * @param certificateInputStream 证书文件流
     * @return byte[] 加密后的字节数组
     * @throws Exception
     */
    byte[] encryptByPublicKey(byte[] data, InputStream certificateInputStream) throws Exception;

    /**
     * 公钥加密
     *
     * @param filePath        文件路径
     * @param certificatePath 证书路径
     * @return byte[] 加密数据
     * @throws Exception
     */
    byte[] encryptFileByPublicKey(String filePath, String certificatePath) throws Exception;

    /**
     * 公钥加密
     *
     * @param srcFilePath     源文件路径
     * @param destFilePath    目标文件路径
     * @param certificatePath 证书路径
     * @return
     * @throws Exception
     */
    void encryptFileByPublicKey(String srcFilePath, String destFilePath, String certificatePath) throws Exception;

    /**
     * 私钥解密
     *
     * @param encryptedData 已加密数据
     * @param keyStorePath  密钥库存储路径
     * @param alias         密钥库别名
     * @param password      密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    byte[] decryptByPrivateKey(byte[] encryptedData, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 私钥解密
     *
     * @param encryptedFilePath 已加密文件路径
     * @param keyStorePath      密钥库存储路径
     * @param alias             密钥库别名
     * @param password          密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    byte[] decryptFileByPrivateKey(String encryptedFilePath, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 私钥解密
     *
     * @param encryptedData 已加密数据
     * @param keyStorePath  密钥库流
     * @param alias         密钥库别名
     * @param password      密钥库密码
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    public byte[] decryptByPrivateKey(byte[] encryptedData, InputStream keyStorePath, String alias, String password) throws Exception;

    /**
     * 私钥解密
     *
     * @param encryptedFilePath 已加密文件路径
     * @param decryptedFilePath 解密后文件路径
     * @param keyStorePath      密钥库存储路径
     * @param alias             密钥库别名
     * @param password          密钥库密码
     * @return
     * @throws Exception
     */
    void decryptFileByPrivateKey(String encryptedFilePath, String decryptedFilePath, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 公钥解密
     *
     * @param encryptedData   已加密数据
     * @param certificatePath 证书存储路径
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    byte[] decryptByPublicKey(byte[] encryptedData, String certificatePath) throws Exception;

    /**
     * 文件解密
     *
     * @param encryptedFilePath 已加密文件
     * @param certificatePath   证书存储路径
     * @return byte[] 已解密的字节数组
     * @throws Exception
     */
    byte[] decryptFileByPublicKey(String encryptedFilePath, String certificatePath) throws Exception;

    /**
     * 文件解密
     *
     * @param encryptedFilePath 源文件
     * @param decryptedFilePath 目标文件
     * @param certificatePath   证书存储路径
     * @throws Exception
     */
    void decryptFileByPublicKey(String encryptedFilePath, String decryptedFilePath, String certificatePath) throws Exception;

    /**
     * 生成数据签名
     *
     * @param data         源数据
     * @param keyStorePath 密钥库存储路径
     * @param alias        密钥库别名
     * @param password     密钥库密码
     * @return byte[] 数据签名字节数组
     * @throws Exception
     */
    byte[] sign(byte[] data, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 生成签名时update的byte数组大小和验证签名时的大小应相同，否则验证无法通过
     *
     * @param filePath
     * @param keyStorePath
     * @param alias
     * @param password
     * @return byte[] 签名结果字节数组
     * @throws Exception
     */
    byte[] signFile(String filePath, String keyStorePath, String alias, String password) throws Exception;

    /**
     * 校验文件签名
     *
     * @param filePath        文件路径
     * @param sign            　签名字节数组
     * @param certificatePath 证书路径
     * @return boolean 是否有效
     * @throws Exception
     */
    boolean verifyFileSign(String filePath, byte[] sign, String certificatePath) throws Exception;
}
