package com.gclife.attachment.certificate;

import java.security.PrivateKey;
import java.security.cert.Certificate;

/**
 * <AUTHOR>
 * create 18-1-5
 * description: 数字证书私钥和证书链
 */

public class CertificateKey {
    /**
     * 私钥
     */
    PrivateKey privateKey;
    /**
     * 证书链
     */
    Certificate[] certificateChain;

    public PrivateKey getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(PrivateKey privateKey) {
        this.privateKey = privateKey;
    }

    public Certificate[] getCertificateChain() {
        return certificateChain;
    }

    public void setCertificateChain(Certificate[] certificateChain) {
        this.certificateChain = certificateChain;
    }
}
