package com.gclife.attachment.validate.parameter;

import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:　attachment参数校验类
 */
@Component
public class AttachmentParameterValidate {

    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentParameterValidate.class);

    /**
     * UploadMediaBase64参数验证
     *
     * @param type              附件类型，如IMAGE
     * @param attachmentRequest 附件上传请求
     * @throws RequestException
     */
    public void validParameterUploadMediaBase64(String type, AttachmentRequest attachmentRequest) throws RequestException {

        AssertUtils.isNotEmpty(LOGGER, attachmentRequest.getFileSuffix(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_FILESUFFEX_IS_NOT_NULL);

        //AssertUtils.isNotEmpty(LOGGER,attachmentRequest.getFileName(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_FILENAME_IS_NOT_NULL);

        AssertUtils.isNotEmpty(LOGGER, attachmentRequest.getFileContent(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_FILECONTENT_IS_NOT_NULL);
        //判断分组类型是否一致
        try {
            AttachmentTermEnum.MEDIA_TYPE_SUFFIX mediaTypeSuffix = AttachmentTermEnum.MEDIA_TYPE_SUFFIX.valueOf(attachmentRequest.getFileSuffix().toUpperCase());
            Optional.ofNullable(mediaTypeSuffix).ifPresent((value -> {
                if (!type.equals(value.group().toUpperCase())) {
                    throw new RequestException();
                }
            }));
        } catch (Exception e) {
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_TYPE_NOT_MATCH_FILE_TYPE_FORMAT_ERROR);
        }
    }

    public static void main(String[] args) {
        AttachmentTermEnum.MEDIA_TYPE_SUFFIX mediaTypeSuffix = AttachmentTermEnum.MEDIA_TYPE_SUFFIX.valueOf("JPG");
        System.out.println(mediaTypeSuffix);
    }

    /**
     * UploadMediaBatchBase64参数验证
     *
     * @param type               附件类型，如IMAGE
     * @param attachmentRequests 附件批量上传请求
     * @throws RequestException
     */
    public void validParameterUploadMediaBatchBase64(String type, List<AttachmentRequest> attachmentRequests) throws RequestException {
        AssertUtils.isNotEmpty(LOGGER, type, AttachmentErrorConfigEnum.CERTIFY_PARAMETER_ATTACHMENT_TYPE_IS_NOT_NULL);
        if (Arrays.stream(AttachmentTermEnum.MEDIA_TYPE.values()).noneMatch(e -> e.name().equals(type))) {
            throw new RequestException(AttachmentErrorConfigEnum.CERTIFY_PARAMETER_ATTACHMENT_TYPE_IS_NOT_FOUND_VALUE);
        }
        //循环验证
        attachmentRequests.stream().map(e -> {
            this.validParameterUploadMediaBase64(type, e);
            return null;
        }).collect(Collectors.toList());

    }

    /**
     * ElectronicPolicyGenerator参数验证
     *
     * @param electronicPolicyGeneratorRequest 电子保单生成请求
     * @throws RequestException
     */
    public void validParameterElectronicPolicyGenerator(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws RequestException {
        AssertUtils.isNotEmpty(LOGGER, electronicPolicyGeneratorRequest.getPdfType(), AttachmentErrorConfigEnum.ATTACHMENT_PARAMETER_PDF_TYPE_IS_NOT_NULL);
    }


}
