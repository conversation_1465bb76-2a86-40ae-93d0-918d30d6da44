package com.gclife.attachment.validate.transform;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.common.AttachmentCommonBusinessService;
import com.gclife.attachment.service.data.AttachmentService;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ByteToInputStream;
import com.gclife.common.util.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 2024/3/5 13:25
 * description:
 */
@Component
@Slf4j
public class PdfTransData {
    @Value("${external_domain_suffix:.aliyuncs.com}")
    private String externalDomainSuffix;
    @Value("${protocol:http://}")
    private String protocol;
    @Autowired
    private OssConfigExtDao ossConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private AttachmentExtDao attachmentExtDao;
    @Autowired
    private AttachmentCommonBusinessService attachmentCommonBusinessService;
    @Autowired
    private AttachmentService attachmentService;
    @Autowired
    private ConfigurableEnvironment env;

    /**
     * PDF转图片
     *
     * @param attachmentId
     */
    public void pdf2Image(String attachmentId) throws Exception {
        log.info("pdf转图片开始--attachmentId: {}", attachmentId);
        if (AssertUtils.isNotNull(attachmentId)) {
            // 获取配置
            OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
            AssertUtils.isNotNull(log, ossConfigBo, AttachmentErrorConfigEnum.ATTACHMENT_BUSINESS_OSS_ERROR);

            // 获取要转成图片的pdf文件字节数组
            byte[] pdfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(attachmentId);
            AttachmentPo attachmentPo = attachmentExtDao.getAttachmentPoByPk(attachmentId);
            AssertUtils.isNotNull(log, attachmentPo, AttachmentErrorConfigEnum.ATTACHMENT_QUERY_ATTACHMENT_ERROR);
            String dynamicDomain = env.getProperty("oss_config.domain." + attachmentPo.getAttachmentDomain());
            List<String> pdfImageUrls = new ArrayList<>();

            // 创建OSSClient
            OSSClient ossClient = new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                    env.getProperty("oss_user.access_key_id." + ossConfigBo.getAccessKeyId()),
                    env.getProperty("oss_user.access_key_secret." + ossConfigBo.getAccessKeySecret()));
            // 创建bucket
            String bucket = env.getProperty("oss_config.bucket." + ossConfigBo.getBucket());
            if (!ossClient.doesBucketExist(bucket)) {
                ossClient.createBucket(bucket);
            }

            try (PDDocument document = PDDocument.load(pdfBytes)) {
                PDFRenderer renderer = new PDFRenderer(document);
                int pages = document.getNumberOfPages();
                log.info("PDF转图片 PDF总页数: {}", pages);
                // 有多少页就需要countdown几次
                for (int i = 0; i < pages; ++i) {
                    // 此方法第二个参数设置pdf转图片的精度 数值越大越清晰，占用的内存就越大
                    BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 100);

                    ByteArrayOutputStream imageOutputStream = new ByteArrayOutputStream();
                    // pdf转图片
                    ImageIO.write(bufferedImage, "JPEG", imageOutputStream);

                    // 时间戳改成uuid
                    String saveKey = attachmentCommonBusinessService.getImageSaveOssKey(ossConfigBo,
                            AttachmentTermEnum.MEDIA_TYPE.PDF2IMAGE.name(),
                            UUIDUtils.getUUIDShort() + ".jpg");
                    InputStream inputStream = ByteToInputStream.byte2Input(imageOutputStream.toByteArray());

                    // 上传文件到阿里云OSS
                    ossClient.putObject(bucket, saveKey, inputStream);

                    pdfImageUrls.add(dynamicDomain + saveKey);
                }
            } finally {
                if (AssertUtils.isNotNull(ossClient)) {
                    ossClient.shutdown();
                }
            }

            String jsonImageUrls = JSONObject.toJSONString(pdfImageUrls);
            // 保存图片路径且更新附件记录信息
            attachmentPo.setPdfTransformImageUrl(jsonImageUrls);
            attachmentService.saveAttachmentPo(attachmentPo);
        }
        log.info("PDF转图片完成--attachmentId: {}", attachmentId);
    }
}
