package com.gclife.attachment.service.print.policy;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.apache.commons.beanutils.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/29
 */
public class GroupInsuranceBenefitsSummaryDataTest extends BaseBusinessServiceImpl {

    // public static final String FILE_NAME = "PRODUCT_17_APPLY_ZH_CN";
    // public static final String FILE_NAME = "PRODUCT_17_APPLY_EN_US_KM_KH";
    public static final String FILE_NAME = "PRODUCT_17_POLICY_ZH_CN";

    /*
    @Test
    public void test713() throws Exception {
        FOProcessor processor = new FOProcessor();

        File rtfPath = new File("C:\\Users\\<USER>\\Desktop\\Documents\\RTF" + FILE_NAME + ".rtf");
        File pdfPath = new File("C:\\Users\\<USER>\\Desktop\\Documents\\RTF" + FILE_NAME + ".pdf");
        try (InputStream rtfInputStream = new FileInputStream(rtfPath);
             FileOutputStream pdfOutputStream = new FileOutputStream(pdfPath)) {

            // Data
            Document document = XmlUtil.map2xml(getData(), "root");
            byte[] xmlBytes = XmlUtil.formatXml(document);
            InputStream xmlInputStream = new ByteArrayInputStream(xmlBytes);
            processor.setData(xmlInputStream);     // set XML input file

            // Template
            byte[] rtfTemplateBytes = new byte[rtfInputStream.available()];
            rtfInputStream.read(rtfTemplateBytes);
            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(rtfTemplateBytes);
            //读取 rtf 模版 转为 xsl模版
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            RTFProcessor rtfProcessor = new RTFProcessor(byteArrayInputStream); //input template
            rtfProcessor.setOutput(byteArrayOutputStream);  // output file
            rtfProcessor.process();
            InputStream rtf = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            processor.setTemplate(rtf); // set XSL input file

            // Output
            final ByteArrayOutputStream byteArrayOutputStream1 = new ByteArrayOutputStream();
            processor.setOutput(byteArrayOutputStream1);  //set output file
            processor.setOutputFormat(FOProcessor.FORMAT_PDF);
            processor.generate();

            final byte[] rtfBytes = byteArrayOutputStream1.toByteArray();

            FileConversionRequest fileConversionRequest = new FileConversionRequest();
            fileConversionRequest.setByteFile(rtfBytes);

            byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
            pdfOutputStream.write(forObject);
            pdfOutputStream.flush();
        }
    }

    @Test
    public void rtfToPdf() throws Exception {

        Document document = XmlUtil.map2xml(getData(), "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream("C:\\Users\\<USER>\\Desktop\\Documents\\RTF\\" + FILE_NAME + ".rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = OracleBiUtils.refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);

        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();

        FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\" + FILE_NAME + ".pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();

    }
    */

    /**
     * 设置模板填充数据
     *
     * @return
     */
    private Map<String, Object> getData() throws Exception {
        HashMap<String, Object> resultData = new HashMap<>();

        ApplyBo applyBo = getApplyBo();
        resultData.putAll(BeanUtils.describe(applyBo));
        // agent
        resultData.put("agentCode", "200017");
        resultData.put("agentName", "CHHUON NORYOU ENG");
        // resultData.put("agentName", "ធានារ៉ាប់រងបន្ថែមសម្រាប់ប្រាក់BaiZhong$Yingឧបត្ថម្ភសម្រាក白忠英នៅមន្ទីរពេទ្យដោយគ្រោះថ្នាក់ជាយថាហេតុ");
        resultData.put("agentMobil", "18334797813");
        // insuredCompany
        resultData.put("insuredCompanyName", "ស។白 Test Data");
        resultData.put("insuredTaxRegistrationNo", "ស។白 Test Data");
        resultData.put("insuredCompanyType", "ស។白 Test Data");
        resultData.put("insuredCompanyAddress", "ស។白 Test Data");
        resultData.put("insuredCompanyPhone", "ស។白 Test Data");
        resultData.put("insuredCompanyMobil", "ស។白 Test Data");
        resultData.put("insuredCompanyEmail", "ស។白 Test Data");
        // insuredCompanyContract
        resultData.put("insuredCompanyContractName", "ស។白 Test Data");
        resultData.put("insuredCompanyContractPosition", "ស។白 Test Data");
        resultData.put("insuredCompanyContractMobil", "ស។白 Test Data");
        resultData.put("insuredCompanyContractPhone", "ស។白 Test Data");
        resultData.put("insuredCompanyContractEmail", "ស។白 Test Data");
        // mainInsuredSum
        resultData.put("mainInsuredSum", 69);
        resultData.put("insuredSum", 7);
        resultData.put("totalSum", 169);
        // SALARY POSITION FIXED YEARS OTHER
        resultData.put("InsuranceAmountCalculation", "OTHER");
        resultData.put("otherRemark", "ស។白 Test Data");
        // photo
        resultData.put("testPhoto", "https://gclife-hk-dev.oss-cn-hongkong.aliyuncs.com/test41.jpg");

        PrintCommon.setPrintDateTime(resultData, "effectiveDate", 1571709625000L, 3);
        PrintCommon.setPrintDateTime(resultData, "maturityDate", 1603385999999L, 3);
        resultData.put("companyName", PrintCommon.getPrintString("ស។白 Test Data", 3));

        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        Map<String, Object> insuredMap1 = new HashMap<>();
        insuredMap1.put("insuredNo", 1);
        insuredMap1.put("name", "ស។白 Test Data");
        insuredMap1.put("birthdayZH_CN", 1);
        insuredMap1.put("sexName", 1);
        insuredMap1.put("ageYear", 1);
        insuredMap1.put("illnessOrAccidentAmount", 1);
        insuredMap1.put("onlyAccidentAmount", 1);
        insuredMap1.put("additionalPlan", 1);
        Map<String, Object> insuredMap2 = new HashMap<>();
        insuredMap2.put("insuredNo", 2);
        insuredMap2.put("name", "ស។白 Test Data");
        insuredMap2.put("birthdayZH_CN", 2);
        insuredMap2.put("sexName", 2);
        insuredMap2.put("ageYear", 2);
        insuredMap2.put("illnessOrAccidentAmount", 2);
        insuredMap2.put("onlyAccidentAmount", 2);
        insuredMap2.put("additionalPlan", 2);
        insuredMapList.add(insuredMap1);
        insuredMapList.add(insuredMap2);
        resultData.put("insuredMapList", insuredMapList);
        resultData.put("illnessOrAccidentTotalAmount", formatTheAmount(100000L));
        resultData.put("onlyAccidentTotalAmount", formatTheAmount(2000000000000000000L));
        resultData.put("totalAmount", formatTheAmount(300000L));
        resultData.put("illnessOrAccidentTotalPremium", formatTheAmount(400000L));
        resultData.put("onlyAccidentTotalPremium", formatTheAmount(500000L));
        resultData.put("additionalPlanTotalPremium", formatTheAmount(600000L));
        resultData.put("totalPremium", formatTheAmount(700000L));

        return resultData;
    }

    /**
     * 金额加逗号，每三位数加一个逗号
     *
     * @param amount
     * @return
     */
    private String formatTheAmount(Long amount) {
        return String.format("%,d", amount);
    }


    private ApplyBo getApplyBo() {
        ApplyBo applyBo = new ApplyBo();
        applyBo.setApplyId("8d7e355ddd974f5ab3e7bebbbfc1ce89");
        applyBo.setApplyNo("A1609235258823401");
        applyBo.setAcceptBranchId(null);
        applyBo.setSalesBranchId("GMA101101");
        applyBo.setManagerBranchId("GMM101004");
        applyBo.setChannelTypeCode("AGENT");
        applyBo.setApplyStatus("APPLY_STATUS_APPROVE_SUCCESS");
        applyBo.setCertifyId(null);
        applyBo.setCertifyClassId(null);
        applyBo.setSignedDate(null);
        applyBo.setApplyDate(1609235258809L);
        applyBo.setRepealDate(null);
        applyBo.setPremiumFrequency(null);
        applyBo.setPaymentMode("BANK_TRANSFER");
        applyBo.setInitialPaymentMode("BANK_TRANSFER");
        applyBo.setSurvivalPayType(null);
        applyBo.setApplySource("AGENT_INPUT");
        applyBo.setAutoPaymentOption(null);
        applyBo.setCurrencyCode("USD");
        applyBo.setReceivablePremium(new BigDecimal("346.00"));
        applyBo.setReceivableAddPremium(new BigDecimal("0.00"));
        applyBo.setFillInPremium(null);
        applyBo.setInureImmeInvest(null);
        applyBo.setOverflowToNext(null);
        applyBo.setSpecialTerm(null);
        applyBo.setApplicantSignStatus(null);
        applyBo.setInsuredSignStatus(null);
        applyBo.setAgentSignStatus(null);
        applyBo.setValidFlag("effective");
        applyBo.setCreatedUserId("20357f64211746b5b408333ec342a608");
        applyBo.setCreatedDate(1609235258954L);
        applyBo.setUpdatedUserId("5072ca66daf84f0594611bc771e627fc");
        applyBo.setUpdatedDate(1609386445667L);
        applyBo.setApplyType("LIFE_INSURANCE_GROUP");
        applyBo.setSignType(null);
        applyBo.setPolicyNo("P1609386373244425");
        applyBo.setEffectiveDate(1609385435000L);
        applyBo.setBizDate(null);
        applyBo.setProviderId("PRO8888888888888");
        applyBo.setVerifyNo(null);
        applyBo.setBackTrackDate(null);
        return applyBo;
    }
}