package com.gclife.attachment.service.print.policy;

/**
 * <AUTHOR>
 * @date 2022/3/29
 */
public class GCMultiProtectOnlineDataTest {
/*
    public static final String FILE_PATH = "C:\\Users\\<USER>\\Documents\\policyTemplate\\product\\PRO880000000000020A\\";


    public static final String FILE_NAME = "PRODUCT_20A_APPLY_EN_US_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_17_APPLY_EN_US_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_17_POLICY_ZH_CN";

    @Test
    public void rtfToPdf() throws Exception {

        Map map = JSONObject.parseObject(json, Map.class);
        // Data
        String contentJson = (String) map.get("content");

        Map<String, Object> planData = getApplyData(json, "EN_US");

        Document document = XmlUtil.map2xml(planData, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream(FILE_PATH + FILE_NAME + ".rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = OracleBiUtils.refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);
        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
        // 添加计划书首尾页
        // forObject = PlanCommon.updateAndSavePageNumber(forObject);
        FileOutputStream fileOutputStream = new FileOutputStream(FILE_PATH + FILE_NAME + ".pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();

    }

    private Map<String, Object> getApplyData(String content, String language) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Long backTrackDate = applyPrintBo.getApplyDate();
        if (AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())) {
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("prohibitedString2", PrintCommon.getPrintString(null, 2));
        map.put("prohibitedString3", PrintCommon.getPrintString(null, 3));
        map.put("prohibitedString4", PrintCommon.getPrintString(null, 4));
        map.put("prohibitedString5", PrintCommon.getPrintString(null, 5));
        map.put("prohibitedString6", PrintCommon.getPrintString(null, 6));
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));
        // 投保人
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        long applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        String applicantExpectedPremiumSources = applicant.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "applicantEPS", applicantExpectedPremiumSources);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        map.put("applicantIncome", PrintCommon.getPrintString(applicant.getIncome(), 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //移动电话
        map.put("applicantMobile_2", PrintCommon.getPrintString(applicant.getMobile_2(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //通讯地址
        String applicantCompanyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String applicantCompanyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("applicantCompanyAddressWhole", PrintCommon.getPrintString(applicantCompanyAreaName + applicantCompanyAddress, 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        map.put("applicantFacebookNo", PrintCommon.getPrintString(applicant.getFacebookNo(), 3));
        map.put("applicantStature", PrintCommon.getPrintString(applicant.getStature(), 3));
        map.put("applicantAvoirdupois", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("applicantExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(applicant.getExpectedPremiumSourcesSpecific(), 3));
        map.put("applicantDoctorName", PrintCommon.getPrintString(applicant.getDoctorName(), 3));
        String applicantDoctorAreaCodeName = AssertUtils.isNotEmpty(applicant.getDoctorAreaCodeName()) ? applicant.getDoctorAreaCodeName() : "";
        String applicantDoctorAddress = AssertUtils.isNotEmpty(applicant.getDoctorAddress()) ? applicant.getDoctorAddress() : "";
        map.put("applicantDoctorAreaCodeName", PrintCommon.getPrintString(applicantDoctorAreaCodeName + applicantDoctorAddress, 3));
        PrintCommon.setSelectionBox(map, "applicantAddressType" + applicant.getAddressType(), applicant.getAddressType());
        // 被保人
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }
        map.put("relationshipName", insured.getRelationshipName());

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        String insuredExpectedPremiumSources = insured.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "insuredEPS", insuredExpectedPremiumSources);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(insured.getIncome(), 2));
        long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //移动电话
        map.put("insuredMobile_2", PrintCommon.getPrintString(insured.getMobile_2(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //通讯地址
        String insuredCompanyAreaName = AssertUtils.isNotEmpty(insured.getCompanyAreaName()) ? insured.getCompanyAreaName() : "";
        String insuredCompanyAddress = AssertUtils.isNotEmpty(insured.getCompanyAddress()) ? insured.getCompanyAddress() : "";
        map.put("insuredCompanyAddressWhole", PrintCommon.getPrintString(insuredCompanyAreaName + insuredCompanyAddress, 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        map.put("insuredFacebookNo", PrintCommon.getPrintString(insured.getFacebookNo(), 3));
        map.put("insuredStature", PrintCommon.getPrintString(insured.getStature(), 3));
        map.put("insuredAvoirdupois", PrintCommon.getPrintString(insured.getAvoirdupois(), 3));
        map.put("insuredExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(insured.getExpectedPremiumSourcesSpecific(), 3));
        map.put("insuredDoctorName", PrintCommon.getPrintString(insured.getDoctorName(), 3));
        String insuredDoctorAreaCodeName = AssertUtils.isNotEmpty(insured.getDoctorAreaCodeName()) ? insured.getDoctorAreaCodeName() : "";
        String insuredDoctorAddress = AssertUtils.isNotEmpty(insured.getDoctorAddress()) ? insured.getDoctorAddress() : "";
        map.put("insuredDoctorAreaCodeName", PrintCommon.getPrintString(insuredDoctorAreaCodeName + insuredDoctorAddress, 3));
        map.put("taxpayerNo", PrintCommon.getPrintString(insured.getTaxpayerNo(), 3));
        PrintCommon.setSelectionBox(map, "insuredAddressType" + insured.getAddressType(), insured.getAddressType());
        // 受益人信息
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //证件类型
                beneficiaryMap.put("homeAddress", PrintCommon.getPrintString(applyBeneficiaryBo.getHomeAddress(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });

            map.put("beneficiaryListMap", beneficiaryListMap);
        }
        // 险种信息
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel(), language);
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount), 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000008".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                if ("PRO880000000000014".equals(applyCoverageBo.getProductId())) {
                    if ("ACCELERATION_CI".equals(applyCoverageBo.getProductLevel())) {
                        coverageMap.put("productLevelZH_CN", "(提前给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                        coverageMap.put("productLevelEN_US", "(Acceleration)");
                    } else {
                        coverageMap.put("productLevelZH_CN", "(额外给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                        coverageMap.put("productLevelEN_US", "(Additional)");
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }

        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));
        // 交费
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        // 缴费周期
        String premiumFrequencyName = applyPrintBo.getPremiumFrequencyName();
        if (SINGLE.name().equals(premiumFrequency)) {
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性缴清";
            }
            if (com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Premium";
            }
        }
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));

        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);
        // 支付方式
        String paymentModeName = applyPrintBo.getPaymentModeName();
        map.put("paymentModeName", PrintCommon.getPrintString(paymentModeName, 2));
        // 其他投保的保险
        List<ApplyOtherInsuranceBo> listApplyOtherInsurancePo = applyPrintBo.getOtherInsurance();
        List<Map<String, Object>> applyOtherInsuranceListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyOtherInsurancePo)) {
            for (ApplyOtherInsuranceBo applyOtherInsuranceBo : listApplyOtherInsurancePo) {
                Map<String, Object> applyOtherInsuranceMap = new HashMap<>();
                applyOtherInsuranceMap.put("otherInsuringInsuredName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuredName(), 3));
                applyOtherInsuranceMap.put("otherInsuringCompany", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuringCompany(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceTypeName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceTypeName(), 3));
                applyOtherInsuranceMap.put("otherInsuringAmount", PrintCommon.getPrintString(applyOtherInsuranceBo.getAmount(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceYear", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceYear(), 3));
                applyOtherInsuranceListMap.add(applyOtherInsuranceMap);
            }
        }
        map.put("applyOtherInsuranceListMap", applyOtherInsuranceListMap);
        // 其他投保的保险
        List<ApplyOccupationNatureBo> occupationNatureList = applyPrintBo.getOccupationNature();
        if (!AssertUtils.isNotEmpty(occupationNatureList)) {
            occupationNatureList = new ArrayList<>();
        }
        ApplyOccupationNatureBo applicantOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> applicantOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "APPLICANT".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (applicantOptionalOccupationNatureBo.isPresent()) {
            applicantOccupationNatureBo = applicantOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "applicantON" + applicantOccupationNatureBo.getOccupationNature(), applicantOccupationNatureBo.getOccupationNature());
        map.put("applicantOccupationNatureSpecific", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("applicantEmployerName", PrintCommon.getPrintString(applicantOccupationNatureBo.getEmployerName(), 3));
        map.put("applicantBusinessNature", PrintCommon.getPrintString(applicantOccupationNatureBo.getBusinessNature(), 3));
        map.put("applicantOccupationExactDuties", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupation(), 3));
        map.put("applicantOccupationClass", PrintCommon.getPrintString(applicantOccupationNatureBo.getExactDuties(), 3));

        ApplyOccupationNatureBo insuredOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> insuredOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "INSURED".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (insuredOptionalOccupationNatureBo.isPresent()) {
            insuredOccupationNatureBo = insuredOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "insuredON" + insuredOccupationNatureBo.getOccupationNature(), insuredOccupationNatureBo.getOccupationNature());
        map.put("insuredOccupationNatureSpecific", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("insuredEmployerName", PrintCommon.getPrintString(insuredOccupationNatureBo.getEmployerName(), 3));
        map.put("insuredBusinessNature", PrintCommon.getPrintString(insuredOccupationNatureBo.getBusinessNature(), 3));
        map.put("insuredOccupationExactDuties", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupation(), 3));
        map.put("insuredOccupationClass", PrintCommon.getPrintString(insuredOccupationNatureBo.getExactDuties(), 3));
        // 账户
        List<ApplyAccountBo> listApplyAccount = applyPrintBo.getListApplyAccount();
        ApplyAccountBo applyAccountBo = new ApplyAccountBo();
        String kmKmBankName = null;
        String bankName = null;
*/
/*            if (AssertUtils.isNotEmpty(listApplyAccount)) {
                applyAccountBo = listApplyAccount.get(0);
                SyscodeResponse kmKmBankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name()).getData();
                SyscodeResponse bankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name().equals(language) ? EN_US.name() : language).getData();
                kmKmBankName = AssertUtils.isNotNull(kmKmBankSyscode) && AssertUtils.isNotEmpty(kmKmBankSyscode.getCodeName()) ? kmKmBankSyscode.getCodeName() : null;
                bankName = AssertUtils.isNotNull(bankSyscode) && AssertUtils.isNotEmpty(bankSyscode.getCodeName()) ? bankSyscode.getCodeName() : null;
            }*//*
*/
/*

        map.put("kmKmBankName", PrintCommon.getPrintString(kmKmBankName, 3));
        map.put("bankName", PrintCommon.getPrintString(bankName, 3));
        map.put("accountOwner", PrintCommon.getPrintString(applyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(applyAccountBo.getAccountNo(), 3));
        *//*

        // 健康告知书
        ProductCalculation.setHealthRemark1(map, applyPrintBo);
        List<ApplyStatementBo> statements = applyPrintBo.getStatements();
        if (AssertUtils.isNotEmpty(statements)) {
            statements.forEach(applyStatementBo -> {
                map.put(applyStatementBo.getStatementCode(), applyStatementBo.getStatementValue());
            });
        }
        // 投保申请日期
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyAgentBo.getAgentMobile(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        return map;
    }

    private String json = "{\"acceptBranchId\":null,\"acceptBranchName\":\"Online Channel\",\"agentSignStatus\":null,\"appSubmitUnderwritingDate\":*************,\"applicant\":{\"addressType\":\"RESIDENCE\",\"applicantId\":\"c73cbca4783347d38ef439a8b3b1b685\",\"applicantType\":\"PERSONAL\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"attachmentId\":null,\"avoirdupois\":\"88\",\"backTrackDate\":null,\"backTrackDateFormat\":null,\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"basisOfSumInsured\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAddressWhole\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"24.93\",\"companyAddress\":null,\"companyAddressWhole\":null,\"companyAreaCode\":null,\"companyAreaName\":null,\"companyContractAddress\":null,\"companyContractDept\":null,\"companyContractEmail\":null,\"companyContractIdExpDate\":null,\"companyContractIdNo\":null,\"companyContractIdType\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractNationality\":null,\"companyContractOfficeNumber\":null,\"companyContractPhone\":null,\"companyContractPosition\":null,\"companyEmail\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyIndustry\":null,\"companyLegalPersonIdExpDate\":null,\"companyLegalPersonIdNo\":null,\"companyLegalPersonIdType\":null,\"companyLegalPersonName\":null,\"companyLegalPersonNationality\":null,\"companyMobile\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"04830d9dda0e4d21a471ee7a479907da\",\"customerSource\":null,\"degree\":null,\"degreeName\":null,\"delegateBirthday\":null,\"delegateCustomerId\":null,\"delegateIdNo\":null,\"delegateIdType\":null,\"delegateMobile\":null,\"delegateName\":null,\"doctorAddress\":null,\"doctorAreaCode\":null,\"doctorAreaCodeName\":null,\"doctorName\":null,\"email\":\"<EMAIL>\",\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"familyName\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Mongkol Borei Banteay Neang Ou Thum 44\",\"givenName\":null,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"44\",\"homeAddressWhole\":null,\"homeAreaCode\":\"***********\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":1648659600000,\"idNo\":\"WWPEQUI\",\"idType\":\"PASSPORT\",\"idTypeName\":\"Passport\",\"income\":null,\"incomeName\":null,\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"listExpectedPremiumSources\":[],\"marriage\":null,\"mobile\":\"4\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"JHM WWPEQUI\",\"nationality\":\"RESIDING\",\"nationalityName\":null,\"nationalityType\":null,\"nations\":null,\"occupationCode\":\"**********\",\"occupationName\":\"Field sales of enterprises and institutions\",\"occupationNature\":null,\"occupationType\":\"2\",\"ocrMrz\":null,\"otherCategorySpecify\":null,\"otherPhone\":null,\"phone\":null,\"pluralityName\":null,\"pluralityType\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"sexName\":\"Male\",\"smokeFlag\":null,\"socialSecurity\":null,\"socialSecurityName\":null,\"startWorkDate\":null,\"stature\":\"188\",\"taxRegistrationNo\":null,\"totalEmployeeNum\":null,\"totalLine\":0,\"updatedDate\":1647505278818,\"updatedUserId\":null,\"validFlag\":\"effective\",\"visaExpDate\":1648659600000,\"wechatNo\":null,\"workType\":null,\"zipCode\":null},\"applicantHealthRemark\":null,\"applicantSignStatus\":null,\"applyAcceptBo\":null,\"applyAgentBo\":{\"agentCode\":\"OL00001\",\"agentId\":\"INIT_AGENT_ONLINE001\",\"agentMobile\":\"88888888\",\"agentName\":\"Online Channel 01\",\"applyAgentId\":\"7a247d227b7c473cb3cea53e3cfd5110\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"createdDate\":1647498978738,\"createdUserId\":null,\"forceSave\":false,\"percent\":null,\"recommendAgentName\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"applyAttachmentConfigBo\":null,\"applyContact\":{\"applyContactId\":\"ca6b58ef30d045368d2fdaef296b16d3\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"contractEmail\":null,\"contractHomePhone\":null,\"contractMobile\":\"4\",\"contractName\":\"JHM WWPEQUI\",\"contractOfficePhone\":null,\"contractPhone\":null,\"createdDate\":1647499494011,\"createdUserId\":null,\"forceSave\":false,\"needPosLetter\":null,\"postcodes\":null,\"sendAddrAreaCode\":\"***********\",\"sendAddrContact\":\"44\",\"sendType\":null,\"smsPhone\":null,\"smsServiceFlag\":null,\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"applyDate\":*************,\"applyFactAgentPo\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"applyInsuredCollect\":null,\"applyNo\":\"IAI22A00283\",\"applyPaymentTransactionBo\":{\"actualPayDate\":*************,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"applyPaymentTransactionItemBos\":[{\"accountId\":null,\"createdDate\":*************,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"forceSave\":false,\"paymentAmount\":115.95,\"paymentItemId\":\"7d1b099692ad4732afc6c133fbb9032c\",\"paymentMethodCode\":\"ABA_PAYWAY_ABA_PAY\",\"paymentParam\":null,\"paymentStatus\":\"PAYMENT_SUCCESS\",\"paymentTransactionId\":\"db03808997ec4dc7a3783faf0e815d78\",\"paymentTransactionItemId\":\"825ddd65b6f54039827e27ac9ec01977\",\"paymentTypeCode\":\"ACTUAL\",\"returnParam\":null,\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"}],\"arrivalDate\":*************,\"createdDate\":*************,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"feeType\":\"PAYMENT\",\"forceSave\":false,\"matchResult\":\"YES\",\"paymentAmount\":115.95,\"paymentDate\":*************,\"paymentId\":\"a8ec46dc9215423d90f9084cd851d100\",\"paymentStatus\":\"PAYMENT_SUCCESS\",\"paymentTransactionId\":\"db03808997ec4dc7a3783faf0e815d78\",\"paymentType\":\"NORMAL_PAYMENT\",\"premiumId\":\"794f5856dc26488f9d17c2268e843397\",\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"},\"applyPlanNo\":\"IJI22A00287\",\"applyPremiumBo\":{\"actualPremium\":115.95,\"addPremium\":0.00,\"advancePremium\":null,\"agentDiscount\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"arrivalDate\":1647499493571,\"companyDiscount\":null,\"createdDate\":1647499289683,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"currencyCode\":\"USD\",\"discountModel\":null,\"discountPremium\":null,\"discountType\":null,\"dividenoPremium\":null,\"expireEndTime\":null,\"feeType\":null,\"forceSave\":false,\"listApplyPaymentAttachment\":[],\"matchResult\":\"YES\",\"memberCompanyName\":null,\"originalPremium\":115.95,\"overPremium\":null,\"paymentCodeNo\":null,\"paymentId\":\"a8ec46dc9215423d90f9084cd851d100\",\"paymentType\":\"NORMAL_PAYMENT\",\"paymentUrl\":null,\"periodOriginalPremium\":115.95,\"periodTotalPremium\":115.95,\"policyPeriod\":\"1\",\"policyYear\":\"1\",\"premiumBeforeDiscount\":null,\"premiumId\":\"794f5856dc26488f9d17c2268e843397\",\"premiumStatus\":\"PAYMENT_SUCCESS\",\"printCount\":null,\"promotionType\":null,\"receivableDate\":*************,\"receivablePremium\":115.95,\"remark\":null,\"shortPremium\":null,\"specialDiscount\":null,\"totalActualPremium\":115.95,\"totalLine\":0,\"totalPremium\":115.95,\"updatedDate\":1647499493571,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"},\"applySource\":\"APP\",\"applyStatus\":\"APPLY_STATUS_APPROVE_SUCCESS\",\"applyType\":\"LIFE_INSURANCE_PERSONAL\",\"approveDate\":*************,\"autoPaymentOption\":null,\"autoRenewalInsurance\":null,\"backTrackDate\":null,\"bizDate\":null,\"certifyClassId\":null,\"certifyId\":null,\"channelTypeCode\":\"ONLINE\",\"createdDate\":*************,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":*************,\"electronicSignatureAttachmentBo\":{\"agentSignatureAttachmentId\":null,\"applicantSignatureAttachmentId\":null,\"insuredSignatureAttachmentId\":null},\"fillInPremium\":null,\"forceSave\":false,\"initialPaymentMode\":\"ABA_PAYWAY_ABA_PAY\",\"insuredHealthRemark\":null,\"insuredSignStatus\":null,\"inureImmeInvest\":null,\"invalidDate\":null,\"listApplyAccount\":[],\"listApplyAddPremiumPo\":[],\"listApplyCoverageAcceptBo\":[],\"listAttachment\":[{\"applyAttachmentId\":\"9ca9e6a134114da9b15215e7debb8edf\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"attachmentId\":\"18e80ee5267948af99bf6951d6f68a57\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE\",\"createdDate\":*************,\"createdUserId\":null,\"description\":null,\"forceSave\":false,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"url\":null,\"validFlag\":\"effective\"},{\"applyAttachmentId\":\"e33a98a951aa472da80bda43441cd224\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"attachmentId\":\"18e80ee5267948af99bf6951d6f68a57\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE\",\"createdDate\":*************,\"createdUserId\":null,\"description\":null,\"forceSave\":false,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"url\":null,\"validFlag\":\"effective\"}],\"listCoverage\":[],\"listHealthNotice\":[{\"answer\":\"N\",\"answerDesc\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"createdDate\":1647499337397,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"customerId\":null,\"customerType\":\"INSURED\",\"forceSave\":false,\"healthQuestionnaireAnswerId\":\"2d4f1323d9f64901ba35afbf92e4a03e\",\"healthQuestionnaireId\":null,\"questionCode\":\"20A_QUESTION_001\",\"questionDesc\":\"1. Have you ever been advised by any medical officers/practitioners/doctors or been diagnosed, treated, or given any medication for any of the following: heart attack, ischaemic heart disease, stroke, transient ischaemic attack, cancer, growth/tumour of any kind, kidney failure, HIV infection, AIDS, high blood pressure, diabetes mellitus, chronic obstructive pulmonary disease, Tuberculosis, cirrhosis, hepatitis (include carrier status), drug abuse, alcoholism, epilepsy or any disease or disorders of heart, kidney, brain, nerve, muscle, joint, bone, liver, blood, mental, respiratory system, urinary system, nervous system?\",\"totalLine\":0,\"updatedDate\":1647499337397,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"},{\"answer\":\"N\",\"answerDesc\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"createdDate\":*************,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"customerId\":null,\"customerType\":\"INSURED\",\"forceSave\":false,\"healthQuestionnaireAnswerId\":\"71ab6c003717442b842100f53408a587\",\"healthQuestionnaireId\":null,\"questionCode\":\"20A_QUESTION_002\",\"questionDesc\":\"2. In the past 3 years, have you had/or been advised to have anyone/more of the following: treatment/hospitalisation/diagnostic tests (including but not limited to X-Rays, ECG, CT Scan, Ultrasound, blood tests, urine tests), other than for pre-employment screenings/routine company or visa application medical check-ups, for any medical condition(s)? \",\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"},{\"answer\":\"N\",\"answerDesc\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"createdDate\":*************,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"customerId\":null,\"customerType\":\"INSURED\",\"forceSave\":false,\"healthQuestionnaireAnswerId\":\"769ec1dabc854c1e88bd597de3f2ae10\",\"healthQuestionnaireId\":null,\"questionCode\":\"20A_QUESTION_003\",\"questionDesc\":\"3. Are you involved in any hazardous hobby (such as but not limited to motor racing, scuba diving, caving, mountain climbing, private flying, parachuting, hang-gliding or any other hazardous activities) or engaged with any hazardous occupation (working at altitudes above 6 meters, working at an oil and gas rig, working in a mining pool, digging a tunnel, working underwater, working underground, working offshore, fishing seafood, crocodile farming, armed forces, fireman or use of explosive at work)?\",\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"},{\"answer\":\"N\",\"answerDesc\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"createdDate\":*************,\"createdUserId\":\"INIT_AGENT_ONLINE001\",\"customerId\":null,\"customerType\":\"INSURED\",\"forceSave\":false,\"healthQuestionnaireAnswerId\":\"0fb8eff57eed4a1283c7b1a573653bd0\",\"healthQuestionnaireId\":null,\"questionCode\":\"20A_QUESTION_004\",\"questionDesc\":\"4. Have you ever had any proposal for medical, critical illness, accident or life insurance being declined, postponed, rated or subject to modified terms?\",\"totalLine\":0,\"updatedDate\":*************,\"updatedUserId\":\"INIT_AGENT_ONLINE001\",\"validFlag\":\"effective\"}],\"listInsured\":[{\"addDate\":null,\"addressType\":\"RESIDENCE\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"attachmentId\":null,\"avoirdupois\":\"88\",\"backTrackDate\":null,\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"24.93\",\"careerType\":null,\"companyAddress\":null,\"companyAreaCode\":null,\"companyAreaName\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"04830d9dda0e4d21a471ee7a479907da\",\"degree\":null,\"degreeName\":null,\"doctorAddress\":null,\"doctorAreaCode\":null,\"doctorAreaCodeName\":null,\"doctorName\":null,\"effectiveDate\":null,\"email\":\"<EMAIL>\",\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"familyName\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Mongkol Borei Banteay Neang Ou Thum 44\",\"givenName\":null,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"44\",\"homeAreaCode\":\"***********\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":1648659600000,\"idNo\":\"WWPEQUI\",\"idType\":\"PASSPORT\",\"idTypeName\":\"Passport\",\"income\":null,\"incomeName\":null,\"incomeSource\":null,\"insuredExtendId\":null,\"insuredId\":\"5c10b6a16e4240bd9af93234b31e83c0\",\"insuredStatus\":null,\"insuredType\":null,\"invalidDate\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"listBeneficiary\":[{\"applyBeneficiaryBo\":{\"addressDetail\":null,\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"avoirdupois\":null,\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"beneficiaryBranchCode\":null,\"beneficiaryBranchId\":null,\"beneficiaryBranchName\":null,\"beneficiaryId\":\"d3ab5a413e6a4ee288118585e7084026\",\"beneficiaryNoOrder\":null,\"beneficiaryNoOrderName\":null,\"beneficiaryProportion\":null,\"birthPlace\":null,\"birthday\":null,\"bmi\":null,\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":null,\"degree\":null,\"email\":null,\"englishName\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"familyName\":null,\"fax\":null,\"forceSave\":false,\"givenName\":null,\"headAttachId\":null,\"health\":null,\"homeAddress\":null,\"homeAreaCode\":null,\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":null,\"idType\":null,\"idTypeName\":null,\"income\":null,\"incomeSource\":null,\"insuredId\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":null,\"mobile\":null,\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"JHM WWPEQUI\",\"nationality\":null,\"nations\":null,\"occupationCode\":null,\"occupationType\":null,\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"relationship\":null,\"relationshipInstructions\":null,\"relationshipName\":null,\"rfidMrz\":null,\"salary\":null,\"sex\":\"FEMALE\",\"sexName\":\"Female\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":null,\"workType\":null,\"zipCode\":null},\"applyBeneficiaryId\":\"e48fef2b13e647fe833340d75b69167d\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"beneficiaryGrade\":null,\"beneficiaryId\":\"d3ab5a413e6a4ee288118585e7084026\",\"beneficiaryNo\":null,\"beneficiaryNoOrder\":\"ORDER_ONE\",\"beneficiaryNoOrderName\":\"First Beneficiary\",\"beneficiaryProportion\":100.00,\"beneficiaryType\":null,\"claimPremDrawType\":null,\"createdDate\":*************,\"createdUserId\":\"GM_USER_101\",\"forceSave\":false,\"idTypeName\":null,\"insuredId\":\"5c10b6a16e4240bd9af93234b31e83c0\",\"listBeneficiaryAttachment\":[],\"modifyFlag\":\"YES\",\"promiseAge\":null,\"promiseRate\":null,\"relationship\":\"CHILD\",\"relationshipInstructions\":null,\"relationshipName\":\"Children\",\"totalLine\":0,\"transferGrantFlag\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"}],\"listCoverage\":[{\"actualPremium\":115.950,\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"5000.00\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"applyNo\":\"IAI22A00283\",\"baseAmount\":null,\"basePremium\":null,\"coverageChangeStatus\":null,\"coverageId\":\"c463ca46a2194496b8295ad67e476180\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\" Year(s)\",\"createdDate\":1647499289550,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"financingMethod\":null,\"financingMethodName\":null,\"forceSave\":false,\"frequency\":null,\"insuredId\":\"5c10b6a16e4240bd9af93234b31e83c0\",\"insuredNum\":null,\"insuredSeq\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"mult\":\"1\",\"originalPremium\":115.95,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premium\":115.95,\"premiumDiscount\":115.95,\"premiumFrequency\":\"SINGLE\",\"premiumPeriod\":\"1\",\"premiumPeriodUnit\":\"SINGLE\",\"premiumPeriodUnitName\":\"Single Payment\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_GC_MULTIPROTECT_ONLINE\",\"productId\":\"PRO880000000000020A\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC MultiProtect(Online)\",\"relationship\":\"ONESELF\",\"totalAmount\":null,\"totalLine\":0,\"totalPremium\":115.95,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null}],\"listExpectedPremiumSources\":[],\"marriage\":null,\"mobile\":\"4\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"mult\":\"1\",\"name\":\"JHM WWPEQUI\",\"nationality\":\"RESIDING\",\"nationalityName\":null,\"nationalityType\":null,\"nations\":null,\"occupationCode\":\"**********\",\"occupationDuty\":null,\"occupationName\":\"Field sales of enterprises and institutions\",\"occupationNature\":null,\"occupationType\":\"2\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityName\":null,\"pluralityType\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"reinsuranceRules\":null,\"relationship\":\"ONESELF\",\"relationshipInstructions\":null,\"relationshipName\":\"Self\",\"rfidMrz\":null,\"salary\":null,\"sameWithApplicant\":null,\"sex\":\"MALE\",\"sexName\":null,\"smokeFlag\":null,\"socialSecurity\":null,\"socialSecurityName\":null,\"startWorkDate\":null,\"stature\":\"188\",\"taxpayerNo\":null,\"totalLine\":0,\"updatedDate\":1647505278821,\"updatedUserId\":null,\"validFlag\":\"effective\",\"visaExpDate\":1648659600000,\"wechatNo\":null,\"workType\":null,\"zipCode\":null}],\"listInsuredCoverage\":[{\"actualPremium\":115.950,\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"5000.00\",\"applyId\":\"50919967e261473c9bb774934f0f0ca4\",\"applyNo\":\"IAI22A00283\",\"baseAmount\":null,\"basePremium\":null,\"coverageChangeStatus\":null,\"coverageId\":\"c463ca46a2194496b8295ad67e476180\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\" Year(s)\",\"createdDate\":1647499289550,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"financingMethod\":null,\"financingMethodName\":null,\"forceSave\":false,\"frequency\":null,\"insuredId\":\"5c10b6a16e4240bd9af93234b31e83c0\",\"insuredNum\":null,\"insuredSeq\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"mult\":\"1\",\"originalPremium\":115.95,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premium\":115.95,\"premiumDiscount\":115.95,\"premiumFrequency\":\"SINGLE\",\"premiumPeriod\":\"1\",\"premiumPeriodUnit\":\"SINGLE\",\"premiumPeriodUnitName\":\"Single Payment\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_GC_MULTIPROTECT_ONLINE\",\"productId\":\"PRO880000000000020A\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC MultiProtect(Online)\",\"relationship\":\"ONESELF\",\"totalAmount\":null,\"totalLine\":0,\"totalPremium\":115.95,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null}],\"listPolicySpecialContract\":[],\"loanContract\":null,\"managerBranchId\":\"GMM\",\"occupationNature\":[],\"otherInsurance\":[],\"overflowToNext\":null,\"paymentId\":null,\"paymentMode\":\"ABA_PAYWAY_ABA_PAY\",\"paymentModeName\":\"ABA PAY\",\"policyId\":null,\"policyNo\":\"IPI22A00030\",\"preUnderwritingFlag\":null,\"premiumFrequency\":\"SINGLE\",\"premiumFrequencyName\":\"Single\",\"providerId\":\"PRO8888888888888\",\"receivableAddPremium\":0.00,\"receivablePremium\":115.95,\"receivablePremiumSum\":null,\"referralInfo\":null,\"repealDate\":null,\"salesBranchId\":\"ONLINE\",\"selfInsuranceFlag\":null,\"signBranchId\":\"GMM\",\"signType\":null,\"signedDate\":null,\"specialTerm\":null,\"statements\":[],\"survivalPayType\":null,\"totalLine\":0,\"underWriteRemark\":null,\"updatedDate\":1647505278831,\"updatedUserId\":null,\"validFlag\":\"effective\",\"verifyNo\":null}";
*/

}