package com.gclife.attachment.service.print.agent;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;

/**
 * <AUTHOR>
 * @date 2022/3/16
 */
public class SingElectronicContractTest extends BaseBusinessServiceImpl {

/*    public static final String FILE_PATH = "D:\\Work\\policyTemplate\\policyTemplate\\contract\\lc\\";

    public static final String FILE_NAME_ZH = "LC_CONTRACT_ZH_CN";
    public static final String FILE_NAME_EN = "LC_CONTRACT_KM_KH_EN_US";

    @Test
    public void rtfToPdf() throws Exception {

        Map<String, Object> planData = getContractData();

        Document document = XmlUtil.map2xml(planData, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream(FILE_PATH + FILE_NAME_EN + ".rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = OracleBiUtils.refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);
        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
        // 添加计划书首尾页
        // forObject = PlanCommon.updateAndSavePageNumber(forObject);
        FileOutputStream fileOutputStream = new FileOutputStream(FILE_PATH + FILE_NAME_EN + ".pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();
    }

    public Map<String, Object> getContractData() {
        HashMap<String, Object> map = new HashMap<>();

        *//*
        signDateYyyy
signDateMm
signDateDd
         *//*
        PrintCommon.setPrintDateTime(map, "signDate", 1647575285773L, 3);
        map.put("agentName", "AN NA XUE JIE");
        map.put("idNoORPassportNo", "7777777777777");
        map.put("agentAddress", "Cambodia Kandal Province Ta Khmau");
        map.put("witnessName", "MA YI XUE JIE");
        map.put("witnessPosition", "XUE JIE");
        map.put("LCSignPicture", "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fp.ivideo.sina.com.cn%2Fvideo%2F397%2F914%2F397%2F397914397_400_300.jpg&refer=http%3A%2F%2Fp.ivideo.sina.com.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=**********&t=0352961db1cbcef2a5f195e6e29aebe6");
        map.put("witnessSignPicture", "https://pics7.baidu.com/feed/2cf5e0fe9925bc312ecb8b144aaad8b7ca137027.jpeg?token=b7ee5f5c24ff8f8e1be56dcda48c1687&s=3CBB1998E442A4FD5E22DB850300208D");
        return map;
    }*/

}
