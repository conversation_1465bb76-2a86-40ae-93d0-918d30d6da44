package com.gclife.attachment.service.print.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.plan.ApplyApplicantPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyCoveragePlanBo;
import com.gclife.attachment.model.policy.plan.ApplyInsuredPlanBo;
import com.gclife.attachment.model.policy.plan.ApplyPlanBo;
import com.gclife.attachment.model.policy.policy.*;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.response.apply.CoveragePremiumFrequencyResponse;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;
import static com.gclife.common.model.config.AuthItemConfigEnum.EFFECTIVE;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
public class GCNearyCareDataTest extends BaseBusinessServiceImpl {

    public static final String FILE_PATH = "C:\\Users\\<USER>\\Documents\\policyTemplate\\product\\PRO880000000000021\\";

    // public static final String FILE_NAME = "PRODUCT_21_PLAN_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_21_PLAN_EN_US";
    // public static final String FILE_NAME = "PRODUCT_21_PLAN_ZH_CN";

    public static final String FILE_NAME = "PRODUCT_21_POLICY_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_21_POLICY_EN_US";
    // public static final String FILE_NAME = "PRODUCT_21_POLICY_ZH_CN";

    /*
    @Test
    public void rtfToPdf() throws Exception {

        Map<String, Object> planData = getPolicyData(getJson(), "ZH_CN");

        Document document = XmlUtil.map2xml(planData, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream(FILE_PATH + FILE_NAME + ".rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = OracleBiUtils.refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);
        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
        // 添加计划书首尾页
        // forObject = PlanCommon.updateAndSavePageNumber(forObject);
        FileOutputStream fileOutputStream = new FileOutputStream(FILE_PATH + FILE_NAME + ".pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();
    }
    */

    public Map<String, Object> getPolicyData(String content, String language) throws Exception {
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        /**********************************投保人信息*****************************************/
        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));
        /**********************************被保人信息**********************************/
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));
        /**********************************保险***************************************/
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        // String mainProductId = electronicPolicyGeneratorRequest.getProductId(); // TODO 取消注释
        String mainProductId = "PRO880000000000021";
        // TODO 删除 begin
        // listPolicyCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000014".equals(applyCoveragePlanBo.getProductId())); // ======================
        // listPolicyCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000015".equals(applyCoveragePlanBo.getProductId())); // ======================
        // listPolicyCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000016A".equals(applyCoveragePlanBo.getProductId())); // ======================
        // listPolicyCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000016B".equals(applyCoveragePlanBo.getProductId())); // ======================
        // TODO 删除 end

        PrintCommon.setProductName(map, mainProductId, "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            if ("PRO880000000000021".equals(coverageBo.getProductId())) {
                maturityAmount = new BigDecimal("0.05").multiply(new BigDecimal(coverageBo.getTotalAmount()));
            }
            coverageMap.put("maturityAmount", PrintCommon.getPrintAmountString(maturityAmount, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Payment";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        /***********************************现金价值******************************************/
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                Map<String, Object> productCashValue = new HashMap<>();
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                policyCashValuesListMap.add(productCashValue);
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }
        /****************************************代理人编码********************************/
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);
        return map;
    }

    private Map<String, Object> getPlanData(String content, String language) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));
        /*******************************************投保人信息***********************************************/
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        /*********************************************被保人信息***************************************************/
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));
        /****************************************************************************获取保险期限  可选其他缴费周期start***********************************************************************************/
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal mainTotalAmount = BigDecimal.ZERO;
        String mainPremiumPeriod = null;
        String mainPremiumFrequency = null;
        String mainCoveragePeriod = null;
        String mainMult = null;
        String mult14 = null;
        String mult15 = null;

        BigDecimal totalAmount14 = BigDecimal.ZERO;
        String productLevel14 = null;
        BigDecimal totalAmount15 = BigDecimal.ZERO;
        BigDecimal totalPremium15 = BigDecimal.ZERO;
        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        // TODO 删除 begin
        listCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000014".equals(applyCoveragePlanBo.getProductId())); // ======================
        listCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000015".equals(applyCoveragePlanBo.getProductId())); // ======================
        listCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000016A".equals(applyCoveragePlanBo.getProductId())); // ======================
        listCoverage.removeIf(applyCoveragePlanBo -> "PRO880000000000016B".equals(applyCoveragePlanBo.getProductId())); // ======================
        // TODO 删除 end
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            BigDecimal totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = new BigDecimal(coverageBo.getAmount());
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintAmountString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = null;
            if ("PRO880000000000021".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mainTotalAmount = amount;
                mainPremiumPeriod = premiumPeriod;
                mainCoveragePeriod = coveragePeriod;
                mainPremiumFrequency = coverageBo.getPremiumFrequency();
                mainMult = coverageBo.getMult();
            } else if ("PRO880000000000014".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                totalAmount14 = amount;
                productLevel14 = productLevel;
                mult14 = coverageBo.getMult();
                if ("ACCELERATION_CI".equals(productLevel)) {
                    coverageMap.put("productLevelZH_CN", "(提前给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                    coverageMap.put("productLevelEN_US", "(Acceleration)");
                } else {
                    coverageMap.put("productLevelZH_CN", "(额外给付)");
                    coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                    coverageMap.put("productLevelEN_US", "(Additional)");
                }
            } else if ("PRO880000000000015".equals(coverageBo.getProductId())) {
                amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
                mult15 = coverageBo.getMult();
                totalAmount15 = amount;
            }
            if ("PRO880000000000016A".equals(coverageBo.getProductId()) || "PRO880000000000016B".equals(coverageBo.getProductId())) {
                map.put("PRO880000000000016", "PRO880000000000016");
            }
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            //每期保费
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int i = YEAR.name().equals(premiumFrequency) ? 1 : SEMIANNUAL.name().equals(premiumFrequency) ? 2 : SEASON.name().equals(premiumFrequency) ? 3 : MONTH.name().equals(premiumFrequency) ? 4 : 0;
            coverageMap.put("totalPremium" + i, PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            //可选缴费周期保险费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 1, coverageMap, cpfList, YEAR.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 2, coverageMap, cpfList, SEMIANNUAL.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 3, coverageMap, cpfList, SEASON.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 4, coverageMap, cpfList, MONTH.name(), premiumFrequency);

            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            BigDecimal maturityAmount = yearTotalPremium.multiply(new BigDecimal(coverageBo.getPremiumPeriod())).setScale(2, RoundingMode.HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", mainPremiumFrequency);
        double totalPremium1 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium1") + "").replace(",", ""))).sum();
        double totalPremium2 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium2") + "").replace(",", ""))).sum();
        double totalPremium3 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium3") + "").replace(",", ""))).sum();
        double totalPremium4 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium4") + "").replace(",", ""))).sum();
        map.put("allTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2), 2));
        map.put("allTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3), 2));
        map.put("allTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4), 2));
        map.put("allYearTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allYearTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2).multiply(new BigDecimal(2)), 2));
        map.put("allYearTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3).multiply(new BigDecimal(4)), 2));
        map.put("allYearTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4).multiply(new BigDecimal(12)), 2));
        /****************************************************************************保险利益***********************************************************************************/
        BigDecimal tpd_benefit_due_to_accident = mainTotalAmount.add(totalAmount15);
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 2));
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintAmountString(mainTotalAmount, 2));

        // 主险 + 附加险的 死亡或高度残疾保险金
        BigDecimal death_benefit_due_to_accident = mainTotalAmount.add(totalAmount15.multiply(new BigDecimal(3)));
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintAmountString(death_benefit_due_to_accident, 2));
        BigDecimal benefitsAmount1 = BigDecimal.ZERO;
        BigDecimal benefitsAmount2 = BigDecimal.ZERO;
        BigDecimal maxBenefitsAmount2 = BigDecimal.ZERO;
        BigDecimal benefitsAmount3 = BigDecimal.ZERO;
        BigDecimal benefitsAmount4 = BigDecimal.ZERO;
        BigDecimal benefitsAmount5 = BigDecimal.ZERO;
        BigDecimal totalHealthyAmount = BigDecimal.ZERO;
        BigDecimal benefitsAmount6 = BigDecimal.ZERO;
        BigDecimal benefitsAmount7 = BigDecimal.ZERO;
        BigDecimal benefitsAmount8 = BigDecimal.ZERO;
        Map<String, Object> individualizationData = planProductDetail.getIndividualizationData();
        if (AssertUtils.isNotNull(individualizationData)) {
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount1"))) {
                benefitsAmount1 = new BigDecimal(individualizationData.get("benefitsAmount1") + "");// 主险 死亡或高度残疾保险金
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount2"))) {
                benefitsAmount2 = new BigDecimal(individualizationData.get("benefitsAmount2") + "");// 主险 满月保险金
                maxBenefitsAmount2 = benefitsAmount2.multiply(new BigDecimal("2"));
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount3"))) {
                benefitsAmount3 = new BigDecimal(individualizationData.get("benefitsAmount3") + "");// 主险 祝贺金
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount4"))) {
                benefitsAmount4 = new BigDecimal(individualizationData.get("benefitsAmount4") + "");// 主险 健康保险金 6 ~ 14
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount5"))) {
                benefitsAmount5 = new BigDecimal(individualizationData.get("benefitsAmount5") + "");// 主险 健康保险金 16 ~ 24
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount6"))) {
                benefitsAmount6 = new BigDecimal(individualizationData.get("benefitsAmount6") + "");// 主险 女性早期重大疾病保险金
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount7"))) {
                benefitsAmount7 = new BigDecimal(individualizationData.get("benefitsAmount7") + "");// 主险 女性晚期重大疾病保险金
            }
            if (AssertUtils.isNotNull(individualizationData.get("benefitsAmount8"))) {
                benefitsAmount8 = new BigDecimal(individualizationData.get("benefitsAmount8") + "");// 主险 满期保险金
            }
        }
        map.put("benefitsAmount1", PrintCommon.getPrintAmountString(benefitsAmount1, 2));
        map.put("benefitsAmount2", PrintCommon.getPrintAmountString(benefitsAmount2, 2));
        map.put("maxBenefitsAmount2", PrintCommon.getPrintAmountString(maxBenefitsAmount2, 2));
        map.put("benefitsAmount3", PrintCommon.getPrintAmountString(benefitsAmount3, 2));
        map.put("benefitsAmount4", PrintCommon.getPrintAmountString(benefitsAmount4, 2));
        map.put("benefitsAmount5", PrintCommon.getPrintAmountString(benefitsAmount5, 2));
        map.put("benefitsAmount6", PrintCommon.getPrintAmountString(benefitsAmount6, 2));
        map.put("benefitsAmount7", PrintCommon.getPrintAmountString(benefitsAmount7, 2));
        map.put("benefitsAmount8", PrintCommon.getPrintAmountString(benefitsAmount8, 2));

        map.put("death_benefit_non_accident", PrintCommon.getPrintAmountString(mainTotalAmount, 2));
        map.put("early_stage_critical_illness_benefit", PrintCommon.getPrintAmountString(totalAmount14.multiply(new BigDecimal("0.25")), 2));
        map.put("late_stage_critical_illness_benefit", PrintCommon.getPrintAmountString(totalAmount14, 2));

        /****************************************************************************利益显示***********************************************************************************/
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int mainCoveragePeriodi = Integer.parseInt(mainCoveragePeriod);
        for (int i = 1; i <= mainCoveragePeriodi; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            interestMap.put("mainTotalAmount", "$" + PrintCommon.getPrintAmountString(mainTotalAmount, 1));
            BigDecimal survivalAmount = BigDecimal.ZERO;
            if (5 == i) {
                survivalAmount = survivalAmount.add(benefitsAmount3);
            }
            // 最后一年不给健康保险金
            if (mainCoveragePeriodi != i) {
                if (6 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (8 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (10 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (12 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (14 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount4);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount4);
                }
                if (16 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (18 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (20 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (22 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
                if (24 == i) {
                    survivalAmount = survivalAmount.add(benefitsAmount5);
                    totalHealthyAmount = totalHealthyAmount.add(benefitsAmount5);
                }
            }
            if (mainCoveragePeriodi == i) {
                survivalAmount = survivalAmount.add(benefitsAmount8);
            }
            // 意外死亡保险金 意外高度残疾保险金
            interestMap.put("mainTotalAmountAddTotalAmount15", "$" + PrintCommon.getPrintAmountString(tpd_benefit_due_to_accident, 2));
            // 生存保险金
            interestMap.put("survivalAmount", "$" + PrintCommon.getPrintAmountString(survivalAmount, 1));
            // 女性重大疾病保险金
            interestMap.put("benefitsAmount6AddBenefitsAmount7", "$" + PrintCommon.getPrintAmountString(benefitsAmount6.add(benefitsAmount7), 1));
            interestMap.put("totalAmount14", "$" + PrintCommon.getPrintAmountString(totalAmount14, 1));
            interestMap.put("totalAmount15", "$" + PrintCommon.getPrintAmountString(totalAmount15, 1));
            interestMap.put("main_totalAmount_add_totalAmount15multiply_3", PrintCommon.getPrintString("$", death_benefit_due_to_accident, 2));
            interestMap.put("totalAmount15multiply3", PrintCommon.getPrintString("$", totalAmount15.multiply(new BigDecimal(3)), 1));
            interestMap.put("mainTotalAmount_15multiply3", PrintCommon.getPrintString("$", mainTotalAmount.add(totalAmount15.multiply(new BigDecimal(3))), 1));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            interestMap.put("interestMapPRO880000000000014", map.get("PRO880000000000014"));
            for (ApplyCoveragePlanBo applyCoveragePlanBo : listCoverage) {
                String productId = applyCoveragePlanBo.getProductId();
                interestMap.put("interestMap" + productId, "interestMap" + productId);
            }
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> "PRO880000000000021".equals(productCashValueBo.getProductId()) && finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                BigDecimal mainCashValue = ProductCalculation.getCashValue(listCashValue, "PRO880000000000021", i);
                BigDecimal cashValue14 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000014", i);
                BigDecimal cashValue15 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000015", i);
                interestMap.put("mainCashValue", PrintCommon.getPrintString("$", mainCashValue, 1));
                interestMap.put("cashValue14", PrintCommon.getPrintString("$", cashValue14, 1));
                interestMap.put("cashValue15", PrintCommon.getPrintString("$", cashValue15, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", mainCashValue.add(cashValue14).add(cashValue15), 1));
            }
            interestListMap.add(interestMap);
        }
        // 总的健康保险金
        map.put("totalHealthyAmount", PrintCommon.getPrintAmountString(totalHealthyAmount, 2));

        map.put("interestListMap", interestListMap);
        /************************************保险利益************************************************/
        map.put("mainTotalAmount", PrintCommon.getPrintString(mainTotalAmount, 2));
        map.put("totalAmount14", PrintCommon.getPrintString(totalAmount14, 2));
        map.put("totalAmount14multiply_25", "$" + PrintCommon.getPrintAmountString(totalAmount14.multiply(new BigDecimal("0.25")), 2));
        map.put("totalAmount14multiply_75", "$" + PrintCommon.getPrintAmountString(totalAmount14.multiply(new BigDecimal("0.75")), 2));
        map.put("totalAmount15", "$" + PrintCommon.getPrintAmountString(totalAmount15, 2));
        map.put("totalAmount15multiply_2", "$" + PrintCommon.getPrintAmountString(totalAmount15.multiply(new BigDecimal(2)), 2));
        map.put("totalAmount15multiply_3", "$" + PrintCommon.getPrintAmountString(totalAmount15.multiply(new BigDecimal(3)), 2));
        /************************************代理人信息************************************************/
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);

        return map;
    }

    private String getJson() {
        // 计划书
        return "{\"agentCode\":\"101103\",\"agentId\":\"GMA101103_AGENT_001\",\"agentMobile\":\"***********\",\"agentName\":\"尔诺\",\"applicant\":{\"applicantPlanId\":\"d9de97227eb84b3f8cb60f5b34fe67f2\",\"applicantType\":\"PERSONAL\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":************,\"bmi\":\"8.94\",\"companyAddress\":\"\",\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":null,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"66e0a748ae674283bb055e52ccb40d50\",\"degree\":null,\"email\":\"\",\"englishName\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":\"\",\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":\"UNMARRIED\",\"mobile\":\"111111\",\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"APPLICANT 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"sex\":\"FEMALE\",\"sexName\":\"女\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"updatedDate\":1640592303989,\"updatedUserId\":null,\"validFlag\":\"effective\",\"workType\":null,\"zipCode\":null},\"applyId\":\"f4ca96904f164a6c98c0287a224ac67f\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"applyPlanNo\":\"AJI21A00175\",\"backTrackDate\":null,\"coverages\":[{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"30000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"d17f58a325384b4ea55de06b42b1ffca\",\"createdDate\":1640592304415,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":45.00,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"relationship\":\"CHILD\",\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":null,\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"b716ba493346487aa45fc6fb7f83706d\",\"createdDate\":1640592304589,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":7.10,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"relationship\":\"CHILD\",\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"20000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"64c5b305258a4f82b82573b3cb718892\",\"createdDate\":1640592304211,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":508.00,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"relationship\":\"CHILD\",\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"20000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"bf5280a458c547d8bf8aa0c08600611c\",\"createdDate\":1640592304313,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":25.80,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"relationship\":\"CHILD\",\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":null,\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"c9a823efb88f47e7b13b860adcc06ccd\",\"createdDate\":1640592304501,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":4.80,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"relationship\":\"CHILD\",\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null}],\"createdDate\":*************,\"createdUserId\":null,\"currencyCode\":\"USD\",\"insured\":{\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"8.94\",\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"9d5cfbc848974155a76a994c877a21cb\",\"degree\":null,\"email\":null,\"englishName\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"insuredPlanId\":\"dc2e19030dc04b5c80814eea218ecc79\",\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"listCoverage\":[{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"30000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"d17f58a325384b4ea55de06b42b1ffca\",\"createdDate\":1640592304415,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":45.00,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"relationship\":\"CHILD\",\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":null,\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"b716ba493346487aa45fc6fb7f83706d\",\"createdDate\":1640592304589,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":7.10,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"relationship\":\"CHILD\",\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"20000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"64c5b305258a4f82b82573b3cb718892\",\"createdDate\":1640592304211,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":508.00,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"relationship\":\"CHILD\",\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":\"20000.00\",\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"bf5280a458c547d8bf8aa0c08600611c\",\"createdDate\":1640592304313,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":25.80,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"relationship\":\"CHILD\",\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null},{\"addPremiumPeriod\":null,\"addPremiumStartDate\":null,\"amount\":null,\"applyPlanId\":\"87295ace0cb24eb59183a69ce1e71a7e\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":null,\"coveragePeriodStartDate\":null,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coveragePlanId\":\"c9a823efb88f47e7b13b860adcc06ccd\",\"createdDate\":1640592304501,\"createdUserId\":null,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"mult\":\"1\",\"originalPremium\":4.80,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"periodCareerAddPremium\":null,\"periodStandardPremium\":null,\"periodWeakAddPremium\":null,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"relationship\":\"CHILD\",\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"waitPeriod\":null,\"waitPeriodEndDate\":null,\"yearCareerAddPremium\":null,\"yearStandardPremium\":null,\"yearWeakAddPremium\":null}],\"marriage\":\"UNMARRIED\",\"mobile\":\"1111111\",\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"INSURED 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationName\":\"Leaders and administration personnels of party and government organizations, enterprises and institutions\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"relationship\":\"CHILD\",\"relationshipName\":\"子女\",\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"sexName\":\"男\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"updatedDate\":1640592304089,\"updatedUserId\":null,\"validFlag\":\"effective\",\"workType\":null,\"zipCode\":null},\"listCashValue\":[{\"age\":19,\"amount\":null,\"cashValue\":0.00,\"policyYear\":1,\"productId\":\"PRO880000000000021\"},{\"age\":20,\"amount\":null,\"cashValue\":582.80,\"policyYear\":2,\"productId\":\"PRO880000000000021\"},{\"age\":21,\"amount\":null,\"cashValue\":1199.40,\"policyYear\":3,\"productId\":\"PRO880000000000021\"},{\"age\":22,\"amount\":null,\"cashValue\":1851.00,\"policyYear\":4,\"productId\":\"PRO880000000000021\"},{\"age\":23,\"amount\":null,\"cashValue\":2540.00,\"policyYear\":5,\"productId\":\"PRO880000000000021\"}],\"planLoanContract\":null,\"planProductDetail\":{\"coveragePremiumFrequencyMap\":{\"PRO880000000000021\":[{\"amount\":null,\"premiumFrequency\":\"SEMIANNUAL\",\"premiumFrequencyName\":\"半年缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":264.16},{\"amount\":null,\"premiumFrequency\":\"SEASON\",\"premiumFrequencyName\":\"季缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":137.16},{\"amount\":null,\"premiumFrequency\":\"MONTH\",\"premiumFrequencyName\":\"月缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":45.72}],\"PRO880000000000016A\":[{\"amount\":null,\"premiumFrequency\":\"SEMIANNUAL\",\"premiumFrequencyName\":\"半年缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":2.60},{\"amount\":null,\"premiumFrequency\":\"SEASON\",\"premiumFrequencyName\":\"季缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":1.40},{\"amount\":null,\"premiumFrequency\":\"MONTH\",\"premiumFrequencyName\":\"月缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":0.50}],\"PRO880000000000015\":[{\"amount\":null,\"premiumFrequency\":\"SEMIANNUAL\",\"premiumFrequencyName\":\"半年缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":23.40},{\"amount\":null,\"premiumFrequency\":\"SEASON\",\"premiumFrequencyName\":\"季缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":12.15},{\"amount\":null,\"premiumFrequency\":\"MONTH\",\"premiumFrequencyName\":\"月缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":4.05}],\"PRO880000000000016B\":[{\"amount\":null,\"premiumFrequency\":\"SEMIANNUAL\",\"premiumFrequencyName\":\"半年缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":3.80},{\"amount\":null,\"premiumFrequency\":\"SEASON\",\"premiumFrequencyName\":\"季缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":2.10},{\"amount\":null,\"premiumFrequency\":\"MONTH\",\"premiumFrequencyName\":\"月缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":0.70}],\"PRO880000000000014\":[{\"amount\":null,\"premiumFrequency\":\"SEMIANNUAL\",\"premiumFrequencyName\":\"半年缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":13.42},{\"amount\":null,\"premiumFrequency\":\"SEASON\",\"premiumFrequencyName\":\"季缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":6.97},{\"amount\":null,\"premiumFrequency\":\"MONTH\",\"premiumFrequencyName\":\"月缴\",\"productLevel\":null,\"productLevelName\":null,\"totalPremium\":2.32}]},\"individualizationData\":{\"productId\":\"PRO880000000000016A\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":4.8,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":9.60,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":14.40,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":19.20,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":24.00,\"policyMonth\":0,\"age\":23}],\"AccidentalDeathOrTPDBenefits2\":\"60,000.00\",\"AccidentalDeathOrTPDBenefits1\":\"30,000.00\",\"optionTwo\":false,\"optionThree\":false,\"productName\":\"Waiver of Premium Rider for Insured\",\"earlyStageCriticalIllnessBenefit\":\"5,000.00\",\"AccidentalDeathOrTPDBenefits3\":\"90,000.00\",\"optionOne\":true,\"returnValues\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0.00,\"totalPremium\":508.00,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":582.80,\"totalPremium\":1016.00,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1199.40,\"totalPremium\":1524.00,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1851.00,\"totalPremium\":2032.00,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":2540.00,\"totalPremium\":2540.00,\"policyMonth\":0,\"age\":23}],\"DeathOrTPDBenefits\":\"20,000.00\",\"lateStageCriticalIllnessBenefit1\":\"20,000.00\",\"lateStageCriticalIllnessBenefit2\":\"15,000.00\",\"maturityBenefit\":\"2,540.00\"},\"individualizationDatas\":[{\"returnValues\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0.00,\"totalPremium\":508.00,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":582.80,\"totalPremium\":1016.00,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1199.40,\"totalPremium\":1524.00,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1851.00,\"totalPremium\":2032.00,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":2540.00,\"totalPremium\":2540.00,\"policyMonth\":0,\"age\":23}],\"DeathOrTPDBenefits\":\"20,000.00\",\"productId\":\"PRO880000000000021\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0.00,\"totalPremium\":508.00,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":582.80,\"totalPremium\":1016.00,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1199.40,\"totalPremium\":1524.00,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":1851.00,\"totalPremium\":2032.00,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":2540.00,\"totalPremium\":2540.00,\"policyMonth\":0,\"age\":23}],\"maturityBenefit\":\"2,540.00\",\"productName\":\"GC Enrich Life\"},{\"productId\":\"PRO880000000000015\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":45,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":90.00,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":135.00,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":180.00,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":225.00,\"policyMonth\":0,\"age\":23}],\"AccidentalDeathOrTPDBenefits2\":\"60,000.00\",\"AccidentalDeathOrTPDBenefits1\":\"30,000.00\",\"productName\":\"Accidental Protection Rider\",\"AccidentalDeathOrTPDBenefits3\":\"90,000.00\"},{\"lateStageCriticalIllnessBenefit1\":\"20,000.00\",\"productId\":\"PRO880000000000014\",\"lateStageCriticalIllnessBenefit2\":\"15,000.00\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":25.8,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":51.60,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":77.40,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":103.20,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":129.00,\"policyMonth\":0,\"age\":23}],\"productName\":\"Critical Illness Plus Rider\",\"earlyStageCriticalIllnessBenefit\":\"5,000.00\"},{\"productId\":\"PRO880000000000016B\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":7.1,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":14.20,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":21.30,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":28.40,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":35.50,\"policyMonth\":0,\"age\":23}],\"optionTwo\":false,\"optionThree\":true,\"productName\":\"Waiver of Premium Rider for Payor\",\"optionOne\":false},{\"productId\":\"PRO880000000000016A\",\"returnDemos\":[{\"deathBenefit\":0,\"policyYear\":1,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":4.8,\"policyMonth\":0,\"age\":19},{\"deathBenefit\":0,\"policyYear\":2,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":9.60,\"policyMonth\":0,\"age\":20},{\"deathBenefit\":0,\"policyYear\":3,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":14.40,\"policyMonth\":0,\"age\":21},{\"deathBenefit\":0,\"policyYear\":4,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":19.20,\"policyMonth\":0,\"age\":22},{\"deathBenefit\":0,\"policyYear\":5,\"topAccountValue\":0,\"cost\":0,\"middleAccountValue\":0,\"lastAccountValue\":0,\"sex\":null,\"liveAmount\":0,\"cashValue\":0,\"totalPremium\":24.00,\"policyMonth\":0,\"age\":23}],\"optionTwo\":false,\"optionThree\":false,\"productName\":\"Waiver of Premium Rider for Insured\",\"optionOne\":true}],\"productBenefits\":[{\"age\":19,\"cashValue\":0.00,\"cost\":0,\"lastAccountValue\":0,\"liveAmount\":0,\"middleAccountValue\":0,\"policyMonth\":0,\"policyYear\":1,\"sex\":null,\"topAccountValue\":0,\"totalPremium\":590.70},{\"age\":20,\"cashValue\":582.80,\"cost\":0,\"lastAccountValue\":0,\"liveAmount\":0,\"middleAccountValue\":0,\"policyMonth\":0,\"policyYear\":2,\"sex\":null,\"topAccountValue\":0,\"totalPremium\":1181.40},{\"age\":21,\"cashValue\":1199.40,\"cost\":0,\"lastAccountValue\":0,\"liveAmount\":0,\"middleAccountValue\":0,\"policyMonth\":0,\"policyYear\":3,\"sex\":null,\"topAccountValue\":0,\"totalPremium\":1772.10},{\"age\":22,\"cashValue\":1851.00,\"cost\":0,\"lastAccountValue\":0,\"liveAmount\":0,\"middleAccountValue\":0,\"policyMonth\":0,\"policyYear\":4,\"sex\":null,\"topAccountValue\":0,\"totalPremium\":2362.80},{\"age\":23,\"cashValue\":2540.00,\"cost\":0,\"lastAccountValue\":0,\"liveAmount\":0,\"middleAccountValue\":0,\"policyMonth\":0,\"policyYear\":5,\"sex\":null,\"topAccountValue\":0,\"totalPremium\":2953.50}],\"productCategorys\":[{\"brannelUrl\":\"http://release-oss.gc-life.com/gclife/product/qyb/banner/1.jpg\",\"currencyCode\":\"USD\",\"dutyGetDesctions\":[],\"index\":100,\"insuranceNotice\":[],\"insuranceTerms\":[],\"mainProductFlag\":\"MAIN\",\"productId\":\"PRO880000000000021\",\"productName\":\"GC Enrich Life\"},{\"brannelUrl\":null,\"currencyCode\":\"USD\",\"dutyGetDesctions\":[],\"index\":200,\"insuranceNotice\":[],\"insuranceTerms\":[],\"mainProductFlag\":\"ADDITIONAL\",\"productId\":\"PRO880000000000016A\",\"productName\":\"Waiver of Premium Rider for Insured\"},{\"brannelUrl\":null,\"currencyCode\":\"USD\",\"dutyGetDesctions\":[],\"index\":200,\"insuranceNotice\":[],\"insuranceTerms\":[],\"mainProductFlag\":\"ADDITIONAL\",\"productId\":\"PRO880000000000015\",\"productName\":\"Accidental Protection Rider\"},{\"brannelUrl\":null,\"currencyCode\":\"USD\",\"dutyGetDesctions\":[],\"index\":200,\"insuranceNotice\":[],\"insuranceTerms\":[],\"mainProductFlag\":\"ADDITIONAL\",\"productId\":\"PRO880000000000016B\",\"productName\":\"Waiver of Premium Rider for Payor\"},{\"brannelUrl\":null,\"currencyCode\":\"USD\",\"dutyGetDesctions\":[],\"index\":200,\"insuranceNotice\":[],\"insuranceTerms\":[],\"mainProductFlag\":\"ADDITIONAL\",\"productId\":\"PRO880000000000014\",\"productName\":\"Critical Illness Plus Rider\"}],\"receiveCategorys\":[]},\"receivablePremium\":590.70,\"recipientName\":\"APPLICANT 11\",\"recipientSex\":\"FEMALE\",\"signature\":null,\"status\":\"FINISH\",\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\"}";
        // 保险证
        // return "{\"acceptBranchId\":null,\"applicant\":{\"addressType\":\"RESIDENCE\",\"applicantId\":\"1585e1a8cca14b40abbefa374a74785d\",\"applicantName\":null,\"applicantType\":\"PERSONAL\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"basisOfSumInsured\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAddressWhole\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":************,\"bmi\":\"8.94\",\"companyAddress\":\"\",\"companyAddressWhole\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractDept\":null,\"companyContractEmail\":null,\"companyContractIdExpDate\":null,\"companyContractIdNo\":null,\"companyContractIdType\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractNationality\":null,\"companyContractOfficeNumber\":null,\"companyContractPhone\":null,\"companyContractPosition\":null,\"companyEmail\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyIndustry\":null,\"companyLegalPersonIdExpDate\":null,\"companyLegalPersonIdNo\":null,\"companyLegalPersonIdType\":null,\"companyLegalPersonName\":null,\"companyLegalPersonNationality\":null,\"companyMobile\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":1640592370858,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"66e0a748ae674283bb055e52ccb40d50\",\"customerSource\":\"KNOWN_CUSTOMER\",\"degree\":null,\"delegateBirthday\":null,\"delegateCustomerId\":null,\"delegateIdNo\":null,\"delegateIdType\":null,\"delegateMobile\":null,\"delegateName\":null,\"doctorAddress\":\"\",\"doctorAreaCode\":null,\"doctorName\":\"\",\"email\":\"\",\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":\"\",\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Poipet 2\",\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAddressWhole\":null,\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":\"\",\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":\"UNMARRIED\",\"mobile\":\"111111\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"APPLICANT 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherCategorySpecify\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":null,\"policyStatus\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"salesBranchId\":null,\"sex\":\"FEMALE\",\"sexName\":\"女\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"taxRegistrationNo\":null,\"totalEmployeeNum\":null,\"totalLine\":0,\"updatedDate\":1640592303975,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":\"\",\"workType\":null,\"zipCode\":null},\"applicantId\":\"1585e1a8cca14b40abbefa374a74785d\",\"applyDate\":1640592305134,\"applyId\":\"f4ca96904f164a6c98c0287a224ac67f\",\"applyNo\":\"AAI21A00275\",\"applySource\":\"APP\",\"approveDate\":1640592368907,\"autoRenewalInsurance\":null,\"backTrackDate\":null,\"bizDate\":1640592368907,\"certifyId\":null,\"channelTypeCode\":\"AGENT\",\"createdDate\":1640592370783,\"createdUserId\":null,\"currencyCode\":\"USD\",\"dataEffectiveDate\":1640592368907,\"delegateBranchId\":null,\"effectiveDate\":1640592368907,\"firstPolicyNo\":\"API21A00130\",\"firstSalesBranchId\":\"GMA101103\",\"forceSave\":false,\"hesitation\":21,\"hesitationEndDate\":null,\"invalidDate\":null,\"listCashValue\":[{\"age\":19,\"amount\":null,\"cashValue\":0.00,\"policyYear\":1,\"productId\":\"PRO880000000000021\"},{\"age\":20,\"amount\":null,\"cashValue\":582.80,\"policyYear\":2,\"productId\":\"PRO880000000000021\"},{\"age\":21,\"amount\":null,\"cashValue\":1199.40,\"policyYear\":3,\"productId\":\"PRO880000000000021\"},{\"age\":22,\"amount\":null,\"cashValue\":1851.00,\"policyYear\":4,\"productId\":\"PRO880000000000021\"},{\"age\":23,\"amount\":null,\"cashValue\":2540.00,\"policyYear\":5,\"productId\":\"PRO880000000000021\"}],\"listCoverage\":[],\"listCoverageExtend\":[],\"listInsured\":[{\"addDate\":null,\"addressType\":\"RESIDENCE\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"8.94\",\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":164**********,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"9d5cfbc848974155a76a994c877a21cb\",\"degree\":null,\"doctorAddress\":null,\"doctorAreaCode\":null,\"doctorName\":null,\"effectiveDate\":null,\"email\":null,\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Poipet 2\",\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredStatus\":null,\"insuredType\":null,\"invalidDate\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"listCoverage\":[{\"actualPremium\":7.10,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371074,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":7.10,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":7.10,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"createdDate\":1640592371088,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":null,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":7.10,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":7.10,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":7.10,\"updatedDate\":1640592371612,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":508.00,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371013,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":508.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":508.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"createdDate\":1640592371016,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":null,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":508.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":508.00,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":508.00,\"updatedDate\":1640592371603,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":25.80,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371038,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":25.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":25.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"createdDate\":1640592371040,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":null,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":25.80,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":25.80,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":25.80,\"updatedDate\":1640592371609,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":45.00,\"agentId\":null,\"amount\":30000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"30000.00\",\"bonusSumAmount\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371043,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":45.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":45.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"createdDate\":1640592371045,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":null,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":45.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":45.00,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"30000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":45.00,\"updatedDate\":1640592371610,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":4.80,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371054,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":4.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":4.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"createdDate\":1640592371069,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":null,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":4.80,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":4.80,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":4.80,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null}],\"listPolicyBeneficiary\":[{\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"beneficiaryGrade\":null,\"beneficiaryId\":\"47b6a3e97c5f4c99a521d16d73ef5b0b\",\"beneficiaryNo\":null,\"beneficiaryNoOrder\":\"ORDER_ONE\",\"beneficiaryNoOrderName\":\"第一受益人\",\"beneficiaryProportion\":100.00,\"beneficiaryType\":null,\"claimPremDrawType\":null,\"createdDate\":*************,\"createdUserId\":null,\"forceSave\":false,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"listBeneficiaryAttachment\":[],\"modifyFlag\":\"YES\",\"policyBeneficiary\":{\"addressDetail\":null,\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"beneficiaryBranchCode\":null,\"beneficiaryBranchId\":null,\"beneficiaryBranchName\":null,\"beneficiaryId\":\"47b6a3e97c5f4c99a521d16d73ef5b0b\",\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"8.94\",\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"9d5cfbc848974155a76a994c877a21cb\",\"degree\":null,\"email\":null,\"englishName\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":\"UNMARRIED\",\"mobile\":\"1111111\",\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"INSURED 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":null,\"workType\":null,\"zipCode\":null},\"policyBeneficiaryId\":\"2f457bc8678b4af198010a511261e45d\",\"promiseAge\":null,\"promiseRate\":null,\"relationship\":\"ONESELF\",\"relationshipInstructions\":null,\"relationshipName\":\"本人\",\"totalLine\":0,\"transferGrantFlag\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"}],\"listPolicyCoverage\":[{\"actualPremium\":7.10,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371074,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":7.10,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":7.10,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"createdDate\":1640592371088,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":null,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":7.10,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":7.10,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":7.10,\"updatedDate\":1640592371612,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":508.00,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371013,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":508.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":508.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"createdDate\":1640592371016,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":null,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":508.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":508.00,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":508.00,\"updatedDate\":1640592371603,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":25.80,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371038,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":25.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":25.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"createdDate\":1640592371040,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":null,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":25.80,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":25.80,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":25.80,\"updatedDate\":1640592371609,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":45.00,\"agentId\":null,\"amount\":30000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"30000.00\",\"bonusSumAmount\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371043,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":45.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":45.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"createdDate\":1640592371045,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":null,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":45.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":45.00,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"30000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":45.00,\"updatedDate\":1640592371610,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":4.80,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371054,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":4.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":4.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"createdDate\":1640592371069,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":null,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":4.80,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":4.80,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":4.80,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null}],\"marriage\":\"UNMARRIED\",\"mobile\":\"1111111\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"mult\":\"1\",\"name\":\"INSURED 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationDuty\":null,\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyInsuredExtendPo\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"reinsuranceRules\":\"AUTOMATIC_RI\",\"relationship\":\"CHILD\",\"relationshipInstructions\":null,\"relationshipName\":\"子女\",\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"sexName\":\"男\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"taxpayerNo\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":null,\"workType\":null,\"zipCode\":null}],\"listInsuredCoverage\":[{\"actualPremium\":7.10,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371074,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":7.10,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":7.10,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"createdDate\":1640592371088,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":null,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":7.10,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":7.10,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":7.10,\"updatedDate\":1640592371612,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":508.00,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371013,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":508.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":508.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"createdDate\":1640592371016,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":null,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":508.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":508.00,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":508.00,\"updatedDate\":1640592371603,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":25.80,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371038,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":25.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":25.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"createdDate\":1640592371040,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":null,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":25.80,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":25.80,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":25.80,\"updatedDate\":1640592371609,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":45.00,\"agentId\":null,\"amount\":30000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"30000.00\",\"bonusSumAmount\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371043,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":45.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":45.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"createdDate\":1640592371045,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":null,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":45.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":45.00,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"30000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":45.00,\"updatedDate\":1640592371610,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":4.80,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371054,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":4.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":4.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"createdDate\":1640592371069,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":null,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":4.80,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":4.80,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":4.80,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null}],\"listPolicyAccount\":[{\"accountNo\":null,\"accountOwner\":null,\"accountType\":null,\"acctuserSignStatus\":null,\"applicantName\":\"APPLICANT 11\",\"authorizedDate\":*************,\"bankCode\":null,\"city\":null,\"createdDate\":*************,\"createdUserId\":null,\"forceSave\":false,\"idNo\":null,\"idType\":null,\"policyAccountId\":\"693d32baed7e414683ba08bc23971b28\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"relationship\":null,\"subbranch\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"useType\":\"PAY\",\"validFlag\":\"effective\"}],\"listPolicyAddPremium\":[],\"listPolicyAttachment\":[],\"listPolicyInsured\":[{\"addDate\":null,\"addressType\":\"RESIDENCE\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"8.94\",\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":164**********,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"9d5cfbc848974155a76a994c877a21cb\",\"degree\":null,\"doctorAddress\":null,\"doctorAreaCode\":null,\"doctorName\":null,\"effectiveDate\":null,\"email\":null,\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Poipet 2\",\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredStatus\":null,\"insuredType\":null,\"invalidDate\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"listCoverage\":[{\"actualPremium\":7.10,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371074,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":7.10,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":7.10,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"createdDate\":1640592371088,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":null,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":7.10,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":7.10,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":7.10,\"updatedDate\":1640592371612,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":508.00,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371013,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":508.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":508.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"createdDate\":1640592371016,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":null,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":508.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":508.00,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":508.00,\"updatedDate\":1640592371603,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":25.80,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371038,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":25.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":25.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"createdDate\":1640592371040,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":null,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":25.80,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":25.80,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":25.80,\"updatedDate\":1640592371609,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":45.00,\"agentId\":null,\"amount\":30000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"30000.00\",\"bonusSumAmount\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371043,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":45.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":45.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"createdDate\":1640592371045,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":null,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":45.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":45.00,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"30000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":45.00,\"updatedDate\":1640592371610,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":4.80,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371054,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":4.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":4.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"createdDate\":1640592371069,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":null,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":4.80,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":4.80,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":4.80,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null}],\"listPolicyBeneficiary\":[{\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"beneficiaryGrade\":null,\"beneficiaryId\":\"47b6a3e97c5f4c99a521d16d73ef5b0b\",\"beneficiaryNo\":null,\"beneficiaryNoOrder\":\"ORDER_ONE\",\"beneficiaryNoOrderName\":\"第一受益人\",\"beneficiaryProportion\":100.00,\"beneficiaryType\":null,\"claimPremDrawType\":null,\"createdDate\":*************,\"createdUserId\":null,\"forceSave\":false,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"listBeneficiaryAttachment\":[],\"modifyFlag\":\"YES\",\"policyBeneficiary\":{\"addressDetail\":null,\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"beneficiaryBranchCode\":null,\"beneficiaryBranchId\":null,\"beneficiaryBranchName\":null,\"beneficiaryId\":\"47b6a3e97c5f4c99a521d16d73ef5b0b\",\"birthPlace\":null,\"birthday\":*************,\"bmi\":\"8.94\",\"companyAddress\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractPhone\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":*************,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"9d5cfbc848974155a76a994c877a21cb\",\"degree\":null,\"email\":null,\"englishName\":null,\"facebookNo\":null,\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":null,\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":\"UNMARRIED\",\"mobile\":\"1111111\",\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"INSURED 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":null,\"workType\":null,\"zipCode\":null},\"policyBeneficiaryId\":\"2f457bc8678b4af198010a511261e45d\",\"promiseAge\":null,\"promiseRate\":null,\"relationship\":\"ONESELF\",\"relationshipInstructions\":null,\"relationshipName\":\"本人\",\"totalLine\":0,\"transferGrantFlag\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"}],\"listPolicyCoverage\":[{\"actualPremium\":7.10,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371074,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":7.10,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":7.10,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"createdDate\":1640592371088,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":null,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":7.10,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":7.10,\"careerAddPremium\":0.00,\"commissionFee\":0.71,\"commissionFeeRate\":10.00,\"coverageId\":\"c9bfd1d13beb44c69a7839eb69ff371b\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371089,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":7.10,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":7.10,\"periodStandardPremium\":7.10,\"periodStandardRate\":100.00,\"periodTotalPremium\":7.10,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"25ea4037ca5f49ba88174062c9571d64\",\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productName\":\"Waiver of Premium Rider for Payor\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPremium\":7.10,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":7.10,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"3c4af469717b48158cde2b42e89fbe5a\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":7.10,\"totalLine\":0,\"totalOriginalPremium\":7.10,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":7.10,\"premiumDiscount\":7.10,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_PAYOR\",\"productId\":\"PRO880000000000016B\",\"productLevel\":\"OPTION_THREE\",\"productLevelName\":\"选项3\",\"productName\":\"Waiver of Premium Rider for Payor\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":7.10,\"updatedDate\":1640592371612,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":508.00,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371013,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":508.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":508.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"createdDate\":1640592371016,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":null,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":508.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":508.00,\"careerAddPremium\":0.00,\"commissionFee\":45.72,\"commissionFeeRate\":9.00,\"coverageId\":\"bf7091b2a2b84e4ea5fe3fe1bfae029a\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371017,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":508.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":508.00,\"periodStandardPremium\":508.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":508.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"2c0c20cdcecc4e39baac2e6233c95215\",\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productName\":\"GC Enrich Life\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPremium\":508.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":508.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"d69bf549a8ed440e9a92b5121aee394c\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":508.00,\"totalLine\":0,\"totalOriginalPremium\":508.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":508.00,\"premiumDiscount\":508.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"MAIN\",\"prodSeq\":null,\"productCode\":\"DZHRS_QYB\",\"productId\":\"PRO880000000000021\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"GC Enrich Life\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":508.00,\"updatedDate\":1640592371603,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":25.80,\"agentId\":null,\"amount\":20000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"20000.00\",\"bonusSumAmount\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371038,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":25.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":25.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"createdDate\":1640592371040,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":null,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":25.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":25.80,\"careerAddPremium\":0.00,\"commissionFee\":2.58,\"commissionFeeRate\":10.00,\"coverageId\":\"9b9555b5c7e94763bdb9d556cb0f52f2\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371041,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":25.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":25.80,\"periodStandardPremium\":25.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":25.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"5c23d691f6f74204b45a68bd02bf88b7\",\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productName\":\"Critical Illness Plus Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPremium\":25.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":25.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"4a7bc61e4f5c4d058a1145dc278ef980\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":25.80,\"totalLine\":0,\"totalOriginalPremium\":25.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":25.80,\"premiumDiscount\":25.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJZDJBBX_PLUS\",\"productId\":\"PRO880000000000014\",\"productLevel\":\"ADDITIONAL_CI\",\"productLevelName\":\"额外给付\",\"productName\":\"Critical Illness Plus Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"20000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":25.80,\"updatedDate\":1640592371609,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":45.00,\"agentId\":null,\"amount\":30000.00,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":\"30000.00\",\"bonusSumAmount\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371043,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":45.00,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":45.00,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"createdDate\":1640592371045,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":null,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":45.00,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":45.00,\"careerAddPremium\":0.00,\"commissionFee\":4.50,\"commissionFeeRate\":10.00,\"coverageId\":\"b594d828b75241eaaa8c2d0bc7149eee\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371046,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":45.00,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":45.00,\"periodStandardPremium\":45.00,\"periodStandardRate\":100.00,\"periodTotalPremium\":45.00,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"7ab8169c1c4349fa9480eb6e797831fc\",\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productName\":\"Accidental Protection Rider\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPremium\":45.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":45.00,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"64bc5d917c564eb58a3cbdd3fb7165a3\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":45.00,\"totalLine\":0,\"totalOriginalPremium\":45.00,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":45.00,\"premiumDiscount\":45.00,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_FJYWSWHGCBX\",\"productId\":\"PRO880000000000015\",\"productLevel\":null,\"productLevelName\":null,\"productName\":\"Accidental Protection Rider\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":\"30000.00\",\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":45.00,\"updatedDate\":1640592371610,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null},{\"actualPremium\":4.80,\"agentId\":null,\"amount\":null,\"approveDate\":null,\"baseAmount\":null,\"baseDividendAmount\":null,\"basePremium\":null,\"baseSumAmount\":null,\"bonusSumAmount\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodEndDate\":1798358768906,\"coveragePeriodStartDate\":1640592368907,\"coveragePeriodUnit\":\"YEAR\",\"coveragePeriodUnitName\":\"年\",\"coverageStatus\":\"EFFECTIVE\",\"createdDate\":1640592371054,\"createdUserId\":null,\"customerId\":null,\"deductPremium\":0.00,\"deductRefundAmount\":0.00,\"dividendAmount\":null,\"dividendReceiveFrequency\":null,\"dividendReceiveMode\":null,\"dividendReceivePeriod\":null,\"dutyChooseFlag\":null,\"dutyId\":null,\"effectiveDate\":1640592368907,\"financingMethod\":null,\"forceSave\":false,\"frequency\":null,\"historyCoverageStatus\":null,\"insuredBirthday\":null,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"insuredName\":null,\"insuredSeq\":null,\"insuredSex\":null,\"lapseReason\":null,\"listAddPremium\":[],\"listCoverageDuty\":[],\"listCoverageLevel\":[],\"listPolicyCoverageDuty\":[],\"listPolicyCoveragePayment\":[],\"listPolicyCoverageSurvival\":[],\"maturityDate\":1798358768906,\"mult\":\"1\",\"originalAddPremium\":0,\"originalPremium\":4.80,\"originalStandardPremium\":0,\"paymentInstallments\":1,\"pensionReceiveDate\":null,\"pensionReceiveDateUnit\":null,\"pensionReceiveFrequency\":null,\"pensionReceiveMode\":null,\"pensionReceivePeriod\":\"0\",\"policyCoverageBonus\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremium\":{\"actualPremium\":4.80,\"addPremiumStartDate\":null,\"addPremiumTerm\":null,\"annOccuAddPremium\":null,\"annStandardPremium\":null,\"annWeakAddPremium\":null,\"consultFee\":null,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"createdDate\":1640592371069,\"createdUserId\":null,\"currencyCode\":\"USD\",\"forceSave\":false,\"frequency\":1,\"payStatus\":\"PAYMENT_SUCCESS\",\"payToDate\":1766768400000,\"paymentCompleteDate\":1766768400000,\"payoutFee\":null,\"periodOccuAddPremium\":null,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":null,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":null,\"policyCoveragePayment\":{\"actualPremium\":4.80,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"agencyFeeRate\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annStandardRate\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"basePremium\":4.80,\"careerAddPremium\":0.00,\"commissionFee\":0.48,\"commissionFeeRate\":10.00,\"coverageId\":\"e384abc2bf824a569438171fbb8d6e0d\",\"coveragePeriod\":\"5\",\"coveragePeriodUnit\":\"YEAR\",\"coverageYear\":0,\"createdDate\":1640592371070,\"createdUserId\":null,\"currencyCode\":\"USD\",\"effectiveDate\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"govStandardPremium\":0.00,\"govStandardRate\":0.00,\"insuredId\":\"43e81bea68744ea0b08aea90aa2f2794\",\"otherAddPremium\":0.00,\"paymentCompleteDate\":1766768400000,\"periodActualPremium\":4.80,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":4.80,\"periodStandardPremium\":4.80,\"periodStandardRate\":100.00,\"periodTotalPremium\":4.80,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyCoveragePaymentId\":\"56f3506f8a3d44b4973a242b6b4caef8\",\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"primaryFlag\":\"ADDITIONAL\",\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productName\":\"Waiver of Premium Rider for Insured\",\"receivableDate\":null,\"serviceChargeFee\":0.000000,\"serviceChargeRate\":0.00,\"standardPremium\":0.00,\"standardRate\":0.00,\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPremium\":4.80,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":4.80,\"valuePremiumRate\":100.00,\"weakAddPremium\":0.00},\"policyCoveragePremiumId\":\"f4540011b0ca424283d8bef9dcf23da4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodType\":\"YEAR\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"receivableDate\":1640592368907,\"refundAmount\":0.00,\"totalActualPremium\":4.80,\"totalLine\":0,\"totalOriginalPremium\":4.80,\"totalPeriod\":null,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyCoveragePremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"policyStatus\":null,\"premium\":4.80,\"premiumDiscount\":4.80,\"premiumFrequency\":\"YEAR\",\"premiumFrequencyName\":\"年缴\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPeriodUnitName\":\"年\",\"primaryFlag\":\"ADDITIONAL\",\"prodSeq\":null,\"productCode\":\"DZHRS_WOP_FOR_INSURED\",\"productId\":\"PRO880000000000016A\",\"productLevel\":\"OPTION_ONE\",\"productLevelName\":\"选项1\",\"productName\":\"Waiver of Premium Rider for Insured\",\"refundAmount\":0.00,\"relationship\":\"CHILD\",\"renewalPermitFlag\":\"YES\",\"specialTerm\":null,\"survivalReceiveFrequency\":null,\"survivalReceiveLevel\":null,\"survivalReceiveMode\":null,\"survivalReceivePeriod\":null,\"survivalReceivePeriodUnit\":null,\"totalAmount\":null,\"totalDividendAmount\":null,\"totalLine\":0,\"totalPremium\":4.80,\"updatedDate\":*************,\"updatedUserId\":null,\"validFlag\":\"effective\",\"versionNo\":null,\"versionNoCoverageId\":null,\"waitPeriod\":null,\"waitPeriodEndDate\":null}],\"marriage\":\"UNMARRIED\",\"mobile\":\"1111111\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"mult\":\"1\",\"name\":\"INSURED 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationDuty\":null,\"occupationType\":\"1\",\"ocrMrz\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyInsuredExtendPo\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"reinsuranceRules\":\"AUTOMATIC_RI\",\"relationship\":\"CHILD\",\"relationshipInstructions\":null,\"relationshipName\":\"子女\",\"rfidMrz\":null,\"salary\":null,\"sex\":\"MALE\",\"sexName\":\"男\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"taxpayerNo\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":null,\"workType\":null,\"zipCode\":null}],\"listPolicyInsuredExtend\":[],\"listPolicyPayment\":[{\"actualPremium\":590.70,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"applyDate\":1640592305134,\"basePremium\":590.70,\"bizBranchId\":\"GMA101103\",\"bizDate\":1640592368907,\"bizYearMonth\":\"202112\",\"businessId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"careerAddPremium\":0.00,\"checkDate\":null,\"commissionFee\":53.99,\"commissionGenerateFlag\":\"GENERATED\",\"coverageId\":null,\"createdDate\":1640592370904,\"createdUserId\":null,\"currencyCode\":\"USD\",\"discountModel\":null,\"discountPremiumFlag\":\"NO\",\"discountType\":null,\"discountTypeName\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"gainedDate\":1640592368907,\"govStandardPremium\":0.00,\"healthAddPremium\":0.00,\"insuredId\":null,\"insuredSum\":null,\"listPolicyCoveragePayment\":[],\"otherAddPremium\":0.00,\"payEndDate\":1766768400000,\"payModeCode\":\"CASH\",\"payModeCodeName\":\"现金支付\",\"payStatusCode\":\"PAYMENT_SUCCESS\",\"paymentBusinessType\":\"BUSINESS_TYPE_NEW_CONTRACT\",\"paymentCompleteDate\":1766768400000,\"paymentModeCode\":\"CASH\",\"paymentStatusCode\":\"PAYMENT_SUCCESS\",\"periodActualPremium\":590.70,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":590.70,\"periodStandardPremium\":590.70,\"periodTotalPremium\":590.70,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"policyPremiumId\":\"a7ae419f37404d41bf728ad6493df906\",\"policyYear\":1,\"premiumBeforeDiscount\":0,\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPersist\":null,\"premiumSource\":\"APP\",\"productId\":null,\"productLevel\":\"OPTION_THREE\",\"promotionType\":null,\"promotionTypeName\":null,\"providerId\":\"PRO8888888888888\",\"receivableDate\":1640592368907,\"serviceChargeFee\":0.000000,\"signTypeCode\":null,\"specialDiscount\":null,\"standardPremium\":0.00,\"statusCode\":\"RECEIVABLE\",\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":590.70,\"totalPremium\":590.70,\"updatedDate\":1640592371614,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":590.70,\"weakAddPremium\":0.00}],\"listPolicySpecialContract\":[],\"loanContract\":null,\"managerBranchId\":\"GMM101004\",\"maturityDate\":1798358768906,\"mustReturnFlag\":null,\"occupationNature\":[{\"businessNature\":null,\"createdDate\":1640592370921,\"createdUserId\":null,\"customerType\":\"APPLICANT\",\"employerName\":null,\"exactDuties\":\"222\",\"forceSave\":false,\"occupation\":\"22\",\"occupationNature\":\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\",\"occupationNatureSpecific\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyOccupationNatureId\":\"5f1463b7ad65447f9bb401b501f51a3b\",\"seq\":\"100\",\"totalLine\":0,\"updatedDate\":1640592370921,\"updatedUserId\":null,\"validFlag\":\"effective\"},{\"businessNature\":null,\"createdDate\":1640592370921,\"createdUserId\":null,\"customerType\":\"INSURED\",\"employerName\":null,\"exactDuties\":\"222\",\"forceSave\":false,\"occupation\":\"22\",\"occupationNature\":\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\",\"occupationNatureSpecific\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyOccupationNatureId\":\"d4bddfbf52e9497aa17c8f6a084005f8\",\"seq\":\"100\",\"totalLine\":0,\"updatedDate\":1640592370921,\"updatedUserId\":null,\"validFlag\":\"effective\"}],\"otherInsurance\":[],\"policyAgent\":{\"agentCode\":\"101103\",\"agentId\":\"GMA101103_AGENT_001\",\"agentName\":\"尔诺\",\"createdDate\":*************,\"createdUserId\":null,\"forceSave\":false,\"percent\":null,\"policyAgentId\":\"c6c3735ad5434160b8b826527e481ba4\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":\"API21A00130\",\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyApplicant\":{\"addressType\":\"RESIDENCE\",\"applicantId\":\"1585e1a8cca14b40abbefa374a74785d\",\"applicantName\":null,\"applicantType\":\"PERSONAL\",\"avoirdupois\":\"11\",\"bankAccountName\":null,\"bankAccountNo\":null,\"bankCode\":null,\"basisOfSumInsured\":null,\"belongsCompanyAddress\":null,\"belongsCompanyAddressWhole\":null,\"belongsCompanyAreaCode\":null,\"belongsCompanyFax\":null,\"belongsCompanyPhone\":null,\"belongsCompanyZipCode\":null,\"birthPlace\":null,\"birthday\":************,\"bmi\":\"8.94\",\"companyAddress\":\"\",\"companyAddressWhole\":null,\"companyAreaCode\":null,\"companyContractAddress\":null,\"companyContractDept\":null,\"companyContractEmail\":null,\"companyContractIdExpDate\":null,\"companyContractIdNo\":null,\"companyContractIdType\":null,\"companyContractMobile\":null,\"companyContractName\":null,\"companyContractNationality\":null,\"companyContractOfficeNumber\":null,\"companyContractPhone\":null,\"companyContractPosition\":null,\"companyEmail\":null,\"companyFax\":null,\"companyIdNo\":null,\"companyIdType\":null,\"companyIndustry\":null,\"companyLegalPersonIdExpDate\":null,\"companyLegalPersonIdNo\":null,\"companyLegalPersonIdType\":null,\"companyLegalPersonName\":null,\"companyLegalPersonNationality\":null,\"companyMobile\":null,\"companyName\":null,\"companyPhone\":null,\"companyType\":null,\"companyZipCode\":null,\"countryCode\":null,\"createdDate\":1640592370858,\"createdUserId\":null,\"creditGrade\":null,\"customerId\":\"66e0a748ae674283bb055e52ccb40d50\",\"customerSource\":\"KNOWN_CUSTOMER\",\"degree\":null,\"delegateBirthday\":null,\"delegateCustomerId\":null,\"delegateIdNo\":null,\"delegateIdType\":null,\"delegateMobile\":null,\"delegateName\":null,\"doctorAddress\":\"\",\"doctorAreaCode\":null,\"doctorName\":\"\",\"email\":\"\",\"englishName\":null,\"expectedPremiumSources\":null,\"expectedPremiumSourcesSpecific\":null,\"facebookNo\":\"\",\"familyIncome\":null,\"familyIncomeSource\":null,\"fax\":null,\"forceSave\":false,\"fullAddress\":\"Cambodia Banteay Meanchey Province Poipet 2\",\"headAttachId\":null,\"health\":null,\"homeAddress\":\"2\",\"homeAddressWhole\":null,\"homeAreaCode\":\"801100\",\"homeFax\":null,\"homePhone\":null,\"homeZipCode\":\"\",\"idAttachId\":null,\"idCategory\":null,\"idExpDate\":null,\"idNo\":\"1\",\"idType\":\"ID\",\"idTypeName\":\"身份证\",\"income\":\"INCOME_1\",\"incomeSource\":null,\"issueDate\":null,\"issuePlace\":null,\"joinCompanyDate\":null,\"license\":null,\"licenseType\":null,\"marriage\":\"UNMARRIED\",\"mobile\":\"111111\",\"mobile_2\":null,\"mrzOne\":null,\"mrzTwo\":null,\"name\":\"APPLICANT 11\",\"nationality\":\"CAMBODIA\",\"nations\":null,\"occupationCode\":\"**********\",\"occupationType\":\"1\",\"ocrMrz\":null,\"otherCategorySpecify\":null,\"otherPhone\":null,\"phone\":null,\"pluralityType\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":null,\"policyStatus\":null,\"position\":null,\"postalAddress\":null,\"registerAddress\":null,\"rfidMrz\":null,\"salary\":null,\"salesBranchId\":null,\"sex\":\"FEMALE\",\"sexName\":\"女\",\"smokeFlag\":null,\"socialSecurity\":null,\"startWorkDate\":null,\"stature\":\"111\",\"taxRegistrationNo\":null,\"totalEmployeeNum\":null,\"totalLine\":0,\"updatedDate\":1640592303975,\"updatedUserId\":null,\"validFlag\":\"effective\",\"wechatNo\":\"\",\"workType\":null,\"zipCode\":null},\"policyContactInfo\":{\"contractAddress\":\"2\",\"contractEmail\":null,\"contractHomePhone\":null,\"contractMobile\":\"111111\",\"contractName\":null,\"contractOfficePhone\":null,\"contractPhone\":null,\"createdDate\":1640592370919,\"createdUserId\":null,\"forceSave\":false,\"needPosLetter\":null,\"policyContactId\":\"7d15b402fc1d4176874f84b729429b45\",\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"postcodes\":\"\",\"sendAddrAreaCode\":\"801100\",\"smsServiceFlag\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyFactAgent\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyInsuredCollect\":null,\"policyNo\":\"API21A00130\",\"policyPayment\":{\"actualPremium\":590.70,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"applyDate\":1640592305134,\"basePremium\":590.70,\"bizBranchId\":\"GMA101103\",\"bizDate\":1640592368907,\"bizYearMonth\":\"202112\",\"businessId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"careerAddPremium\":0.00,\"checkDate\":null,\"commissionFee\":53.99,\"commissionGenerateFlag\":\"GENERATED\",\"coverageId\":null,\"createdDate\":1640592370904,\"createdUserId\":null,\"currencyCode\":\"USD\",\"discountModel\":null,\"discountPremiumFlag\":\"NO\",\"discountType\":null,\"discountTypeName\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"gainedDate\":1640592368907,\"govStandardPremium\":0.00,\"healthAddPremium\":0.00,\"insuredId\":null,\"insuredSum\":null,\"listPolicyCoveragePayment\":[],\"otherAddPremium\":0.00,\"payEndDate\":1766768400000,\"payModeCode\":\"CASH\",\"payModeCodeName\":\"现金支付\",\"payStatusCode\":\"PAYMENT_SUCCESS\",\"paymentBusinessType\":\"BUSINESS_TYPE_NEW_CONTRACT\",\"paymentCompleteDate\":1766768400000,\"paymentModeCode\":\"CASH\",\"paymentStatusCode\":\"PAYMENT_SUCCESS\",\"periodActualPremium\":590.70,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":590.70,\"periodStandardPremium\":590.70,\"periodTotalPremium\":590.70,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"policyPremiumId\":\"a7ae419f37404d41bf728ad6493df906\",\"policyYear\":1,\"premiumBeforeDiscount\":0,\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPersist\":null,\"premiumSource\":\"APP\",\"productId\":null,\"productLevel\":\"OPTION_THREE\",\"promotionType\":null,\"promotionTypeName\":null,\"providerId\":\"PRO8888888888888\",\"receivableDate\":1640592368907,\"serviceChargeFee\":0.000000,\"signTypeCode\":null,\"specialDiscount\":null,\"standardPremium\":0.00,\"statusCode\":\"RECEIVABLE\",\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":590.70,\"totalPremium\":590.70,\"updatedDate\":1640592371614,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":590.70,\"weakAddPremium\":0.00},\"policyPayorInfo\":null,\"policyPeriod\":1,\"policyPremium\":{\"actualPremium\":590.70,\"agentDiscount\":null,\"approveDate\":null,\"chargingMethod\":\"CASH\",\"companyDiscount\":null,\"consultFee\":null,\"createdDate\":1640592370863,\"createdUserId\":null,\"currencyCode\":\"USD\",\"discountModel\":null,\"discountType\":null,\"forceSave\":false,\"frequency\":\"1\",\"listCoveragePremium\":[],\"nextChargingMethod\":null,\"payStatus\":\"PAYMENT_SUCCESS\",\"paymentCompleteDate\":1766768400000,\"paymentNo\":\"284b5b4764e14cf5b94daff6d2ea7f5e\",\"payoutFee\":null,\"periodOriginalPremium\":590.70,\"periodTotalPremium\":590.70,\"policyBalance\":590.70,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyNo\":null,\"policyPayment\":{\"actualPremium\":590.70,\"addPremiumStartDate\":0,\"addPremiumTerm\":0,\"agencyFee\":0.00,\"annOccuAddPremium\":0.00,\"annStandardPremium\":0.00,\"annTotalPremium\":0.00,\"annWeakAddPremium\":0.00,\"applyDate\":1640592305134,\"basePremium\":590.70,\"bizBranchId\":\"GMA101103\",\"bizDate\":1640592368907,\"bizYearMonth\":\"202112\",\"businessId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"careerAddPremium\":0.00,\"checkDate\":null,\"commissionFee\":53.99,\"commissionGenerateFlag\":\"GENERATED\",\"coverageId\":null,\"createdDate\":1640592370904,\"createdUserId\":null,\"currencyCode\":\"USD\",\"discountModel\":null,\"discountPremiumFlag\":\"NO\",\"discountType\":null,\"discountTypeName\":null,\"extraPremium\":0.00,\"forceSave\":false,\"frequency\":1,\"gainedDate\":1640592368907,\"govStandardPremium\":0.00,\"healthAddPremium\":0.00,\"insuredId\":null,\"insuredSum\":null,\"listPolicyCoveragePayment\":[],\"otherAddPremium\":0.00,\"payEndDate\":1766768400000,\"payModeCode\":\"CASH\",\"payModeCodeName\":\"现金支付\",\"payStatusCode\":\"PAYMENT_SUCCESS\",\"paymentBusinessType\":\"BUSINESS_TYPE_NEW_CONTRACT\",\"paymentCompleteDate\":1766768400000,\"paymentModeCode\":\"CASH\",\"paymentStatusCode\":\"PAYMENT_SUCCESS\",\"periodActualPremium\":590.70,\"periodOccuAddPremium\":0.00,\"periodOriginalPremium\":590.70,\"periodStandardPremium\":590.70,\"periodTotalPremium\":590.70,\"periodWeakAddPremium\":0.00,\"policyAddPremiumId\":null,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"policyPaymentId\":\"a048fd85cba647c7a5efd4397d3e87e6\",\"policyPremiumId\":\"a7ae419f37404d41bf728ad6493df906\",\"policyYear\":1,\"premiumBeforeDiscount\":0,\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumPersist\":null,\"premiumSource\":\"APP\",\"productId\":null,\"productLevel\":\"OPTION_THREE\",\"promotionType\":null,\"promotionTypeName\":null,\"providerId\":\"PRO8888888888888\",\"receivableDate\":1640592368907,\"serviceChargeFee\":0.000000,\"signTypeCode\":null,\"specialDiscount\":null,\"standardPremium\":0.00,\"statusCode\":\"RECEIVABLE\",\"totalAddPremium\":0.00,\"totalDiscountPremium\":0.00,\"totalLine\":0,\"totalOriginalPremium\":590.70,\"totalPremium\":590.70,\"updatedDate\":1640592371614,\"updatedUserId\":null,\"validFlag\":\"effective\",\"valuePremium\":590.70,\"weakAddPremium\":0.00},\"policyPremiumId\":\"a7ae419f37404d41bf728ad6493df906\",\"policyStatus\":null,\"premDueDate\":1640592368907,\"premiumBeforeDiscount\":null,\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"5\",\"premiumPeriodUnit\":\"YEAR\",\"premiumSource\":null,\"premiumStatus\":\"PAYMENT_SUCCESS\",\"promotionType\":null,\"receivableDate\":1640592368907,\"remark\":null,\"specialDiscount\":null,\"totalActualPremium\":590.70,\"totalDeductPremium\":0.00,\"totalDeductRefundAmount\":0.00,\"totalLine\":0,\"totalOriginalPremium\":590.70,\"totalPeriod\":null,\"totalRefundAmount\":0.00,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyPrintInfo\":null,\"policyReceiptInfo\":{\"createdDate\":1640592370928,\"createdUserId\":null,\"forceSave\":false,\"policyId\":\"9b77f8ab5ec647b5b2690f262657151b\",\"receiptDate\":null,\"receiptInfoId\":\"0d6c6a44cafc450c9c4629ee97dba413\",\"receiptNo\":null,\"receiptReturnDate\":null,\"receiptReviewDecisionRemark\":null,\"receiptStatus\":null,\"receiptSubmitDate\":null,\"returnDate\":null,\"signDate\":null,\"submitDate\":null,\"totalLine\":0,\"updatedDate\":null,\"updatedUserId\":null,\"validFlag\":\"effective\"},\"policyStatus\":\"POLICY_STATUS_EFFECTIVE\",\"policyType\":\"LIFE_INSURANCE_PERSONAL\",\"preUnderwritingFlag\":null,\"providerId\":\"PRO8888888888888\",\"referralInfo\":null,\"riskCommencementDate\":1640592368907,\"salesBranchId\":\"GMA101103\",\"selfInsuranceFlag\":\"NO\",\"signBranchId\":\"GMM\",\"statements\":[],\"thoroughInvalidDate\":null,\"totalLine\":0,\"updatedDate\":1640592371613,\"updatedUserId\":null,\"validFlag\":\"effective\",\"verifyNo\":\"14075757\",\"versionNo\":\"20211227150610\"}";

    }

}