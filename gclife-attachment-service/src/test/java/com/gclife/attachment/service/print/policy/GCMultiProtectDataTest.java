package com.gclife.attachment.service.print.policy;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;

/**
 * <AUTHOR>
 * @date 2021/10/29
 */
public class GCMultiProtectDataTest extends BaseBusinessServiceImpl {
    /*
    public static final String FILE_PATH = "C:\\Users\\<USER>\\Documents\\policyTemplate\\product\\PRO880000000000020\\";

    // public static final String FILE_NAME = "PRODUCT_17_APPLY_ZH_CN";
    // public static final String FILE_NAME = "PRODUCT_17_APPLY_EN_US_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_17_POLICY_ZH_CN";

    // public static final String FILE_NAME = "PRODUCT_20_PLAN_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_20_PLAN_EN_US";
    // public static final String FILE_NAME = "PRODUCT_20_PLAN_ZH_CN";

    // public static final String FILE_NAME = "PRODUCT_20_APPLY_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_20_APPLY_EN_US";
    // public static final String FILE_NAME = "PRODUCT_20_APPLY_ZH_CN";

    // public static final String FILE_NAME = "PRODUCT_20_POLICY_KM_KH";
    // public static final String FILE_NAME = "PRODUCT_20_POLICY_EN_US";
    public static final String FILE_NAME = "PRODUCT_20_POLICY_ZH_CN";

    @Test
    public void rtfToPdf() throws Exception {

        // 计划书 中文
        // String json = "{\"attachmentResponseList\":[],\"content\":\"{\\\"agentCode\\\":\\\"101103\\\",\\\"agentId\\\":\\\"GMA101103_AGENT_001\\\",\\\"agentMobile\\\":\\\"***********\\\",\\\"agentName\\\":\\\"尔诺\\\",\\\"applicant\\\":{\\\"applicantPlanId\\\":\\\"f9b1141e5d2f4783900417378d4a1040\\\",\\\"applicantType\\\":null,\\\"applyPlanId\\\":\\\"fd3b2690369b4c4abdf2eb78d0daa384\\\",\\\"avoirdupois\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":null,\\\"companyAddress\\\":null,\\\"companyAreaCode\\\":null,\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":null,\\\"degree\\\":null,\\\"email\\\":null,\\\"englishName\\\":null,\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":null,\\\"homeAreaCode\\\":null,\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":null,\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":null,\\\"idNo\\\":null,\\\"idType\\\":null,\\\"idTypeName\\\":null,\\\"income\\\":null,\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":null,\\\"mobile\\\":null,\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"APPLICANT 41 DZQM\\\",\\\"nationality\\\":null,\\\"nations\\\":null,\\\"occupationCode\\\":null,\\\"occupationType\\\":null,\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"Female\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":null,\\\"updatedDate\\\":1639623315080,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"applyId\\\":\\\"6955076145c64a19b5ffab957f8b0ab0\\\",\\\"applyPlanId\\\":\\\"fd3b2690369b4c4abdf2eb78d0daa384\\\",\\\"applyPlanNo\\\":\\\"AJI21A00143\\\",\\\"backTrackDate\\\":null,\\\"coverages\\\":[{\\\"addPremiumPeriod\\\":null,\\\"addPremiumStartDate\\\":null,\\\"amount\\\":\\\"20000.00\\\",\\\"applyPlanId\\\":\\\"fd3b2690369b4c4abdf2eb78d0daa384\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":null,\\\"coveragePeriodStartDate\\\":null,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\" Year(s)\\\",\\\"coveragePlanId\\\":\\\"62ff9b19212b4091bab84a39c3f7473b\\\",\\\"createdDate\\\":1639623313346,\\\"createdUserId\\\":null,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"mult\\\":\\\"1\\\",\\\"originalPremium\\\":403.00,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"periodCareerAddPremium\\\":null,\\\"periodStandardPremium\\\":null,\\\"periodWeakAddPremium\\\":null,\\\"premiumDiscount\\\":403.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"Annual\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\" Year(s)\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"relationship\\\":null,\\\"totalPremium\\\":403.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null,\\\"yearCareerAddPremium\\\":null,\\\"yearStandardPremium\\\":null,\\\"yearWeakAddPremium\\\":null}],\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"insured\\\":{\\\"applyPlanId\\\":\\\"fd3b2690369b4c4abdf2eb78d0daa384\\\",\\\"avoirdupois\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":null,\\\"companyAddress\\\":null,\\\"companyAreaCode\\\":null,\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":null,\\\"degree\\\":null,\\\"email\\\":null,\\\"englishName\\\":null,\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":null,\\\"homeAreaCode\\\":null,\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":null,\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":null,\\\"idNo\\\":null,\\\"idType\\\":null,\\\"idTypeName\\\":null,\\\"income\\\":null,\\\"incomeSource\\\":null,\\\"insuredPlanId\\\":\\\"a0706c6593fd465d8cab2895785d02d5\\\",\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"listCoverage\\\":[{\\\"addPremiumPeriod\\\":null,\\\"addPremiumStartDate\\\":null,\\\"amount\\\":\\\"20000.00\\\",\\\"applyPlanId\\\":\\\"fd3b2690369b4c4abdf2eb78d0daa384\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":null,\\\"coveragePeriodStartDate\\\":null,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\" Year(s)\\\",\\\"coveragePlanId\\\":\\\"62ff9b19212b4091bab84a39c3f7473b\\\",\\\"createdDate\\\":1639623313346,\\\"createdUserId\\\":null,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"mult\\\":\\\"1\\\",\\\"originalPremium\\\":403.00,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"periodCareerAddPremium\\\":null,\\\"periodStandardPremium\\\":null,\\\"periodWeakAddPremium\\\":null,\\\"premiumDiscount\\\":403.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"Annual\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\" Year(s)\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"relationship\\\":null,\\\"totalPremium\\\":403.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null,\\\"yearCareerAddPremium\\\":null,\\\"yearStandardPremium\\\":null,\\\"yearWeakAddPremium\\\":null}],\\\"marriage\\\":null,\\\"mobile\\\":null,\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"APPLICANT 41 DZQM\\\",\\\"nationality\\\":null,\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationName\\\":\\\"Leaders and administration personnels of party and government organizations, enterprises and institutions\\\",\\\"occupationType\\\":null,\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"relationship\\\":\\\"ONESELF\\\",\\\"relationshipName\\\":\\\"Self\\\",\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"Female\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":null,\\\"updatedDate\\\":1639623315085,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"listCashValue\\\":[{\\\"age\\\":27,\\\"amount\\\":null,\\\"cashValue\\\":0.00,\\\"policyYear\\\":1,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":28,\\\"amount\\\":null,\\\"cashValue\\\":462.80,\\\"policyYear\\\":2,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":29,\\\"amount\\\":null,\\\"cashValue\\\":952.60,\\\"policyYear\\\":3,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":30,\\\"amount\\\":null,\\\"cashValue\\\":1469.40,\\\"policyYear\\\":4,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":31,\\\"amount\\\":null,\\\"cashValue\\\":2015.00,\\\"policyYear\\\":5,\\\"productId\\\":\\\"PRO880000000000020\\\"}],\\\"planLoanContract\\\":null,\\\"planProductDetail\\\":{\\\"coveragePremiumFrequencyMap\\\":{\\\"PRO880000000000020\\\":[{\\\"amount\\\":null,\\\"premiumFrequency\\\":\\\"SEMIANNUAL\\\",\\\"premiumFrequencyName\\\":\\\"Semi-Annual\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"totalPremium\\\":209.56},{\\\"amount\\\":null,\\\"premiumFrequency\\\":\\\"SEASON\\\",\\\"premiumFrequencyName\\\":\\\"Quarterly\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"totalPremium\\\":108.81},{\\\"amount\\\":null,\\\"premiumFrequency\\\":\\\"MONTH\\\",\\\"premiumFrequencyName\\\":\\\"Monthly\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"totalPremium\\\":36.27}]},\\\"individualizationData\\\":{\\\"returnValues\\\":[{\\\"deathBenefit\\\":0,\\\"policyYear\\\":1,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":0.00,\\\"totalPremium\\\":403.00,\\\"policyMonth\\\":0,\\\"age\\\":27},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":2,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":462.80,\\\"totalPremium\\\":806.00,\\\"policyMonth\\\":0,\\\"age\\\":28},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":3,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":952.60,\\\"totalPremium\\\":1209.00,\\\"policyMonth\\\":0,\\\"age\\\":29},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":4,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":1469.40,\\\"totalPremium\\\":1612.00,\\\"policyMonth\\\":0,\\\"age\\\":30},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":5,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":2015.00,\\\"totalPremium\\\":2015.00,\\\"policyMonth\\\":0,\\\"age\\\":31}],\\\"DeathOrTPDBenefits\\\":\\\"20,000.00\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"returnDemos\\\":[{\\\"deathBenefit\\\":0,\\\"policyYear\\\":1,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":0.00,\\\"totalPremium\\\":403.00,\\\"policyMonth\\\":0,\\\"age\\\":27},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":2,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":462.80,\\\"totalPremium\\\":806.00,\\\"policyMonth\\\":0,\\\"age\\\":28},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":3,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":952.60,\\\"totalPremium\\\":1209.00,\\\"policyMonth\\\":0,\\\"age\\\":29},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":4,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":1469.40,\\\"totalPremium\\\":1612.00,\\\"policyMonth\\\":0,\\\"age\\\":30},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":5,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":2015.00,\\\"totalPremium\\\":2015.00,\\\"policyMonth\\\":0,\\\"age\\\":31}],\\\"maturityBenefit\\\":\\\"2,015.00\\\",\\\"productName\\\":\\\"GC Enrich Life\\\"},\\\"individualizationDatas\\\":[{\\\"returnValues\\\":[{\\\"deathBenefit\\\":0,\\\"policyYear\\\":1,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":0.00,\\\"totalPremium\\\":403.00,\\\"policyMonth\\\":0,\\\"age\\\":27},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":2,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":462.80,\\\"totalPremium\\\":806.00,\\\"policyMonth\\\":0,\\\"age\\\":28},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":3,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":952.60,\\\"totalPremium\\\":1209.00,\\\"policyMonth\\\":0,\\\"age\\\":29},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":4,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":1469.40,\\\"totalPremium\\\":1612.00,\\\"policyMonth\\\":0,\\\"age\\\":30},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":5,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":2015.00,\\\"totalPremium\\\":2015.00,\\\"policyMonth\\\":0,\\\"age\\\":31}],\\\"DeathOrTPDBenefits\\\":\\\"20,000.00\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"returnDemos\\\":[{\\\"deathBenefit\\\":0,\\\"policyYear\\\":1,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":0.00,\\\"totalPremium\\\":403.00,\\\"policyMonth\\\":0,\\\"age\\\":27},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":2,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":462.80,\\\"totalPremium\\\":806.00,\\\"policyMonth\\\":0,\\\"age\\\":28},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":3,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":952.60,\\\"totalPremium\\\":1209.00,\\\"policyMonth\\\":0,\\\"age\\\":29},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":4,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":1469.40,\\\"totalPremium\\\":1612.00,\\\"policyMonth\\\":0,\\\"age\\\":30},{\\\"deathBenefit\\\":0,\\\"policyYear\\\":5,\\\"topAccountValue\\\":0,\\\"cost\\\":0,\\\"middleAccountValue\\\":0,\\\"lastAccountValue\\\":0,\\\"sex\\\":null,\\\"liveAmount\\\":0,\\\"cashValue\\\":2015.00,\\\"totalPremium\\\":2015.00,\\\"policyMonth\\\":0,\\\"age\\\":31}],\\\"maturityBenefit\\\":\\\"2,015.00\\\",\\\"productName\\\":\\\"GC Enrich Life\\\"}],\\\"productBenefits\\\":[{\\\"age\\\":27,\\\"cashValue\\\":0.00,\\\"cost\\\":0,\\\"lastAccountValue\\\":0,\\\"liveAmount\\\":0,\\\"middleAccountValue\\\":0,\\\"policyMonth\\\":0,\\\"policyYear\\\":1,\\\"sex\\\":null,\\\"topAccountValue\\\":0,\\\"totalPremium\\\":403.00},{\\\"age\\\":28,\\\"cashValue\\\":462.80,\\\"cost\\\":0,\\\"lastAccountValue\\\":0,\\\"liveAmount\\\":0,\\\"middleAccountValue\\\":0,\\\"policyMonth\\\":0,\\\"policyYear\\\":2,\\\"sex\\\":null,\\\"topAccountValue\\\":0,\\\"totalPremium\\\":806.00},{\\\"age\\\":29,\\\"cashValue\\\":952.60,\\\"cost\\\":0,\\\"lastAccountValue\\\":0,\\\"liveAmount\\\":0,\\\"middleAccountValue\\\":0,\\\"policyMonth\\\":0,\\\"policyYear\\\":3,\\\"sex\\\":null,\\\"topAccountValue\\\":0,\\\"totalPremium\\\":1209.00},{\\\"age\\\":30,\\\"cashValue\\\":1469.40,\\\"cost\\\":0,\\\"lastAccountValue\\\":0,\\\"liveAmount\\\":0,\\\"middleAccountValue\\\":0,\\\"policyMonth\\\":0,\\\"policyYear\\\":4,\\\"sex\\\":null,\\\"topAccountValue\\\":0,\\\"totalPremium\\\":1612.00},{\\\"age\\\":31,\\\"cashValue\\\":2015.00,\\\"cost\\\":0,\\\"lastAccountValue\\\":0,\\\"liveAmount\\\":0,\\\"middleAccountValue\\\":0,\\\"policyMonth\\\":0,\\\"policyYear\\\":5,\\\"sex\\\":null,\\\"topAccountValue\\\":0,\\\"totalPremium\\\":2015.00}],\\\"productCategorys\\\":[{\\\"brannelUrl\\\":\\\"http://release-oss.gc-life.com/gclife/product/qyb/banner/1.jpg\\\",\\\"currencyCode\\\":\\\"USD\\\",\\\"dutyGetDesctions\\\":[],\\\"index\\\":100,\\\"insuranceNotice\\\":[],\\\"insuranceTerms\\\":[],\\\"mainProductFlag\\\":\\\"MAIN\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productName\\\":\\\"GC Enrich Life\\\"}],\\\"receiveCategorys\\\":[]},\\\"receivablePremium\\\":403.00,\\\"recipientName\\\":\\\"APPLICANT 41 DZQM\\\",\\\"recipientSex\\\":\\\"FEMALE\\\",\\\"signature\\\":null,\\\"status\\\":\\\"FINISH\\\",\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}\",\"language\":\"EN_US\",\"pdfType\":\"PLAN\",\"productId\":\"PRO880000000000020\"}";
        // 投保单 中文
        // String json = "{\"attachmentResponseList\":[],\"content\":\"{\\\"acceptBranchId\\\":null,\\\"acceptBranchName\\\":\\\"隆边区营业部\\\",\\\"agentSignStatus\\\":null,\\\"appSubmitUnderwritingDate\\\":*************,\\\"applicant\\\":{\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"applicantId\\\":\\\"53835d04e9cf4eaa9071d5adc2d9c0ed\\\",\\\"applicantType\\\":\\\"PERSONAL\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":null,\\\"avoirdupois\\\":\\\"44\\\",\\\"backTrackDate\\\":null,\\\"backTrackDateFormat\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"basisOfSumInsured\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAddressWhole\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"7.39\\\",\\\"companyAddress\\\":\\\"66\\\",\\\"companyAddressWhole\\\":\\\"Cambodia Battambang Province Samlout Kampong Lpov Kampong Lpov 66\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyAreaName\\\":\\\"Cambodia Battambang Province Samlout Kampong Lpov Kampong Lpov\\\",\\\"companyContractAddress\\\":null,\\\"companyContractDept\\\":null,\\\"companyContractEmail\\\":null,\\\"companyContractIdExpDate\\\":null,\\\"companyContractIdNo\\\":null,\\\"companyContractIdType\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractNationality\\\":null,\\\"companyContractOfficeNumber\\\":null,\\\"companyContractPhone\\\":null,\\\"companyContractPosition\\\":null,\\\"companyEmail\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyIndustry\\\":null,\\\"companyLegalPersonIdExpDate\\\":null,\\\"companyLegalPersonIdNo\\\":null,\\\"companyLegalPersonIdType\\\":null,\\\"companyLegalPersonName\\\":null,\\\"companyLegalPersonNationality\\\":null,\\\"companyMobile\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":null,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"583561548e59421c9de47f427a3f2fa9\\\",\\\"customerSource\\\":\\\"KNOWN_CUSTOMER\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"degreeName\\\":null,\\\"delegateBirthday\\\":null,\\\"delegateCustomerId\\\":null,\\\"delegateIdNo\\\":null,\\\"delegateIdType\\\":null,\\\"delegateMobile\\\":null,\\\"delegateName\\\":null,\\\"doctorAddress\\\":\\\"88\\\",\\\"doctorAreaCode\\\":\\\"80300202001\\\",\\\"doctorAreaCodeName\\\":\\\"Cambodia Kampong Cham Province Chamkar Leu Chamkar Andoung Chamkar Andoung\\\",\\\"doctorName\\\":\\\"8\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"55\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Banteay Meanchey Province Poipet 6\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"6\\\",\\\"homeAddressWhole\\\":null,\\\"homeAreaCode\\\":\\\"801100\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"666\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1767200400000,\\\"idNo\\\":\\\"4\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeName\\\":\\\"<5,000\\\",\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"listExpectedPremiumSources\\\":[],\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"44441\\\",\\\"mobile_2\\\":\\\"5\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"APPLICANT 41 DZQM\\\",\\\"nationality\\\":\\\"CAMBODIA\\\",\\\"nationalityName\\\":\\\"柬埔寨\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationName\\\":\\\"党政机关、企事业单位负责人和行政管理人员\\\",\\\"occupationNature\\\":null,\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherCategorySpecify\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityName\\\":null,\\\"pluralityType\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"女\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"socialSecurityName\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"244\\\",\\\"taxRegistrationNo\\\":null,\\\"totalEmployeeNum\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056005764,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"555\\\",\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"applicantHealthRemark\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applicantSignStatus\\\":null,\\\"applyAcceptBo\\\":null,\\\"applyAgentBo\\\":{\\\"agentCode\\\":\\\"101103\\\",\\\"agentId\\\":\\\"GMA101103_AGENT_001\\\",\\\"agentMobile\\\":\\\"***********\\\",\\\"agentName\\\":\\\"尔诺\\\",\\\"applyAgentId\\\":\\\"069ffcf8c5e44293921277b367faf685\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1639016075574,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"percent\\\":null,\\\"recommendAgentName\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"applyAttachmentConfigBo\\\":null,\\\"applyContact\\\":{\\\"applyContactId\\\":\\\"7e897985dd2d44538ff5505237cc4bdf\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"contractEmail\\\":null,\\\"contractHomePhone\\\":null,\\\"contractMobile\\\":\\\"44441\\\",\\\"contractName\\\":\\\"APPLICANT 41 DZQM\\\",\\\"contractOfficePhone\\\":null,\\\"contractPhone\\\":null,\\\"createdDate\\\":1639016278995,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"needPosLetter\\\":null,\\\"postcodes\\\":\\\"666\\\",\\\"sendAddrAreaCode\\\":\\\"801100\\\",\\\"sendAddrContact\\\":\\\"6\\\",\\\"sendType\\\":null,\\\"smsPhone\\\":null,\\\"smsServiceFlag\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"applyDate\\\":*************,\\\"applyFactAgentPo\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyInsuredCollect\\\":null,\\\"applyNo\\\":\\\"AAI21A00199\\\",\\\"applyPaymentTransactionBo\\\":{\\\"actualPayDate\\\":***********89,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyPaymentTransactionItemBos\\\":[{\\\"accountId\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"forceSave\\\":false,\\\"paymentAmount\\\":526.00,\\\"paymentItemId\\\":\\\"542a4c1e23464ee59dd65107ee2b12ae\\\",\\\"paymentMethodCode\\\":\\\"CASH\\\",\\\"paymentParam\\\":null,\\\"paymentStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentTransactionId\\\":\\\"fb408eee093b43ba995a6d3b63f5991f\\\",\\\"paymentTransactionItemId\\\":\\\"89968a5eb5474a1ab095d5c8c70d2646\\\",\\\"paymentTypeCode\\\":\\\"ACTUAL\\\",\\\"returnParam\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"}],\\\"arrivalDate\\\":*************,\\\"createdDate\\\":*************,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"feeType\\\":\\\"SUSPENSE_PREMIUM\\\",\\\"forceSave\\\":false,\\\"matchResult\\\":\\\"YES\\\",\\\"paymentAmount\\\":526.00,\\\"paymentDate\\\":*************,\\\"paymentId\\\":\\\"cf6b08af42954fd5a2bb12a7f0180b60\\\",\\\"paymentStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentTransactionId\\\":\\\"fb408eee093b43ba995a6d3b63f5991f\\\",\\\"paymentType\\\":\\\"PREPAID_PREMIUM\\\",\\\"premiumId\\\":\\\"6135a3ee227f4731ab325fae7c444b4c\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"},\\\"applyPlanNo\\\":\\\"AJI21A00104\\\",\\\"applyPremiumBo\\\":{\\\"actualPremium\\\":526.00,\\\"addPremium\\\":0.00,\\\"advancePremium\\\":null,\\\"agentDiscount\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"arrivalDate\\\":1640056033470,\\\"companyDiscount\\\":null,\\\"createdDate\\\":1639016375317,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"currencyCode\\\":\\\"USD\\\",\\\"discountModel\\\":null,\\\"discountPremium\\\":null,\\\"discountType\\\":null,\\\"dividenoPremium\\\":null,\\\"expireEndTime\\\":null,\\\"feeType\\\":null,\\\"forceSave\\\":false,\\\"listApplyPaymentAttachment\\\":[],\\\"matchResult\\\":\\\"YES\\\",\\\"originalPremium\\\":526.00,\\\"overPremium\\\":null,\\\"paymentCodeNo\\\":null,\\\"paymentId\\\":\\\"cf6b08af42954fd5a2bb12a7f0180b60\\\",\\\"paymentType\\\":\\\"PREPAID_PREMIUM\\\",\\\"paymentUrl\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodTotalPremium\\\":526.00,\\\"policyPeriod\\\":\\\"1\\\",\\\"policyYear\\\":\\\"1\\\",\\\"premiumBeforeDiscount\\\":null,\\\"premiumId\\\":\\\"6135a3ee227f4731ab325fae7c444b4c\\\",\\\"premiumStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"printCount\\\":null,\\\"promotionType\\\":null,\\\"receivableDate\\\":***********89,\\\"receivablePremium\\\":526.00,\\\"remark\\\":null,\\\"shortPremium\\\":null,\\\"specialDiscount\\\":null,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":1640056033470,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"},\\\"applySource\\\":\\\"APP\\\",\\\"applyStatus\\\":\\\"APPLY_STATUS_APPROVE_SUCCESS\\\",\\\"applyType\\\":\\\"LIFE_INSURANCE_PERSONAL\\\",\\\"approveDate\\\":***********89,\\\"autoPaymentOption\\\":null,\\\"autoRenewalInsurance\\\":null,\\\"backTrackDate\\\":null,\\\"bizDate\\\":null,\\\"certifyClassId\\\":null,\\\"certifyId\\\":null,\\\"channelTypeCode\\\":\\\"AGENT\\\",\\\"createdDate\\\":1639016075545,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":***********89,\\\"electronicSignatureAttachmentBo\\\":{\\\"agentSignatureAttachmentId\\\":\\\"38230b8b8d7148b89b04d8f3d797006c\\\",\\\"applicantSignatureAttachmentId\\\":\\\"219b14bfe99d4d75b40f2bc70f3f9170\\\",\\\"insuredSignatureAttachmentId\\\":\\\"cb774b04307343f6a7eb61911566535b\\\"},\\\"fillInPremium\\\":null,\\\"forceSave\\\":false,\\\"initialPaymentMode\\\":\\\"CASH\\\",\\\"insuredHealthRemark\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"insuredSignStatus\\\":null,\\\"inureImmeInvest\\\":null,\\\"invalidDate\\\":null,\\\"listApplyAccount\\\":[{\\\"accountNo\\\":\\\"88\\\",\\\"accountOwner\\\":\\\"APPLICANT 41 DZQM\\\",\\\"accountType\\\":\\\"DEBIT_CARD\\\",\\\"acctuserSignStatus\\\":null,\\\"applicantName\\\":\\\"APPLICANT 41 DZQM\\\",\\\"applicantSignStatus\\\":null,\\\"applyAccountId\\\":\\\"af0a3b3690574f6385330d8ca072c035\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"areaCode\\\":\\\"***********\\\",\\\"authorizedDate\\\":*************,\\\"bankCode\\\":\\\"ABA BANK\\\",\\\"bankName\\\":\\\"ABA银行\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"idNo\\\":\\\"4\\\",\\\"idType\\\":\\\"ID\\\",\\\"relationship\\\":\\\"ONESELF\\\",\\\"subbranch\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"useType\\\":\\\"PAY\\\",\\\"validFlag\\\":\\\"effective\\\"}],\\\"listApplyAddPremiumPo\\\":[],\\\"listApplyCoverageAcceptBo\\\":[],\\\"listAttachment\\\":[{\\\"applyAttachmentId\\\":\\\"e7a18975d1e74df08bfcac0094debc82\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"f10610d885c244269dc81b0cf60f34ee\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"14f25dc6202a47e1989bc2f206da21c5\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"eff8317804ba4795a7a50213e16b9bf2\\\",\\\"attachmentSeq\\\":2,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"b7f58a2aa1954cd4b9df40a8addc4c8f\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"b7626544c02c44588e8ae9dc1dad1687\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE\\\",\\\"createdDate\\\":1639018101783,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"fb5c9d8f2c064792bc2d3303b589a9ac\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"5615d09a72594266a8ea2fe7c336bc13\\\",\\\"attachmentSeq\\\":2,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE\\\",\\\"createdDate\\\":1639018101784,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"fea013e1083a42d6991758338036a24f\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"e64ac9d3e8e34460b7b46f20c53daaf5\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_IDTYPE\\\",\\\"createdDate\\\":1639018103885,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"d7edbf4f101145679d8b91cbe969ba59\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"940b777987cd4c2e8ee974e9f4b2e3d5\\\",\\\"attachmentSeq\\\":2,\\\"attachmentTypeCode\\\":\\\"CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_IDTYPE\\\",\\\"createdDate\\\":1639018103935,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"bd1ad06fb6eb4507861374f98ef01dd7\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"15c9a24485974d67acd6b9b7f5535b2c\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"APPLY_PLAN_OTHER\\\",\\\"createdDate\\\":1639018105551,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"68bba70ffb39418fab76f5c8fe538386\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"fc66d2ce8d6b44bf874204f91990035e\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"CUSTOMER_INSURED_PHOTO\\\",\\\"createdDate\\\":1639018109926,\\\"createdUserId\\\":null,\\\"description\\\":null,\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"c8aee051d0bb480f8493a38e141bf1f9\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"74a9a1c016c743679a2ec64be7fabee3\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"APPLY_PLAN_SIGNATURE_APPLICANT\\\",\\\"createdDate\\\":1639018640707,\\\"createdUserId\\\":null,\\\"description\\\":\\\"219b14bfe99d4d75b40f2bc70f3f9170\\\",\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"fb6d67589ec449a181d7d4dbcb1476da\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"87e44c66adf241f88b7f221085a8395b\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"APPLY_PLAN_SIGNATURE_AGENT\\\",\\\"createdDate\\\":1639018712991,\\\"createdUserId\\\":null,\\\"description\\\":\\\"38230b8b8d7148b89b04d8f3d797006c\\\",\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyAttachmentId\\\":\\\"bf4b25a75482415dbbfac84fd0a935af\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":\\\"f63fdd9439844370b2f1e70c3b587827\\\",\\\"attachmentSeq\\\":1,\\\"attachmentTypeCode\\\":\\\"APPLY_PLAN_SIGNATURE_INSURED\\\",\\\"createdDate\\\":1639018647270,\\\"createdUserId\\\":null,\\\"description\\\":\\\"cb774b04307343f6a7eb61911566535b\\\",\\\"forceSave\\\":false,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"url\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"listCoverage\\\":[],\\\"listHealthNotice\\\":[{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"52b76d8035e84a0fab1746fac66226a5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_011\\\",\\\"questionDesc\\\":\\\"(c)胸部不适或紧绷，胸痛，心慌，心脏病，高胆固醇，高血压，贫血或血液，心脏或血管疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"5\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6535a6ac4d7d43f9a4bd3336923bdf9d\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_001\\\",\\\"questionDesc\\\":\\\"1.(a)您现在抽烟吗？如果是，请在下面说明详细情况。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3177fc6a83494dc0b66a68f9d8cee740\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_002\\\",\\\"questionDesc\\\":\\\"(b)在过去的12个月中，您是否抽过烟？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"f49f2eefec34450e8a9f472ed4ee5c9b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_003\\\",\\\"questionDesc\\\":\\\"(c)您是否饮酒？如果是，您饮什么类型的酒（例如啤酒，白兰地，威士忌，葡萄酒）和平均每周饮几杯？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b2589d1627f14459b16000b5f5fdac3f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_004\\\",\\\"questionDesc\\\":\\\"2.(a)您是否从事或打算从事以下活动：航空（以乘客身份乘坐认可航线除外），赛车，潜水，探险，爬山或其它危险活动？如果是，请提供详细信息。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4bc46843a502462e86057bc303f7a95f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_005\\\",\\\"questionDesc\\\":\\\"(b)您是否打算在未来3个月内除短暂度假或旅行以外目的旅行或居住在当前居住国以外的其他国家？如果是，请提供国家名称。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e9ddac16eada48a4b9c63364dfaaa485\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_006\\\",\\\"questionDesc\\\":\\\"3.您是否曾被拒绝，推迟，加费或修改您对人寿保险，团体保险，信用人寿保险，重疾保险，健康或意外保险的投保申请或复效申请？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"f5b3375c00f6460d9afbe42f464b8f22\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_007\\\",\\\"questionDesc\\\":\\\"4.在过去的12个月中，您的体重变化是否超过5千克？如果是，请给出确切的数据和原因。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6675624fc70449b19f9dc7274b7afd98\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_008\\\",\\\"questionDesc\\\":\\\"5.您的亲生父母，兄弟姐妹中是否曾患有癌症，心脏病，中风，糖尿病，肾病，精神疾病或遗传性疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9fe52f1dcf8b419097be1a1603864d55\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_009\\\",\\\"questionDesc\\\":\\\"6.您是否曾经或被告知您曾经或曾经接受过以下治疗：(a) 流鼻血，双重影像，耳聋，失明，眼，鼻，耳，喉或声带疾病，酒精中毒，吸毒成瘾或药物成瘾，身体缺陷，健康受损？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"92216c18ff4f4f6cb9e186dd296741c0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_025\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"1c2b030f481e48719e18146b09d653e3\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_026\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"fe8eca2be9eb4878928e622280bd46ff\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_027\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករក���រ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"952712db7b794fd9ac257d3e9cbd082c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_028\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9e175cfd767c4b718cadebf04d05eda6\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_010\\\",\\\"questionDesc\\\":\\\"(b) 哮喘，支气管炎，肺结核，呼吸系统疾病或其它肺部疾病，甲状腺疾病，甲状腺或内分泌系统失调？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"a927f36dc1654139ada80cd7b1374650\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_029\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"cca70ca097df4b858319c69e168b92ef\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_030\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9805f1262c87408a95c8d367dad4e56f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_031\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"169a25910c22473a992474c357ce4b5f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_032\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4e686e7bebfc4f55931ecba7a59f8fa8\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_033\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4ad0b5000a9144f4816ca5c19d1b8376\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_034\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"cfb68171e7ec4580933c4be45969a331\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_035\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"aec38cca6e6b400782d4aee35230736e\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_036\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"7753af8d19164d0cb7f3a230a5f75bdf\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_012\\\",\\\"questionDesc\\\":\\\"(d) 黄疸，肝炎，肝炎携带者，十二指肠或胃溃疡，疝气，痔疮或其他胃，肠，肝或胆囊的疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9d413cb04be4464d9ac7bddfa18dbf4c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_037\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b6b4c3dcbbb34d15a5d687a654f4011f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_038\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្���ើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9880ae5000a04c3db0c359cb70733ee9\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_039\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"abd903cc335e4c73925e528c635421a0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_040\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"749a11528c80418184a0d6fcf00ca03b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_013\\\",\\\"questionDesc\\\":\\\"(e) 糖尿病，糖蛋白，尿液中有血液或脓，肾结石或泌尿生殖系统的疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"617473a64c184a5ebf48588489098641\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_041\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"0089ffecde63404789f976e420722c4c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_042\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9e7970ff1da34fe9a59f28d918d6c463\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_043\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9fea59d7f2ae4919bff5bcaeac656518\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_044\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"31cc5c824e6347c7883b15dad22885ef\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_014\\\",\\\"questionDesc\\\":\\\"(f) 关节炎，痛风，椎间盘脱垂，风湿病或脊椎，背部，关节，骨骼或肌肉疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"43b435c71ebf466e8821c182812ecdcb\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_045\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4179ffc27f2a4138bf1a396a9a0c4926\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_046\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ad7219b60a9141828e98637fbae6af63\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_047\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"25f7c6acc45a4c1797bd961e1d2f0b39\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_048\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c9483070fd6d4499b50059f90b85f2a0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_015\\\",\\\"questionDesc\\\":\\\"(g) 反复性头疼或眩晕，癫痫，中风，昏厥，瘫痪，精神或神经疾病，大脑或神经系统异常？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកម���ករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ecf9de34ff8c4024a6f8d005612632b7\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_049\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"662b7bde147e4a88b74428dcb5716e59\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_050\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"92ff5e3c6e6042399d4e1a7b2763c5b8\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_051\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"31e5605602de4a6d91224ab83e1e593a\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_052\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"f1a71a53541b4f4eb320f6138b2db654\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_016\\\",\\\"questionDesc\\\":\\\"(h)严重的皮肤感染，蝴蝶样的皮疹，淋巴结肿大，囊肿，肿瘤，肿块，异常肿胀或癌症？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"26286456d2c149a1a77c73289fc26feb\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_053\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"59f1a87f234540d48bb7af400ce8675a\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_054\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"f461a9b659474bddbc4295eb22b929f5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_055\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"0fc2ab44709346e0b24bad70ec18f0c8\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_056\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"515788311ce54c82b17a7a1e7a7d0c75\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_017\\\",\\\"questionDesc\\\":\\\"7.您是否曾经发生过性传播疾病，艾滋病毒或艾滋病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"d53db8879ed7471c9cb53c3292931f9c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_057\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2b407b93a6c940a8ad25118dfe88ced6\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_058\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"8eda8ff3941d4d138beea84478dcbd56\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_059\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"21edbfa9570145e1bafc20aadb6c45fa\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_060\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"264a997f50c04b4997274c6bb0b9f7a0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_018\\\",\\\"questionDesc\\\":\\\"8.在过去的5年中，您是否有：(a)之前未提及的手术？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"ម្ៅម្ៅម្លើកមមករកាប់នរព\\\\nអនកការពារម្ភ្លើងម្ឆេះនរព \\\\nអនកបងាក ត់ពូជសតាច្ត បបបាទ\\\\nអនក\\\\nបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទ\\\\nកមមកររ ករកផរ៉ាងមឬ\\\\nកមមកររ ករកផរ៉ាខាច្់\\\\nកមមករម្ីាើការកនុងិញ្ញស័យផរ៉ា\\\\nកមមករម្របងទាុំងអស់ម្ៅ\\\\nសម រទ អនកម ជទឹក\\\\nអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យ\\\\nន្យិង\\\\nកមមករម្ីាើការតាមកផន្យលង\\\\nរក\\\\nុ\\\\nមជួយស\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"8035c6609d654626aa9bddf6dda6b5e4\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_061\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e5432d53a90a4f1d90d1113d1e492877\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_062\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"77cbcdd7c0bb4d56a6fc87ec9a01d57f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_063\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"0566949ab5b94957bb5398636486287b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_064\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"5bd957e082a1490b97215d0ba89acf5b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_019\\\",\\\"questionDesc\\\":\\\"(b) 以前都未提及疾病，医疗建议，医院治疗，意外事故或伤害？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"32a0bbab3ed346718336b3e3b7454811\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_065\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"a08da18a397b47cab2069a49326fb8ab\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_066\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2cc53df48d614cceac13831c7680a434\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_067\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"1aaa7c2f27124c38b80d3961793b15f2\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_068\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"1fb99b16d4444dbcbbda8e1228b06702\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_020\\\",\\\"questionDesc\\\":\\\"(c)进行身体检查或检验，包括但不限于CT / MRI扫描，X射线，乳房X射线，心电图，超声波扫描，超声波心电图，检查，血液或尿液检查？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"135f623391a248419622a3356f946577\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_069\\\",\\\"questionDesc\\\":\\\"i) 检测日期和原因\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរក���ជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3067ca35097f49f09208104b5bece41c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_070\\\",\\\"questionDesc\\\":\\\"ii) 检测的名称和结果。请提供检测结果副本（如果有）。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"6\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"d4f5cd86e07d4e7a816c24013a9333c5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_021\\\",\\\"questionDesc\\\":\\\"9.女性专用：(a) 您目前是否怀孕？如果是，您怀孕多少个月？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"5243d7546f3549378a67e6de44808653\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_022\\\",\\\"questionDesc\\\":\\\"(b) 您是否患有乳房囊肿？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"999c648ef4684e91ac6ed293084e6a66\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_071\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"41a37bbc66fc42648cae54c2afd35df1\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_072\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4aef8e64fae243babddbc9dd219f24c2\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_073\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c12f6da4f8c148769afc17a8ae4a35fc\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_074\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"977cd3548749428e881b9bc2a02b9cf0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_023\\\",\\\"questionDesc\\\":\\\"(c)您是否患有乳房疾病，女性器官疾病，死胎，分娩时的并发症，子宫膜片检查或月经不调？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"d830fa2f7f7a47be988cd6a4babbab8c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_075\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"aa21b9b7011a43b7af2c7dbbd9e52108\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_076\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"69249fdc1f0240248e4b9be62fc6714d\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_077\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"7a347e47b1cb4cb3ae8e35564b3875ec\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_078\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2d8c7d5290bb4c92ae9f3c0b4bea8957\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_024\\\",\\\"questionDesc\\\":\\\"10.您是否曾经遭受除以上未披露的症状或其它疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3f6c9a3b9fa64fa380e5a16fb1743fc7\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_079\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c4012cd2e9b943e78b00e5f3fba790d4\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_080\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e6a2c2ea8d9647b78878d715fa4d9bae\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_081\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006435,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e3e9b9c943154c279ffc21393631da41\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_082\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006435,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"14c47b4fec2d4935aa010f5c7757b8f6\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_164\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"646810041aca42e187e6b7a2ef6b4168\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_084\\\",\\\"questionDesc\\\":\\\"(b)在过去的12个月中，您是否抽过烟？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6b82571a5a3b463b9a3096737584220d\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_085\\\",\\\"questionDesc\\\":\\\"(c)您是否饮酒？如果是，您饮什么类型的酒（例如啤酒，白兰地，威士忌，葡萄酒）和平均每周饮几杯？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3de3333e1a764331a44f7bdb6278d07a\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_086\\\",\\\"questionDesc\\\":\\\"2.(a)您是否从事或打算从事以下活动：航空（以乘客身份乘坐认可航线除外），赛车，潜水，探险，爬山或其它危险活动？如果是，请提供详细信息。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c9bce9624e3146e9996754792d340e1f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_087\\\",\\\"questionDesc\\\":\\\"(b)您是否打算在未来3个月内除短暂度假或旅行以外目的旅行或居住在当前居住国以外的其他国家？如果是，请提供国家名称。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c44d33c8dd7e44ae8d02b2b3c644d150\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_088\\\",\\\"questionDesc\\\":\\\"3.您是否曾被拒绝，推迟，加费或修改您对人寿保险，团体保险，信用人寿保险，重疾保险，健康或意外保险的投保申请或复效申请？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"a4accd6e7ddb48c9b591616103468852\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_089\\\",\\\"questionDesc\\\":\\\"4.在过去的12个月中，您的体重变化是否超过5千克？如果是，请给出确切的数据和原因。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"06463ec7fc9b47979cd448c6bf625f02\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_090\\\",\\\"questionDesc\\\":\\\"5.您的亲生父母，兄弟姐妹中是否曾患有癌症，心脏病，中风，糖尿病，肾病，精神疾病或遗传性疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6c920bdc9de64f6493a6270d219b1899\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_091\\\",\\\"questionDesc\\\":\\\"6.您是否曾经或被告知您曾经或曾经接受过以下治疗：(a) 流鼻血，双重影像，耳聋，失明，眼，鼻，耳，喉或声带疾病，酒精中毒，吸毒成瘾或药物成瘾，身体缺陷，健康受损？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"de6d647bcdaf487e84f1181da3fdabfa\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_107\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b45365f0a9d74e3db291c29a3aef139e\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_096\\\",\\\"questionDesc\\\":\\\"(f) 关节炎，痛风，椎间盘脱垂，风湿病或脊椎，背部，关节，骨骼或肌肉疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"52378b0dfc1a440da54cc60e2e8e9c79\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_108\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"eed05edff69b4bc08fab14fdd687e021\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_109\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"db6df6807c7e447280d993ce13331d34\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_110\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b99698928ab3491c888fbf6c1d0f0eff\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_092\\\",\\\"questionDesc\\\":\\\"(b) 哮喘，支气管炎，肺结核，呼吸系统疾病或其它肺部疾病，甲状腺疾病，甲状腺或内分泌系统失调？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"a4326662bdcc48c9ace1bd2d7dc7b2a5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_111\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"91b9cb1915d1431d8a3707ae42b1910d\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_112\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"cf26668d087548c3a9bca788f7ff5abd\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_113\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"af2a9c10a2f34df2b21cb67deee2e2c1\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_101\\\",\\\"questionDesc\\\":\\\"(b) 以前都未提及疾病，医疗建议，医院治疗，意外事故或伤害？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c0f13c21a11145bbb92e23616c53ccb0\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_114\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"dd06913a040543b490360b9cf4603942\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_093\\\",\\\"questionDesc\\\":\\\"(c)胸部不适或紧绷，胸痛，心慌，心脏病，高胆固醇，高血压，贫血或血液，心脏或血管疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"7d0d2a176b2046ae962bdf1f07ec5c7f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_115\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c985e5e46f25428fb89cb50378394cf3\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_116\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ba2ccf292a3a40cfa2a828325442505c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_117\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"fc439eeec43d49a684acecafea78d425\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_118\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"d7f0bc99758d40588524bc0a3344214c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_094\\\",\\\"questionDesc\\\":\\\"(d) 黄疸，肝炎，肝炎携带者，十二指肠或胃溃疡，疝气，痔疮或其他胃，肠，肝或胆囊的疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b32945e71869454e96f858bcb4447e1c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_119\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"cba504ed165147819423f66c919eba03\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_120\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ef415d3930094f24936927cbafd95c97\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_121\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"104befd7ad2d4a07bddd349760981b9b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_122\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"488c6db4c7134381985c707a9a40ecfe\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_095\\\",\\\"questionDesc\\\":\\\"(e) 糖尿病，糖蛋白，尿液中有血液或脓，肾结石或泌尿生殖系统的疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3007064a76384f76860d868e403a766c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_123\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"efdee11eb4714e089dc8d1f5da106d8c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_124\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3b0ce362b4e143a4a09517b26839dfab\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_125\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2a98290f9c0c4c6daba139a41e47919e\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_126\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"30410d8287b1447db7dc0606e528c773\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_127\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"c60231a6299442f3ade05126ba0df463\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_128\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"77f0dac0b8c044de9dd0f381aef4b332\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_129\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"5336951a87394d1484fe2723d0a5a860\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_130\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"9a4dfffba30f4c4092da8410b3511428\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_097\\\",\\\"questionDesc\\\":\\\"(g) 反复性头疼或眩晕，癫痫，中风，昏厥，瘫痪，精神或神经疾病，大脑或神经系统异常？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e4bfc3c38c3f4ac38c491c47bd7b4f7e\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_131\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ca7f6fd9d60c4d5ba464c3ed1df0bd5a\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_132\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"081c6744574448f6b3cd84997ce8df55\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_133\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"3487c33757d64378aba23544ea5786cf\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_134\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"99eccb5210594a7aa3166a95f839f37a\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_098\\\",\\\"questionDesc\\\":\\\"(h)严重的皮肤感染，蝴蝶样的皮疹，淋巴结肿大，囊肿，肿瘤，肿块，异常肿胀或癌症？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6a97348774814fb48800c2768201bf47\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_135\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"69e7b56274c84a758a24eaeb7e5dd87b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_136\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"eb095891f56d4e2e8650a4c752f7c272\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_137\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2cc857a5451541deb70ec914ce589c58\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_138\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ba2c7be56f0c43a19102c34aacffa847\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_099\\\",\\\"questionDesc\\\":\\\"7.您是否曾经发生过性传播疾病，艾滋病毒或艾滋病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអ���កបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"195139be30fd40fc83a6d8cd3a6123fd\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_139\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"ffc9a50c93684becb847e686a93c85d1\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_140\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"47cf495389f846f2a7fa609eee83fe81\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_141\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"f88b2b728c594c5bafd5f79a29bf4208\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_142\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"082db472895b4f22b1741331f188a6b5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_100\\\",\\\"questionDesc\\\":\\\"8.在过去的5年中，您是否有：(a)之前未提及的手术？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"65c1d57e98774f0a94b4426818b09b0f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_143\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"cb24eeeec8a14cd3ad9898e182c1619e\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_144\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"434ffa6e68e2479a958780958da27bea\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_145\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e1ba6c08205b43be8b4336d7a01f1a3f\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_146\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2859fa7a4d5e44f79e2109125f170d7c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_147\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6a3ccfdf6d354dce8b17f628cdde9326\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_148\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e1fe7a50a167494c829517b47db280ae\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_149\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ���ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"7cf306b94e5746fdb94b7b93c63a1960\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_150\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"b0b271a689cb45d8ab378011415ebe84\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_102\\\",\\\"questionDesc\\\":\\\"(c)进行身体检查或检验，包括但不限于CT / MRI扫描，X射线，乳房X射线，心电图，超声波扫描，超声波心电图，检查，血液或尿液检查？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"e4782bb3ae52409ba8522c08b3e70f32\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_151\\\",\\\"questionDesc\\\":\\\"i) 检测日期和原因\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"5fdb86b21a5046a4bf56ed4a33b9182c\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_152\\\",\\\"questionDesc\\\":\\\"ii) 检测的名称和结果。请提供检测结果副本（如果有）。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"6\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"2d6a1f5b8e2c46fc97c5ead1ae31dab2\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_103\\\",\\\"questionDesc\\\":\\\"9.女性专用：(a) 您目前是否怀孕？如果是，您怀孕多少个月？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"67f8450f16774921a0e380515f733ef5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_104\\\",\\\"questionDesc\\\":\\\"(b) 您是否患有乳房囊肿？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"68adf77142de42daaf0cf3edced706c8\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_153\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"5e0e26558403479683bc7b9d1ad06ae9\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_154\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"6c02b70d050147509f508ccb4c297a95\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_155\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"09f9700e0dbf4a96ae40b95efd87ef75\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_156\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"4b61276c3fa94a6db8c2910dbaf76b12\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_105\\\",\\\"questionDesc\\\":\\\"(c)您是否患有乳房疾病，女性器官疾病，死胎，分娩时的并发症，子宫膜片检查或月经不调？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"eba17d6f69554bf4900e004eaa0f7703\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_157\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"acba69dff15143c2acb98bca3b28483b\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_158\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"023c06996ea1413c8af8a8e5aa38f087\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_159\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"13523ba9969d4be79cc05578e6d6e4c8\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_160\\\",\\\"questionDesc\\\":\\\"iv) 主治医生或医院的名称和地址\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"721c2b43757a41d19c49921c25653363\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_106\\\",\\\"questionDesc\\\":\\\"10.您是否曾经遭受除以上未披露的症状或其它疾病？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"beb24af6e23d41989ef74f6ed7f7e1a3\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_161\\\",\\\"questionDesc\\\":\\\"i) 医疗情况名称\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"37eb5ab9fd99448f958b2b4315e51a99\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_162\\\",\\\"questionDesc\\\":\\\"ii) 发生日期或诊断日期 (MM/YY or YY)\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":null,\\\"answerDesc\\\":\\\"សុំម្ៅម្ៅម្លើកមមករកាប់នរពអនកការពារម្ភ្លើងម្ឆេះនរព អនកបងាក ត់ពូជសតាច្តបបាទអនកបងាក ត់សតាោន្យពិស អនកម្ន្យស្វទម្ៅសម រទកមមកររ ករកផរ៉ាឬកមមកររ ករកផរ៉ាខាច្់កមមករម្ីាើការកនុងិញ្ញស័យផរ៉ាកមមករម្របងទាុំងអស់ម្ៅសម រទ អនកម ជទឹកអនកម្បើកបររ ៉ឺម៉ាកម្ោយម្របើោ៉ាស ីន្យន្យិងកមមករម្ាើការតាមកផន្យលងរកមជួយសម្្ងាគ េះអនកម្បើកឧ\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":1640056006434,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"031e465f1c154e8d87dc97b5f0a964d1\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_163\\\",\\\"questionDesc\\\":\\\"iii) 您是否仍然有这种状况或其症状，或为此状况去看医生？如果是，您在最近12个月内多久遇到一次这种情况并提供必要的详细信息？\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056006434,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"answer\\\":\\\"Y\\\",\\\"answerDesc\\\":\\\"9\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"forceSave\\\":false,\\\"healthQuestionnaireAnswerId\\\":\\\"0bd69a1cce904214ab51c12fbe615cf5\\\",\\\"healthQuestionnaireId\\\":null,\\\"questionCode\\\":\\\"QYB_QUESTION_083\\\",\\\"questionDesc\\\":\\\"1.(a)您现在抽烟吗？如果是，请在下面说明详细情况。\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"listInsured\\\":[{\\\"addDate\\\":null,\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"attachmentId\\\":null,\\\"avoirdupois\\\":\\\"55\\\",\\\"backTrackDate\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"8.46\\\",\\\"careerType\\\":null,\\\"companyAddress\\\":\\\"77\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyAreaName\\\":\\\"Cambodia Kampong Cham Province Batheay Batheay Batheay\\\",\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"degreeName\\\":null,\\\"doctorAddress\\\":\\\"99\\\",\\\"doctorAreaCode\\\":\\\"***********\\\",\\\"doctorAreaCodeName\\\":\\\"Cambodia Battambang Province Samlout Kampong Lpov Kampong Lpov\\\",\\\"doctorName\\\":\\\"9\\\",\\\"effectiveDate\\\":null,\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"666\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Kampot Province Chum Kiri Srae Samraong Roka Thmei 7\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"7\\\",\\\"homeAreaCode\\\":\\\"80700406004\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"8\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1735664400000,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeName\\\":\\\"<5,000\\\",\\\"incomeSource\\\":null,\\\"insuredExtendId\\\":null,\\\"insuredId\\\":\\\"********************************\\\",\\\"insuredStatus\\\":null,\\\"insuredType\\\":null,\\\"invalidDate\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"listBeneficiary\\\":[{\\\"applyBeneficiaryBo\\\":{\\\"addressDetail\\\":null,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"avoirdupois\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"beneficiaryBranchCode\\\":null,\\\"beneficiaryBranchId\\\":null,\\\"beneficiaryBranchName\\\":null,\\\"beneficiaryId\\\":\\\"833d599551ce4fa0a4e6349779d9865f\\\",\\\"beneficiaryNoOrder\\\":null,\\\"beneficiaryNoOrderName\\\":null,\\\"beneficiaryProportion\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":null,\\\"companyAddress\\\":null,\\\"companyAreaCode\\\":null,\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":null,\\\"email\\\":null,\\\"englishName\\\":null,\\\"facebookNo\\\":null,\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":null,\\\"homeAreaCode\\\":null,\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":null,\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":null,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":null,\\\"incomeSource\\\":null,\\\"insuredId\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":null,\\\"mobile\\\":null,\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"BENEFICIARY 41 DZQM\\\",\\\"nationality\\\":null,\\\"nations\\\":null,\\\"occupationCode\\\":null,\\\"occupationType\\\":null,\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"relationship\\\":null,\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"女\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":null,\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"applyBeneficiaryId\\\":\\\"95d11c4d3f1548009c7f3ac0eec4783c\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"beneficiaryGrade\\\":null,\\\"beneficiaryId\\\":\\\"833d599551ce4fa0a4e6349779d9865f\\\",\\\"beneficiaryNo\\\":null,\\\"beneficiaryNoOrder\\\":\\\"ORDER_ONE\\\",\\\"beneficiaryNoOrderName\\\":\\\"第一受益人\\\",\\\"beneficiaryProportion\\\":100.00,\\\"beneficiaryType\\\":null,\\\"claimPremDrawType\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"idTypeName\\\":null,\\\"insuredId\\\":\\\"********************************\\\",\\\"listBeneficiaryAttachment\\\":[],\\\"modifyFlag\\\":\\\"YES\\\",\\\"promiseAge\\\":null,\\\"promiseRate\\\":null,\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"totalLine\\\":0,\\\"transferGrantFlag\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"listCoverage\\\":[{\\\"actualPremium\\\":526.000,\\\"addPremiumPeriod\\\":null,\\\"addPremiumStartDate\\\":null,\\\"amount\\\":\\\"20000.00\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyNo\\\":\\\"AAI21A00199\\\",\\\"baseAmount\\\":null,\\\"basePremium\\\":null,\\\"coverageId\\\":\\\"48373c277a10414aad3d0b56d1f59dd3\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":null,\\\"coveragePeriodStartDate\\\":null,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"createdDate\\\":1640056006098,\\\"createdUserId\\\":null,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"financingMethod\\\":null,\\\"financingMethodName\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"insuredId\\\":\\\"********************************\\\",\\\"insuredNum\\\":null,\\\"insuredSeq\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"mult\\\":\\\"1\\\",\\\"originalPremium\\\":526.00,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"periodCareerAddPremium\\\":null,\\\"periodStandardPremium\\\":null,\\\"periodWeakAddPremium\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"relationship\\\":\\\"CHILD\\\",\\\"totalAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null,\\\"yearCareerAddPremium\\\":null,\\\"yearStandardPremium\\\":null,\\\"yearWeakAddPremium\\\":null}],\\\"listExpectedPremiumSources\\\":[],\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"55551\\\",\\\"mobile_2\\\":\\\"55555\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"mult\\\":\\\"1\\\",\\\"name\\\":\\\"INSURED 41 DZQM\\\",\\\"nationality\\\":\\\"AUSTRALIA\\\",\\\"nationalityName\\\":\\\"澳大利亚\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationDuty\\\":null,\\\"occupationName\\\":\\\"党政机关、企事业单位负责人和行政管理人员\\\",\\\"occupationNature\\\":null,\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityName\\\":null,\\\"pluralityType\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"reinsuranceRules\\\":\\\"AUTOMATIC_RI\\\",\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sameWithApplicant\\\":null,\\\"sex\\\":\\\"MALE\\\",\\\"sexName\\\":null,\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"socialSecurityName\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"255\\\",\\\"taxpayerNo\\\":\\\"9999\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"6666\\\",\\\"workType\\\":null,\\\"zipCode\\\":null}],\\\"listInsuredCoverage\\\":[{\\\"actualPremium\\\":526.000,\\\"addPremiumPeriod\\\":null,\\\"addPremiumStartDate\\\":null,\\\"amount\\\":\\\"20000.00\\\",\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyNo\\\":\\\"AAI21A00199\\\",\\\"baseAmount\\\":null,\\\"basePremium\\\":null,\\\"coverageId\\\":\\\"48373c277a10414aad3d0b56d1f59dd3\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":null,\\\"coveragePeriodStartDate\\\":null,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"createdDate\\\":1640056006098,\\\"createdUserId\\\":null,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"financingMethod\\\":null,\\\"financingMethodName\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"insuredId\\\":\\\"********************************\\\",\\\"insuredNum\\\":null,\\\"insuredSeq\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"mult\\\":\\\"1\\\",\\\"originalPremium\\\":526.00,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"periodCareerAddPremium\\\":null,\\\"periodStandardPremium\\\":null,\\\"periodWeakAddPremium\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"relationship\\\":\\\"CHILD\\\",\\\"totalAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null,\\\"yearCareerAddPremium\\\":null,\\\"yearStandardPremium\\\":null,\\\"yearWeakAddPremium\\\":null}],\\\"listPolicySpecialContract\\\":[],\\\"loanContract\\\":null,\\\"managerBranchId\\\":\\\"GMM101004\\\",\\\"occupationNature\\\":[{\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyOccupationNatureId\\\":\\\"d38309ddda734654ba1810e032ef1a05\\\",\\\"businessNature\\\":\\\"77\\\",\\\"createdDate\\\":1640056005522,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"customerType\\\":\\\"APPLICANT\\\",\\\"employerName\\\":\\\"7\\\",\\\"exactDuties\\\":\\\"7777\\\",\\\"forceSave\\\":false,\\\"occupation\\\":\\\"777\\\",\\\"occupationNature\\\":\\\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\\\",\\\"occupationNatureSpecific\\\":null,\\\"seq\\\":\\\"100\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056005522,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyOccupationNatureId\\\":\\\"4955fe5cf65c4e6abf41079f5942d951\\\",\\\"businessNature\\\":\\\"888\\\",\\\"createdDate\\\":1640056005846,\\\"createdUserId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"employerName\\\":\\\"88\\\",\\\"exactDuties\\\":\\\"88888\\\",\\\"forceSave\\\":false,\\\"occupation\\\":\\\"8888\\\",\\\"occupationNature\\\":\\\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\\\",\\\"occupationNatureSpecific\\\":null,\\\"seq\\\":\\\"100\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056005846,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"otherInsurance\\\":[{\\\"amount\\\":30000.00,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyOtherInsuranceId\\\":\\\"327dee150d684541ae910aac9361f8ef\\\",\\\"createdDate\\\":1639016396507,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"forceSave\\\":false,\\\"insuranceType\\\":\\\"LIFE\\\",\\\"insuranceTypeName\\\":\\\"人寿\\\",\\\"insuranceYear\\\":\\\"5\\\",\\\"insuredName\\\":\\\"INSURED 41 DZQM\\\",\\\"insuringCompany\\\":\\\"333\\\",\\\"seq\\\":\\\"100\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1639016396507,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"}],\\\"overflowToNext\\\":null,\\\"paymentId\\\":null,\\\"paymentMode\\\":\\\"CASH\\\",\\\"policyId\\\":null,\\\"policyNo\\\":\\\"API21A00124\\\",\\\"preUnderwritingFlag\\\":null,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"providerId\\\":\\\"PRO8888888888888\\\",\\\"receivableAddPremium\\\":0.00,\\\"receivablePremium\\\":526.00,\\\"receivablePremiumSum\\\":null,\\\"referralInfo\\\":null,\\\"repealDate\\\":null,\\\"salesBranchId\\\":\\\"GMA101103\\\",\\\"selfInsuranceFlag\\\":\\\"YES\\\",\\\"signBranchId\\\":\\\"GMM\\\",\\\"signType\\\":null,\\\"signedDate\\\":null,\\\"specialTerm\\\":null,\\\"statements\\\":[{\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyStatementId\\\":\\\"587c28a3928e4b969095cb4d87d3ef2e\\\",\\\"createdDate\\\":1639018111680,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"forceSave\\\":false,\\\"statementCode\\\":\\\"STATEMENT_CODE_A\\\",\\\"statementValue\\\":\\\"NO\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1639018111680,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"},{\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyStatementId\\\":\\\"c7cf1259a6394e58a5fbf13e456e56a3\\\",\\\"createdDate\\\":1639018111680,\\\"createdUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"forceSave\\\":false,\\\"statementCode\\\":\\\"STATEMENT_CODE_B\\\",\\\"statementValue\\\":\\\"NO\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1639018111680,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\"}],\\\"survivalPayType\\\":null,\\\"totalLine\\\":0,\\\"underWriteRemark\\\":null,\\\"updatedDate\\\":1640056036179,\\\"updatedUserId\\\":\\\"GMA101103_AGENT_001\\\",\\\"validFlag\\\":\\\"effective\\\",\\\"verifyNo\\\":null}\",\"language\":\"ZH_CN\",\"pdfType\":\"APPLY\",\"productId\":\"PERSONAL_UNIVERSAL\"}";
        // 保险证 中文
        String json = "{\"attachmentResponseList\":[],\"content\":\"{\\\"acceptBranchId\\\":null,\\\"applicant\\\":{\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"applicantId\\\":\\\"a9307b97148b4bdaae6119d48a433bc7\\\",\\\"applicantName\\\":null,\\\"applicantType\\\":\\\"PERSONAL\\\",\\\"avoirdupois\\\":\\\"44\\\",\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"basisOfSumInsured\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAddressWhole\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"7.39\\\",\\\"companyAddress\\\":\\\"66\\\",\\\"companyAddressWhole\\\":\\\"Cambodia Battambang Province Samlout Kampong Lpov Kampong Lpov 66\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyContractAddress\\\":null,\\\"companyContractDept\\\":null,\\\"companyContractEmail\\\":null,\\\"companyContractIdExpDate\\\":null,\\\"companyContractIdNo\\\":null,\\\"companyContractIdType\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractNationality\\\":null,\\\"companyContractOfficeNumber\\\":null,\\\"companyContractPhone\\\":null,\\\"companyContractPosition\\\":null,\\\"companyEmail\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyIndustry\\\":null,\\\"companyLegalPersonIdExpDate\\\":null,\\\"companyLegalPersonIdNo\\\":null,\\\"companyLegalPersonIdType\\\":null,\\\"companyLegalPersonName\\\":null,\\\"companyLegalPersonNationality\\\":null,\\\"companyMobile\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":1640056035408,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"583561548e59421c9de47f427a3f2fa9\\\",\\\"customerSource\\\":\\\"KNOWN_CUSTOMER\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"delegateBirthday\\\":null,\\\"delegateCustomerId\\\":null,\\\"delegateIdNo\\\":null,\\\"delegateIdType\\\":null,\\\"delegateMobile\\\":null,\\\"delegateName\\\":null,\\\"doctorAddress\\\":\\\"88\\\",\\\"doctorAreaCode\\\":\\\"80300202001\\\",\\\"doctorName\\\":\\\"8\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"55\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Banteay Meanchey Province Poipet 6\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"6\\\",\\\"homeAddressWhole\\\":null,\\\"homeAreaCode\\\":\\\"801100\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"666\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1767200400000,\\\"idNo\\\":\\\"4\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"44441\\\",\\\"mobile_2\\\":\\\"5\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"APPLICANT 41 DZQM\\\",\\\"nationality\\\":\\\"CAMBODIA\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherCategorySpecify\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":null,\\\"policyStatus\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"salesBranchId\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"女\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"244\\\",\\\"taxRegistrationNo\\\":null,\\\"totalEmployeeNum\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056005764,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"555\\\",\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"applicantId\\\":\\\"a9307b97148b4bdaae6119d48a433bc7\\\",\\\"applyDate\\\":*************,\\\"applyId\\\":\\\"264149b3aaec49eabaec30a671a8c948\\\",\\\"applyNo\\\":\\\"AAI21A00199\\\",\\\"applySource\\\":\\\"APP\\\",\\\"approveDate\\\":***********89,\\\"autoRenewalInsurance\\\":null,\\\"backTrackDate\\\":null,\\\"bizDate\\\":***********89,\\\"certifyId\\\":null,\\\"channelTypeCode\\\":\\\"AGENT\\\",\\\"createdDate\\\":1640056035301,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"dataEffectiveDate\\\":***********89,\\\"delegateBranchId\\\":null,\\\"effectiveDate\\\":***********89,\\\"firstPolicyNo\\\":\\\"API21A00124\\\",\\\"firstSalesBranchId\\\":\\\"GMA101103\\\",\\\"forceSave\\\":false,\\\"hesitation\\\":21,\\\"hesitationEndDate\\\":null,\\\"invalidDate\\\":null,\\\"listCashValue\\\":[{\\\"age\\\":23,\\\"amount\\\":null,\\\"cashValue\\\":0.00,\\\"policyYear\\\":1,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":24,\\\"amount\\\":null,\\\"cashValue\\\":604.60,\\\"policyYear\\\":2,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":25,\\\"amount\\\":null,\\\"cashValue\\\":1241.80,\\\"policyYear\\\":3,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":26,\\\"amount\\\":null,\\\"cashValue\\\":1916.20,\\\"policyYear\\\":4,\\\"productId\\\":\\\"PRO880000000000020\\\"},{\\\"age\\\":27,\\\"amount\\\":null,\\\"cashValue\\\":2630.00,\\\"policyYear\\\":5,\\\"productId\\\":\\\"PRO880000000000020\\\"}],\\\"listCoverage\\\":[],\\\"listCoverageExtend\\\":[],\\\"listInsured\\\":[{\\\"addDate\\\":null,\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"avoirdupois\\\":\\\"55\\\",\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"8.46\\\",\\\"companyAddress\\\":\\\"77\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"doctorAddress\\\":\\\"99\\\",\\\"doctorAreaCode\\\":\\\"***********\\\",\\\"doctorName\\\":\\\"9\\\",\\\"effectiveDate\\\":null,\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"666\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Kampot Province Chum Kiri Srae Samraong Roka Thmei 7\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"7\\\",\\\"homeAreaCode\\\":\\\"80700406004\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"8\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1735664400000,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeSource\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredStatus\\\":null,\\\"insuredType\\\":null,\\\"invalidDate\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"listCoverage\\\":[{\\\"actualPremium\\\":526.00,\\\"agentId\\\":null,\\\"amount\\\":20000.00,\\\"approveDate\\\":null,\\\"baseAmount\\\":null,\\\"baseDividendAmount\\\":null,\\\"basePremium\\\":null,\\\"baseSumAmount\\\":\\\"20000.00\\\",\\\"bonusSumAmount\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":1797822433288,\\\"coveragePeriodStartDate\\\":***********89,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"coverageStatus\\\":\\\"EFFECTIVE\\\",\\\"createdDate\\\":1640056035656,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"deductPremium\\\":0.00,\\\"deductRefundAmount\\\":0.00,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"effectiveDate\\\":***********89,\\\"financingMethod\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"historyCoverageStatus\\\":null,\\\"insuredBirthday\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredName\\\":null,\\\"insuredSeq\\\":null,\\\"insuredSex\\\":null,\\\"lapseReason\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"listPolicyCoverageDuty\\\":[],\\\"listPolicyCoveragePayment\\\":[],\\\"listPolicyCoverageSurvival\\\":[],\\\"maturityDate\\\":1797822433288,\\\"mult\\\":\\\"1\\\",\\\"originalAddPremium\\\":0,\\\"originalPremium\\\":526.00,\\\"originalStandardPremium\\\":0,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"policyCoverageBonus\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremium\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":null,\\\"addPremiumTerm\\\":null,\\\"annOccuAddPremium\\\":null,\\\"annStandardPremium\\\":null,\\\"annWeakAddPremium\\\":null,\\\"consultFee\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"createdDate\\\":1640056035661,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"payToDate\\\":1766250000000,\\\"paymentCompleteDate\\\":1766250000000,\\\"payoutFee\\\":null,\\\"periodOccuAddPremium\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":null,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodType\\\":\\\"YEAR\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"receivableDate\\\":***********89,\\\"refundAmount\\\":0.00,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyCoveragePremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyStatus\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"refundAmount\\\":0.00,\\\"relationship\\\":\\\"CHILD\\\",\\\"renewalPermitFlag\\\":\\\"YES\\\",\\\"specialTerm\\\":null,\\\"survivalReceiveFrequency\\\":null,\\\"survivalReceiveLevel\\\":null,\\\"survivalReceiveMode\\\":null,\\\"survivalReceivePeriod\\\":null,\\\"survivalReceivePeriodUnit\\\":null,\\\"totalAmount\\\":\\\"20000.00\\\",\\\"totalDividendAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"versionNo\\\":null,\\\"versionNoCoverageId\\\":null,\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null}],\\\"listPolicyBeneficiary\\\":[{\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"beneficiaryGrade\\\":null,\\\"beneficiaryId\\\":\\\"1f171783269541d4a47ed336ee1dc0e7\\\",\\\"beneficiaryNo\\\":null,\\\"beneficiaryNoOrder\\\":\\\"ORDER_ONE\\\",\\\"beneficiaryNoOrderName\\\":\\\"第一受益人\\\",\\\"beneficiaryProportion\\\":100.00,\\\"beneficiaryType\\\":null,\\\"claimPremDrawType\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"listBeneficiaryAttachment\\\":[],\\\"modifyFlag\\\":\\\"YES\\\",\\\"policyBeneficiary\\\":{\\\"addressDetail\\\":null,\\\"avoirdupois\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"beneficiaryBranchCode\\\":null,\\\"beneficiaryBranchId\\\":null,\\\"beneficiaryBranchName\\\":null,\\\"beneficiaryId\\\":\\\"1f171783269541d4a47ed336ee1dc0e7\\\",\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":null,\\\"companyAddress\\\":null,\\\"companyAreaCode\\\":null,\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":null,\\\"email\\\":null,\\\"englishName\\\":null,\\\"facebookNo\\\":null,\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":null,\\\"homeAreaCode\\\":null,\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":null,\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":null,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":null,\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":null,\\\"mobile\\\":null,\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"BENEFICIARY 41 DZQM\\\",\\\"nationality\\\":null,\\\"nations\\\":null,\\\"occupationCode\\\":null,\\\"occupationType\\\":null,\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":null,\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"policyBeneficiaryId\\\":\\\"24a0ab12d9394e4a9d507830af9afdf1\\\",\\\"promiseAge\\\":null,\\\"promiseRate\\\":null,\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"totalLine\\\":0,\\\"transferGrantFlag\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"listPolicyCoverage\\\":[{\\\"actualPremium\\\":526.00,\\\"agentId\\\":null,\\\"amount\\\":20000.00,\\\"approveDate\\\":null,\\\"baseAmount\\\":null,\\\"baseDividendAmount\\\":null,\\\"basePremium\\\":null,\\\"baseSumAmount\\\":\\\"20000.00\\\",\\\"bonusSumAmount\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":1797822433288,\\\"coveragePeriodStartDate\\\":***********89,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"coverageStatus\\\":\\\"EFFECTIVE\\\",\\\"createdDate\\\":1640056035656,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"deductPremium\\\":0.00,\\\"deductRefundAmount\\\":0.00,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"effectiveDate\\\":***********89,\\\"financingMethod\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"historyCoverageStatus\\\":null,\\\"insuredBirthday\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredName\\\":null,\\\"insuredSeq\\\":null,\\\"insuredSex\\\":null,\\\"lapseReason\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"listPolicyCoverageDuty\\\":[],\\\"listPolicyCoveragePayment\\\":[],\\\"listPolicyCoverageSurvival\\\":[],\\\"maturityDate\\\":1797822433288,\\\"mult\\\":\\\"1\\\",\\\"originalAddPremium\\\":0,\\\"originalPremium\\\":526.00,\\\"originalStandardPremium\\\":0,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"policyCoverageBonus\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremium\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":null,\\\"addPremiumTerm\\\":null,\\\"annOccuAddPremium\\\":null,\\\"annStandardPremium\\\":null,\\\"annWeakAddPremium\\\":null,\\\"consultFee\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"createdDate\\\":1640056035661,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"payToDate\\\":1766250000000,\\\"paymentCompleteDate\\\":1766250000000,\\\"payoutFee\\\":null,\\\"periodOccuAddPremium\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":null,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodType\\\":\\\"YEAR\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"receivableDate\\\":***********89,\\\"refundAmount\\\":0.00,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyCoveragePremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyStatus\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"refundAmount\\\":0.00,\\\"relationship\\\":\\\"CHILD\\\",\\\"renewalPermitFlag\\\":\\\"YES\\\",\\\"specialTerm\\\":null,\\\"survivalReceiveFrequency\\\":null,\\\"survivalReceiveLevel\\\":null,\\\"survivalReceiveMode\\\":null,\\\"survivalReceivePeriod\\\":null,\\\"survivalReceivePeriodUnit\\\":null,\\\"totalAmount\\\":\\\"20000.00\\\",\\\"totalDividendAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"versionNo\\\":null,\\\"versionNoCoverageId\\\":null,\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null}],\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"55551\\\",\\\"mobile_2\\\":\\\"55555\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"mult\\\":\\\"1\\\",\\\"name\\\":\\\"INSURED 41 DZQM\\\",\\\"nationality\\\":\\\"AUSTRALIA\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationDuty\\\":null,\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyInsuredExtendPo\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"reinsuranceRules\\\":\\\"AUTOMATIC_RI\\\",\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"MALE\\\",\\\"sexName\\\":\\\"男\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"255\\\",\\\"taxpayerNo\\\":\\\"9999\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"6666\\\",\\\"workType\\\":null,\\\"zipCode\\\":null}],\\\"listInsuredCoverage\\\":[{\\\"actualPremium\\\":526.00,\\\"agentId\\\":null,\\\"amount\\\":20000.00,\\\"approveDate\\\":null,\\\"baseAmount\\\":null,\\\"baseDividendAmount\\\":null,\\\"basePremium\\\":null,\\\"baseSumAmount\\\":\\\"20000.00\\\",\\\"bonusSumAmount\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":1797822433288,\\\"coveragePeriodStartDate\\\":***********89,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"coverageStatus\\\":\\\"EFFECTIVE\\\",\\\"createdDate\\\":1640056035656,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"deductPremium\\\":0.00,\\\"deductRefundAmount\\\":0.00,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"effectiveDate\\\":***********89,\\\"financingMethod\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"historyCoverageStatus\\\":null,\\\"insuredBirthday\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredName\\\":null,\\\"insuredSeq\\\":null,\\\"insuredSex\\\":null,\\\"lapseReason\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"listPolicyCoverageDuty\\\":[],\\\"listPolicyCoveragePayment\\\":[],\\\"listPolicyCoverageSurvival\\\":[],\\\"maturityDate\\\":1797822433288,\\\"mult\\\":\\\"1\\\",\\\"originalAddPremium\\\":0,\\\"originalPremium\\\":526.00,\\\"originalStandardPremium\\\":0,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"policyCoverageBonus\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremium\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":null,\\\"addPremiumTerm\\\":null,\\\"annOccuAddPremium\\\":null,\\\"annStandardPremium\\\":null,\\\"annWeakAddPremium\\\":null,\\\"consultFee\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"createdDate\\\":1640056035661,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"payToDate\\\":1766250000000,\\\"paymentCompleteDate\\\":1766250000000,\\\"payoutFee\\\":null,\\\"periodOccuAddPremium\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":null,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodType\\\":\\\"YEAR\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"receivableDate\\\":***********89,\\\"refundAmount\\\":0.00,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyCoveragePremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyStatus\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"refundAmount\\\":0.00,\\\"relationship\\\":\\\"CHILD\\\",\\\"renewalPermitFlag\\\":\\\"YES\\\",\\\"specialTerm\\\":null,\\\"survivalReceiveFrequency\\\":null,\\\"survivalReceiveLevel\\\":null,\\\"survivalReceiveMode\\\":null,\\\"survivalReceivePeriod\\\":null,\\\"survivalReceivePeriodUnit\\\":null,\\\"totalAmount\\\":\\\"20000.00\\\",\\\"totalDividendAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"versionNo\\\":null,\\\"versionNoCoverageId\\\":null,\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null}],\\\"listPolicyAccount\\\":[{\\\"accountNo\\\":\\\"88\\\",\\\"accountOwner\\\":\\\"APPLICANT 41 DZQM\\\",\\\"accountType\\\":\\\"DEBIT_CARD\\\",\\\"acctuserSignStatus\\\":null,\\\"applicantName\\\":\\\"APPLICANT 41 DZQM\\\",\\\"authorizedDate\\\":*************,\\\"bankCode\\\":\\\"ABA BANK\\\",\\\"city\\\":\\\"***********\\\",\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"idNo\\\":\\\"4\\\",\\\"idType\\\":\\\"ID\\\",\\\"policyAccountId\\\":\\\"27d36118d613410ab8bf3f7f170eb3f7\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"relationship\\\":\\\"ONESELF\\\",\\\"subbranch\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"useType\\\":\\\"PAY\\\",\\\"validFlag\\\":\\\"effective\\\"}],\\\"listPolicyAddPremium\\\":[],\\\"listPolicyAttachment\\\":[],\\\"listPolicyInsured\\\":[{\\\"addDate\\\":null,\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"avoirdupois\\\":\\\"55\\\",\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"8.46\\\",\\\"companyAddress\\\":\\\"77\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"doctorAddress\\\":\\\"99\\\",\\\"doctorAreaCode\\\":\\\"***********\\\",\\\"doctorName\\\":\\\"9\\\",\\\"effectiveDate\\\":null,\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"666\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Kampot Province Chum Kiri Srae Samraong Roka Thmei 7\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"7\\\",\\\"homeAreaCode\\\":\\\"80700406004\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"8\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1735664400000,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeSource\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredStatus\\\":null,\\\"insuredType\\\":null,\\\"invalidDate\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"listCoverage\\\":[{\\\"actualPremium\\\":526.00,\\\"agentId\\\":null,\\\"amount\\\":20000.00,\\\"approveDate\\\":null,\\\"baseAmount\\\":null,\\\"baseDividendAmount\\\":null,\\\"basePremium\\\":null,\\\"baseSumAmount\\\":\\\"20000.00\\\",\\\"bonusSumAmount\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":1797822433288,\\\"coveragePeriodStartDate\\\":***********89,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"coverageStatus\\\":\\\"EFFECTIVE\\\",\\\"createdDate\\\":1640056035656,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"deductPremium\\\":0.00,\\\"deductRefundAmount\\\":0.00,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"effectiveDate\\\":***********89,\\\"financingMethod\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"historyCoverageStatus\\\":null,\\\"insuredBirthday\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredName\\\":null,\\\"insuredSeq\\\":null,\\\"insuredSex\\\":null,\\\"lapseReason\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"listPolicyCoverageDuty\\\":[],\\\"listPolicyCoveragePayment\\\":[],\\\"listPolicyCoverageSurvival\\\":[],\\\"maturityDate\\\":1797822433288,\\\"mult\\\":\\\"1\\\",\\\"originalAddPremium\\\":0,\\\"originalPremium\\\":526.00,\\\"originalStandardPremium\\\":0,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"policyCoverageBonus\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremium\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":null,\\\"addPremiumTerm\\\":null,\\\"annOccuAddPremium\\\":null,\\\"annStandardPremium\\\":null,\\\"annWeakAddPremium\\\":null,\\\"consultFee\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"createdDate\\\":1640056035661,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"payToDate\\\":1766250000000,\\\"paymentCompleteDate\\\":1766250000000,\\\"payoutFee\\\":null,\\\"periodOccuAddPremium\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":null,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodType\\\":\\\"YEAR\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"receivableDate\\\":***********89,\\\"refundAmount\\\":0.00,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyCoveragePremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyStatus\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"refundAmount\\\":0.00,\\\"relationship\\\":\\\"CHILD\\\",\\\"renewalPermitFlag\\\":\\\"YES\\\",\\\"specialTerm\\\":null,\\\"survivalReceiveFrequency\\\":null,\\\"survivalReceiveLevel\\\":null,\\\"survivalReceiveMode\\\":null,\\\"survivalReceivePeriod\\\":null,\\\"survivalReceivePeriodUnit\\\":null,\\\"totalAmount\\\":\\\"20000.00\\\",\\\"totalDividendAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"versionNo\\\":null,\\\"versionNoCoverageId\\\":null,\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null}],\\\"listPolicyBeneficiary\\\":[{\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"beneficiaryGrade\\\":null,\\\"beneficiaryId\\\":\\\"1f171783269541d4a47ed336ee1dc0e7\\\",\\\"beneficiaryNo\\\":null,\\\"beneficiaryNoOrder\\\":\\\"ORDER_ONE\\\",\\\"beneficiaryNoOrderName\\\":\\\"第一受益人\\\",\\\"beneficiaryProportion\\\":100.00,\\\"beneficiaryType\\\":null,\\\"claimPremDrawType\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"listBeneficiaryAttachment\\\":[],\\\"modifyFlag\\\":\\\"YES\\\",\\\"policyBeneficiary\\\":{\\\"addressDetail\\\":null,\\\"avoirdupois\\\":null,\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"beneficiaryBranchCode\\\":null,\\\"beneficiaryBranchId\\\":null,\\\"beneficiaryBranchName\\\":null,\\\"beneficiaryId\\\":\\\"1f171783269541d4a47ed336ee1dc0e7\\\",\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":null,\\\"companyAddress\\\":null,\\\"companyAreaCode\\\":null,\\\"companyContractAddress\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractPhone\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":*************,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"678f126d32fd479d8ac97b4499a0972b\\\",\\\"degree\\\":null,\\\"email\\\":null,\\\"englishName\\\":null,\\\"facebookNo\\\":null,\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":null,\\\"homeAreaCode\\\":null,\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":null,\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":null,\\\"idNo\\\":\\\"5\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":null,\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":null,\\\"mobile\\\":null,\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"BENEFICIARY 41 DZQM\\\",\\\"nationality\\\":null,\\\"nations\\\":null,\\\"occupationCode\\\":null,\\\"occupationType\\\":null,\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":null,\\\"startWorkDate\\\":null,\\\"stature\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":null,\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"policyBeneficiaryId\\\":\\\"24a0ab12d9394e4a9d507830af9afdf1\\\",\\\"promiseAge\\\":null,\\\"promiseRate\\\":null,\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"totalLine\\\":0,\\\"transferGrantFlag\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"listPolicyCoverage\\\":[{\\\"actualPremium\\\":526.00,\\\"agentId\\\":null,\\\"amount\\\":20000.00,\\\"approveDate\\\":null,\\\"baseAmount\\\":null,\\\"baseDividendAmount\\\":null,\\\"basePremium\\\":null,\\\"baseSumAmount\\\":\\\"20000.00\\\",\\\"bonusSumAmount\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodEndDate\\\":1797822433288,\\\"coveragePeriodStartDate\\\":***********89,\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coveragePeriodUnitName\\\":\\\"年\\\",\\\"coverageStatus\\\":\\\"EFFECTIVE\\\",\\\"createdDate\\\":1640056035656,\\\"createdUserId\\\":null,\\\"customerId\\\":null,\\\"deductPremium\\\":0.00,\\\"deductRefundAmount\\\":0.00,\\\"dividendAmount\\\":null,\\\"dividendReceiveFrequency\\\":null,\\\"dividendReceiveMode\\\":null,\\\"dividendReceivePeriod\\\":null,\\\"dutyChooseFlag\\\":null,\\\"dutyId\\\":null,\\\"effectiveDate\\\":***********89,\\\"financingMethod\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":null,\\\"historyCoverageStatus\\\":null,\\\"insuredBirthday\\\":null,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"insuredName\\\":null,\\\"insuredSeq\\\":null,\\\"insuredSex\\\":null,\\\"lapseReason\\\":null,\\\"listAddPremium\\\":[],\\\"listCoverageDuty\\\":[],\\\"listCoverageLevel\\\":[],\\\"listPolicyCoverageDuty\\\":[],\\\"listPolicyCoveragePayment\\\":[],\\\"listPolicyCoverageSurvival\\\":[],\\\"maturityDate\\\":1797822433288,\\\"mult\\\":\\\"1\\\",\\\"originalAddPremium\\\":0,\\\"originalPremium\\\":526.00,\\\"originalStandardPremium\\\":0,\\\"paymentInstallments\\\":1,\\\"pensionReceiveDate\\\":null,\\\"pensionReceiveDateUnit\\\":null,\\\"pensionReceiveFrequency\\\":null,\\\"pensionReceiveMode\\\":null,\\\"pensionReceivePeriod\\\":\\\"0\\\",\\\"policyCoverageBonus\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremium\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":null,\\\"addPremiumTerm\\\":null,\\\"annOccuAddPremium\\\":null,\\\"annStandardPremium\\\":null,\\\"annWeakAddPremium\\\":null,\\\"consultFee\\\":null,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"createdDate\\\":1640056035661,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"payToDate\\\":1766250000000,\\\"paymentCompleteDate\\\":1766250000000,\\\"payoutFee\\\":null,\\\"periodOccuAddPremium\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":null,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":null,\\\"policyCoveragePayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"agencyFeeRate\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annStandardRate\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"basePremium\\\":526.00,\\\"careerAddPremium\\\":0.00,\\\"commissionFee\\\":47.34,\\\"commissionFeeRate\\\":9.00,\\\"coverageId\\\":\\\"86847a0f2c36491199e5d53752c34a70\\\",\\\"coveragePeriod\\\":\\\"5\\\",\\\"coveragePeriodUnit\\\":\\\"YEAR\\\",\\\"coverageYear\\\":0,\\\"createdDate\\\":1640056035663,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"effectiveDate\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"govStandardPremium\\\":0.00,\\\"govStandardRate\\\":0.00,\\\"insuredId\\\":\\\"785bfa028ae34cb49bd2401c9bf127c2\\\",\\\"otherAddPremium\\\":0.00,\\\"paymentCompleteDate\\\":1766250000000,\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodStandardRate\\\":100.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyCoveragePaymentId\\\":\\\"4c4878c8318f498bb88d17c0f4389004\\\",\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"receivableDate\\\":null,\\\"serviceChargeFee\\\":0.000000,\\\"serviceChargeRate\\\":0.00,\\\"standardPremium\\\":0.00,\\\"standardRate\\\":0.00,\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"valuePremiumRate\\\":100.00,\\\"weakAddPremium\\\":0.00},\\\"policyCoveragePremiumId\\\":\\\"f15c401fc7c84ab294f87e9e0fd55ce9\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodType\\\":\\\"YEAR\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"receivableDate\\\":***********89,\\\"refundAmount\\\":0.00,\\\"totalActualPremium\\\":526.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyCoveragePremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyStatus\\\":null,\\\"premium\\\":526.00,\\\"premiumDiscount\\\":526.00,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumFrequencyName\\\":\\\"年缴\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPeriodUnitName\\\":\\\"年\\\",\\\"primaryFlag\\\":\\\"MAIN\\\",\\\"prodSeq\\\":null,\\\"productCode\\\":\\\"DZHRS_QYB\\\",\\\"productId\\\":\\\"PRO880000000000020\\\",\\\"productLevel\\\":null,\\\"productLevelName\\\":null,\\\"productName\\\":\\\"GC Enrich Life\\\",\\\"refundAmount\\\":0.00,\\\"relationship\\\":\\\"CHILD\\\",\\\"renewalPermitFlag\\\":\\\"YES\\\",\\\"specialTerm\\\":null,\\\"survivalReceiveFrequency\\\":null,\\\"survivalReceiveLevel\\\":null,\\\"survivalReceiveMode\\\":null,\\\"survivalReceivePeriod\\\":null,\\\"survivalReceivePeriodUnit\\\":null,\\\"totalAmount\\\":\\\"20000.00\\\",\\\"totalDividendAmount\\\":null,\\\"totalLine\\\":0,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":*************,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"versionNo\\\":null,\\\"versionNoCoverageId\\\":null,\\\"waitPeriod\\\":null,\\\"waitPeriodEndDate\\\":null}],\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"55551\\\",\\\"mobile_2\\\":\\\"55555\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"mult\\\":\\\"1\\\",\\\"name\\\":\\\"INSURED 41 DZQM\\\",\\\"nationality\\\":\\\"AUSTRALIA\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationDuty\\\":null,\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyInsuredExtendPo\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"reinsuranceRules\\\":\\\"AUTOMATIC_RI\\\",\\\"relationship\\\":\\\"CHILD\\\",\\\"relationshipInstructions\\\":null,\\\"relationshipName\\\":\\\"子女\\\",\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"sex\\\":\\\"MALE\\\",\\\"sexName\\\":\\\"男\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"255\\\",\\\"taxpayerNo\\\":\\\"9999\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"6666\\\",\\\"workType\\\":null,\\\"zipCode\\\":null}],\\\"listPolicyInsuredExtend\\\":[],\\\"listPolicyPayment\\\":[{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"applyDate\\\":*************,\\\"basePremium\\\":526.00,\\\"bizBranchId\\\":\\\"GMA101103\\\",\\\"bizDate\\\":***********89,\\\"bizYearMonth\\\":\\\"202112\\\",\\\"businessId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"careerAddPremium\\\":0.00,\\\"checkDate\\\":null,\\\"commissionFee\\\":47.34,\\\"commissionGenerateFlag\\\":\\\"GENERATED\\\",\\\"coverageId\\\":null,\\\"createdDate\\\":1640056035426,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"discountModel\\\":null,\\\"discountPremiumFlag\\\":\\\"NO\\\",\\\"discountType\\\":null,\\\"discountTypeName\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"gainedDate\\\":***********89,\\\"govStandardPremium\\\":0.00,\\\"healthAddPremium\\\":0.00,\\\"insuredId\\\":null,\\\"insuredSum\\\":null,\\\"listPolicyCoveragePayment\\\":[],\\\"otherAddPremium\\\":0.00,\\\"payEndDate\\\":1766250000000,\\\"payModeCode\\\":\\\"CASH\\\",\\\"payModeCodeName\\\":\\\"现金支付\\\",\\\"payStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentBusinessType\\\":\\\"BUSINESS_TYPE_NEW_CONTRACT\\\",\\\"paymentCompleteDate\\\":1766250000000,\\\"paymentModeCode\\\":\\\"CASH\\\",\\\"paymentStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"policyPremiumId\\\":\\\"55cbc4d9e5134e25aa188a9147363ff6\\\",\\\"policyYear\\\":1,\\\"premiumBeforeDiscount\\\":0,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPersist\\\":null,\\\"premiumSource\\\":\\\"APP\\\",\\\"productId\\\":null,\\\"productLevel\\\":null,\\\"promotionType\\\":null,\\\"promotionTypeName\\\":null,\\\"providerId\\\":\\\"PRO8888888888888\\\",\\\"receivableDate\\\":***********89,\\\"serviceChargeFee\\\":0.000000,\\\"signTypeCode\\\":null,\\\"specialDiscount\\\":null,\\\"standardPremium\\\":0.00,\\\"statusCode\\\":\\\"RECEIVABLE\\\",\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":1640056035944,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"weakAddPremium\\\":0.00}],\\\"listPolicySpecialContract\\\":[],\\\"loanContract\\\":null,\\\"managerBranchId\\\":\\\"GMM101004\\\",\\\"maturityDate\\\":1797822433288,\\\"mustReturnFlag\\\":null,\\\"occupationNature\\\":[{\\\"businessNature\\\":\\\"77\\\",\\\"createdDate\\\":1640056035469,\\\"createdUserId\\\":null,\\\"customerType\\\":\\\"APPLICANT\\\",\\\"employerName\\\":\\\"7\\\",\\\"exactDuties\\\":\\\"7777\\\",\\\"forceSave\\\":false,\\\"occupation\\\":\\\"777\\\",\\\"occupationNature\\\":\\\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\\\",\\\"occupationNatureSpecific\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyOccupationNatureId\\\":\\\"2d85dcba126546cdb995cb6027b5d4fa\\\",\\\"seq\\\":\\\"100\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056035469,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},{\\\"businessNature\\\":\\\"888\\\",\\\"createdDate\\\":1640056035469,\\\"createdUserId\\\":null,\\\"customerType\\\":\\\"INSURED\\\",\\\"employerName\\\":\\\"88\\\",\\\"exactDuties\\\":\\\"88888\\\",\\\"forceSave\\\":false,\\\"occupation\\\":\\\"8888\\\",\\\"occupationNature\\\":\\\"EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR\\\",\\\"occupationNatureSpecific\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyOccupationNatureId\\\":\\\"2d0df0fbd7f349e48656fcd2f87473e2\\\",\\\"seq\\\":\\\"100\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056035469,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"}],\\\"otherInsurance\\\":[],\\\"policyAgent\\\":{\\\"agentCode\\\":\\\"101103\\\",\\\"agentId\\\":\\\"GMA101103_AGENT_001\\\",\\\"agentName\\\":\\\"尔诺\\\",\\\"createdDate\\\":1640056035307,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"percent\\\":null,\\\"policyAgentId\\\":\\\"66a3841cbfd34b38b228375f5d97593a\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":\\\"API21A00124\\\",\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyApplicant\\\":{\\\"addressType\\\":\\\"RESIDENCE\\\",\\\"applicantId\\\":\\\"a9307b97148b4bdaae6119d48a433bc7\\\",\\\"applicantName\\\":null,\\\"applicantType\\\":\\\"PERSONAL\\\",\\\"avoirdupois\\\":\\\"44\\\",\\\"bankAccountName\\\":null,\\\"bankAccountNo\\\":null,\\\"bankCode\\\":null,\\\"basisOfSumInsured\\\":null,\\\"belongsCompanyAddress\\\":null,\\\"belongsCompanyAddressWhole\\\":null,\\\"belongsCompanyAreaCode\\\":null,\\\"belongsCompanyFax\\\":null,\\\"belongsCompanyPhone\\\":null,\\\"belongsCompanyZipCode\\\":null,\\\"birthPlace\\\":null,\\\"birthday\\\":************,\\\"bmi\\\":\\\"7.39\\\",\\\"companyAddress\\\":\\\"66\\\",\\\"companyAddressWhole\\\":\\\"Cambodia Battambang Province Samlout Kampong Lpov Kampong Lpov 66\\\",\\\"companyAreaCode\\\":\\\"***********\\\",\\\"companyContractAddress\\\":null,\\\"companyContractDept\\\":null,\\\"companyContractEmail\\\":null,\\\"companyContractIdExpDate\\\":null,\\\"companyContractIdNo\\\":null,\\\"companyContractIdType\\\":null,\\\"companyContractMobile\\\":null,\\\"companyContractName\\\":null,\\\"companyContractNationality\\\":null,\\\"companyContractOfficeNumber\\\":null,\\\"companyContractPhone\\\":null,\\\"companyContractPosition\\\":null,\\\"companyEmail\\\":null,\\\"companyFax\\\":null,\\\"companyIdNo\\\":null,\\\"companyIdType\\\":null,\\\"companyIndustry\\\":null,\\\"companyLegalPersonIdExpDate\\\":null,\\\"companyLegalPersonIdNo\\\":null,\\\"companyLegalPersonIdType\\\":null,\\\"companyLegalPersonName\\\":null,\\\"companyLegalPersonNationality\\\":null,\\\"companyMobile\\\":null,\\\"companyName\\\":null,\\\"companyPhone\\\":null,\\\"companyType\\\":null,\\\"companyZipCode\\\":null,\\\"countryCode\\\":null,\\\"createdDate\\\":1640056035408,\\\"createdUserId\\\":null,\\\"creditGrade\\\":null,\\\"customerId\\\":\\\"583561548e59421c9de47f427a3f2fa9\\\",\\\"customerSource\\\":\\\"KNOWN_CUSTOMER\\\",\\\"degree\\\":\\\"PRIMARY_SCHOOL\\\",\\\"delegateBirthday\\\":null,\\\"delegateCustomerId\\\":null,\\\"delegateIdNo\\\":null,\\\"delegateIdType\\\":null,\\\"delegateMobile\\\":null,\\\"delegateName\\\":null,\\\"doctorAddress\\\":\\\"88\\\",\\\"doctorAreaCode\\\":\\\"80300202001\\\",\\\"doctorName\\\":\\\"8\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"englishName\\\":null,\\\"expectedPremiumSources\\\":\\\"[\\\\\\\"SALARY_SAVINGS\\\\\\\"]\\\",\\\"expectedPremiumSourcesSpecific\\\":null,\\\"facebookNo\\\":\\\"55\\\",\\\"familyIncome\\\":null,\\\"familyIncomeSource\\\":null,\\\"fax\\\":null,\\\"forceSave\\\":false,\\\"fullAddress\\\":\\\"Cambodia Banteay Meanchey Province Poipet 6\\\",\\\"headAttachId\\\":null,\\\"health\\\":null,\\\"homeAddress\\\":\\\"6\\\",\\\"homeAddressWhole\\\":null,\\\"homeAreaCode\\\":\\\"801100\\\",\\\"homeFax\\\":null,\\\"homePhone\\\":null,\\\"homeZipCode\\\":\\\"666\\\",\\\"idAttachId\\\":null,\\\"idCategory\\\":null,\\\"idExpDate\\\":1767200400000,\\\"idNo\\\":\\\"4\\\",\\\"idType\\\":\\\"ID\\\",\\\"idTypeName\\\":\\\"身份证\\\",\\\"income\\\":\\\"INCOME_1\\\",\\\"incomeSource\\\":null,\\\"issueDate\\\":null,\\\"issuePlace\\\":null,\\\"joinCompanyDate\\\":null,\\\"license\\\":null,\\\"licenseType\\\":null,\\\"marriage\\\":\\\"UNMARRIED\\\",\\\"mobile\\\":\\\"44441\\\",\\\"mobile_2\\\":\\\"5\\\",\\\"mrzOne\\\":null,\\\"mrzTwo\\\":null,\\\"name\\\":\\\"APPLICANT 41 DZQM\\\",\\\"nationality\\\":\\\"CAMBODIA\\\",\\\"nations\\\":null,\\\"occupationCode\\\":\\\"**********\\\",\\\"occupationType\\\":\\\"1\\\",\\\"ocrMrz\\\":null,\\\"otherCategorySpecify\\\":null,\\\"otherPhone\\\":null,\\\"phone\\\":null,\\\"pluralityType\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":null,\\\"policyStatus\\\":null,\\\"position\\\":null,\\\"postalAddress\\\":null,\\\"registerAddress\\\":null,\\\"rfidMrz\\\":null,\\\"salary\\\":null,\\\"salesBranchId\\\":null,\\\"sex\\\":\\\"FEMALE\\\",\\\"sexName\\\":\\\"女\\\",\\\"smokeFlag\\\":null,\\\"socialSecurity\\\":\\\"HAVE_SOCIAL_SECURITY\\\",\\\"startWorkDate\\\":null,\\\"stature\\\":\\\"244\\\",\\\"taxRegistrationNo\\\":null,\\\"totalEmployeeNum\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056005764,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"wechatNo\\\":\\\"555\\\",\\\"workType\\\":null,\\\"zipCode\\\":null},\\\"policyContactInfo\\\":{\\\"contractAddress\\\":\\\"6\\\",\\\"contractEmail\\\":null,\\\"contractHomePhone\\\":null,\\\"contractMobile\\\":\\\"44441\\\",\\\"contractName\\\":null,\\\"contractOfficePhone\\\":null,\\\"contractPhone\\\":null,\\\"createdDate\\\":1640056035442,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"needPosLetter\\\":null,\\\"policyContactId\\\":\\\"cddece1fa9bc4cf1b0615e1bd15b3f7a\\\",\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"postcodes\\\":\\\"666\\\",\\\"sendAddrAreaCode\\\":\\\"801100\\\",\\\"smsServiceFlag\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyFactAgent\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyInsuredCollect\\\":null,\\\"policyNo\\\":\\\"API21A00124\\\",\\\"policyPayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"applyDate\\\":*************,\\\"basePremium\\\":526.00,\\\"bizBranchId\\\":\\\"GMA101103\\\",\\\"bizDate\\\":***********89,\\\"bizYearMonth\\\":\\\"202112\\\",\\\"businessId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"careerAddPremium\\\":0.00,\\\"checkDate\\\":null,\\\"commissionFee\\\":47.34,\\\"commissionGenerateFlag\\\":\\\"GENERATED\\\",\\\"coverageId\\\":null,\\\"createdDate\\\":1640056035426,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"discountModel\\\":null,\\\"discountPremiumFlag\\\":\\\"NO\\\",\\\"discountType\\\":null,\\\"discountTypeName\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"gainedDate\\\":***********89,\\\"govStandardPremium\\\":0.00,\\\"healthAddPremium\\\":0.00,\\\"insuredId\\\":null,\\\"insuredSum\\\":null,\\\"listPolicyCoveragePayment\\\":[],\\\"otherAddPremium\\\":0.00,\\\"payEndDate\\\":1766250000000,\\\"payModeCode\\\":\\\"CASH\\\",\\\"payModeCodeName\\\":\\\"现金支付\\\",\\\"payStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentBusinessType\\\":\\\"BUSINESS_TYPE_NEW_CONTRACT\\\",\\\"paymentCompleteDate\\\":1766250000000,\\\"paymentModeCode\\\":\\\"CASH\\\",\\\"paymentStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"policyPremiumId\\\":\\\"55cbc4d9e5134e25aa188a9147363ff6\\\",\\\"policyYear\\\":1,\\\"premiumBeforeDiscount\\\":0,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPersist\\\":null,\\\"premiumSource\\\":\\\"APP\\\",\\\"productId\\\":null,\\\"productLevel\\\":null,\\\"promotionType\\\":null,\\\"promotionTypeName\\\":null,\\\"providerId\\\":\\\"PRO8888888888888\\\",\\\"receivableDate\\\":***********89,\\\"serviceChargeFee\\\":0.000000,\\\"signTypeCode\\\":null,\\\"specialDiscount\\\":null,\\\"standardPremium\\\":0.00,\\\"statusCode\\\":\\\"RECEIVABLE\\\",\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":1640056035944,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"weakAddPremium\\\":0.00},\\\"policyPayorInfo\\\":null,\\\"policyPeriod\\\":1,\\\"policyPremium\\\":{\\\"actualPremium\\\":526.00,\\\"agentDiscount\\\":null,\\\"approveDate\\\":null,\\\"chargingMethod\\\":\\\"CASH\\\",\\\"companyDiscount\\\":null,\\\"consultFee\\\":null,\\\"createdDate\\\":1640056035424,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"discountModel\\\":null,\\\"discountType\\\":null,\\\"forceSave\\\":false,\\\"frequency\\\":\\\"1\\\",\\\"listCoveragePremium\\\":[],\\\"nextChargingMethod\\\":null,\\\"payStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentCompleteDate\\\":1766250000000,\\\"paymentNo\\\":\\\"cf6b08af42954fd5a2bb12a7f0180b60\\\",\\\"payoutFee\\\":null,\\\"periodOriginalPremium\\\":526.00,\\\"periodTotalPremium\\\":526.00,\\\"policyBalance\\\":526.00,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyNo\\\":null,\\\"policyPayment\\\":{\\\"actualPremium\\\":526.00,\\\"addPremiumStartDate\\\":0,\\\"addPremiumTerm\\\":0,\\\"agencyFee\\\":0.00,\\\"annOccuAddPremium\\\":0.00,\\\"annStandardPremium\\\":0.00,\\\"annTotalPremium\\\":0.00,\\\"annWeakAddPremium\\\":0.00,\\\"applyDate\\\":*************,\\\"basePremium\\\":526.00,\\\"bizBranchId\\\":\\\"GMA101103\\\",\\\"bizDate\\\":***********89,\\\"bizYearMonth\\\":\\\"202112\\\",\\\"businessId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"careerAddPremium\\\":0.00,\\\"checkDate\\\":null,\\\"commissionFee\\\":47.34,\\\"commissionGenerateFlag\\\":\\\"GENERATED\\\",\\\"coverageId\\\":null,\\\"createdDate\\\":1640056035426,\\\"createdUserId\\\":null,\\\"currencyCode\\\":\\\"USD\\\",\\\"discountModel\\\":null,\\\"discountPremiumFlag\\\":\\\"NO\\\",\\\"discountType\\\":null,\\\"discountTypeName\\\":null,\\\"extraPremium\\\":0.00,\\\"forceSave\\\":false,\\\"frequency\\\":1,\\\"gainedDate\\\":***********89,\\\"govStandardPremium\\\":0.00,\\\"healthAddPremium\\\":0.00,\\\"insuredId\\\":null,\\\"insuredSum\\\":null,\\\"listPolicyCoveragePayment\\\":[],\\\"otherAddPremium\\\":0.00,\\\"payEndDate\\\":1766250000000,\\\"payModeCode\\\":\\\"CASH\\\",\\\"payModeCodeName\\\":\\\"现金支付\\\",\\\"payStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"paymentBusinessType\\\":\\\"BUSINESS_TYPE_NEW_CONTRACT\\\",\\\"paymentCompleteDate\\\":1766250000000,\\\"paymentModeCode\\\":\\\"CASH\\\",\\\"paymentStatusCode\\\":\\\"PAYMENT_SUCCESS\\\",\\\"periodActualPremium\\\":526.00,\\\"periodOccuAddPremium\\\":0.00,\\\"periodOriginalPremium\\\":526.00,\\\"periodStandardPremium\\\":526.00,\\\"periodTotalPremium\\\":526.00,\\\"periodWeakAddPremium\\\":0.00,\\\"policyAddPremiumId\\\":null,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"policyPaymentId\\\":\\\"fd23cbe5ccbe47a4a2743cdd7fb99e84\\\",\\\"policyPremiumId\\\":\\\"55cbc4d9e5134e25aa188a9147363ff6\\\",\\\"policyYear\\\":1,\\\"premiumBeforeDiscount\\\":0,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumPersist\\\":null,\\\"premiumSource\\\":\\\"APP\\\",\\\"productId\\\":null,\\\"productLevel\\\":null,\\\"promotionType\\\":null,\\\"promotionTypeName\\\":null,\\\"providerId\\\":\\\"PRO8888888888888\\\",\\\"receivableDate\\\":***********89,\\\"serviceChargeFee\\\":0.000000,\\\"signTypeCode\\\":null,\\\"specialDiscount\\\":null,\\\"standardPremium\\\":0.00,\\\"statusCode\\\":\\\"RECEIVABLE\\\",\\\"totalAddPremium\\\":0.00,\\\"totalDiscountPremium\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPremium\\\":526.00,\\\"updatedDate\\\":1640056035944,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"valuePremium\\\":526.00,\\\"weakAddPremium\\\":0.00},\\\"policyPremiumId\\\":\\\"55cbc4d9e5134e25aa188a9147363ff6\\\",\\\"policyStatus\\\":null,\\\"premDueDate\\\":***********89,\\\"premiumBeforeDiscount\\\":null,\\\"premiumFrequency\\\":\\\"YEAR\\\",\\\"premiumPeriod\\\":\\\"5\\\",\\\"premiumPeriodUnit\\\":\\\"YEAR\\\",\\\"premiumSource\\\":null,\\\"premiumStatus\\\":\\\"PAYMENT_SUCCESS\\\",\\\"promotionType\\\":null,\\\"receivableDate\\\":***********89,\\\"remark\\\":null,\\\"specialDiscount\\\":null,\\\"totalActualPremium\\\":526.00,\\\"totalDeductPremium\\\":0.00,\\\"totalDeductRefundAmount\\\":0.00,\\\"totalLine\\\":0,\\\"totalOriginalPremium\\\":526.00,\\\"totalPeriod\\\":null,\\\"totalRefundAmount\\\":0.00,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyPrintInfo\\\":null,\\\"policyReceiptInfo\\\":{\\\"createdDate\\\":1640056035523,\\\"createdUserId\\\":null,\\\"forceSave\\\":false,\\\"policyId\\\":\\\"c81907011a654ec5bc523b041207631b\\\",\\\"receiptDate\\\":null,\\\"receiptInfoId\\\":\\\"b763d45f35464c04a13b7fcad97e93fd\\\",\\\"receiptNo\\\":null,\\\"receiptReturnDate\\\":null,\\\"receiptReviewDecisionRemark\\\":null,\\\"receiptStatus\\\":null,\\\"receiptSubmitDate\\\":null,\\\"returnDate\\\":null,\\\"signDate\\\":null,\\\"submitDate\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":null,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\"},\\\"policyStatus\\\":\\\"POLICY_STATUS_EFFECTIVE\\\",\\\"policyType\\\":\\\"LIFE_INSURANCE_PERSONAL\\\",\\\"preUnderwritingFlag\\\":null,\\\"providerId\\\":\\\"PRO8888888888888\\\",\\\"referralInfo\\\":null,\\\"riskCommencementDate\\\":***********89,\\\"salesBranchId\\\":\\\"GMA101103\\\",\\\"selfInsuranceFlag\\\":\\\"YES\\\",\\\"signBranchId\\\":\\\"GMM\\\",\\\"statements\\\":[],\\\"thoroughInvalidDate\\\":null,\\\"totalLine\\\":0,\\\"updatedDate\\\":1640056035942,\\\"updatedUserId\\\":null,\\\"validFlag\\\":\\\"effective\\\",\\\"verifyNo\\\":\\\"81017641\\\",\\\"versionNo\\\":\\\"20211221100715\\\"}\",\"language\":\"ZH_CN\",\"pdfType\":\"POLICY\",\"productId\":\"PRO880000000000020\"}";

        Map map = JSONObject.parseObject(json, Map.class);
        // Data
        String contentJson = (String) map.get("content");

        Map<String, Object> planData = getPolicyData(contentJson, "ZH_CN");

        planData.put("c1", 1);
        planData.put("c2", 0);

        Document document = XmlUtil.map2xml(planData, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream(FILE_PATH + FILE_NAME + ".rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = OracleBiUtils.refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);
        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post("http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
        // 添加计划书首尾页
        // forObject = PlanCommon.updateAndSavePageNumber(forObject);
        FileOutputStream fileOutputStream = new FileOutputStream(FILE_PATH + FILE_NAME + ".pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();

    }

    private Map<String, Object> getPolicyData(String content, String language) throws Exception {
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        Long backTrackDate = policyBo.getApproveDate();

        Map<String, Object> map = new HashMap<>();
        Long riskCommencementDate = policyBo.getApproveDate();
        if (AssertUtils.isNotNull(policyBo.getRiskCommencementDate())) {
            riskCommencementDate = policyBo.getRiskCommencementDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", riskCommencementDate, 3);
        //合同号  保单号
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));

        PolicyApplicantBo policyApplicant = policyBo.getPolicyApplicant();
        //投保人姓名
        map.put("applicantName", PrintCommon.getPrintString(policyApplicant.getName(), 3));
        //投保人性别
        map.put("applicantSexName", PrintCommon.getPrintString(policyApplicant.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", policyApplicant.getBirthday(), 3);
        //证件号码
        String applicantIdNoAndIdTypeName = null;
        map.put("applicantIdNo", PrintCommon.getPrintString(policyApplicant.getIdNo(), 3));
        if (AssertUtils.isNotEmpty(policyApplicant.getIdTypeName()) && AssertUtils.isNotEmpty(policyApplicant.getIdNo())) {
            applicantIdNoAndIdTypeName = policyApplicant.getIdTypeName() + " / " + policyApplicant.getIdNo();
        }
        map.put("applicantIdNoAndIdTypeName", PrintCommon.getPrintString(applicantIdNoAndIdTypeName, 3));
        //手机号
        map.put("applicantMobile", PrintCommon.getPrintString(policyApplicant.getMobile(), 3));
        Integer applicantAgeYear = DateUtils.getAgeYear(new Date(policyApplicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantFullAddress", PrintCommon.getPrintString(policyApplicant.getFullAddress(), 3));

        // 被保人信息
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured()) && AssertUtils.isNotNull(policyBo.getListPolicyInsured().get(0))) {
            policyInsuredBo = policyBo.getListPolicyInsured().get(0);
        }
        //投保人姓名
        map.put("insuredName", PrintCommon.getPrintString(policyInsuredBo.getName(), 3));
        //投保人性别
        map.put("insuredSexName", PrintCommon.getPrintString(policyInsuredBo.getSexName(), 3));
        //投保人 出生年月日
        PrintCommon.setPrintDateTime(map, "insuredBirthday", policyInsuredBo.getBirthday(), 3);
        Integer insuredAgeYear = DateUtils.getAgeYear(new Date(policyInsuredBo.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //投保人证件号码
        String insuredIdNoAndIdTypeName = null;
        if (AssertUtils.isNotEmpty(policyInsuredBo.getIdTypeName()) && AssertUtils.isNotEmpty(policyInsuredBo.getIdNo())) {
            insuredIdNoAndIdTypeName = policyInsuredBo.getIdTypeName() + " / " + policyInsuredBo.getIdNo();
        }
        map.put("insuredIdNoAndIdTypeName", PrintCommon.getPrintString(insuredIdNoAndIdTypeName, 3));
        //与投保人什么关系
        map.put("insuredRelationshipName", PrintCommon.getPrintString(policyInsuredBo.getRelationshipName(), 3));
        //手机号
        map.put("insuredMobile", PrintCommon.getPrintString(policyInsuredBo.getMobile(), 3));
        map.put("insuredFullAddress", PrintCommon.getPrintString(policyInsuredBo.getFullAddress(), 3));

        // 保险
        List<ProductCashValueBo> policyCashValues = policyBo.getListCashValue();

        List<PolicyAddPremiumBo> listPolicyAddPremium = policyBo.getListPolicyAddPremium();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        List<PolicyCoverageBo> listPolicyCoverage = policyInsuredBo.getListPolicyCoverage();
        if (!AssertUtils.isNotEmpty(listPolicyCoverage)) {
            listPolicyCoverage = new ArrayList<>();
        }
        Map<String, List<ProductCashValueBo>> policyCashValueListMap = policyCashValues.stream().collect(Collectors.groupingBy(ProductCashValueBo::getProductId));
        PrintCommon.setProductName(map, "PRO880000000000020", "Main", null, null);
        for (PolicyCoverageBo coverageBo : listPolicyCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = PrintCommon.getPrintString(AssertUtils.isNotEmpty(coverageBo.getTotalAmount()) ? PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getTotalAmount())) : null, 2);
            coverageMap.put("totalAmount", totalAmount);
            coverageMap.put(coverageBo.getProductId() + "totalAmount", totalAmount);
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageMap.put(coverageBo.getProductId() + "premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            int premiumPeriodInteger = Integer.parseInt(coverageBo.getPremiumPeriod());
            long premiumCessationDate = coverageBo.getCoveragePeriodStartDate();
            String premiumFrequency = coverageBo.getPremiumFrequency();
            if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 1);
            } else if (SEASON.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 4);
            } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 6);
            } else if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                premiumCessationDate = DateUtils.addStringMonthRT(coverageBo.getCoveragePeriodStartDate(), (premiumPeriodInteger * 12) - 12);
            }
            PrintCommon.setPrintDateTime(coverageMap, "premiumCessationDate", premiumCessationDate, 3);
            PrintCommon.setPrintDateTime(coverageMap, "coveragePeriodEndDate", coverageBo.getCoveragePeriodEndDate(), 3);
            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maturityAmount = null;
            List<ProductCashValueBo> productCashValueBos = policyCashValueListMap.get(coverageBo.getProductId());
            if (AssertUtils.isNotEmpty(productCashValueBos)) {
                OptionalDouble max = productCashValueBos.stream().mapToDouble(p -> p.getCashValue().doubleValue()).max();
                if (max.isPresent()) {
                    maturityAmount = new BigDecimal(max.getAsDouble());
                }
            }
            map.put(coverageBo.getProductId() + "maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            coverageMap.put("maturityAmount", PrintCommon.getPrintString(maturityAmount, 2));
            //额外加费
            BigDecimal extraPremium = null;
            BigDecimal totalPremium = coverageBo.getTotalPremium();
            if (AssertUtils.isNotEmpty(listPolicyAddPremium)) {
                List<PolicyAddPremiumBo> policyAddPremiumBoList = listPolicyAddPremium.stream()
                        .filter(policyAddPremiumBo -> coverageBo.getCoverageId().equals(policyAddPremiumBo.getCoverageId()) &&
                                AssertUtils.isNotNull(policyAddPremiumBo.getTotalAddPremium()) &&
                                EFFECTIVE.name().equals(policyAddPremiumBo.getAddPremiumStatus())).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyAddPremiumBoList)) {
                    double totalAddPremium = policyAddPremiumBoList.stream().mapToDouble(policyAddPremiumBo -> policyAddPremiumBo.getTotalAddPremium().doubleValue()).sum();
                    extraPremium = new BigDecimal(totalAddPremium).multiply(conversionFactor);
                    totalPremium = totalPremium.subtract(extraPremium);
                    yearTotalPremium = yearTotalPremium.subtract(new BigDecimal(totalAddPremium));
                }
            }
            coverageMap.put("extraPremium", PrintCommon.getPrintString(extraPremium, 2));
            coverageMap.put("totalPremium", PrintCommon.getPrintString(totalPremium, 2));
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));
            coverageListMap.add(coverageMap);
        }
        PolicyCoverageBo mainCoverageBo = listPolicyCoverage.stream().filter(policyCoverage -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverage.getPrimaryFlag())).findFirst().get();
        String premiumFrequencyName = mainCoverageBo.getPremiumFrequencyName();
        String premiumMonthFrequency = null;
        if (SINGLE.name().equals(mainCoverageBo.getPremiumFrequency())) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                premiumFrequencyName = "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                premiumFrequencyName = "Single Payment";
            }
        } else if (YEAR.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "12";
        } else if (SEMIANNUAL.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "06";
        } else if (SEASON.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "03";
        } else if (MONTH.name().equals(mainCoverageBo.getPremiumFrequency())) {
            premiumMonthFrequency = "01";
        }
        map.put("premiumMonthFrequency", PrintCommon.getPrintString(premiumMonthFrequency, 3));
        map.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 3));
        double totalPremiumSum = listPolicyCoverage.stream().filter(policyCoverage -> AssertUtils.isNotNull(policyCoverage.getTotalPremium())).mapToDouble(policyCoverage -> policyCoverage.getTotalPremium().doubleValue()).sum();
        map.put("totalPremiumSum", PrintCommon.getPrintString(new BigDecimal(totalPremiumSum), 3));

        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);

        // 现金价值
        // ProductCalculation.policyCashValues13(map, policyCashValueListMap);
        List<Map<String, Object>> policyCashValuesListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyCashValues)) {
            policyCashValues.forEach(productCashValueBo -> {
                Map<String, Object> productCashValue = new HashMap<>();
                productCashValue.put("pcvPolicyYear", productCashValueBo.getPolicyYear());
                productCashValue.put("pcvAmount", PrintCommon.getPrintString(productCashValueBo.getAmount(), 3));
                productCashValue.put("pcvCashValue", PrintCommon.getPrintString(productCashValueBo.getCashValue(), 3));
                policyCashValuesListMap.add(productCashValue);
            });
            map.put("policyCashValuesListMap", policyCashValuesListMap);
        }

        // 代理人编码
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("agentName", PrintCommon.getPrintString(policyAgent.getAgentName(), 3));
        //签发日期
        PrintCommon.setPrintDateTime(map, "approveDate", policyBo.getApproveDate(), 3);


        return map;
    }

    private Map<String, Object> getApplyData(String content, String language) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Long backTrackDate = applyPrintBo.getApplyDate();
        if (AssertUtils.isNotNull(applyPrintBo.getBackTrackDate())) {
            backTrackDate = applyPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("prohibitedString2", PrintCommon.getPrintString(null, 2));
        map.put("prohibitedString3", PrintCommon.getPrintString(null, 3));
        map.put("prohibitedString4", PrintCommon.getPrintString(null, 4));
        map.put("prohibitedString5", PrintCommon.getPrintString(null, 5));
        map.put("prohibitedString6", PrintCommon.getPrintString(null, 6));
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applyPlanNo", PrintCommon.getPrintString(applyPrintBo.getApplyPlanNo(), 3));

        // 投保人
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "applicantSex" + applicant.getSex(), applicant.getSex());
        //出生年月日
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        long applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        //证件类型
        PrintCommon.setSelectionBox(map, "applicantIdType" + applicant.getIdType(), applicant.getIdType());
        //国籍
        map.put("applicantNationalityName", PrintCommon.getPrintString(applicant.getNationalityName(), 2));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "applicantIdExpDate", applicant.getIdExpDate(), 3);
        //证件号
        map.put("applicantIdTypeName", PrintCommon.getPrintString(applicant.getIdTypeName(), 3));
        map.put("applicantIdNo", PrintCommon.getPrintString(applicant.getIdNo(), 3));
        //婚姻状况
        String applicantMarriage = applicant.getMarriage();
        PrintCommon.setSelectionBox(map, "applicantMarriage" + applicantMarriage, applicantMarriage);
        String applicantExpectedPremiumSources = applicant.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "applicantEPS", applicantExpectedPremiumSources);
        //工作单位
        map.put("applicantCompanyName", PrintCommon.getPrintString(applicant.getCompanyName(), 3));
        //收入
        map.put("applicantIncome", PrintCommon.getPrintString(applicant.getIncome(), 2));
        //固定电话
        map.put("applicantPhone", PrintCommon.getPrintString(applicant.getHomePhone(), 3));
        //移动电话
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));
        //移动电话
        map.put("applicantMobile_2", PrintCommon.getPrintString(applicant.getMobile_2(), 3));
        //邮箱
        map.put("applicantEmail", PrintCommon.getPrintString(applicant.getEmail(), 2));
        //通讯地址
        map.put("applicantHomeAddress", PrintCommon.getPrintString(applicant.getFullAddress(), 3));
        //通讯地址
        String applicantCompanyAreaName = AssertUtils.isNotEmpty(applicant.getCompanyAreaName()) ? applicant.getCompanyAreaName() : "";
        String applicantCompanyAddress = AssertUtils.isNotEmpty(applicant.getCompanyAddress()) ? applicant.getCompanyAddress() : "";
        map.put("applicantCompanyAddressWhole", PrintCommon.getPrintString(applicantCompanyAreaName + applicantCompanyAddress, 3));
        //邮政编码
        map.put("applicantZipCode", PrintCommon.getPrintString(applicant.getHomeZipCode(), 3));
        //职业
        map.put("applicantOccupationName", PrintCommon.getPrintString(applicant.getOccupationName(), 3));
        //兼职
        map.put("applicantPluralityName", PrintCommon.getPrintString(applicant.getPluralityName(), 3));
        //职业代码
        map.put("applicantOccupationCode", PrintCommon.getPrintString(applicant.getOccupationCode(), 3));
        map.put("applicantFacebookNo", PrintCommon.getPrintString(applicant.getFacebookNo(), 3));
        map.put("applicantStature", PrintCommon.getPrintString(applicant.getStature(), 3));
        map.put("applicantAvoirdupois", PrintCommon.getPrintString(applicant.getAvoirdupois(), 3));
        map.put("applicantExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(applicant.getExpectedPremiumSourcesSpecific(), 3));
        map.put("applicantDoctorName", PrintCommon.getPrintString(applicant.getDoctorName(), 3));
        String applicantDoctorAreaCodeName = AssertUtils.isNotEmpty(applicant.getDoctorAreaCodeName()) ? applicant.getDoctorAreaCodeName() : "";
        String applicantDoctorAddress = AssertUtils.isNotEmpty(applicant.getDoctorAddress()) ? applicant.getDoctorAddress() : "";
        map.put("applicantDoctorAreaCodeName", PrintCommon.getPrintString(applicantDoctorAreaCodeName + applicantDoctorAddress, 3));
        PrintCommon.setSelectionBox(map, "applicantAddressType" + applicant.getAddressType(), applicant.getAddressType());

        // 被保人
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo insured = new ApplyInsuredBo();
        if (AssertUtils.isNotEmpty(listInsured) && AssertUtils.isNotNull(listInsured.get(0))) {
            insured = listInsured.get(0);
        }
        //与投保人关系
        if (AssertUtils.isNotEmpty(insured.getRelationshipInstructions()) && OTHER.name().equals(insured.getRelationship())) {
            map.put("relationshipInstructions", insured.getRelationshipInstructions());
        }
        map.put("relationshipName", insured.getRelationshipName());

        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        //性别
        PrintCommon.setSelectionBox(map, "insuredSex" + insured.getSex(), insured.getSex());
        //出生年月日
        map.put("insuredBirthday", PrintCommon.getPrintString(insured.getBirthday(), 3));
        PrintCommon.setPrintDateTime(map, "insuredBirthday", insured.getBirthday(), 3);
        //证件类型
        PrintCommon.setSelectionBox(map, "insuredIdType" + insured.getIdType(), insured.getIdType());
        //国籍
        map.put("insuredNationalityName", PrintCommon.getPrintString(insured.getNationalityName(), 3));
        //证件有效期
        PrintCommon.setPrintDateTime(map, "insuredIdExpDate", insured.getIdExpDate(), 3);
        map.put("insuredIdNo", PrintCommon.getPrintString(insured.getIdNo(), 3));
        map.put("insuredIdTypeName", PrintCommon.getPrintString(insured.getIdTypeName(), 3));
        //婚姻状况
        String insuredMarriage = insured.getMarriage();
        PrintCommon.setSelectionBox(map, "insuredMarriage" + insuredMarriage, insuredMarriage);
        String insuredExpectedPremiumSources = insured.getExpectedPremiumSources();
        PrintCommon.setSelectionBoxList(map, "insuredEPS", insuredExpectedPremiumSources);
        //工作单位
        map.put("insuredCompanyName", PrintCommon.getPrintString(insured.getCompanyName(), 3));
        //收入 隐藏被保险人收入
        map.put("insuredIncome", PrintCommon.getPrintString(insured.getIncome(), 2));
        long insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        //固定电话
        map.put("insuredPhone", PrintCommon.getPrintString(insured.getHomePhone(), 3));
        //移动电话
        map.put("insuredMobile", PrintCommon.getPrintString(insured.getMobile(), 3));
        //移动电话
        map.put("insuredMobile_2", PrintCommon.getPrintString(insured.getMobile_2(), 3));
        //邮箱
        map.put("insuredEmail", PrintCommon.getPrintString(insured.getEmail(), 3));
        //通讯地址
        map.put("insuredHomeAddress", PrintCommon.getPrintString(insured.getFullAddress(), 3));
        //通讯地址
        String insuredCompanyAreaName = AssertUtils.isNotEmpty(insured.getCompanyAreaName()) ? insured.getCompanyAreaName() : "";
        String insuredCompanyAddress = AssertUtils.isNotEmpty(insured.getCompanyAddress()) ? insured.getCompanyAddress() : "";
        map.put("insuredCompanyAddressWhole", PrintCommon.getPrintString(insuredCompanyAreaName + insuredCompanyAddress, 3));
        //邮政编码
        map.put("insuredZipCode", PrintCommon.getPrintString(insured.getHomeZipCode(), 3));
        //职业
        map.put("insuredOccupationName", PrintCommon.getPrintString(insured.getOccupationName(), 3));
        //兼职
        map.put("insuredPluralityName", PrintCommon.getPrintString(insured.getPluralityName(), 3));
        //职业代码
        map.put("insuredOccupationCode", PrintCommon.getPrintString(insured.getOccupationCode(), 3));
        map.put("insuredFacebookNo", PrintCommon.getPrintString(insured.getFacebookNo(), 3));
        map.put("insuredStature", PrintCommon.getPrintString(insured.getStature(), 3));
        map.put("insuredAvoirdupois", PrintCommon.getPrintString(insured.getAvoirdupois(), 3));
        map.put("insuredExpectedPremiumSourcesSpecific", PrintCommon.getPrintString(insured.getExpectedPremiumSourcesSpecific(), 3));
        map.put("insuredDoctorName", PrintCommon.getPrintString(insured.getDoctorName(), 3));
        String insuredDoctorAreaCodeName = AssertUtils.isNotEmpty(insured.getDoctorAreaCodeName()) ? insured.getDoctorAreaCodeName() : "";
        String insuredDoctorAddress = AssertUtils.isNotEmpty(insured.getDoctorAddress()) ? insured.getDoctorAddress() : "";
        map.put("insuredDoctorAreaCodeName", PrintCommon.getPrintString(insuredDoctorAreaCodeName + insuredDoctorAddress, 3));
        map.put("taxpayerNo", PrintCommon.getPrintString(insured.getTaxpayerNo(), 3));
        PrintCommon.setSelectionBox(map, "insuredAddressType" + insured.getAddressType(), insured.getAddressType());

        // 受益人信息
        List<ApplyBeneficiaryInfoBo> listBeneficiary = insured.getListBeneficiary();
        if (AssertUtils.isNotEmpty(listBeneficiary)) {
            List<Map<String, Object>> beneficiaryListMap = new ArrayList<>();
            listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                Map<String, Object> beneficiaryMap = new HashMap<>();
                //收益人信息
                //收益顺序
                beneficiaryMap.put("beneficiaryNo", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryNoOrderName(), 3));
                //姓名
                String beneficiaryName = applyBeneficiaryBo.getName();
                String idNo = applyBeneficiaryBo.getIdNo();
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getBeneficiaryBranchCode())) {
                    beneficiaryName = applyBeneficiaryBo.getBeneficiaryBranchName();
                    idNo = applyBeneficiaryBo.getBeneficiaryBranchCode();
                }
                beneficiaryMap.put("beneficiaryName", PrintCommon.getPrintString(beneficiaryName, 3));
                //性别
                beneficiaryMap.put("beneficiarySexName", PrintCommon.getPrintString(applyBeneficiaryBo.getSexName(), 1));
                //是被保险人的
                String relationshipName = applyBeneficiaryInfoBo.getRelationshipName();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getRelationshipInstructions()) && OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())) {
                    relationshipName = applyBeneficiaryInfoBo.getRelationshipInstructions();
                }
                beneficiaryMap.put("relationshipName", PrintCommon.getPrintString(relationshipName, 2));
                //收益份额
                beneficiaryMap.put("beneficiaryProportion", PrintCommon.getPrintString(applyBeneficiaryInfoBo.getBeneficiaryProportion(), 3));
                //证件类型
                beneficiaryMap.put("beneficiaryIdTypeName", PrintCommon.getPrintString(applyBeneficiaryBo.getIdTypeName(), 3));
                //证件类型
                beneficiaryMap.put("homeAddress", PrintCommon.getPrintString(applyBeneficiaryBo.getHomeAddress(), 3));
                //出生年月日
                PrintCommon.setPrintDateTime(beneficiaryMap, "beneficiaryBirthday", applyBeneficiaryBo.getBirthday(), 3);
                //证件号码
                beneficiaryMap.put("beneficiaryIdNo", PrintCommon.getPrintString(idNo, 3));
                beneficiaryListMap.add(beneficiaryMap);
            });
            map.put("beneficiaryListMap", beneficiaryListMap);
        }

        // 险种信息
        List<ApplyCoverageBo> listCoverage = insured.getListCoverage();
        AtomicReference<String> pensionReceiveFrequency = new AtomicReference<>();
        AtomicReference<String> productLevel = new AtomicReference<>();
        AtomicReference<String> productId = new AtomicReference<>();
        AtomicReference<String> financingMethod = new AtomicReference<>();
        AtomicReference<String> premiumPeriod = new AtomicReference<>();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<Map<String, Object>> coverageListMap = new ArrayList<>();
            listCoverage.forEach(applyCoverageBo -> {
                Map<String, Object> coverageMap = new HashMap<>();
                //险种名称
                PrintCommon.setProductName(coverageMap, applyCoverageBo.getProductId(), applyCoverageBo.getProductLevel(), language);
                BigDecimal totalAmount = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                    totalAmount = new BigDecimal(applyCoverageBo.getAmount());
                }
                coverageMap.put("totalAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount), 2));
                //领取年龄及方式　　
                if (AssertUtils.isNotNull(applyCoverageBo.getPensionReceiveFrequency())) {
                    pensionReceiveFrequency.set(applyCoverageBo.getPensionReceiveFrequency());
                }
                //保险期限
                String coveragePeriodUnitName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnitName())) {
                    coveragePeriodUnitName = applyCoverageBo.getCoveragePeriod() + " " + applyCoverageBo.getCoveragePeriodUnitName();
                    if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getCoveragePeriodUnit())) {
                        coveragePeriodUnitName = applyCoverageBo.getCoveragePeriodUnitName() + " " + applyCoverageBo.getCoveragePeriod();
                    }
                }
                if ("PRO88000000000008".equals(applyCoverageBo.getProductId())) {
                    coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
                }
                coverageMap.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
                //保险费金额
                coverageMap.put("totalPremium", PrintCommon.getPrintString(applyCoverageBo.getTotalPremium(), 2));
                //缴费期限
                String premiumPeriodName = null;
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriod()) && AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnitName())) {
                    premiumPeriodName = applyCoverageBo.getPremiumPeriod() + " " + applyCoverageBo.getPremiumPeriodUnitName();
                    if (SINGLE.name().equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = KM_KH.name().equals(language) ? "បង់ផ្តាច់តែម្តង" : ZH_CN.name().equals(language) ? "一次性全额缴清" : "Single Payment";
                    }
                    if (KM_KH.name().equals(language) && "AGE".equals(applyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriodName = applyCoverageBo.getPremiumPeriodUnitName() + " " + applyCoverageBo.getPremiumPeriod();
                    }
                }
                if ("PRO880000000000014".equals(applyCoverageBo.getProductId())) {
                    if ("ACCELERATION_CI".equals(applyCoverageBo.getProductLevel())) {
                        coverageMap.put("productLevelZH_CN", "(提前给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍ផ្តល់ជូនមុន)");
                        coverageMap.put("productLevelEN_US", "(Acceleration)");
                    } else {
                        coverageMap.put("productLevelZH_CN", "(额外给付)");
                        coverageMap.put("productLevelKM_KH", "(អត្ថប្រយោជន៍បន្ថែម)");
                        coverageMap.put("productLevelEN_US", "(Additional)");
                    }
                }
                coverageMap.put("premiumPeriodName", PrintCommon.getPrintString(premiumPeriodName, 2));
                coverageListMap.add(coverageMap);
                if (AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    productLevel.set(applyCoverageBo.getProductLevel());
                    productId.set(applyCoverageBo.getProductId());
                    financingMethod.set(applyCoverageBo.getFinancingMethod());
                    premiumPeriod.set(applyCoverageBo.getPremiumPeriod());
                }

            });
            map.put("coverageListMap", coverageListMap);
        }
        //领取年龄及方式
        PrintCommon.setSelectionBox(map, "pensionReceiveFrequency" + pensionReceiveFrequency.get(), pensionReceiveFrequency.get());
        //保费合计总额 美元
        map.put("allTotalPremium", PrintCommon.getPrintString(applyPrintBo.getReceivablePremium(), 2));

        // 交费
        String premiumFrequency = applyPrintBo.getPremiumFrequency();
        PrintCommon.setSelectionBox(map, "premiumFrequency" + premiumFrequency, premiumFrequency);
        //缴费形式
        String paymentMode = applyPrintBo.getPaymentMode();
        if (AttachmentTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CASH.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMode)) {
        } else if (AttachmentTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(paymentMode)) {
        } else {
            paymentMode = "OTHER";
        }
        PrintCommon.setSelectionBox(map, "paymentMode" + paymentMode, paymentMode);

        // 其他投保的保险
        List<ApplyOtherInsuranceBo> listApplyOtherInsurancePo = applyPrintBo.getOtherInsurance();
        List<Map<String, Object>> applyOtherInsuranceListMap = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyOtherInsurancePo)) {
            for (ApplyOtherInsuranceBo applyOtherInsuranceBo : listApplyOtherInsurancePo) {
                Map<String, Object> applyOtherInsuranceMap = new HashMap<>();
                applyOtherInsuranceMap.put("otherInsuringInsuredName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuredName(), 3));
                applyOtherInsuranceMap.put("otherInsuringCompany", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuringCompany(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceTypeName", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceTypeName(), 3));
                applyOtherInsuranceMap.put("otherInsuringAmount", PrintCommon.getPrintString(applyOtherInsuranceBo.getAmount(), 3));
                applyOtherInsuranceMap.put("otherInsuringInsuranceYear", PrintCommon.getPrintString(applyOtherInsuranceBo.getInsuranceYear(), 3));
                applyOtherInsuranceListMap.add(applyOtherInsuranceMap);
            }
        }
        map.put("applyOtherInsuranceListMap", applyOtherInsuranceListMap);

        // 其他投保的保险
        List<ApplyOccupationNatureBo> occupationNatureList = applyPrintBo.getOccupationNature();
        if (!AssertUtils.isNotEmpty(occupationNatureList)) {
            occupationNatureList = new ArrayList<>();
        }
        ApplyOccupationNatureBo applicantOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> applicantOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "APPLICANT".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (applicantOptionalOccupationNatureBo.isPresent()) {
            applicantOccupationNatureBo = applicantOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "applicantON" + applicantOccupationNatureBo.getOccupationNature(), applicantOccupationNatureBo.getOccupationNature());
        map.put("applicantOccupationNatureSpecific", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("applicantEmployerName", PrintCommon.getPrintString(applicantOccupationNatureBo.getEmployerName(), 3));
        map.put("applicantBusinessNature", PrintCommon.getPrintString(applicantOccupationNatureBo.getBusinessNature(), 3));
        map.put("applicantOccupationExactDuties", PrintCommon.getPrintString(applicantOccupationNatureBo.getOccupation(), 3));
        map.put("applicantOccupationClass", PrintCommon.getPrintString(applicantOccupationNatureBo.getExactDuties(), 3));

        ApplyOccupationNatureBo insuredOccupationNatureBo = new ApplyOccupationNatureBo();
        Optional<ApplyOccupationNatureBo> insuredOptionalOccupationNatureBo = occupationNatureList.stream().filter(applyOccupationNatureBo -> "INSURED".equals(applyOccupationNatureBo.getCustomerType())).findFirst();
        if (insuredOptionalOccupationNatureBo.isPresent()) {
            insuredOccupationNatureBo = insuredOptionalOccupationNatureBo.get();
        }
        PrintCommon.setSelectionBox(map, "insuredON" + insuredOccupationNatureBo.getOccupationNature(), insuredOccupationNatureBo.getOccupationNature());
        map.put("insuredOccupationNatureSpecific", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupationNatureSpecific(), 3));
        map.put("insuredEmployerName", PrintCommon.getPrintString(insuredOccupationNatureBo.getEmployerName(), 3));
        map.put("insuredBusinessNature", PrintCommon.getPrintString(insuredOccupationNatureBo.getBusinessNature(), 3));
        map.put("insuredOccupationExactDuties", PrintCommon.getPrintString(insuredOccupationNatureBo.getOccupation(), 3));
        map.put("insuredOccupationClass", PrintCommon.getPrintString(insuredOccupationNatureBo.getExactDuties(), 3));

        // 账户
        List<ApplyAccountBo> listApplyAccount = applyPrintBo.getListApplyAccount();
        ApplyAccountBo applyAccountBo = new ApplyAccountBo();
        String kmKmBankName = null;
        String bankName = null;

            // if (AssertUtils.isNotEmpty(listApplyAccount)) {
            //     applyAccountBo = listApplyAccount.get(0);
            //     SyscodeResponse kmKmBankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name()).getData();
            //     SyscodeResponse bankSyscode = platformInternationalBaseApi.queryOneInternational(BANK.getCode(), applyAccountBo.getBankCode(), KM_KH.name().equals(language) ? EN_US.name() : language).getData();
            //     kmKmBankName = AssertUtils.isNotNull(kmKmBankSyscode) && AssertUtils.isNotEmpty(kmKmBankSyscode.getCodeName()) ? kmKmBankSyscode.getCodeName() : null;
            //     bankName = AssertUtils.isNotNull(bankSyscode) && AssertUtils.isNotEmpty(bankSyscode.getCodeName()) ? bankSyscode.getCodeName() : null;
            // }

        map.put("kmKmBankName", PrintCommon.getPrintString(kmKmBankName, 3));
        map.put("bankName", PrintCommon.getPrintString(bankName, 3));
        map.put("accountOwner", PrintCommon.getPrintString(applyAccountBo.getAccountOwner(), 3));
        map.put("accountNo", PrintCommon.getPrintString(applyAccountBo.getAccountNo(), 3));

        // 健康告知书
        ProductCalculation.setHealthRemark1(map, applyPrintBo);
        List<ApplyStatementBo> statements = applyPrintBo.getStatements();
        if (AssertUtils.isNotEmpty(statements)) {
            statements.forEach(applyStatementBo -> {
                map.put(applyStatementBo.getStatementCode(), applyStatementBo.getStatementValue());
            });
        }

        // 投保申请日期
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        //受理机构
        map.put("acceptBranchName", PrintCommon.getPrintString(applyPrintBo.getAcceptBranchName(), 3));
        //经办人
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 3));
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 3));
        map.put("agentMobile", PrintCommon.getPrintString(applyAgentBo.getAgentMobile(), 3));
        //受理时间
        PrintCommon.setPrintDateTime(map, "acceptDate", applyPrintBo.getApplyDate(), 3);
        return map;
    }

    public Map<String, Object> getPlanData(String content, String language) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ApplyPlanBo planPrintBo = JSON.parseObject(content, ApplyPlanBo.class);
        Long backTrackDate = planPrintBo.getCreatedDate();
        if (AssertUtils.isNotNull(planPrintBo.getBackTrackDate())) {
            map.put("showBackTrackDateFlag", PrintCommon.getPrintString("YES", 3));
            map.put("backTrackDateNameZH_CN", PrintCommon.getPrintString("回溯日期：", 3));
            backTrackDate = planPrintBo.getBackTrackDate();
        }
        PrintCommon.setPrintDateTime(map, "backTrackDate", backTrackDate, 3);
        //计划书信息
        map.put("applyPlanNo", PrintCommon.getPrintString(planPrintBo.getApplyPlanNo(), 3));

        // 投保人信息
        ApplyApplicantPlanBo applicant = planPrintBo.getApplicant();
        if (!AssertUtils.isNotNull(applicant)) {
            applicant = new ApplyApplicantPlanBo();
        }
        Integer applicantAgeYear = null;
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        if (AssertUtils.isNotNull(applicant.getBirthday())) {
            applicantAgeYear = DateUtils.getAgeYear(new Date(applicant.getBirthday()), new Date(backTrackDate));
        }
        map.put("applicantAgeYear", PrintCommon.getPrintString(applicantAgeYear, 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));

        // 被保人信息
        ApplyInsuredPlanBo insured = planPrintBo.getInsured();
        if (!AssertUtils.isNotNull(insured)) {
            insured = new ApplyInsuredPlanBo();
        }
        map.put("insuredName", PrintCommon.getPrintString(insured.getName(), 3));
        Integer insuredAgeYear = null;
        if (AssertUtils.isNotNull(insured.getBirthday())) {
            insuredAgeYear = DateUtils.getAgeYear(new Date(insured.getBirthday()), new Date(backTrackDate));
        }
        map.put("insuredAgeYear", PrintCommon.getPrintString(insuredAgeYear, 3));
        map.put("insuredSexName", PrintCommon.getPrintString(insured.getSexName(), 3));
        map.put("insuredSex", PrintCommon.getPrintString(insured.getSex(), 3));

        // 获取保险期限  可选其他缴费周期start
        PlanProductDetailResponse planProductDetail = planPrintBo.getPlanProductDetail();
        Map<String, List<CoveragePremiumFrequencyResponse>> coveragePremiumFrequencyMap = planProductDetail.getCoveragePremiumFrequencyMap();
        List<ApplyCoveragePlanBo> listCoverage = planPrintBo.getCoverages();
        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        BigDecimal totalAmount20 = BigDecimal.ZERO;
        String premiumFrequency20 = null;
        String coveragePeriod20 = null;

        if (!AssertUtils.isNotEmpty(listCoverage)) {
            listCoverage = new ArrayList<>();
        }
        for (ApplyCoveragePlanBo coverageBo : listCoverage) {
            Map<String, Object> coverageMap = new HashMap<>();
            String productId = coverageBo.getProductId();
            map.put(coverageBo.getProductId(), coverageBo.getProductId());
            map.put(coverageBo.getProductId() + "ProductLevel", coverageBo.getProductLevel());
            coverageMap.put("productId", coverageBo.getProductId());
            PrintCommon.setProductName(coverageMap, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
            coverageMap.put("primaryFlag", coverageBo.getPrimaryFlag());
            String productLevel = coverageBo.getProductLevel();
            coverageMap.put("productLevel", coverageBo.getProductLevel());
            String totalAmount = null;
            if (AssertUtils.isNotEmpty(coverageBo.getAmount())) {
                totalAmount = PrintCommon.decimalFormat1.format(new BigDecimal(coverageBo.getAmount()));
            }
            coverageMap.put("totalAmount", PrintCommon.getPrintString(totalAmount, 2));
            //保险期间
            String coveragePeriod = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) ? coverageBo.getCoveragePeriod() : "";
            String coveragePeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName()) ? coverageBo.getCoveragePeriodUnitName() : "";
            String insurancePeriod = coveragePeriod + coveragePeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                insurancePeriod = coveragePeriodUnitName + coveragePeriod;
            }
            coverageMap.put("coveragePeriod", PrintCommon.getPrintString(insurancePeriod, 2));
            //交费期限
            String premiumPeriod = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod()) ? coverageBo.getPremiumPeriod() : "";
            String premiumPeriodUnitName = AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnitName()) ? coverageBo.getPremiumPeriodUnitName() : "";
            String premiumPeriodAndUnitName = premiumPeriod + premiumPeriodUnitName;
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getPremiumPeriodUnit())) {
                premiumPeriodAndUnitName = premiumPeriodUnitName + premiumPeriod;
            }
            //交费类型
            String premiumFrequencyName = coverageBo.getPremiumFrequencyName();
            if (SINGLE.name().equals(coverageBo.getPremiumFrequency())) {
                if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                    premiumFrequencyName = "បង់ផ្តាច់តែម្តង";
                }
                if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                    premiumFrequencyName = "一次性全额缴清";
                }
                if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                    premiumFrequencyName = "Single Payment";
                }
                premiumPeriodAndUnitName = premiumFrequencyName;
            }
            coverageMap.put("premiumFrequencyName", PrintCommon.getPrintString(premiumFrequencyName, 2));
            coverageMap.put("premiumPeriod", PrintCommon.getPrintString(premiumPeriodAndUnitName, 2));
            coverageBo.setMult(AssertUtils.isNotEmpty(coverageBo.getMult()) ? coverageBo.getMult() : "1");
            BigDecimal amount = null;
            amount = new BigDecimal(coverageBo.getAmount()).multiply(new BigDecimal(coverageBo.getMult()));
            totalAmount20 = amount;
            coveragePeriod20 = coveragePeriod;
            premiumFrequency20 = coverageBo.getPremiumFrequency();
            coverageMap.put("productAmount", PrintCommon.getPrintString(amount, 2));
            //每期保费
            String premiumFrequency = coverageBo.getPremiumFrequency();
            int i = YEAR.name().equals(premiumFrequency) ? 1 : SEMIANNUAL.name().equals(premiumFrequency) ? 2 : SEASON.name().equals(premiumFrequency) ? 3 : MONTH.name().equals(premiumFrequency) ? 4 : 0;
            coverageMap.put("totalPremium" + i, PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
            //可选缴费周期保险费
            List<CoveragePremiumFrequencyResponse> cpfList = coveragePremiumFrequencyMap.get(productId);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 1, coverageMap, cpfList, YEAR.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 2, coverageMap, cpfList, SEMIANNUAL.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 3, coverageMap, cpfList, SEASON.name(), premiumFrequency);
            ProductCalculation.getPremiumFrequencyTotalPremium(map, 4, coverageMap, cpfList, MONTH.name(), premiumFrequency);

            BigDecimal conversionFactor = BigDecimal.valueOf(AttachmentTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(coverageBo.getPremiumFrequency()).value());
            BigDecimal yearTotalPremium = coverageBo.getTotalPremium().divide(conversionFactor, 2, RoundingMode.HALF_UP);
            map.put(coverageBo.getProductId() + "yearTotalPremium", PrintCommon.getPrintString(yearTotalPremium, 2));

            coverageListMap.add(coverageMap);
        }
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);
        map.put("premiumFrequency", premiumFrequency20);
        double totalPremium1 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium1") + "").replace(",", ""))).sum();
        double totalPremium2 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium2") + "").replace(",", ""))).sum();
        double totalPremium3 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium3") + "").replace(",", ""))).sum();
        double totalPremium4 = coverageListMap.stream().mapToDouble(mapper -> Double.parseDouble((mapper.get("totalPremium4") + "").replace(",", ""))).sum();
        map.put("allTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2), 2));
        map.put("allTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3), 2));
        map.put("allTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4), 2));
        map.put("allYearTotalPremium1", PrintCommon.getPrintString(new BigDecimal(totalPremium1), 2));
        map.put("allYearTotalPremium2", PrintCommon.getPrintString(new BigDecimal(totalPremium2).multiply(new BigDecimal(2)), 2));
        map.put("allYearTotalPremium3", PrintCommon.getPrintString(new BigDecimal(totalPremium3).multiply(new BigDecimal(4)), 2));
        map.put("allYearTotalPremium4", PrintCommon.getPrintString(new BigDecimal(totalPremium4).multiply(new BigDecimal(12)), 2));

        // 保险利益
        BigDecimal tpd_benefit_due_to_accident = totalAmount20.multiply(new BigDecimal("2"));
        map.put("tpd_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        tpd_benefit_due_to_accident)
                , 2));
        map.put("tpd_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        totalAmount20)
                , 2));
        BigDecimal death_benefit_due_to_accident = totalAmount20.multiply(new BigDecimal("4"));
        map.put("death_benefit_due_to_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        death_benefit_due_to_accident)
                , 2));
        map.put("death_benefit_non_accident", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        totalAmount20)
                , 2));
        BigDecimal busAccidentalDeathAmount = totalAmount20.multiply(new BigDecimal("3"));
        map.put("busAccidentalDeathAmount", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(
                        busAccidentalDeathAmount)
                , 2));

        // 利益显示
        List<Map<String, Object>> interestListMap = new ArrayList<>();
        List<ProductCashValueBo> listCashValue = planPrintBo.getListCashValue();
        List<Map<String, Object>> individualizationDataList = planProductDetail.getIndividualizationDatas();
        int coveragePeriod20i = Integer.parseInt(coveragePeriod20);
        for (int i = 1; i <= coveragePeriod20i; i++) {
            Map<String, Object> interestMap = new HashMap<>();
            //保单年度
            interestMap.put("policyYear", i);
            interestMap.put("ageYear", insuredAgeYear + (i - 1));
            interestMap.put("totalAmount20", PrintCommon.getPrintString("$", PrintCommon.decimalFormat1.format(totalAmount20), 1));
            interestMap.put("maxTotalAmount20", PrintCommon.getPrintString("$", PrintCommon.decimalFormat1.format(death_benefit_due_to_accident), 1));
            BigDecimal policyYearTotalPremium = ProductCalculation.policyYearTotalPremium(individualizationDataList, i, new BigDecimal(0));
            interestMap.put("policyYearTotalPremium", PrintCommon.getPrintString("$", policyYearTotalPremium, 1));
            //现金价值
            int finalI = i;
            Optional<ProductCashValueBo> first = listCashValue.stream().filter(productCashValueBo -> finalI == productCashValueBo.getPolicyYear()).findFirst();
            if (first.isPresent()) {
                BigDecimal cashValue20 = ProductCalculation.getCashValue(listCashValue, "PRO880000000000020", i);
                interestMap.put("cashValue20", PrintCommon.getPrintString("$", cashValue20, 1));
                interestMap.put("cashValueSum", PrintCommon.getPrintString("$", cashValue20, 1));
            }
            interestListMap.add(interestMap);
        }
        map.put("interestListMap", interestListMap);

        // 保险利益
        map.put("totalAmount20", PrintCommon.getPrintString(PrintCommon.decimalFormat1.format(totalAmount20), 2));

        // 代理人信息
        //代理人姓名
        map.put("agentName", PrintCommon.getPrintString(planPrintBo.getAgentName(), 3));
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(planPrintBo.getAgentCode(), 3));
        //代理人手机号
        map.put("agentMobile", PrintCommon.getPrintString(planPrintBo.getAgentMobile(), 3));
        //制作日期
        PrintCommon.setPrintDateTime(map, "createdDate", planPrintBo.getCreatedDate(), 3);

        return map;
    }

    // 设置模板填充数据
    private Map<String, Object> getData() throws Exception {
        HashMap<String, Object> resultData = new HashMap<>();

        ApplyBo applyBo = getApplyBo();
        resultData.putAll(BeanUtils.describe(applyBo));
        // agent
        resultData.put("agentCode", "200017");
        resultData.put("agentName", "CHHUON NORYOU ENG");
        // resultData.put("agentName", "ធានារ៉ាប់រងបន្ថែមសម្រាប់ប្រាក់BaiZhong$Yingឧបត្ថម្ភសម្រាក白忠英នៅមន្ទីរពេទ្យដោយគ្រោះថ្នាក់ជាយថាហេតុ");
        resultData.put("agentMobil", "18334797813");
        // insuredCompany
        resultData.put("insuredCompanyName", "ស។白 Test Data");
        resultData.put("insuredTaxRegistrationNo", "ស។白 Test Data");
        resultData.put("insuredCompanyType", "ស។白 Test Data");
        resultData.put("insuredCompanyAddress", "ស។白 Test Data");
        resultData.put("insuredCompanyPhone", "ស។白 Test Data");
        resultData.put("insuredCompanyMobil", "ស។白 Test Data");
        resultData.put("insuredCompanyEmail", "ស។白 Test Data");
        // insuredCompanyContract
        resultData.put("insuredCompanyContractName", "ស។白 Test Data");
        resultData.put("insuredCompanyContractPosition", "ស។白 Test Data");
        resultData.put("insuredCompanyContractMobil", "ស។白 Test Data");
        resultData.put("insuredCompanyContractPhone", "ស។白 Test Data");
        resultData.put("insuredCompanyContractEmail", "ស។白 Test Data");
        // mainInsuredSum
        resultData.put("mainInsuredSum", 69);
        resultData.put("insuredSum", 7);
        resultData.put("totalSum", 169);
        // SALARY POSITION FIXED YEARS OTHER
        resultData.put("InsuranceAmountCalculation", "OTHER");
        resultData.put("otherRemark", "ស។白 Test Data");
        // photo
        resultData.put("testPhoto", "https://gclife-hk-dev.oss-cn-hongkong.aliyuncs.com/test41.jpg");

        PrintCommon.setPrintDateTime(resultData, "effectiveDate", 1571709625000L, 3);
        PrintCommon.setPrintDateTime(resultData, "maturityDate", 1603385999999L, 3);
        resultData.put("companyName", PrintCommon.getPrintString("ស។白 Test Data", 3));

        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        Map<String, Object> insuredMap1 = new HashMap<>();
        insuredMap1.put("insuredNo", 1);
        insuredMap1.put("name", "ស។白 Test Data");
        insuredMap1.put("birthdayZH_CN", 1);
        insuredMap1.put("sexName", 1);
        insuredMap1.put("ageYear", 1);
        insuredMap1.put("illnessOrAccidentAmount", 1);
        insuredMap1.put("onlyAccidentAmount", 1);
        insuredMap1.put("additionalPlan", 1);
        Map<String, Object> insuredMap2 = new HashMap<>();
        insuredMap2.put("insuredNo", 2);
        insuredMap2.put("name", "ស។白 Test Data");
        insuredMap2.put("birthdayZH_CN", 2);
        insuredMap2.put("sexName", 2);
        insuredMap2.put("ageYear", 2);
        insuredMap2.put("illnessOrAccidentAmount", 2);
        insuredMap2.put("onlyAccidentAmount", 2);
        insuredMap2.put("additionalPlan", 2);
        insuredMapList.add(insuredMap1);
        insuredMapList.add(insuredMap2);
        resultData.put("insuredMapList", insuredMapList);
        resultData.put("illnessOrAccidentTotalAmount", formatTheAmount(100000L));
        resultData.put("onlyAccidentTotalAmount", formatTheAmount(2000000000000000000L));
        resultData.put("totalAmount", formatTheAmount(300000L));
        resultData.put("illnessOrAccidentTotalPremium", formatTheAmount(400000L));
        resultData.put("onlyAccidentTotalPremium", formatTheAmount(500000L));
        resultData.put("additionalPlanTotalPremium", formatTheAmount(600000L));
        resultData.put("totalPremium", formatTheAmount(700000L));

        return resultData;
    }

    // 金额加逗号，每三位数加一个逗号
    private String formatTheAmount(Long amount) {
        return String.format("%,d", amount);
    }

    private ApplyBo getApplyBo() {
        ApplyBo applyBo = new ApplyBo();
        applyBo.setApplyId("8d7e355ddd974f5ab3e7bebbbfc1ce89");
        applyBo.setApplyNo("A1609235258823401");
        applyBo.setAcceptBranchId(null);
        applyBo.setSalesBranchId("GMA101101");
        applyBo.setManagerBranchId("GMM101004");
        applyBo.setChannelTypeCode("AGENT");
        applyBo.setApplyStatus("APPLY_STATUS_APPROVE_SUCCESS");
        applyBo.setCertifyId(null);
        applyBo.setCertifyClassId(null);
        applyBo.setSignedDate(null);
        applyBo.setApplyDate(1609235258809L);
        applyBo.setRepealDate(null);
        applyBo.setPremiumFrequency(null);
        applyBo.setPaymentMode("BANK_TRANSFER");
        applyBo.setInitialPaymentMode("BANK_TRANSFER");
        applyBo.setSurvivalPayType(null);
        applyBo.setApplySource("AGENT_INPUT");
        applyBo.setAutoPaymentOption(null);
        applyBo.setCurrencyCode("USD");
        applyBo.setReceivablePremium(new BigDecimal("346.00"));
        applyBo.setReceivableAddPremium(new BigDecimal("0.00"));
        applyBo.setFillInPremium(null);
        applyBo.setInureImmeInvest(null);
        applyBo.setOverflowToNext(null);
        applyBo.setSpecialTerm(null);
        applyBo.setApplicantSignStatus(null);
        applyBo.setInsuredSignStatus(null);
        applyBo.setAgentSignStatus(null);
        applyBo.setValidFlag("effective");
        applyBo.setCreatedUserId("20357f64211746b5b408333ec342a608");
        applyBo.setCreatedDate(1609235258954L);
        applyBo.setUpdatedUserId("5072ca66daf84f0594611bc771e627fc");
        applyBo.setUpdatedDate(1609386445667L);
        applyBo.setApplyType("LIFE_INSURANCE_GROUP");
        applyBo.setSignType(null);
        applyBo.setPolicyNo("P1609386373244425");
        applyBo.setEffectiveDate(1609385435000L);
        applyBo.setBizDate(null);
        applyBo.setProviderId("PRO8888888888888");
        applyBo.setVerifyNo(null);
        applyBo.setBackTrackDate(null);
        return applyBo;
    }
    */

}