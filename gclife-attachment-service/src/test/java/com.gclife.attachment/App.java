package com.gclife.attachment;

import java.io.File;
import java.io.FileOutputStream;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfContentByte;
import org.seuksa.itextkhmer.KhmerLigaturizer;

import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.PdfWriter;

/**
 * Hello Sample Test PDF with iText
 * Website: http://ask.osify.com
 * See library: https://github.com/seuksa/iTextKhmer
 *
 * <AUTHOR>
 */
/*
public class App {

    public static void getLength(String text, int fontSize, float startX, float startY, PdfContentByte canvas)throws Exception {

            BaseFont baseEnFont = BaseFont.createFont("Times New Roman.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font enFont = new Font(baseEnFont, fontSize, Font.NORMAL, BaseColor.BLACK);
            BaseFont baseKhFont = BaseFont.createFont("kh_battambang.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font khFont = new Font(baseKhFont, fontSize, Font.NORMAL, BaseColor.BLACK);
            BaseFont baseCnFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font cNFont = new Font(baseCnFont, fontSize, Font.NORMAL, BaseColor.BLACK);

            KhmerLigaturizer d = new KhmerLigaturizer();

            // for循环逐个打印
            for (int i = 0; i < text.length(); i++) {
                float x = 0.00f;
                String s = text.substring(i, i + 1);
                if (Character.UnicodeScript.of(text.charAt(i)) == Character.UnicodeScript.KHMER) {
                    ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(d.process(s), khFont), startX, startY, 0);
                    x = baseKhFont.getWidthPoint(d.process(s), fontSize);
                } else if (Character.UnicodeScript.of(text.charAt(i)) == Character.UnicodeScript.HAN) {
                    ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(s, cNFont), startX, startY, 0);
                    x = baseCnFont.getWidthPoint(s, fontSize);
                } else {
                    ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(s, enFont), startX, startY, 0);
                    x = baseEnFont.getWidthPoint(s, fontSize);
                }
                startX = startX + x;
            }

    }

    public static void main(String[] args) {
        System.out.println("Hello World!");
        Document document = new Document();
        String localFontPath = "kh_battambang.ttf";
        String text = "ហេតុនេះ​យើង​ត្រូវ​តែរួម​គ្នា​ដោះ​ស្រាយ ខ្លួន ស្វាគមន៍មកកាន់អូស៊ីហ្វាយabcd中国人的一条";
        int fontSize = 10;
        float startX = 200.00f;
        float startY = 300.00f;
//        getLength(text, fontSize, startX, startY);
        try {
            File file = new File("UnicodeExamplePDF-MT.pdf");
//        	if (!file.exists()) {
//        		file.createNewFile();
//        	}
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(file));
            document.open();

            PdfContentByte canvas = writer.getDirectContentUnder();

            ColumnText columnText = new ColumnText(writer.getDirectContent());
            columnText.setRunDirection(PdfWriter.RUN_DIRECTION_LTR);

//            KhmerLigaturizer d = new KhmerLigaturizer();
//        	//String strProcess = d.process("\u0936\u093e\u0902\u0924\u094d\u093f");
//            String strProcess = d.process("ហេតុនេះ​យើង​ត្រូវ​តែរួម​គ្នា​ដោះ​ស្រាយ ខ្លួន ស្វាគមន៍មកកាន់អូស៊ីហ្វាយ");
//
//            BaseFont bfComic = BaseFont.createFont(localFontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
//            //document.add(new Paragraph("Unicode: ការ​សិក្សា​ខាងបច្ចេក​វិទ្យា​នៅ​មាន​តម្រូវការខ្លាំង​លើ​ការ​ប្រើប្រាស់​ភាសា​ខ្មែរ", new Font(bfComic, 12)));
//            document.add(new Paragraph("Hello"));
//            document.add(new Paragraph(strProcess, new Font(bfComic, 22)));


            //columnText.addElement(new Paragraph("Unicode: ហេតុនេះ​យើង​ត្រូវ​តែរូម​គ្នា​ដោះ​ស្រាយ......", new Font(bfComic, 12)));
//            document.add(new Paragraph(d.process("\u1780\u17B6\u179A\u200B\u179F\u17B7\u1780\u17D2\u179F\u17B6\u200B\u1781\u17B6\u1784\u1794\u1785\u17D2\u1785\u17C1\u1780\u200B\u179C\u17B7\u1791\u17D2\u1799\u17B6\u200B\u1793\u17C5\u200B\u1798\u17B6\u1793\u200B\u178F\u1798\u17D2\u179A\u17BC\u179C\u1780\u17B6\u179A\u1781\u17D2\u179B\u17B6\u17C6\u1784\u200B\u179B\u17BE\u200B\u1780\u17B6\u179A\u200B\u1794\u17D2"), new Font(bfComic, 12)));

            getLength(text, fontSize, startX, startY, canvas);

        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
        document.close();
    }
}
*/
