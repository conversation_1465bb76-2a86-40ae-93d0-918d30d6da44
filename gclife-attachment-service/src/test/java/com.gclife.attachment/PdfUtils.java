package com.gclife.attachment;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.PrintObject;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.seuksa.itextkhmer.KhmerLigaturizer;

import java.io.*;
import java.util.*;

/*
public class PdfUtils {
    //PATH 支持语言路径
    public static String ZH_CN_FONT_PATH = File.separator + "fonts" + File.separator + "zh-cn" + File.separator + "hanyi-zhongdengxian.ttf";
    public static String KM_KH_FONT_PATH = File.separator + "fonts" + File.separator + "km-kh" + File.separator + "kh_battambang.ttf";
    public static String EN_US_FONT_PATH = File.separator + "fonts" + File.separator + "en-us" + File.separator + "Times New Roman.ttf";
    public static final KhmerLigaturizer khmerLigaturizer = new KhmerLigaturizer();


    public static void test() throws IOException, DocumentException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\POLICY_OFFSPRING_PROSPERITYS_PLAN_ZH_CN.pdf"; // pdf模板
        PdfReader reader = new PdfReader(fileName);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        */
/* 将要生成的目标PDF文件名称 *//*

        PdfStamper ps = new PdfStamper(reader, bos);
        PdfContentByte under = ps.getUnderContent(1);

        */
/* 使用中文字体 *//*

        BaseFont ZH_CN = BaseFont.createFont(PrintCommon.ZH_CN_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        BaseFont KM_KH = BaseFont.createFont(PrintCommon.KM_KH_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        BaseFont EN_US = BaseFont.createFont(PrintCommon.EN_US_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

        ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
        fontList.add(ZH_CN);
        fontList.add(EN_US);
        fontList.add(KM_KH);

        */
/* 取出报表模板中的所有字段 *//*

        AcroFields fields = ps.getAcroFields();
        fields.setSubstitutionFonts(fontList);
        fillData(fields, data());

        */
/* 必须要调用这个，否则文档不会生成的 *//*

        ps.setFormFlattening(true);
        ps.close();


        OutputStream fos = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\11111111111.pdf");
        fos.write(bos.toByteArray());
        fos.flush();
        fos.close();
        bos.close();
    }

    public static void fillData(AcroFields fields, Map<String, String> data)
            throws IOException, DocumentException {
        for (String key : data.keySet()) {
            String value = data.get(key);
            fields.setField(key, khmerLigaturizer.process(value)); // 为字段赋值,注意字段名称是区分大小写的
        }
    }

    public static Map<String, String> data() {
        List<PrintObject> printObjectList = new ArrayList<>();
        PrintCommon.setPrintData(printObjectList, new String[]{"productLevel4UP", "productLevel4AMOUNT_FIXED"}, "productLevel4UP");

        Map<String, String> data = new HashMap<String, String>();
        data.put("applicantName","白忠英");
        for (PrintObject printObject : printObjectList) {
            data.put(printObject.getKey(),printObject.getValue());
        }

        return data;
    }


    public static void main(String[] args) throws Exception {
        test();
        System.out.println("success");
    }

}
*/
