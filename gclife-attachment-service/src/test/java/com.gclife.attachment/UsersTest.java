package com.gclife.attachment;

import com.alibaba.fastjson.JSON;
//import com.gclife.attachment.core.jooq.tables.daos.PdfTemplateConfigDao;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.model.base.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-22
 * \* Time: 下午11:53
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class UsersTest {

    protected Users getCurrentLoginUsers() {
        Users users = new Users();
        users.setUserId("1");
        users.setUsername("user1");
        users.setLanguage("EN_US");
        users.setCreatedDate(111111111L);
        return users;
    }

    @Test
    public void getCurrentLoginUsersTest() {
        try {
            System.out.println(JSON.toJSONString(getCurrentLoginUsers()));
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}*/
