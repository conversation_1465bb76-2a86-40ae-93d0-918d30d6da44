package com.gclife.attachment;



import com.alibaba.fastjson.JSON;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.request.AttachmentBatchRequest;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.IoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.lang.System.err;
import static java.lang.System.out;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class OssServiceTest extends UsersTest{

    @Autowired
    OssConfigExtDao ossConfigExtDao;


    @Test
    public void loadMediaByteTest(){
        Users users  = this.getCurrentLoginUsers();
        System.out.println("=============================================下载指定缩略比例接口返回字节数组 start================================================");
        OssConfigBo ossConfigBo = ossConfigExtDao.loadOssConfigBo("OSS_MANAGER_ROLE");
        err.println(JSON.toJSONString(ossConfigBo));
        System.out.println("=============================================下载指定缩略比例接口返回字节数组 end================================================");

    }



}*/
