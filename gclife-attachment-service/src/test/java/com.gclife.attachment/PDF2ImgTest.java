package com.gclife.attachment;


import com.gclife.attachment.aliyun.OssOptionService;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/12/3
 */
// @Slf4j
// @RunWith(SpringRunner.class)
// @SpringBootTest
public class PDF2ImgTest {

    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private OssConfigExtDao ossConfigExtDao;
    @Autowired
    private OssOptionService ossOptionService;

    /*
    @Test
    public void PDF2ImgTest1() throws Exception {
        //Loading an existing PDF document
        File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_ZH_CN.pdf");
        // File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_EN_US.pdf");
        // File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_KM_KH.pdf");
        PDDocument document = PDDocument.load(file);
        //Instantiating the PDFRenderer class
        PDFRenderer renderer = new PDFRenderer(document);
        //Rendering an image from the PDF document
        BufferedImage image = renderer.renderImage(0);
        //Writing the image to a file
        ImageIO.write(image, "JPEG", new File("C:\\Users\\<USER>\\Desktop\\Documents\\img\\POLICY_TERMS_ZH_CN.jpg"));
        //Closing the document
        document.close();
    }

    @Test
    public void PDF2ImgTest2() throws Exception {
        //Loading an existing PDF document
        File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_ZH_CN.pdf");
        // File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_EN_US.pdf");
        // File file = new File("C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\POLICY_TERMS_KM_KH.pdf");

        PDDocument doc = PDDocument.load(file);
        PDFRenderer renderer = new PDFRenderer(doc);
        int pageCount = doc.getNumberOfPages();
        for (int i = 0; i < pageCount; i++) {
            BufferedImage image = renderer.renderImageWithDPI(i, 296);
            // BufferedImage image = renderer.renderImage(i, 2.5f);
            ImageIO.write(image, "PNG", new File("C:\\Users\\<USER>\\Desktop\\Documents\\img\\POLICY_TERMS_ZH_CN.jpg"));
        }

    }

    @Test
    public void PDF2ImgTest3() throws Exception {
        List<AttachmentResponse> attachmentResponseList = new ArrayList<>();
        try (PDDocument document = PDDocument.load(attachmentBusinessService.loadOssObjectByAttachmentId("90ffe4905fdf4622a5c8bed7cb322c28"))) {
            PDFRenderer renderer = new PDFRenderer(document);
            for (int i = 0; i < document.getNumberOfPages(); ++i) {
                BufferedImage bufferedImage = renderer.renderImageWithDPI(i, 100);
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "PNG", out);
                AttachmentRequest attachmentRequest = new AttachmentRequest();
                attachmentRequest.setFileContent(new BASE64Encoder().encode(out.toByteArray()));
                attachmentRequest.setFileSuffix("jpg");
                AttachmentResponse attachmentResponse = attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.IMAGE.name(), attachmentRequest);
                attachmentResponseList.add(attachmentResponse);
            }
        }
        log.info("attachmentResponseList:{}", attachmentResponseList);
    }

    @Test
    public void PDF2ImgTest4() throws Exception {
        String pdfDirectory = "C:\\Users\\<USER>\\Desktop\\Documents\\PDF\\";
        String imgDirectory = "C:\\Users\\<USER>\\Desktop\\Documents\\img\\";

        // String pdfFileName = pdfDirectory + "POLICY_TERMS_ZH_CN.pdf";
        // String pdfFileName = pdfDirectory + "POLICY_TERMS_EN_US.pdf";
        // String pdfFileName = pdfDirectory + "POLICY_TERMS_KM_KH.pdf";
        String pdfFileName = pdfDirectory + "03d4007976c84b5a9f2cf89b11b6e17a.pdf";

        // String imgFileName = imgDirectory + "POLICY_TERMS_ZH_CN.jpg";
        // String imgFileName = imgDirectory + "POLICY_TERMS_EN_US.jpg";
        // String imgFileName = imgDirectory + "POLICY_TERMS_KM_KH.jpg";
        String imgFileName = imgDirectory + "03d4007976c84b5a9f2cf89b11b6e17a.jpg";


        byte[] pdfBytes = attachmentBusinessService.loadOssObjectByAttachmentId("90ffe4905fdf4622a5c8bed7cb322c28");

        byte[] bytes = TRANSFORM_UTIL.pdfToSingleImage(pdfBytes);
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(bytes));
        attachmentRequest.setFileSuffix("jpg");
        AttachmentResponse attachmentResponse = attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.IMAGE.name(), attachmentRequest);

        log.info("attachmentResponse:{}", JSONObject.toJSONString(attachmentResponse));
    }
    */

    /*
    @Test
    public void PDF2ImgTest66() throws Exception {
        System.out.println("测试======");
        String filePath = "C:\\Users\\<USER>\\Desktop\\Documents\\webm\\579dd73508b049368d5cb6d17ebce739.webm";
        File file = new File(filePath);
        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file);
        String fileName = null;
        Frame captured_frame = null;
        FFmpegFrameRecorder recorder = null;
        try {
            frameGrabber.start();
            fileName = file.getAbsolutePath() + "__.mp4";
            recorder = new FFmpegFrameRecorder(fileName, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setSampleRate(frameGrabber.getSampleRate());
            recorder.setFrameRate(frameGrabber.getFrameRate());
            recorder.setVideoBitrate(10 * 1024 * 1024);
            recorder.start();
            while ((captured_frame = frameGrabber.grabFrame()) != null) {
                try {
                    recorder.setTimestamp(frameGrabber.getTimestamp());
                    recorder.record(captured_frame);
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
            recorder.stop();
            recorder.release();
            frameGrabber.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void PDF2ImgTest6() {
        System.out.println("测试======");
        String filePath = "C:\\Users\\<USER>\\Desktop\\Documents\\webm\\579dd73508b049368d5cb6d17ebce739.webm";
        File file = new File(filePath);
        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        Frame captured_frame = null;
        FFmpegFrameRecorder recorder = null;
        try {
            frameGrabber.start();
            recorder = new FFmpegFrameRecorder(byteArrayOutputStream, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("avi");
            recorder.setSampleRate(frameGrabber.getSampleRate());
            recorder.setFrameRate(frameGrabber.getFrameRate());
            recorder.setVideoBitrate(10 * 1024 * 1024);
            recorder.start();
            boolean bool = false;
            while ((captured_frame = frameGrabber.grabFrame()) != null) {
                try {
                    System.out.println("时长2===" + captured_frame.timestamp);
                    recorder.setTimestamp(frameGrabber.getTimestamp());
                    recorder.record(captured_frame);
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
            recorder.stop();
            recorder.release();
            frameGrabber.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
        byte[] bytes = byteArrayOutputStream.toByteArray();
        System.out.println("bytes====: " + Arrays.toString(bytes));


        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(byteArrayOutputStream.toByteArray()));
        attachmentRequest.setFileSuffix("jpg");
        AttachmentResponse attachmentResponse = attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.VIDEO.name(), attachmentRequest);
        log.info("attachmentResponse: {}", attachmentResponse);
    }

    @Test
    public void PDF2ImgTest7() {
        System.out.println("测试======");
        String filePath = "/C:\\Users\\<USER>\\Desktop\\Documents\\webm\\579dd73508b049368d5cb6d17ebce739.webm";
        File file=new File(filePath);
        FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file);
        String fileName = null;
        Frame captured_frame = null;
        FFmpegFrameRecorder recorder = null;
        try {
            frameGrabber.start();
            fileName = file.getAbsolutePath() + "__.mp4";
            recorder = new FFmpegFrameRecorder(fileName, frameGrabber.getImageWidth(), frameGrabber.getImageHeight(), frameGrabber.getAudioChannels());
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setSampleRate(frameGrabber.getSampleRate());
            recorder.setFrameRate(frameGrabber.getFrameRate());
            recorder.setVideoBitrate(10 * 1024 * 1024);
            recorder.start();
            boolean bool=false;
            while ((captured_frame = frameGrabber.grabFrame()) != null) {
                try {
                    System.out.println("时长2==="+captured_frame.timestamp);
                    recorder.setTimestamp(frameGrabber.getTimestamp());
                    recorder.record(captured_frame);
                } catch (Exception e) {
                    System.out.println(e);
                }
            }
            recorder.stop();
            recorder.release();
            frameGrabber.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void PDF2ImgTest8() {
        String tmpdir = System.getProperty("java.io.tmpdir");
        System.out.println(tmpdir);
    }
    */

}
