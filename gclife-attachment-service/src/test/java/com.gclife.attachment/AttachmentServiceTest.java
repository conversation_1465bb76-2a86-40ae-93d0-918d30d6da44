package com.gclife.attachment;



import com.alibaba.fastjson.JSON;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.request.AttachmentBatchRequest;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.request.QRCodeRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.IoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.lang.System.err;
import static java.lang.System.out;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class AttachmentServiceTest  extends UsersTest{

    @Autowired
    AttachmentBusinessService attachmentBusinessService;


    @Test
    public void uploadMediaBase64Test(){

        Users users  = this.getCurrentLoginUsers();

        System.out.println("=============================================base64文件上传接口测试 start================================================");
        Map<String, String> fileContentMap = IoUtils.getFileContent("/home/<USER>/Desktop/Download/media/333.png");
       // System.err.println("file_content : "+fileContentMap.get("file_content"));
        fileContentMap.put("file_content","iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAIAAACKIl8oAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABgUlEQVRIic1WMa6CQBCd/WzA2GOrVCQ2VJzBGk9AYmPhSYweQk4AlXewsjb0FPTQOb/YH0Jmd9n5Jn7/q2CYebx5u7MgEBHeg6838bqpd7udlDJJEgDYbDa/ohY2Q4QQthqmh2bqCV7+CwyGVFXFEeWEQTVHssK08E/skO122/d9HMfjICLy58BsiLOek/OHhgghTqeTs6xtW+dq0744nTIzqeo0TT3Pc/Le73f3y1FDFEXG+ADf96cTFKyDPtEs0zTzDkFE2yrNZrMsy5y8PywAUJbl9XoFgKZphrlIkkRvExhWDJlA70eYz+fDo6IoiCYHddd1JKQOfnW93+91uiAI8jx/PB4qqKw3UKvUAYgYhuG48fV6PeY9Ho9E8vP5NDZhMERdXC4XKSUJosVrc3C5XA4PyIYFgPP5PK60WXy73QzUOFo6mxzO0q1WK1p7OBxIqK5rws7ZEnqCAO07pA/baxHWeY2MsdYh0TTTnC8vyVksFjThNUUcfO6f759SfwMXDbpcfrByZgAAAABJRU5ErkJggg==");
        fileContentMap.put("file_suffix","png");
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(fileContentMap.get("file_content"));
        attachmentRequest.setFileName(fileContentMap.get("file_name"));
        attachmentRequest.setFileSuffix(fileContentMap.get("file_suffix"));
        //attachmentRequest.setFileSuffix("doc");
        ResultObject resultObject = attachmentBusinessService.uploadMediaBase64(users,AttachmentTermEnum.MEDIA_TYPE.IMAGE.name(), attachmentRequest);
        err.println(JSON.toJSONString(resultObject));
        System.out.println("=============================================base64文件上传接口测试 end================================================");

    }



    @Test
    public void uploadMediaBatchBase64Test(){

        List<AttachmentRequest>  attachmentRequests = new ArrayList<>();

        Users users  = this.getCurrentLoginUsers();

        System.out.println("=============================================base64文件批量上传接口测试 start================================================");
        out.println("-----------------------------------------标准产品数据处理接口 （save） start---------------------d------------------");
        Map<String, String> fileContentMap = IoUtils.getFileContent("/home/<USER>/Desktop/Download/media/444.doc");
       // fileContentMap.put("file_content","iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAIAAACKIl8oAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABgUlEQVRIic1WMa6CQBCd/WzA2GOrVCQ2VJzBGk9AYmPhSYweQk4AlXewsjb0FPTQOb/YH0Jmd9n5Jn7/q2CYebx5u7MgEBHeg6838bqpd7udlDJJEgDYbDa/ohY2Q4QQthqmh2bqCV7+CwyGVFXFEeWEQTVHssK08E/skO122/d9HMfjICLy58BsiLOek/OHhgghTqeTs6xtW+dq0744nTIzqeo0TT3Pc/Le73f3y1FDFEXG+ADf96cTFKyDPtEs0zTzDkFE2yrNZrMsy5y8PywAUJbl9XoFgKZphrlIkkRvExhWDJlA70eYz+fDo6IoiCYHddd1JKQOfnW93+91uiAI8jx/PB4qqKw3UKvUAYgYhuG48fV6PeY9Ho9E8vP5NDZhMERdXC4XKSUJosVrc3C5XA4PyIYFgPP5PK60WXy73QzUOFo6mxzO0q1WK1p7OBxIqK5rws7ZEnqCAO07pA/baxHWeY2MsdYh0TTTnC8vyVksFjThNUUcfO6f759SfwMXDbpcfrByZgAAAABJRU5ErkJggg==");
        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(fileContentMap.get("file_content"));
        attachmentRequest.setFileName(fileContentMap.get("file_name"));
        attachmentRequest.setFileSuffix(fileContentMap.get("file_suffix"));
        attachmentRequests.add(attachmentRequest);
        String sql =JSON.toJSONString(attachmentRequests);
        System.err.println(sql);
        AttachmentBatchRequest attachmentBatchRequest = new AttachmentBatchRequest();
        attachmentBatchRequest.setListAttachment(sql);
        ResultObject resultObject = attachmentBusinessService.uploadMediaBatchBase64(users,AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentBatchRequest);
        err.println(JSON.toJSONString(resultObject));
        System.out.println("=============================================base64文件批量上传接口测试 end================================================");

    }



    @Test
    public void loadMediaProportionTest(){
        try {

        Users users  = this.getCurrentLoginUsers();
        System.out.println("=============================================下载指定缩略比例接口返回url地址 start================================================");
        ResultObject resultObject = attachmentBusinessService.loadMediaProportion(users,1, "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7");
        err.println(JSON.toJSONString(resultObject));
        System.out.println("=============================================下载指定缩略比例接口返回url地址 end================================================");

        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void loadMediaByteTest(){
        try {
            Users users = this.getCurrentLoginUsers();
            System.out.println("=============================================下载指定缩略比例接口返回字节数组 start================================================");
            ResultObject resultObject = attachmentBusinessService.loadMediaByte(users, "ATTACHMENT_b68209af-fda5-48ae-a786-1df15222c5ad");
            err.println(JSON.toJSONString(resultObject));
            System.out.println("=============================================下载指定缩略比例接口返回字节数组 end================================================");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void electronicPolicyGeneratorTest() {
        try {

        Users users = this.getCurrentLoginUsers();

        System.out.println(getClass().getClassLoader().getResource("kh_battambang.ttf").getPath());

        ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();
        electronicPolicyGeneratorRequest.setPdfType("POLICY");
        electronicPolicyGeneratorRequest.setProductId("PRO88000000000002");
        electronicPolicyGeneratorRequest.setContent("{\"policyNo\": \"123456789\",\"verifyNo\": \"8M87872\",\"applicantName\": \"អ្នកបង់ធានារ៉ាប់រង\",\"insuredName\": \"李六\",\"idNo\": \"9087MQ\",\"agentCode\": \"A345KY\"}");

        attachmentBusinessService.electronicPolicyGenerator(users, electronicPolicyGeneratorRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void electronicPolicyDownloadTest() {
//        Users users = this.getCurrentLoginUsers();
//        try {
//            String attachmentId = "ATTACHMENT_0427bd0e-9960-410b-82d6-4b0fb91e6b8";
//            ResultObject<AttachmentByteResponse> resultObject = attachmentBusinessService.electronicPolicyDownload(attachmentId);
//            System.out.println(JSON.toJSONString(resultObject));
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Test
    public void policyGeneratorPRO88000000000001Test() {
        try {

            Users users = this.getCurrentLoginUsers();

            System.out.println(getClass().getClassLoader().getResource("kh_battambang.ttf").getPath());

            ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();
            electronicPolicyGeneratorRequest.setPdfType("POLICY");
            electronicPolicyGeneratorRequest.setProductId("PRO88000000000001");
            electronicPolicyGeneratorRequest.setContent("{\"acceptBranchId\":\"GT\",\"applicantId\":\"eb26614ee99b4d0da7406aadaae44f59\",\"applyDate\":*************,\"applyId\":\"fbca2c9df90f487baab0653982c67ff6\",\"applyNo\":\"GCLIFE-0002-2018-02-01-00001\",\"applySource\":\"ACCEPT_INPUT\",\"approveDate\":*************,\"channelTypeCode\":\"AGENT\",\"createdDate\":*************,\"effectiveDate\":*************,\"hesitation\":0,\"hesitationEndDate\":*************,\"listPolicyAccount\":[],\"listPolicyAttachment\":[{\"attachmentId\":\"ATTACHMENT_4e7fb26a-6749-4109-af2a-a732bda0ed24\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE\",\"createdDate\":*************,\"policyAttachmentId\":\"5c87c82bee8a46e9a0bce02a0bbe6b42\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_46dc2f26-58c3-4774-9d68-9b3345ca526a\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_BOOK\",\"createdDate\":*************,\"policyAttachmentId\":\"e84fca6db7eb4badad7bab2bf5d47c20\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_67292023-cbd1-4db5-bbe8-257f18aca750\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE\",\"createdDate\":*************,\"policyAttachmentId\":\"22d18af0b38f4efcb4f7f9d6c23c09f5\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_17bf7417-8a41-4937-8497-aeb161c2e309\",\"attachmentSeq\":1,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_OTHER\",\"createdDate\":1517476278858,\"policyAttachmentId\":\"4100b0305650431591ed96b7768b0f3b\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_40a3febb-b7b3-4a70-9318-f323fd761b75\",\"attachmentSeq\":2,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_BOOK\",\"createdDate\":1517476278858,\"policyAttachmentId\":\"7723cb6c8a184679a52d7d51a5c32295\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_b3167bd7-79fa-4eaa-909f-5e4dfbc1f16e\",\"attachmentSeq\":2,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE\",\"createdDate\":1517476278859,\"policyAttachmentId\":\"687e109d35874847ad79821b42f8b273\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"},{\"attachmentId\":\"ATTACHMENT_ea6c6439-f351-4461-8755-b043b91490e5\",\"attachmentSeq\":3,\"attachmentTypeCode\":\"CERTIFY_ATTACHMENT_APPLY_BOOK\",\"createdDate\":1517476278860,\"policyAttachmentId\":\"7ed1f1cc68b644a68b4b5cb39c28c7df\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"validFlag\":\"effective\"}],\"listPolicyInsured\":[{\"avoirdupois\":\"20\",\"birthday\":1262304000000,\"createdDate\":1517476278898,\"customerId\":\"CUSTOMER_b40e6817-4d37-4ec6-876c-e29b998ba784\",\"homeAddress\":\"123号\",\"homeAreaCode\":\"110101\",\"idNo\":\"9112356014\",\"idType\":\"ID\",\"insuredId\":\"7e130432ed634979bc9b5b570b52c9c8\",\"listPolicyBeneficiary\":[{\"beneficiaryId\":\"f11ebaf24cf24836aa955049b5358ca1\",\"beneficiaryProportion\":100.00,\"createdDate\":1517476278976,\"insuredId\":\"7e130432ed634979bc9b5b570b52c9c8\",\"policyBeneficiary\":{\"beneficiaryId\":\"f11ebaf24cf24836aa955049b5358ca1\",\"birthday\":567993600000,\"createdDate\":1517476278951,\"customerId\":\"CUSTOMER_7715336e-9bdd-44ea-bf12-1feb9cce986b\",\"idNo\":\"8112356014\",\"idType\":\"ID\",\"name\":\"受益已\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"sex\":\"FEMALE\",\"validFlag\":\"effective\"},\"policyBeneficiaryId\":\"0da0c24ce08d4d2e8faf358604690729\"}],\"listPolicyCoverage\":[{\"baseSumAmount\":\"0.00\",\"coverageId\":\"35137a70aeb74e3daacdc094fee8e3c7\",\"coveragePeriod\":\"22\",\"coveragePeriodUnit\":\"AGE\",\"createdDate\":1517476278985,\"effectiveDate\":*************,\"insuredId\":\"7e130432ed634979bc9b5b570b52c9c8\",\"mult\":\"1\",\"policyCoveragePremium\":{\"coverageId\":\"35137a70aeb74e3daacdc094fee8e3c7\",\"createdDate\":1517476279001,\"frequency\":1,\"payStatus\":\"PAYMENT_FINISHED\",\"payToDate\":1612084277168,\"periodTotalPremium\":1874.35,\"policyCoveragePremiumId\":\"ab3ffeadf8f54aa98bff73f0b0f2d3cc\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"premiumFrequency\":\"YEAR\",\"premiumPeriod\":\"3\",\"premiumPeriodType\":\"YEAR\"},\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"policyNo\":\"6df3982f-f44c-49ab-90dc-2037f7052e61\",\"primaryFlag\":\"MAIN\",\"productCode\":\"DZHRS_ZNCCBX\",\"productId\":\"PRO88000000000001\",\"productName\":\"Offspring Prosperity Insurance\",\"relationship\":\"FATHER_AND_SON\"}],\"marriage\":\"UNMARRIED\",\"name\":\"អ្នកបង់ធានារ៉ាប់រង\",\"nationality\":\"CHINA\",\"occupationCode\":\"GCL-000210000100003\",\"occupationType\":\"2\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"relationship\":\"FATHER_AND_SON\",\"relationshipName\":\"Father & Son\",\"sex\":\"MALE\",\"stature\":\"100\",\"validFlag\":\"effective\"}],\"managerBranchId\":\"GTA103\",\"policyAgent\":{\"agentCode\":\"20174\",\"agentId\":\"3139249499654fb5899a3ad7eaa992ad\",\"createdDate\":1517476278711,\"policyAgentId\":\"bcbd7c4a56a743ffb1ede90cab1f5d82\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"policyNo\":\"6df3982f-f44c-49ab-90dc-2037f7052e61\"},\"policyApplicant\":{\"applicantId\":\"eb26614ee99b4d0da7406aadaae44f59\",\"avoirdupois\":\"60\",\"birthday\":599616000000,\"createdDate\":1517476278759,\"customerId\":\"CUSTOMER_f0a61081-d0ea-43c4-954e-e220b1841ffb\",\"email\":\"<EMAIL>\",\"homeAddress\":\"123号\",\"homeAreaCode\":\"120102\",\"idNo\":\"1112356014\",\"idType\":\"ID\",\"income\":\"50000\",\"marriage\":\"MARRIED\",\"mobile\":\"15500020001\",\"name\":\"龙少爷一\",\"nationality\":\"CAMBODIA\",\"occupationCode\":\"GCL-000010000100001\",\"occupationType\":\"1\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"sex\":\"MALE\",\"sexName\":\"Male\",\"stature\":\"180\",\"updatedDate\":1517475504710,\"validFlag\":\"effective\"},\"policyContactInfo\":{\"contractAddress\":\"\",\"createdDate\":1517476278824,\"policyContactId\":\"098e47f9b9bc49ea9d66783f405cba21\",\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\"},\"policyId\":\"f01c04b57d3e45459eb406e451a55ee3\",\"policyNo\":\"6df3982f-f44c-49ab-90dc-2037f7052e61\",\"policyStatus\":\"SAFEGUARDING\",\"salesBranchId\":\"GTA103101101\",\"updatedDate\":1517476278783,\"verifyNo\":\"2.399207168334326E7\"}");

             attachmentBusinessService.electronicPolicyGenerator(users, electronicPolicyGeneratorRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void generateQrcode() {
        try {

            QRCodeRequest qrCodeRequest = new QRCodeRequest();
            qrCodeRequest.setSceneStr("aaa");
            qrCodeRequest.setExpiresIn(1256000L);
            ResultObject<AttachmentResponse> resultObject = attachmentBusinessService.generateQrcode(qrCodeRequest);
            System.out.println(JSON.toJSONString(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
*/
