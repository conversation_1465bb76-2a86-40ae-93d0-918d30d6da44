package com.gclife.attachment;



import com.alibaba.fastjson.JSON;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.request.AttachmentBatchRequest;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.business.ImageZoomBusinessService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.IoUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.lang.System.err;
import static java.lang.System.out;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class ImageZoomServiceTest extends UsersTest{

    @Autowired
    ImageZoomBusinessService imageZoomBusinessService;

    @Test
    public void loadMediaProportionTest(){

        Users users  = this.getCurrentLoginUsers();
        System.out.println("=============================================下载指定缩略比例接口返回url地址 start================================================");
        ResultObject resultObject = imageZoomBusinessService.loadMediaRouteFixedFrame(users, "ATTACHMENT_fa578eb3-9a2e-4547-b951-006d7cb77ed7",100,100);
        err.println(JSON.toJSONString(resultObject));
        System.out.println("=============================================下载指定缩略比例接口返回url地址 end================================================");

    }


}*/
