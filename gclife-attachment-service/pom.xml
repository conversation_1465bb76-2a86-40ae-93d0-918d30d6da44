<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-business-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-attachment-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>附件模块</description>

    <properties>
        <server.port>20900</server.port>
        <itext.pdf.version>********</itext.pdf.version>
        <javacv.version>1.5.6</javacv.version>
        <classifier.version>linux-x86_64</classifier.version>
        <!-- <classifier.version>windows-x86_64</classifier.version> -->
    </properties>

    <developers>
        <developer>
            <name>caoqinghua</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Sentinel Datasource Nacos -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- 负载均衡-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    &lt;!&ndash; 跟pdf生成有冲突了&ndash;&gt;-->
<!--                    <groupId>org.bouncycastle</groupId>-->
<!--                    <artifactId>bcpkix-jdk15on</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-enum-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-payment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-policy</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-agent</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-renewal</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-endorse</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-platform</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-attachment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-attachment-tool</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-attachment-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jooq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.bouncycastle</groupId>-->
<!--            <artifactId>bcpkix-jdk15on</artifactId>-->
<!--            <version>1.55</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
<!--            <version>5.5.9</version>-->
            <version>${itext.pdf.version}</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-pdfa</artifactId>
            <version>${itext.pdf.version}</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-xtra</artifactId>
            <version>${itext.pdf.version}</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>${itext.pdf.version}</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
            <version>7.1.3</version>
        </dependency>

        <dependency>
            <groupId>org.seuksa.itextkhmer</groupId>
            <artifactId>iTextKhmer</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!--rtf to pdf -->
        <dependency>
            <groupId>com.documents4j</groupId>
            <artifactId>documents4j-api</artifactId>
            <version>0.2.1</version>
        </dependency>

        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>aolj</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>collections</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>dvt-jclient</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>dvt-utils</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 该jar导致项目启动不了 升级一下版本-->
<!--        <dependency>-->
<!--            <groupId>com.oracle</groupId>-->
<!--            <artifactId>groovy-all-1.6.3</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>3.0.9</version>
        </dependency>


        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>i18nAPI_v3</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>jewt-core-jewt4</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>jewt4</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>mail</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>********.0-atlassian-hosted</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc7</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdl</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>orai18n</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>orai18n-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>orai18n-collation-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>orai18n-mapping</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>orai18n-mapping-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>osdt_cert</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>osdt_cms</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>osdt_core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>osdt_smime</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>share</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>xdocore</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>xdoparser</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>xdoparser11g</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>xdoparser12c</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- 暂时注释-->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>xmlparserv2</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- pdf转图片 -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>
        <!--
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.6</version>
        </dependency>
        -->
        <!-- webm转mp4 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
            <version>${javacv.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacpp</artifactId>
            <version>${javacv.version}</version>
        </dependency>
        <!-- classifier指定操作系统环境 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>opencv</artifactId>
            <version>4.5.3-${javacv.version}</version>
            <classifier>${classifier.version}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>openblas</artifactId>
            <version>0.3.17-${javacv.version}</version>
            <classifier>${classifier.version}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>4.4-${javacv.version}</version>
            <classifier>${classifier.version}</classifier>
        </dependency>


        <!-- 消息队列 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.gclife</groupId>
                                    <artifactId>gclife-attachment-core</artifactId>
                                    <version>0.0.1-SNAPSHOT</version>
                                    <type>jar</type>
                                    <overWrite>true</overWrite>
                                    <outputDirectory>./target/classes</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <service.port>${server.port}</service.port>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>