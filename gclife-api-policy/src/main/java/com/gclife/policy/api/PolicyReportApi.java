package com.gclife.policy.api;

import com.gclife.common.model.ResultObject;

import com.gclife.policy.model.response.*;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "gclife-policy-service")
public interface PolicyReportApi {
    /**
     * 查询业务报表（投保人资料）
     *
     * @return
     */
    @GetMapping(value = "v1/base/policy/policyApplicants")
    ResultObject<List<PolicyApplicantReportResponse>> listPolicyApplicantRecord(@RequestParam(value = "currentPage") Integer currentPage,
                                                                                @RequestParam(value = "pageSize") Integer pageSize,
                                                                                @RequestParam(value = "startDate") String startDate);

    /**
     * 查询业务报表（被保人资料）
     *
     * @return
     */
    @GetMapping(value = "v1/base/policy/policyInsureds")
    ResultObject<List<PolicyInsuredReportResponse>> listPolicyInsuredsRecord(@RequestParam(value = "currentPage") Integer currentPage,
                                                                             @RequestParam(value = "pageSize") Integer pageSize,
                                                                             @RequestParam(value = "startDate") String startDate);

    /**
     * 查询承保清单
     */
    @GetMapping(value = "v1/base/policy/policyReport")
    ResultObject<List<PolicyReportUnderwritingResponse>> listPolicyReportUnderwriting(@RequestParam(value = "currentPage") Integer currentPage,
                                                                                      @RequestParam(value = "pageSize") Integer pageSize,
                                                                                      @RequestParam(value = "startDate") String startDate,
                                                                                      @RequestParam(value = "reportType") String reportType);

    /**
     * 查询团险承保清单
     */
    @GetMapping(value = "v1/base/policy/group/policyReport")
    ResultObject<List<PolicyGroupReportUnderwritingResponse>> listPolicyGroupReportUnderwriting(@RequestParam(value = "currentPage") Integer currentPage,
                                                                                                @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "startDate") String startDate);
    /**
     * 查询学校发展基金报表
     */
    @GetMapping(value = "v1/base/policy/group/sdf/report")
    ResultObject<List<GroupSdfReportResponse>> listPolicyGroupSdfReport(@RequestParam(value = "currentPage") Integer currentPage,
                                                                        @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "startDate") String startDate);

    /**
     * 实收业绩明细报表
     */
    @PostMapping(value = "v1/base/policy/actual/performance")
    ResultObject<List<ActualPerformanceReportBo>> queryActualPerformance(@RequestBody List<String> policyIdList);

    /**
     * 实收历史版业绩明细表统计
     */
    @PostMapping(value = "v1/base/policy/policy/version/actual/performance")
    ResultObject<List<ActualPerformanceReportBo>> queryPolicyVersionActualPerformance(@RequestBody List<ActualPerformanceReportBo> actualPerformanceReportBoList);


    /**
     * 季度准备金提取报表统计
     *
     * @param quarterDate 统计月份
     * @return
     */
    @GetMapping(value = "v1/report/quarterly/statistics/reserve/withdrawal/report")
    ResultObject quarterlyStatisticsReserveWithdrawalReport(@RequestParam(value = "quarterDate",required = false) String quarterDate);


    @ApiOperation(value = "执行同步保单银保渠道手续费费用明细表", notes = "执行同步保单银保渠道手续费费用明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/policy/sync/policy/service/charge/bank/channel")
    ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannel(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate);

    @ApiOperation(value = "执行同步保单银保渠道手续费费用明细表-支付明细", notes = "执行同步保单银保渠道手续费费用明细表-支付明细")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/policy/sync/policy/service/charge/bank/channel/payment")
    ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannelPayment(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) ;



    @ApiOperation(value = "执行同步销售报表数据", notes = "执行同步销售报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/policy/sync/sale/apply/policy")
    ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) ;

    /**
     * 系统预警查询已承保的保单列表
     */
    @GetMapping(value = "v1/policy/system/warning/query/policies")
    ResultObject<List<PolicyQueryListBo>> querySystemWarningPolicyList(@RequestParam(value = "applyId") String applyId);
}
