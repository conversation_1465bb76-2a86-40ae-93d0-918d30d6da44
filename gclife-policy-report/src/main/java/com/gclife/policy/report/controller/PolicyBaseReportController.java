package com.gclife.policy.report.controller;

import com.gclife.common.configuration.system.permissions.AutoSaveURL;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.policy.report.model.response.GroupSdfReportResponse;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.policy.report.model.response.PolicyInsuredReportResponse;
import com.gclife.policy.report.service.PolicyReportBusinessService;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-11-8
 * description:
 */
@Api(tags = "保单报表基础接口", description = "保单报表基础接口")
@RefreshScope
@RestController
@RequestMapping("v1/base/policy/")
public class PolicyBaseReportController extends BaseController {
    @Autowired
    private PolicyReportBusinessService policyReportBusinessService;

    @ApiOperation(value = "获取保单报表数据", notes = "获取保单报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "report")
    @AutoSaveURL()
    public ResultObject syncReportPolicy() {
        return null;
    }

    @ApiOperation(value = "查询保单信息跟保人信息--收付费明细报表", notes = "查询保单信息跟保人信息--收付费明细报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "policys")
    public ResultObject listPolicyRecord(@RequestBody List<String> businessNo) {
        return policyReportBusinessService.queryListPolicyReportBo(businessNo);
    }

    @ApiOperation(value = "查询业务报表（投保人资料）", notes = "查询业务报表（投保人资料）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyApplicants")
    public ResultObject listPolicyApplicantRecord(Integer currentPage, Integer pageSize, String startDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.queryListPolicyApplicantReportBo(basePageRequest, startDate);
    }

    @ApiOperation(value = "查询被保人资料", notes = "查询被保人资料")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyInsureds")
    public ResultObject<List<PolicyInsuredReportResponse>> listPolicyInsuredsRecord(Integer currentPage, Integer pageSize, String startDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.queryListPolicyInsuredsReportBo(basePageRequest, startDate);
    }


    @ApiOperation(value = "查询承保清单", notes = "查询承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyReport")
    public ResultObject listPolicyReportUnderwriting(Integer currentPage, Integer pageSize, String startDate, String reportType) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.queryListPolicyReportUnderwritingBo(basePageRequest, startDate, reportType);
    }

    @ApiOperation(value = "查询团险承保清单", notes = "查询团险承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "group/policyReport")
    public ResultObject listPolicyGroupReportUnderwriting(Integer currentPage, Integer pageSize, String startDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.queryListGroupPolicyReportUnderwritingBo(basePageRequest, startDate);
    }

    /**
     * 实收业绩明细报表
     */
    @ApiOperation(value = "实收业绩明细表统计", notes = "实收业绩明细表统计")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "actual/performance")
    ResultObject<List<ActualPerformanceReportBo>> queryActualPerformance(@RequestBody List<String> policyIdList){
        return policyReportBusinessService.queryActualPerformance(policyIdList);
    }

    /**
     * 实收业绩明细报表
     */
    @ApiOperation(value = "实收历史版业绩明细表统计", notes = "实收历史版业绩明细表统计")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "policy/version/actual/performance")
    ResultObject<List<ActualPerformanceReportBo>> queryPolicyVersionActualPerformance(@RequestBody List<ActualPerformanceReportBo> actualPerformanceReportBoList){
        return policyReportBusinessService.queryPolicyVersionActualPerformance(actualPerformanceReportBoList);
    }


    @ApiOperation(value = "执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表", notes = "执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/policy/service/charge/bank/channel")
    public ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannel(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.syncPolicyServiceChargeBankChannel(basePageRequest,syncDate);
    }

    @ApiOperation(value = "执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表-支付明细", notes = "执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表-支付明细")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/policy/service/charge/bank/channel/payment")
    public ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannelPayment(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.syncPolicyServiceChargeBankChannelPayment(basePageRequest,syncDate);
    }


    @ApiOperation(value = "执行同步销售报表数据", notes = "执行同步销售报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/sale/apply/policy")
    public  ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate){
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.syncSaleApplyPolicy(basePageRequest,syncDate);
    }

    @ApiOperation(value = "查询学校发展基金报表", notes = "查询学校发展基金报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "group/sdf/report")
    public ResultObject<List<GroupSdfReportResponse>> listPolicyGroupSdfReport(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "startDate") String startDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyReportBusinessService.queryListPolicyGroupSdfReport(basePageRequest, startDate);
    }

}
